#!/usr/bin/env python3
# 🚨 AUTO-MIGRATED: Old messaging components replaced with HybridQueueManager
# See MIGRATION_GUIDE.md for details

"""
增强服务注册中心
支持服务发现、健康检查、负载均衡
"""
import asyncio
import json
import time
import statistics
from typing import Dict, List, Optional, Set, Callable
from dataclasses import dataclass, asdict
from enum import Enum

from ..utils.logger import get_logger
from ..messaging.hybrid_queue_manager import HybridQueueManager
from ..infrastructure.redis_sentinel_client import RedisClient
from ..utils.metrics import get_metrics_collector

logger = get_logger(__name__)
metrics = get_metrics_collector()


class ServiceState(Enum):
    """服务状态"""
    HEALTHY = "healthy"
    DEGRADED = "degraded"
    UNHEALTHY = "unhealthy"
    OFFLINE = "offline"


@dataclass
class ServiceInfo:
    """服务信息"""
    service_id: str
    service_type: str
    host_id: str
    endpoint: str
    metadata: Dict
    health_score: float = 100.0
    state: ServiceState = ServiceState.HEALTHY
    last_heartbeat: float = 0.0
    load_factor: float = 0.0
    version: str = "1.0.0"
    capabilities: List[str] = None
    
    def __post_init__(self):
        if self.capabilities is None:
            self.capabilities = []


@dataclass
class LoadBalancerConfig:
    """负载均衡配置"""
    algorithm: str = "round_robin"  # round_robin, least_connections, weighted
    health_threshold: float = 80.0
    enable_sticky_sessions: bool = False
    session_timeout: int = 300


class ServiceRegistry:
    """增强服务注册中心"""
    
    def __init__(self, redis_client: RedisClient, queue_manager: JetStreamClient, 
                 config: Dict = None):
        self.redis = redis_client
        self.jetstream = queue_manager
        self.config = config or {}
        
        # 服务存储
        self.services: Dict[str, ServiceInfo] = {}
        self.service_groups: Dict[str, Set[str]] = {}
        
        # 负载均衡器
        self.load_balancer_config = LoadBalancerConfig(**self.config.get('load_balancer', {}))
        self.round_robin_counters: Dict[str, int] = {}
        
        # 健康检查
        self.health_check_interval = self.config.get('health_check_interval', 30)
        self.health_check_timeout = self.config.get('health_check_timeout', 10)
        self.health_checkers: Dict[str, Callable] = {}
        
        # 事件监听器
        self.event_listeners: Dict[str, List[Callable]] = {}
        
        # 运行状态
        self.running = False
        self.tasks: List[asyncio.Task] = []
        
        # Redis键前缀
        self.service_key_prefix = "mt5:services:"
        self.health_key_prefix = "mt5:health:"
        self.load_key_prefix = "mt5:load:"
    
    async def start(self):
        """启动服务注册中心"""
        if self.running:
            return
        
        logger.info("启动增强服务注册中心")
        self.running = True
        
        try:
            # 设置消息订阅
            await self._setup_subscriptions()
            
            # 启动后台任务
            self.tasks = [
                asyncio.create_task(self._health_check_loop()),
                asyncio.create_task(self._service_cleanup_loop()),
                asyncio.create_task(self._metrics_collection_loop())
            ]
            
            logger.info("增强服务注册中心已启动")
            
        except Exception as e:
            logger.error(f"启动增强服务注册中心失败: {e}")
            await self.stop()
            raise
    
    async def stop(self):
        """停止服务注册中心"""
        self.running = False
        
        # 取消所有任务
        for task in self.tasks:
            task.cancel()
        
        # 等待任务完成
        await asyncio.gather(*self.tasks, return_exceptions=True)
        
        logger.info("增强服务注册中心已停止")
    
    async def register_service(self, service_info: ServiceInfo) -> bool:
        """注册服务"""
        try:
            service_info.last_heartbeat = time.time()
            
            # 存储到内存
            self.services[service_info.service_id] = service_info
            
            # 添加到服务组
            if service_info.service_type not in self.service_groups:
                self.service_groups[service_info.service_type] = set()
            self.service_groups[service_info.service_type].add(service_info.service_id)
            
            # 存储到Redis
            key = f"{self.service_key_prefix}{service_info.service_id}"
            await self.redis.setex(key, 300, json.dumps(asdict(service_info)))  # 5分钟过期
            
            # 发布服务注册事件
            await self._publish_service_event("service_registered", service_info)
            
            # 更新指标
            metrics.increment_counter("service_registry_registrations_total",
                                    tags={'service_type': service_info.service_type})
            
            logger.info(f"服务已注册: {service_info.service_id} ({service_info.service_type})")
            return True
            
        except Exception as e:
            logger.error(f"注册服务失败: {e}")
            return False
    
    async def unregister_service(self, service_id: str) -> bool:
        """注销服务"""
        try:
            if service_id not in self.services:
                return False
            
            service_info = self.services[service_id]
            
            # 从内存中删除
            del self.services[service_id]
            
            # 从服务组中删除
            if service_info.service_type in self.service_groups:
                self.service_groups[service_info.service_type].discard(service_id)
                if not self.service_groups[service_info.service_type]:
                    del self.service_groups[service_info.service_type]
            
            # 从Redis中删除
            key = f"{self.service_key_prefix}{service_id}"
            await self.redis.delete(key)
            
            # 发布服务注销事件
            await self._publish_service_event("service_unregistered", service_info)
            
            logger.info(f"服务已注销: {service_id}")
            return True
            
        except Exception as e:
            logger.error(f"注销服务失败: {e}")
            return False
    
    async def discover_services(self, service_type: str = None, 
                              healthy_only: bool = True) -> List[ServiceInfo]:
        """发现服务"""
        try:
            services = []
            
            if service_type:
                # 查找特定类型的服务
                service_ids = self.service_groups.get(service_type, set())
                for service_id in service_ids:
                    if service_id in self.services:
                        service = self.services[service_id]
                        if not healthy_only or service.state in [ServiceState.HEALTHY, ServiceState.DEGRADED]:
                            services.append(service)
            else:
                # 查找所有服务
                for service in self.services.values():
                    if not healthy_only or service.state in [ServiceState.HEALTHY, ServiceState.DEGRADED]:
                        services.append(service)
            
            return services
            
        except Exception as e:
            logger.error(f"发现服务失败: {e}")
            return []
    
    async def get_service(self, service_id: str) -> Optional[ServiceInfo]:
        """获取特定服务"""
        return self.services.get(service_id)
    
    async def select_service(self, service_type: str, **criteria) -> Optional[ServiceInfo]:
        """根据负载均衡算法选择服务"""
        try:
            # 获取健康的服务
            available_services = await self.discover_services(service_type, healthy_only=True)
            
            if not available_services:
                return None
            
            # 根据算法选择服务
            algorithm = self.load_balancer_config.algorithm
            
            if algorithm == "round_robin":
                return self._select_round_robin(service_type, available_services)
            elif algorithm == "least_connections":
                return self._select_least_connections(available_services)
            elif algorithm == "weighted":
                return self._select_weighted(available_services)
            else:
                # 默认返回第一个
                return available_services[0]
                
        except Exception as e:
            logger.error(f"选择服务失败: {e}")
            return None
    
    def _select_round_robin(self, service_type: str, services: List[ServiceInfo]) -> ServiceInfo:
        """轮询算法"""
        if service_type not in self.round_robin_counters:
            self.round_robin_counters[service_type] = 0
        
        index = self.round_robin_counters[service_type] % len(services)
        self.round_robin_counters[service_type] += 1
        
        return services[index]
    
    def _select_least_connections(self, services: List[ServiceInfo]) -> ServiceInfo:
        """最少连接算法"""
        return min(services, key=lambda s: s.load_factor)
    
    def _select_weighted(self, services: List[ServiceInfo]) -> ServiceInfo:
        """加权算法（基于健康分数）"""
        return max(services, key=lambda s: s.health_score)
    
    async def update_service_health(self, service_id: str, health_score: float, 
                                  metadata: Dict = None):
        """更新服务健康状态"""
        if service_id not in self.services:
            return
        
        service = self.services[service_id]
        old_state = service.state
        
        # 更新健康分数
        service.health_score = health_score
        service.last_heartbeat = time.time()
        
        if metadata:
            service.metadata.update(metadata)
        
        # 根据健康分数更新状态
        threshold = self.load_balancer_config.health_threshold
        if health_score >= threshold:
            service.state = ServiceState.HEALTHY
        elif health_score >= threshold * 0.6:
            service.state = ServiceState.DEGRADED
        else:
            service.state = ServiceState.UNHEALTHY
        
        # 如果状态发生变化，发布事件
        if old_state != service.state:
            await self._publish_service_event("service_state_changed", service)
        
        # 更新Redis
        key = f"{self.service_key_prefix}{service_id}"
        await self.redis.setex(key, 300, json.dumps(asdict(service)))
    
    async def update_service_load(self, service_id: str, load_factor: float):
        """更新服务负载"""
        if service_id not in self.services:
            return
        
        service = self.services[service_id]
        service.load_factor = load_factor
        service.last_heartbeat = time.time()
        
        # 更新Redis中的负载信息
        load_key = f"{self.load_key_prefix}{service_id}"
        await self.redis.setex(load_key, 60, str(load_factor))
    
    def register_health_checker(self, service_type: str, checker: Callable):
        """注册健康检查器"""
        self.health_checkers[service_type] = checker
        logger.info(f"已注册健康检查器: {service_type}")
    
    def register_event_listener(self, event_type: str, listener: Callable):
        """注册事件监听器"""
        if event_type not in self.event_listeners:
            self.event_listeners[event_type] = []
        self.event_listeners[event_type].append(listener)
    
    async def _setup_subscriptions(self):
        """设置消息订阅"""
        # 订阅服务心跳
        await self.jetstream.subscribe(
            "MT5.SERVICE.HEARTBEAT.*",
            self._handle_service_heartbeat
        )
        
        # 订阅服务状态更新
        await self.jetstream.subscribe(
            "MT5.SERVICE.STATUS.*",
            self._handle_service_status
        )
    
    async def _handle_service_heartbeat(self, msg):
        """处理服务心跳"""
        try:
            data = json.loads(msg.data.decode('utf-8'))
            service_id = data.get('service_id')
            health_score = data.get('health_score', 100.0)
            load_factor = data.get('load_factor', 0.0)
            
            if service_id:
                await self.update_service_health(service_id, health_score)
                await self.update_service_load(service_id, load_factor)
            
            await msg.ack()
            
        except Exception as e:
            logger.error(f"处理服务心跳失败: {e}")
            try:
                await msg.nak()
            except:
                pass
    
    async def _handle_service_status(self, msg):
        """处理服务状态更新"""
        try:
            data = json.loads(msg.data.decode('utf-8'))
            # 处理服务状态更新逻辑
            
            await msg.ack()
            
        except Exception as e:
            logger.error(f"处理服务状态更新失败: {e}")
            try:
                await msg.nak()
            except:
                pass
    
    async def _health_check_loop(self):
        """健康检查循环"""
        while self.running:
            try:
                await self._perform_health_checks()
                await asyncio.sleep(self.health_check_interval)
                
            except Exception as e:
                logger.error(f"健康检查循环错误: {e}")
                await asyncio.sleep(10)
    
    async def _perform_health_checks(self):
        """执行健康检查"""
        current_time = time.time()
        
        for service_id, service in list(self.services.items()):
            try:
                # 检查心跳超时
                if current_time - service.last_heartbeat > 60:  # 1分钟超时
                    service.state = ServiceState.OFFLINE
                    await self._publish_service_event("service_timeout", service)
                    continue
                
                # 执行自定义健康检查
                checker = self.health_checkers.get(service.service_type)
                if checker:
                    health_score = await checker(service)
                    await self.update_service_health(service_id, health_score)
                
            except Exception as e:
                logger.error(f"健康检查失败 {service_id}: {e}")
    
    async def _service_cleanup_loop(self):
        """服务清理循环"""
        while self.running:
            try:
                await self._cleanup_offline_services()
                await asyncio.sleep(300)  # 每5分钟清理一次
                
            except Exception as e:
                logger.error(f"服务清理循环错误: {e}")
                await asyncio.sleep(60)
    
    async def _cleanup_offline_services(self):
        """清理离线服务"""
        current_time = time.time()
        offline_services = []
        
        for service_id, service in self.services.items():
            # 如果服务离线超过10分钟，则清理
            if (service.state == ServiceState.OFFLINE and 
                current_time - service.last_heartbeat > 600):
                offline_services.append(service_id)
        
        for service_id in offline_services:
            await self.unregister_service(service_id)
            logger.info(f"清理离线服务: {service_id}")
    
    async def _metrics_collection_loop(self):
        """指标收集循环"""
        while self.running:
            try:
                await self._collect_metrics()
                await asyncio.sleep(60)  # 每分钟收集一次
                
            except Exception as e:
                logger.error(f"指标收集循环错误: {e}")
                await asyncio.sleep(30)
    
    async def _collect_metrics(self):
        """收集指标"""
        # 服务统计
        total_services = len(self.services)
        healthy_services = len([s for s in self.services.values() 
                              if s.state == ServiceState.HEALTHY])
        degraded_services = len([s for s in self.services.values() 
                               if s.state == ServiceState.DEGRADED])
        unhealthy_services = len([s for s in self.services.values() 
                                if s.state == ServiceState.UNHEALTHY])
        
        # 更新Prometheus指标
        metrics.set_gauge("service_registry_total_services", total_services)
        metrics.set_gauge("service_registry_healthy_services", healthy_services)
        metrics.set_gauge("service_registry_degraded_services", degraded_services)
        metrics.set_gauge("service_registry_unhealthy_services", unhealthy_services)
        
        # 按服务类型统计
        for service_type, service_ids in self.service_groups.items():
            type_services = [self.services[sid] for sid in service_ids if sid in self.services]
            type_healthy = len([s for s in type_services if s.state == ServiceState.HEALTHY])
            
            metrics.set_gauge("service_registry_services_by_type", len(type_services),
                            tags={'service_type': service_type})
            metrics.set_gauge("service_registry_healthy_by_type", type_healthy,
                            tags={'service_type': service_type})
    
    async def _publish_service_event(self, event_type: str, service_info: ServiceInfo):
        """发布服务事件"""
        try:
            # 发布到NATS
            event_data = {
                'event_type': event_type,
                'service_info': asdict(service_info),
                'timestamp': time.time()
            }
            
            await self.jetstream.publish(
                f"MT5.SERVICE.EVENT.{event_type.upper()}",
                json.dumps(event_data).encode('utf-8')
            )
            
            # 调用本地监听器
            listeners = self.event_listeners.get(event_type, [])
            for listener in listeners:
                try:
                    await listener(service_info)
                except Exception as e:
                    logger.error(f"事件监听器执行失败: {e}")
            
        except Exception as e:
            logger.error(f"发布服务事件失败: {e}")
    
    def get_service_statistics(self) -> Dict:
        """获取服务统计信息"""
        stats = {
            'total_services': len(self.services),
            'service_groups': len(self.service_groups),
            'states': {
                'healthy': 0,
                'degraded': 0,
                'unhealthy': 0,
                'offline': 0
            },
            'load_distribution': [],
            'health_distribution': []
        }
        
        # 状态分布
        for service in self.services.values():
            stats['states'][service.state.value] += 1
            stats['load_distribution'].append(service.load_factor)
            stats['health_distribution'].append(service.health_score)
        
        # 计算平均值
        if stats['load_distribution']:
            stats['avg_load'] = statistics.mean(stats['load_distribution'])
            stats['avg_health'] = statistics.mean(stats['health_distribution'])
        else:
            stats['avg_load'] = 0.0
            stats['avg_health'] = 0.0
        
        return stats


# 全局服务注册中心实例
_service_registry = None

def get_service_registry() -> ServiceRegistry:
    """获取全局服务注册中心实例"""
    global _service_registry
    if _service_registry is None:
        raise RuntimeError("服务注册中心未初始化")
    return _service_registry

def initialize_service_registry(redis_client: RedisClient, queue_manager: JetStreamClient, 
                               config: Dict = None) -> ServiceRegistry:
    """初始化全局服务注册中心"""
    global _service_registry
    _service_registry = ServiceRegistry(redis_client, queue_manager, config)
    return _service_registry