#!/usr/bin/env python3
"""
MT5统一启动器
整合所有启动功能：角色管理、服务启动、系统验证

用法:
  python3 scripts/mt5_launcher.py                    # 交互式启动
  python3 scripts/mt5_launcher.py start              # 根据角色分配启动
  python3 scripts/mt5_launcher.py start --master ACC001 --slaves ACC002  # 指定角色启动
  python3 scripts/mt5_launcher.py role status        # 查看角色状态
  python3 scripts/mt5_launcher.py role switch        # 切换角色
  python3 scripts/mt5_launcher.py verify             # 验证系统
"""
import asyncio
import sys
import signal
import argparse
import yaml
import subprocess
from pathlib import Path
from typing import Optional, Dict, List, Any

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent.parent))

# 核心分离进程架构 - 最新最优实现
from src.core.mt5_coordinator import DistributedMT5Coordinator
# from src.executors.risk_manager import RiskManager, RiskSettings  # 暂时不使用
from src.messaging.jetstream_client import JetStreamClient, JetStreamConfig
from src.infrastructure.redis_sentinel_client import RedisSentinelClient, RedisSentinelConfig
from src.utils.logger import get_logger, setup_logging
from src.utils.metrics import start_performance_monitoring, stop_performance_monitoring

logger = get_logger(__name__)


class MT5Launcher:
    """MT5统一启动器"""
    
    def __init__(self, config_dir: str = "config"):
        self.config_dir = Path(config_dir)
        self.accounts_dir = self.config_dir / "accounts"
        self.role_config_path = self.config_dir / "role_assignment.yaml"
        
        self.services = {}
        self.running = False
        
    def load_role_assignment(self) -> Dict:
        """加载角色分配配置"""
        try:
            if self.role_config_path.exists():
                with open(self.role_config_path, 'r', encoding='utf-8') as f:
                    return yaml.safe_load(f) or {}
            else:
                return self._create_default_assignment()
        except Exception as e:
            logger.error(f"加载角色分配配置失败: {e}")
            return {}
    
    def _create_default_assignment(self) -> Dict:
        """创建默认角色分配"""
        available_accounts = self.get_available_accounts()
        if not available_accounts:
            logger.warning("没有找到可用的账户配置")
            return {}
        
        default_assignment = {
            'role_assignment': {
                'master_account': available_accounts[0],
                'slave_accounts': available_accounts[1:] if len(available_accounts) > 1 else [],
                'assignment_history': [],
                'manual_control': {'enabled': True},
                'account_status': {}
            }
        }
        
        # 初始化账户状态
        for i, account in enumerate(available_accounts):
            default_assignment['role_assignment']['account_status'][account] = {
                'status': 'active',
                'role': 'master' if i == 0 else 'slave'
            }
        
        # 保存默认配置
        with open(self.role_config_path, 'w', encoding='utf-8') as f:
            yaml.dump(default_assignment, f, default_flow_style=False, allow_unicode=True)
        
        return default_assignment
    
    def get_available_accounts(self) -> List[str]:
        """获取可用的账户列表（仅本机账户）"""
        accounts = []
        for yaml_file in self.accounts_dir.glob("*.yaml"):
            if yaml_file.name not in ["README.md"]:
                account_id = yaml_file.stem.upper()
                config = self.load_account_config(account_id)
                if config:
                    # 跳过远程主机账户
                    deployment = config.get('deployment', {})
                    if deployment.get('host_type') == 'remote' or deployment.get('local_enabled') == False:
                        logger.debug(f"跳过远程主机账户: {account_id}")
                        continue

                    # 跳过禁用的账户
                    if not config.get('enabled', True):
                        logger.debug(f"跳过已禁用账户: {account_id}")
                        continue

                    if self.validate_account(account_id):
                        accounts.append(account_id)
        return sorted(accounts)
    
    def load_account_config(self, account_id: str) -> Optional[Dict]:
        """加载账户配置"""
        config_file = self.accounts_dir / f"{account_id}.yaml"
        if not config_file.exists():
            logger.error(f"账户配置文件不存在: {config_file}")
            return None
        
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
                return config.get('account', {})
        except Exception as e:
            logger.error(f"加载账户配置失败 {account_id}: {e}")
            return None
    
    def validate_account(self, account_id: str) -> bool:
        """验证账户配置"""
        config = self.load_account_config(account_id)
        if not config:
            return False

        # 跳过远程主机账户或本机禁用的账户
        deployment = config.get('deployment', {})
        if deployment.get('host_type') == 'remote' or deployment.get('local_enabled') == False:
            logger.info(f"跳过远程主机账户验证: {account_id}")
            return True  # 远程账户配置有效，但不在本机验证

        # 检查本机是否启用
        if not config.get('enabled', True):
            logger.info(f"跳过已禁用账户: {account_id}")
            return True

        required_fields = ['id', 'connection.login', 'connection.server']
        for field in required_fields:
            keys = field.split('.')
            current = config
            try:
                for key in keys:
                    current = current[key]
            except (KeyError, TypeError):
                return False
        return True
    
    def load_system_config(self) -> Dict:
        """加载系统配置"""
        for config_file in ["optimized_system.yaml", "system.yaml"]:
            config_path = self.config_dir / config_file
            if config_path.exists():
                try:
                    with open(config_path, 'r', encoding='utf-8') as f:
                        return yaml.safe_load(f) or {}
                except Exception as e:
                    logger.error(f"加载系统配置失败 {config_file}: {e}")
        return {}
    
    async def create_jetstream_client(self, account_id: str) -> Optional[JetStreamClient]:
        """创建JetStream客户端"""
        try:
            jetstream_config = JetStreamConfig(
                servers=["nats://localhost:4222"],
                name=f"{account_id.lower()}-client",
                max_reconnect_attempts=10,
                reconnect_time_wait=2.0,
                connect_timeout=10.0
            )
            
            client = JetStreamClient(jetstream_config)
            if await client.connect():
                logger.info(f"✅ JetStream连接成功: {account_id}")
                return client
            else:
                logger.error(f"❌ JetStream连接失败: {account_id}")
                return None
        except Exception as e:
            logger.error(f"JetStream客户端创建失败 {account_id}: {e}")
            return None
    
    async def create_redis_client(self, account_id: str) -> Optional[RedisSentinelClient]:
        """创建Redis Sentinel客户端"""
        # 暂时跳过Redis连接，直接返回None以在无缓存模式下运行
        logger.warning(f"⚠️  跳过Redis连接: {account_id}，将在无缓存模式下运行")
        return None
    
    async def start_master_service(self, account_id: str, slave_ids: List[str]) -> bool:
        """启动主账户服务"""
        try:
            logger.info(f"🚀 启动主账户服务: {account_id}")
            logger.info(f"📋 从账户列表: {slave_ids}")

            logger.info(f"📂 加载账户配置: {account_id}")
            account_config = self.load_account_config(account_id)
            if not account_config:
                logger.error(f"❌ 账户配置加载失败: {account_id}")
                return False
            logger.info(f"✅ 账户配置加载成功: {account_id}")

            logger.info(f"🔗 创建JetStream客户端: {account_id}")
            jetstream_client = await self.create_jetstream_client(account_id)
            logger.info(f"🔗 创建Redis客户端: {account_id}")
            redis_client = await self.create_redis_client(account_id)
            logger.info(f"✅ Redis客户端创建完成: {account_id}")

            if not jetstream_client:
                logger.error(f"❌ JetStream连接失败，无法启动主账户服务: {account_id}")
                return False
            logger.info(f"✅ JetStream客户端创建成功: {account_id}")
            
            # 监控器配置
            logger.info(f"⚙️  配置主账户监控器: {account_id}")
            monitoring_config = account_config.get('monitoring', {})
            monitor_config = MonitorConfig(
                base_polling_interval=monitoring_config.get('polling_interval', 0.001),
                adaptive_enabled=monitoring_config.get('adaptive_polling', True),
                batch_size=10,
                heartbeat_interval=30.0
            )
            logger.info(f"📊 监控器配置: 轮询间隔={monitor_config.base_polling_interval}s")

            # 创建监控器
            logger.info(f"🏗️  创建主账户监控器: {account_id}")
            try:
                monitor = OptimizedMasterMonitor(
                    account_config=account_config,
                    jetstream_client=jetstream_client,
                    redis_client=redis_client,
                    monitor_config=monitor_config,
                    slave_ids=slave_ids
                )
                logger.info(f"✅ 主账户监控器创建成功: {account_id}")
            except Exception as e:
                logger.error(f"❌ 主账户监控器创建失败: {e}")
                import traceback
                logger.error(f"详细错误: {traceback.format_exc()}")
                return False

            logger.info(f"💾 注册服务: master_{account_id}")
            self.services[f"master_{account_id}"] = {
                'type': 'master',
                'account_id': account_id,
                'service': monitor,
                'jetstream_client': jetstream_client,
                'redis_client': redis_client
            }

            # 启动监控器
            logger.info(f"🎯 启动主账户监控器: {account_id}")
            try:
                await monitor.start()
                logger.info(f"🎉 主账户服务启动完成: {account_id}")
                return True
            except Exception as e:
                logger.error(f"❌ 主账户监控器启动失败: {e}")
                import traceback
                logger.error(f"详细错误: {traceback.format_exc()}")
                return False
            
        except Exception as e:
            logger.error(f"❌ 启动主账户服务失败 {account_id}: {e}")
            import traceback
            logger.error(f"详细错误信息: {traceback.format_exc()}")
            return False
    
    async def start_slave_service(self, account_id: str) -> bool:
        """启动从账户服务"""
        try:
            logger.info(f"🚀 启动从账户服务: {account_id}")

            logger.info(f"📂 加载从账户配置: {account_id}")
            account_config = self.load_account_config(account_id)
            if not account_config:
                logger.error(f"❌ 从账户配置加载失败: {account_id}")
                return False
            logger.info(f"✅ 从账户配置加载成功: {account_id}")

            logger.info(f"🔗 创建从账户JetStream客户端: {account_id}")
            jetstream_client = await self.create_jetstream_client(account_id)
            logger.info(f"🔗 创建从账户Redis客户端: {account_id}")
            redis_client = await self.create_redis_client(account_id)

            if not jetstream_client:
                logger.error(f"❌ JetStream连接失败，无法启动从账户服务: {account_id}")
                return False
            logger.info(f"✅ 从账户JetStream客户端创建成功: {account_id}")
            
            # 创建执行器
            logger.info(f"🏗️  创建从账户执行器: {account_id}")
            executor = OptimizedSlaveExecutor(
                account_id=account_id,
                jetstream_client=jetstream_client,
                config_manager=self.config_manager,
                mt5_client=None,
                host_id=self.host_id
            )
            logger.info(f"✅ 从账户执行器创建成功: {account_id}")

            logger.info(f"💾 注册服务: slave_{account_id}")
            self.services[f"slave_{account_id}"] = {
                'type': 'slave',
                'account_id': account_id,
                'service': executor,
                'jetstream_client': jetstream_client,
                'redis_client': redis_client
            }
            
            # 启动执行器
            logger.info(f"🎯 启动从账户执行器: {account_id}")
            await executor.start()
            logger.info(f"🎉 从账户服务启动完成: {account_id}")
            return True

        except Exception as e:
            logger.error(f"❌ 启动从账户服务失败 {account_id}: {e}")
            import traceback
            logger.error(f"详细错误信息: {traceback.format_exc()}")
            return False
    
    async def start_services(self, master_account: str = None, slave_accounts: List[str] = None) -> bool:
        """启动服务"""
        try:
            # 如果没有指定角色，从配置文件读取
            if master_account is None:
                assignment = self.load_role_assignment()
                role_config = assignment.get('role_assignment', {})
                master_account = role_config.get('master_account')
                slave_accounts = role_config.get('slave_accounts', []) if slave_accounts is None else slave_accounts
            
            if not master_account:
                logger.error("未指定主账户")
                return False
            
            logger.info(f"📋 启动角色分配:")
            logger.info(f"   主账户: {master_account}")
            logger.info(f"   从账户: {', '.join(slave_accounts) if slave_accounts else '无'}")
            
            # 设置监控（跳过日志重新配置，保持现有日志设置）
            system_config = self.load_system_config()
            # setup_logging(system_config.get('logging', {}))  # 注释掉，避免覆盖现有日志配置
            start_performance_monitoring()
            
            # 设置信号处理
            self._setup_signal_handlers()
            self.running = True
            
            # 启动服务 - 严格主从分离，防止循环跟单
            logger.info(f"🔧 开始创建服务任务...")
            tasks = []

            # 只为主账户启动监控器服务（不启动执行器）
            logger.info(f"📋 创建主账户监控器任务: {master_account}")
            master_task = self.start_master_service(master_account, slave_accounts or [])
            tasks.append(master_task)
            logger.info(f"✅ 主账户监控器任务已创建")

            # 只为从账户启动执行器服务（不启动监控器）
            for slave_account in (slave_accounts or []):
                logger.info(f"📋 创建从账户执行器任务: {slave_account}")
                slave_task = self.start_slave_service(slave_account)
                tasks.append(slave_task)
                logger.info(f"✅ 从账户执行器任务已创建: {slave_account}")

            logger.info(f"🚀 开始并发执行 {len(tasks)} 个服务任务...")

            # 逐个启动服务，而不是并发启动，以便更好地调试
            results = []
            service_names = [f"master_{master_account}"] + [f"slave_{acc}" for acc in (slave_accounts or [])]

            for i, (task, service_name) in enumerate(zip(tasks, service_names)):
                try:
                    logger.info(f"🎯 启动服务 {i+1}/{len(tasks)}: {service_name}")
                    result = await task
                    results.append(result)
                    if result is True:
                        logger.info(f"✅ 服务启动成功: {service_name}")
                    else:
                        logger.error(f"❌ 服务启动失败: {service_name}, 返回值: {result}")
                except Exception as e:
                    logger.error(f"❌ 服务启动异常: {service_name}, 错误: {e}")
                    import traceback
                    logger.error(f"详细错误信息: {traceback.format_exc()}")
                    results.append(e)

            logger.info(f"📊 服务任务执行完成，收到 {len(results)} 个结果")

            success_count = sum(1 for r in results if r is True)
            logger.info(f"📈 成功启动服务数量: {success_count}/{len(results)}")

            # 详细记录启动结果
            for i, (result, service_name) in enumerate(zip(results, service_names)):
                if isinstance(result, Exception):
                    logger.error(f"服务启动失败 {service_name}: {result}")
                elif result is True:
                    logger.info(f"服务启动成功 {service_name}")
                else:
                    logger.warning(f"服务启动返回 {service_name}: {result}")

            if success_count == len(results):
                logger.info(f"✅ 所有服务启动成功 ({success_count}/{len(results)})")
                return True
            else:
                logger.error(f"❌ 部分服务启动失败 ({success_count}/{len(results)})")
                return False
                
        except Exception as e:
            logger.error(f"启动服务失败: {e}")
            return False
    
    async def stop_services(self):
        """停止所有服务"""
        if not self.running:
            return
        
        logger.info("🛑 正在停止所有服务...")
        self.running = False
        
        for service_name, service_info in self.services.items():
            try:
                logger.info(f"停止服务: {service_name}")
                await service_info['service'].stop()
                
                if service_info['jetstream_client']:
                    await service_info['jetstream_client'].disconnect()
                if service_info['redis_client']:
                    await service_info['redis_client'].disconnect()
                    
            except Exception as e:
                logger.error(f"停止服务失败 {service_name}: {e}")
        
        stop_performance_monitoring()
        logger.info("✅ 所有服务已停止")
    
    def _setup_signal_handlers(self):
        """设置信号处理器"""
        def signal_handler(signum, frame):
            logger.info(f"收到信号 {signum}，正在停止服务...")
            asyncio.create_task(self.stop_services())
        
        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)
    
    def show_role_status(self):
        """显示角色状态"""
        assignment = self.load_role_assignment()
        role_config = assignment.get('role_assignment', {})
        
        print("\n" + "="*50)
        print("📊 当前角色分配状态")
        print("="*50)
        
        master = role_config.get('master_account', '未配置')
        slaves = role_config.get('slave_accounts', [])
        
        print(f"主账户: {master}")
        print(f"从账户: {', '.join(slaves) if slaves else '无'}")
        
        available_accounts = self.get_available_accounts()
        print(f"\n可用账户:")
        for account in available_accounts:
            status = "✅" if self.validate_account(account) else "❌"
            print(f"  {account} {status}")
    
    def switch_roles_interactive(self):
        """交互式角色切换"""
        available_accounts = self.get_available_accounts()
        if not available_accounts:
            print("❌ 没有找到可用的账户")
            return False
        
        print("\n🔄 交互式角色切换")
        print(f"可用账户: {', '.join(available_accounts)}")
        
        # 选择主账户
        master = input("\n请输入主账户ID: ").strip().upper()
        if master not in available_accounts:
            print(f"❌ 无效的主账户: {master}")
            return False
        
        # 选择从账户
        slaves_input = input("请输入从账户ID (多个用逗号分隔，可为空): ").strip().upper()
        slaves = [s.strip() for s in slaves_input.split(',') if s.strip()] if slaves_input else []
        
        # 验证从账户
        invalid_slaves = [s for s in slaves if s not in available_accounts or s == master]
        if invalid_slaves:
            print(f"❌ 无效的从账户: {', '.join(invalid_slaves)}")
            return False
        
        # 更新角色分配
        return self.switch_roles(master, slaves)
    
    def switch_roles(self, master: str, slaves: List[str]) -> bool:
        """切换角色"""
        try:
            assignment = self.load_role_assignment()
            role_config = assignment.get('role_assignment', {})
            
            # 更新角色分配
            role_config['master_account'] = master
            role_config['slave_accounts'] = slaves
            
            # 更新账户状态
            account_status = role_config.get('account_status', {})
            for account in self.get_available_accounts():
                if account not in account_status:
                    account_status[account] = {'status': 'active'}
                
                if account == master:
                    account_status[account]['role'] = 'master'
                elif account in slaves:
                    account_status[account]['role'] = 'slave'
                else:
                    account_status[account]['role'] = 'inactive'
            
            # 保存配置
            with open(self.role_config_path, 'w', encoding='utf-8') as f:
                yaml.dump(assignment, f, default_flow_style=False, allow_unicode=True)
            
            logger.info(f"✅ 角色切换成功: 主账户={master}, 从账户={slaves}")
            return True
            
        except Exception as e:
            logger.error(f"角色切换失败: {e}")
            return False
    
    def verify_system(self) -> bool:
        """验证系统组件"""
        print("🔧 验证优化系统组件...")
        try:
            result = subprocess.run([
                sys.executable, "scripts/migrate_to_optimized.py", "--verify-only"
            ], capture_output=True, text=True, cwd=Path(__file__).parent.parent)
            
            if result.returncode == 0:
                print("✅ 系统组件验证通过!")
                if result.stdout:
                    print(result.stdout)
                return True
            else:
                print("❌ 系统组件验证失败!")
                if result.stderr:
                    print(result.stderr)
                return False
        except Exception as e:
            print(f"❌ 验证过程出错: {e}")
            return False
    
    def show_interactive_menu(self):
        """显示交互式菜单"""
        while True:
            print("\n" + "="*50)
            print("🚀 MT5统一启动器")
            print("="*50)
            print("1. 📊 查看角色状态")
            print("2. 🔄 切换角色")
            print("3. 🚀 启动服务 (根据当前角色分配)")
            print("4. 🎯 手动指定角色启动")
            print("5. 🔧 验证系统组件")
            print("0. 🚪 退出")
            print("="*50)
            
            try:
                choice = input("请选择操作 (0-5): ").strip()
                
                if choice == '0':
                    print("👋 再见!")
                    break
                elif choice == '1':
                    self.show_role_status()
                elif choice == '2':
                    if self.switch_roles_interactive():
                        print("✅ 角色切换成功!")
                        self.show_role_status()
                elif choice == '3':
                    print("🚀 准备启动服务...")
                    
                    # 直接启动新的事件循环
                    import threading
                    import time
                    
                    def run_services():
                        try:
                            new_loop = asyncio.new_event_loop()
                            asyncio.set_event_loop(new_loop)
                            new_loop.run_until_complete(self.start_and_run())
                        except Exception as e:
                            print(f"❌ 启动失败: {e}")
                        finally:
                            new_loop.close()
                    
                    print("启动服务中...")
                    thread = threading.Thread(target=run_services)
                    thread.daemon = True
                    thread.start()
                    
                    try:
                        while thread.is_alive():
                            time.sleep(0.1)
                    except KeyboardInterrupt:
                        print("\n收到中断信号，正在停止...")
                elif choice == '4':
                    self.manual_start_menu()
                elif choice == '5':
                    self.verify_system()
                else:
                    print("❌ 无效选择")
                    
                if choice not in ['0', '3']:
                    input("\n按回车继续...")
                    
            except KeyboardInterrupt:
                print("\n👋 再见!")
                break
    
    def manual_start_menu(self):
        """手动启动菜单"""
        available_accounts = self.get_available_accounts()
        if not available_accounts:
            print("❌ 没有可用的账户")
            return
        
        print(f"\n🎯 手动指定角色启动")
        print(f"可用账户: {', '.join(available_accounts)}")
        
        master = input("请输入主账户ID: ").strip().upper()
        if master not in available_accounts:
            print(f"❌ 无效的主账户: {master}")
            return
        
        slaves_input = input("请输入从账户ID (多个用逗号分隔，可为空): ").strip().upper()
        slaves = [s.strip() for s in slaves_input.split(',') if s.strip()] if slaves_input else []
        
        invalid_slaves = [s for s in slaves if s not in available_accounts or s == master]
        if invalid_slaves:
            print(f"❌ 无效的从账户: {', '.join(invalid_slaves)}")
            return
        
        print(f"🚀 启动服务: 主账户={master}, 从账户={slaves}")
        
        # 直接启动新的事件循环
        import threading
        import time
        
        def run_services():
            try:
                new_loop = asyncio.new_event_loop()
                asyncio.set_event_loop(new_loop)
                new_loop.run_until_complete(self.start_and_run(master, slaves))
            except Exception as e:
                print(f"❌ 启动失败: {e}")
            finally:
                new_loop.close()
        
        print("启动服务中...")
        thread = threading.Thread(target=run_services)
        thread.daemon = True
        thread.start()
        
        try:
            while thread.is_alive():
                time.sleep(0.1)
        except KeyboardInterrupt:
            print("\n收到中断信号，正在停止...")
    
    async def start_and_run(self, master: str = None, slaves: List[str] = None):
        """启动并运行服务"""
        try:
            success = await self.start_services(master, slaves)
            if not success:
                print("❌ 服务启动失败")
                return
            
            print("✅ 服务启动成功! 按 Ctrl+C 停止服务")
            
            # 保持运行
            while self.running:
                await asyncio.sleep(1)
                
        except KeyboardInterrupt:
            print("\n收到中断信号")
        finally:
            await self.stop_services()


async def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='MT5统一启动器')
    parser.add_argument('--config-dir', '-c', default='config', help='配置目录')
    
    subparsers = parser.add_subparsers(dest='command', help='可用命令')
    
    # start命令
    start_parser = subparsers.add_parser('start', help='启动服务')
    start_parser.add_argument('--master', '-m', help='主账户ID')
    start_parser.add_argument('--slaves', '-s', nargs='*', default=[], help='从账户ID列表')
    
    # role命令
    role_parser = subparsers.add_parser('role', help='角色管理')
    role_subparsers = role_parser.add_subparsers(dest='role_action')
    role_subparsers.add_parser('status', help='查看角色状态')
    role_subparsers.add_parser('switch', help='切换角色')
    
    # verify命令
    verify_parser = subparsers.add_parser('verify', help='验证系统组件')
    
    args = parser.parse_args()
    
    launcher = MT5Launcher(args.config_dir)
    
    try:
        if args.command == 'start':
            await launcher.start_and_run(args.master, args.slaves)
        elif args.command == 'role':
            if args.role_action == 'status':
                launcher.show_role_status()
            elif args.role_action == 'switch':
                launcher.switch_roles_interactive()
        elif args.command == 'verify':
            launcher.verify_system()
        else:
            # 默认显示交互式菜单
            launcher.show_interactive_menu()
    except Exception as e:
        logger.error(f"程序执行失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())