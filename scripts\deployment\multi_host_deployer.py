#!/usr/bin/env python3
"""
多主机部署工具
自动化部署MT5分布式交易系统到多个主机
"""
import asyncio
import json
import yaml
import subprocess
import paramiko
import time
from pathlib import Path
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass, asdict
from concurrent.futures import ThreadPoolExecutor

import sys
sys.path.append(str(Path(__file__).parent.parent.parent))

from src.utils.logger import get_logger

logger = get_logger(__name__)


@dataclass
class HostConfig:
    """主机配置"""
    host_id: str
    hostname: str
    ip_address: str
    ssh_port: int = 22
    ssh_user: str = "root"
    ssh_key_path: Optional[str] = None
    ssh_password: Optional[str] = None
    docker_compose_file: str = "docker-compose-production.yml"
    capabilities: List[str] = None
    accounts: List[str] = None
    region: str = "default"
    environment: str = "production"


@dataclass
class DeploymentTask:
    """部署任务"""
    task_id: str
    host_id: str
    task_type: str  # 'setup', 'deploy', 'start', 'stop', 'update', 'health_check'
    command: str
    status: str = "pending"  # 'pending', 'running', 'completed', 'failed'
    start_time: Optional[float] = None
    end_time: Optional[float] = None
    output: str = ""
    error: str = ""


class MultiHostDeployer:
    """多主机部署器"""
    
    def __init__(self, config_file: str):
        self.config_file = Path(config_file)
        self.config = self._load_config()
        self.hosts: Dict[str, HostConfig] = {}
        self.deployment_tasks: List[DeploymentTask] = []
        self.ssh_connections: Dict[str, paramiko.SSHClient] = {}
        
        # 加载主机配置
        self._load_host_configs()
        
        # 线程池用于并行执行
        self.executor = ThreadPoolExecutor(max_workers=10)
    
    def _load_config(self) -> Dict:
        """加载配置文件"""
        try:
            with open(self.config_file, 'r', encoding='utf-8') as f:
                if self.config_file.suffix.lower() in ['.yaml', '.yml']:
                    return yaml.safe_load(f)
                else:
                    return json.load(f)
        except Exception as e:
            logger.error(f"加载配置文件失败: {e}")
            raise
    
    def _load_host_configs(self):
        """加载主机配置"""
        hosts_config = self.config.get('hosts', [])
        
        for host_data in hosts_config:
            host_config = HostConfig(
                host_id=host_data['host_id'],
                hostname=host_data['hostname'],
                ip_address=host_data['ip_address'],
                ssh_port=host_data.get('ssh_port', 22),
                ssh_user=host_data.get('ssh_user', 'root'),
                ssh_key_path=host_data.get('ssh_key_path'),
                ssh_password=host_data.get('ssh_password'),
                docker_compose_file=host_data.get('docker_compose_file', 'docker-compose-production.yml'),
                capabilities=host_data.get('capabilities', ['trading']),
                accounts=host_data.get('accounts', []),
                region=host_data.get('region', 'default'),
                environment=host_data.get('environment', 'production')
            )
            
            self.hosts[host_config.host_id] = host_config
        
        logger.info(f"加载了 {len(self.hosts)} 个主机配置")
    
    async def deploy_all_hosts(self) -> bool:
        """部署到所有主机"""
        logger.info("开始多主机部署...")
        
        try:
            # 1. 连接测试
            await self._test_all_connections()
            
            # 2. 环境准备
            await self._prepare_all_environments()
            
            # 3. 文件传输
            await self._transfer_files_to_all_hosts()
            
            # 4. 服务部署
            await self._deploy_services_to_all_hosts()
            
            # 5. 健康检查
            await self._health_check_all_hosts()
            
            logger.info("✅ 多主机部署完成")
            return True
            
        except Exception as e:
            logger.error(f"❌ 多主机部署失败: {e}")
            return False
        
        finally:
            self._close_all_connections()
    
    async def _test_all_connections(self):
        """测试所有主机连接"""
        logger.info("🔗 测试主机连接...")
        
        tasks = []
        for host_id, host_config in self.hosts.items():
            task = asyncio.create_task(self._test_host_connection(host_id, host_config))
            tasks.append(task)
        
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        failed_hosts = []
        for i, result in enumerate(results):
            host_id = list(self.hosts.keys())[i]
            if isinstance(result, Exception):
                failed_hosts.append(host_id)
                logger.error(f"❌ 主机 {host_id} 连接失败: {result}")
            else:
                logger.info(f"✅ 主机 {host_id} 连接成功")
        
        if failed_hosts:
            raise Exception(f"以下主机连接失败: {failed_hosts}")
    
    async def _test_host_connection(self, host_id: str, host_config: HostConfig):
        """测试单个主机连接"""
        ssh = paramiko.SSHClient()
        ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())
        
        try:
            if host_config.ssh_key_path:
                ssh.connect(
                    hostname=host_config.ip_address,
                    port=host_config.ssh_port,
                    username=host_config.ssh_user,
                    key_filename=host_config.ssh_key_path,
                    timeout=30
                )
            else:
                ssh.connect(
                    hostname=host_config.ip_address,
                    port=host_config.ssh_port,
                    username=host_config.ssh_user,
                    password=host_config.ssh_password,
                    timeout=30
                )
            
            # 测试基本命令
            stdin, stdout, stderr = ssh.exec_command('echo "connection test"')
            output = stdout.read().decode('utf-8').strip()
            
            if output != "connection test":
                raise Exception("SSH命令执行测试失败")
            
            self.ssh_connections[host_id] = ssh
            
        except Exception as e:
            ssh.close()
            raise Exception(f"SSH连接失败: {e}")
    
    async def _prepare_all_environments(self):
        """准备所有主机环境"""
        logger.info("🛠️ 准备主机环境...")
        
        tasks = []
        for host_id in self.hosts.keys():
            task = asyncio.create_task(self._prepare_host_environment(host_id))
            tasks.append(task)
        
        await asyncio.gather(*tasks)
        logger.info("✅ 所有主机环境准备完成")
    
    async def _prepare_host_environment(self, host_id: str):
        """准备单个主机环境"""
        ssh = self.ssh_connections[host_id]
        
        # 准备命令列表
        commands = [
            # 更新系统
            "apt-get update -y",
            
            # 安装Docker
            "curl -fsSL https://get.docker.com -o get-docker.sh && sh get-docker.sh",
            
            # 安装Docker Compose
            "curl -L \"https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)\" -o /usr/local/bin/docker-compose",
            "chmod +x /usr/local/bin/docker-compose",
            
            # 创建工作目录
            "mkdir -p /opt/mt5-trading",
            "mkdir -p /opt/mt5-trading/logs",
            "mkdir -p /opt/mt5-trading/data",
            "mkdir -p /opt/mt5-trading/config",
            
            # 启动Docker服务
            "systemctl enable docker",
            "systemctl start docker"
        ]
        
        for command in commands:
            try:
                await self._execute_ssh_command(ssh, command, timeout=300)
                logger.debug(f"✅ {host_id}: {command}")
            except Exception as e:
                logger.warning(f"⚠️ {host_id}: 命令执行警告 - {command}: {e}")
    
    async def _transfer_files_to_all_hosts(self):
        """传输文件到所有主机"""
        logger.info("📁 传输文件到主机...")
        
        tasks = []
        for host_id in self.hosts.keys():
            task = asyncio.create_task(self._transfer_files_to_host(host_id))
            tasks.append(task)
        
        await asyncio.gather(*tasks)
        logger.info("✅ 文件传输完成")
    
    async def _transfer_files_to_host(self, host_id: str):
        """传输文件到单个主机"""
        ssh = self.ssh_connections[host_id]
        host_config = self.hosts[host_id]
        
        # 创建SFTP连接
        sftp = ssh.open_sftp()
        
        try:
            # 项目根目录
            project_root = Path(__file__).parent.parent.parent
            
            # 需要传输的文件和目录
            transfer_items = [
                ('docker', '/opt/mt5-trading/docker'),
                ('config', '/opt/mt5-trading/config'),
                ('src', '/opt/mt5-trading/src'),
                ('requirements.txt', '/opt/mt5-trading/requirements.txt'),
                ('.env.example', '/opt/mt5-trading/.env'),
            ]
            
            for local_path, remote_path in transfer_items:
                local_full_path = project_root / local_path
                
                if local_full_path.is_file():
                    # 传输文件
                    await self._transfer_file(sftp, str(local_full_path), remote_path)
                elif local_full_path.is_dir():
                    # 传输目录
                    await self._transfer_directory(sftp, str(local_full_path), remote_path)
                
                logger.debug(f"✅ {host_id}: 传输 {local_path} -> {remote_path}")
            
            # 传输主机特定的配置文件
            await self._transfer_host_specific_configs(sftp, host_id, host_config)
            
        finally:
            sftp.close()
    
    async def _transfer_file(self, sftp, local_path: str, remote_path: str):
        """传输单个文件"""
        # 确保远程目录存在
        remote_dir = str(Path(remote_path).parent)
        try:
            sftp.makedirs(remote_dir)
        except:
            pass
        
        sftp.put(local_path, remote_path)
    
    async def _transfer_directory(self, sftp, local_dir: str, remote_dir: str):
        """传输目录"""
        local_path = Path(local_dir)
        
        # 创建远程目录
        try:
            sftp.makedirs(remote_dir)
        except:
            pass
        
        # 递归传输文件
        for item in local_path.rglob('*'):
            if item.is_file():
                relative_path = item.relative_to(local_path)
                remote_file_path = f"{remote_dir}/{relative_path}".replace('\\', '/')
                
                # 确保远程目录存在
                remote_file_dir = str(Path(remote_file_path).parent)
                try:
                    sftp.makedirs(remote_file_dir)
                except:
                    pass
                
                sftp.put(str(item), remote_file_path)
    
    async def _transfer_host_specific_configs(self, sftp, host_id: str, host_config: HostConfig):
        """传输主机特定配置"""
        # 生成主机特定的环境变量文件
        env_content = self._generate_host_env_file(host_config)
        
        # 写入临时文件
        temp_env_file = f"/tmp/.env.{host_id}"
        with open(temp_env_file, 'w') as f:
            f.write(env_content)
        
        # 传输到远程主机
        sftp.put(temp_env_file, '/opt/mt5-trading/.env')
        
        # 清理临时文件
        Path(temp_env_file).unlink()
    
    def _generate_host_env_file(self, host_config: HostConfig) -> str:
        """生成主机环境变量文件"""
        env_vars = {
            'MT5_HOST_ID': host_config.host_id,
            'MT5_HOST_IP': host_config.ip_address,
            'MT5_REGION': host_config.region,
            'MT5_ENVIRONMENT': host_config.environment,
            'MT5_CAPABILITIES': ','.join(host_config.capabilities),
            'MT5_ACCOUNTS': ','.join(host_config.accounts),
            
            # Redis配置
            'REDIS_HOST': self.config.get('redis', {}).get('host', 'localhost'),
            'REDIS_PORT': str(self.config.get('redis', {}).get('port', 6379)),
            
            # NATS配置
            'NATS_HOST': self.config.get('nats', {}).get('host', 'localhost'),
            'NATS_PORT': str(self.config.get('nats', {}).get('port', 4222)),
            
            # Prometheus配置
            'PROMETHEUS_PUSHGATEWAY_URL': self.config.get('prometheus', {}).get('pushgateway_url', 'http://localhost:9091'),
        }
        
        return '\n'.join([f"{key}={value}" for key, value in env_vars.items()])
    
    async def _deploy_services_to_all_hosts(self):
        """部署服务到所有主机"""
        logger.info("🚀 部署服务到主机...")
        
        tasks = []
        for host_id in self.hosts.keys():
            task = asyncio.create_task(self._deploy_services_to_host(host_id))
            tasks.append(task)
        
        await asyncio.gather(*tasks)
        logger.info("✅ 服务部署完成")
    
    async def _deploy_services_to_host(self, host_id: str):
        """部署服务到单个主机"""
        ssh = self.ssh_connections[host_id]
        host_config = self.hosts[host_id]
        
        # 部署命令
        commands = [
            # 进入工作目录
            "cd /opt/mt5-trading",
            
            # 停止现有服务
            f"docker-compose -f docker/{host_config.docker_compose_file} down || true",
            
            # 拉取最新镜像
            f"docker-compose -f docker/{host_config.docker_compose_file} pull",
            
            # 构建自定义镜像
            f"docker-compose -f docker/{host_config.docker_compose_file} build",
            
            # 启动服务
            f"docker-compose -f docker/{host_config.docker_compose_file} up -d",
        ]
        
        for command in commands:
            try:
                output = await self._execute_ssh_command(ssh, command, timeout=600)
                logger.debug(f"✅ {host_id}: {command}")
            except Exception as e:
                logger.error(f"❌ {host_id}: 命令执行失败 - {command}: {e}")
                raise
    
    async def _health_check_all_hosts(self):
        """健康检查所有主机"""
        logger.info("🏥 执行健康检查...")
        
        tasks = []
        for host_id in self.hosts.keys():
            task = asyncio.create_task(self._health_check_host(host_id))
            tasks.append(task)
        
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        healthy_hosts = []
        unhealthy_hosts = []
        
        for i, result in enumerate(results):
            host_id = list(self.hosts.keys())[i]
            if isinstance(result, Exception):
                unhealthy_hosts.append(host_id)
                logger.error(f"❌ 主机 {host_id} 健康检查失败: {result}")
            elif result:
                healthy_hosts.append(host_id)
                logger.info(f"✅ 主机 {host_id} 健康检查通过")
            else:
                unhealthy_hosts.append(host_id)
                logger.warning(f"⚠️ 主机 {host_id} 健康检查未通过")
        
        logger.info(f"健康检查结果: {len(healthy_hosts)} 健康, {len(unhealthy_hosts)} 不健康")
        
        if unhealthy_hosts:
            logger.warning(f"不健康的主机: {unhealthy_hosts}")
    
    async def _health_check_host(self, host_id: str) -> bool:
        """健康检查单个主机"""
        ssh = self.ssh_connections[host_id]
        host_config = self.hosts[host_id]
        
        try:
            # 检查Docker服务状态
            output = await self._execute_ssh_command(
                ssh, 
                f"cd /opt/mt5-trading && docker-compose -f docker/{host_config.docker_compose_file} ps",
                timeout=60
            )
            
            # 检查关键服务是否运行
            required_services = ['redis', 'nats']
            for service in required_services:
                if service not in output or 'Up' not in output:
                    logger.warning(f"服务 {service} 在主机 {host_id} 上未正常运行")
                    return False
            
            # 检查端口连通性
            ports_to_check = [6379, 4222]  # Redis, NATS
            for port in ports_to_check:
                try:
                    output = await self._execute_ssh_command(
                        ssh,
                        f"nc -z localhost {port}",
                        timeout=10
                    )
                except:
                    logger.warning(f"端口 {port} 在主机 {host_id} 上不可达")
                    return False
            
            return True
            
        except Exception as e:
            logger.error(f"主机 {host_id} 健康检查异常: {e}")
            return False
    
    async def _execute_ssh_command(self, ssh: paramiko.SSHClient, command: str, timeout: int = 60) -> str:
        """执行SSH命令"""
        stdin, stdout, stderr = ssh.exec_command(command, timeout=timeout)
        
        # 等待命令完成
        exit_status = stdout.channel.recv_exit_status()
        
        output = stdout.read().decode('utf-8')
        error = stderr.read().decode('utf-8')
        
        if exit_status != 0:
            raise Exception(f"命令执行失败 (退出码: {exit_status}): {error}")
        
        return output
    
    def _close_all_connections(self):
        """关闭所有SSH连接"""
        for ssh in self.ssh_connections.values():
            ssh.close()
        self.ssh_connections.clear()
    
    async def stop_all_hosts(self):
        """停止所有主机服务"""
        logger.info("🛑 停止所有主机服务...")
        
        tasks = []
        for host_id in self.hosts.keys():
            task = asyncio.create_task(self._stop_host_services(host_id))
            tasks.append(task)
        
        await asyncio.gather(*tasks)
        logger.info("✅ 所有主机服务已停止")
    
    async def _stop_host_services(self, host_id: str):
        """停止单个主机服务"""
        ssh = self.ssh_connections[host_id]
        host_config = self.hosts[host_id]
        
        command = f"cd /opt/mt5-trading && docker-compose -f docker/{host_config.docker_compose_file} down"
        
        try:
            await self._execute_ssh_command(ssh, command, timeout=120)
            logger.info(f"✅ 主机 {host_id} 服务已停止")
        except Exception as e:
            logger.error(f"❌ 停止主机 {host_id} 服务失败: {e}")
    
    def generate_deployment_report(self) -> Dict:
        """生成部署报告"""
        return {
            'deployment_time': time.time(),
            'total_hosts': len(self.hosts),
            'hosts': {
                host_id: {
                    'hostname': config.hostname,
                    'ip_address': config.ip_address,
                    'environment': config.environment,
                    'capabilities': config.capabilities,
                    'accounts': config.accounts
                }
                for host_id, config in self.hosts.items()
            },
            'tasks': [asdict(task) for task in self.deployment_tasks]
        }


async def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='MT5多主机部署工具')
    parser.add_argument('--config', required=True, help='部署配置文件路径')
    parser.add_argument('--action', choices=['deploy', 'stop', 'health-check'], 
                       default='deploy', help='执行的操作')
    
    args = parser.parse_args()
    
    deployer = MultiHostDeployer(args.config)
    
    try:
        if args.action == 'deploy':
            success = await deployer.deploy_all_hosts()
            if success:
                print("✅ 部署成功完成")
                
                # 生成部署报告
                report = deployer.generate_deployment_report()
                report_file = f"deployment_report_{int(time.time())}.json"
                with open(report_file, 'w') as f:
                    json.dump(report, f, indent=2)
                print(f"📊 部署报告已保存: {report_file}")
            else:
                print("❌ 部署失败")
                return 1
                
        elif args.action == 'stop':
            await deployer.stop_all_hosts()
            print("✅ 所有服务已停止")
            
        elif args.action == 'health-check':
            await deployer._test_all_connections()
            await deployer._health_check_all_hosts()
            print("✅ 健康检查完成")
        
        return 0
        
    except Exception as e:
        logger.error(f"操作失败: {e}")
        print(f"❌ 操作失败: {e}")
        return 1


if __name__ == "__main__":
    import sys
    sys.exit(asyncio.run(main()))
