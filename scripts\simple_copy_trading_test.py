#!/usr/bin/env python3
"""
简化的跟单系统测试 - 直接测试核心功能
"""

import asyncio
import sys
import os
import time
from pathlib import Path
from dotenv import load_dotenv

# 添加项目路径
sys.path.insert(0, str(Path(__file__).parent.parent))

from src.utils.logger import get_logger, setup_logging
from src.core.mt5_client import MT5Client
from src.core.signal_types import UnifiedTradeSignal, OrderType
from src.messaging.jetstream_client import JetStreamClient
from src.core.copy_utils import UnifiedCopyProcessor
from src.utils.config_manager import ConfigManager

logger = get_logger(__name__)


class SimpleCopyTradingTest:
    """简化的跟单测试"""
    
    def __init__(self):
        # 加载环境变量
        load_dotenv()
        
        self.config_manager = ConfigManager('config/optimized_system.yaml')
        self.mt5_clients = {}
        self.jetstream_client = None
        self.strategy_processor = CopyStrategyProcessor(self.config_manager)
        
    async def setup(self):
        """初始化"""
        logger.info("🔧 初始化测试环境...")
        
        # 初始化 JetStream
        self.jetstream_client = JetStreamClient(['nats://localhost:4222'])
        await self.jetstream_client.connect()
        
        logger.info("✅ 测试环境初始化完成")
    
    async def test_mt5_connections(self):
        """测试MT5连接"""
        logger.info("🧪 测试MT5连接...")
        
        accounts = [
            {
                'id': 'ACC001',
                'login': ********,
                'password': os.getenv('MT5_ACC001_PASSWORD'),
                'server': 'TradeMaxGlobal-Demo',
                'terminal_path': 'D:/MetaTrader5/v1-mt5/terminal64.exe'
            },
            {
                'id': 'ACC002',
                'login': ********,
                'password': os.getenv('MT5_ACC002_PASSWORD'),
                'server': 'TradeMaxGlobal-Demo',
                'terminal_path': 'D:/MetaTrader5/v2-mt5/terminal64.exe'
            }
        ]
        
        for account in accounts:
            try:
                logger.info(f"连接账户: {account['id']}")
                
                client = MT5Client(
                    account_id=account['id'],
                    login=account['login'],
                    password=account['password'],
                    server=account['server'],
                    terminal_path=account['terminal_path']
                )
                
                success = await client.connect()
                
                if success:
                    logger.info(f"✅ {account['id']} 连接成功")
                    
                    # 获取账户信息
                    if client.account_info:
                        logger.info(f"  余额: {client.account_info.balance}")
                        logger.info(f"  权益: {client.account_info.equity}")

                    # 获取持仓
                    positions = await client.get_positions()
                    logger.info(f"  当前持仓: {len(positions) if positions else 0}")
                    
                    self.mt5_clients[account['id']] = client
                else:
                    logger.error(f"❌ {account['id']} 连接失败")
                    
            except Exception as e:
                logger.error(f"❌ {account['id']} 连接异常: {e}")
        
        return len(self.mt5_clients) > 0
    
    async def test_copy_trading_logic(self):
        """测试跟单逻辑"""
        logger.info("🧪 测试跟单逻辑...")
        
        try:
            # 创建测试信号
            test_signal = UnifiedTradeSignal.create_open_signal(
                master_account="ACC001",
                symbol="EURUSD",
                order_type=OrderType.BUY,
                volume=0.01,
                price=1.1000,
                position_id=99999,
                sl=1.0950,
                tp=1.1050,
                comment="Copy trading test"
            )
            
            logger.info(f"原始信号: {test_signal.symbol} {test_signal.order_type.value} {test_signal.volume}")
            
            # 应用跟单策略
            copy_signal = self.strategy_processor.apply_copy_strategy(
                signal=test_signal,
                master_account="ACC001",
                slave_account="ACC002"
            )
            
            if copy_signal:
                logger.info(f"✅ 跟单信号: {copy_signal.symbol} {copy_signal.order_type.value} {copy_signal.volume}")
                
                # 验证策略应用
                if copy_signal.volume == 0.005:  # 0.01 * 0.5
                    logger.info("✅ 手数调整正确")
                else:
                    logger.warning(f"⚠️ 手数调整异常: 期望=0.005, 实际={copy_signal.volume}")
                
                return True
            else:
                logger.error("❌ 跟单信号生成失败")
                return False
                
        except Exception as e:
            logger.error(f"❌ 跟单逻辑测试异常: {e}")
            return False
    
    async def monitor_accounts(self, duration=30):
        """监控账户变化"""
        logger.info(f"🔍 开始监控账户变化 ({duration}秒)...")
        logger.info("💡 现在可以在ACC001进行交易操作，观察ACC002的跟单行为")
        
        # 记录初始状态
        initial_states = {}
        for account_id, client in self.mt5_clients.items():
            try:
                positions = await client.get_positions()

                initial_states[account_id] = {
                    'balance': client.account_info.balance if client.account_info else 0,
                    'equity': client.account_info.equity if client.account_info else 0,
                    'positions': len(positions) if positions else 0
                }
                
                logger.info(f"{account_id} 初始状态: 余额={initial_states[account_id]['balance']:.2f}, "
                          f"权益={initial_states[account_id]['equity']:.2f}, "
                          f"持仓={initial_states[account_id]['positions']}")
            except Exception as e:
                logger.error(f"获取 {account_id} 初始状态失败: {e}")
        
        # 监控循环
        start_time = time.time()
        check_count = 0
        
        while time.time() - start_time < duration:
            await asyncio.sleep(3)  # 每3秒检查一次
            check_count += 1
            
            logger.info(f"📊 检查 #{check_count} ({int(time.time() - start_time)}s)")
            
            for account_id, client in self.mt5_clients.items():
                try:
                    positions = await client.get_positions()

                    current_balance = client.account_info.balance if client.account_info else 0
                    current_equity = client.account_info.equity if client.account_info else 0
                    current_positions = len(positions) if positions else 0
                    
                    # 检查变化
                    if account_id in initial_states:
                        initial = initial_states[account_id]
                        
                        balance_change = current_balance - initial['balance']
                        equity_change = current_equity - initial['equity']
                        position_change = current_positions - initial['positions']
                        
                        if abs(balance_change) > 0.01 or abs(equity_change) > 0.01 or position_change != 0:
                            logger.info(f"🔄 {account_id} 变化检测:")
                            logger.info(f"  余额: {initial['balance']:.2f} -> {current_balance:.2f} ({balance_change:+.2f})")
                            logger.info(f"  权益: {initial['equity']:.2f} -> {current_equity:.2f} ({equity_change:+.2f})")
                            logger.info(f"  持仓: {initial['positions']} -> {current_positions} ({position_change:+d})")
                            
                            # 显示持仓详情
                            if positions:
                                for i, pos in enumerate(positions):
                                    logger.info(f"    持仓{i+1}: {pos.get('symbol', 'N/A')} "
                                              f"{pos.get('type', 'N/A')} "
                                              f"{pos.get('volume', 'N/A')} "
                                              f"@ {pos.get('price_open', 'N/A')}")
                    
                except Exception as e:
                    logger.error(f"监控 {account_id} 时出错: {e}")
        
        logger.info("✅ 监控完成")
    
    async def cleanup(self):
        """清理"""
        logger.info("🧹 清理资源...")
        
        for account_id, client in self.mt5_clients.items():
            try:
                await client.disconnect()
                logger.info(f"✅ {account_id} 连接已断开")
            except Exception as e:
                logger.warning(f"⚠️ {account_id} 断开时出错: {e}")
        
        if self.jetstream_client:
            await self.jetstream_client.disconnect()
            logger.info("✅ JetStream 连接已断开")


async def main():
    """主函数"""
    # 设置日志
    setup_logging({
        'level': 'INFO',
        'format': 'standard'
    })
    
    logger.info("=" * 60)
    logger.info("🚀 简化跟单系统测试")
    logger.info("=" * 60)
    
    tester = SimpleCopyTradingTest()
    
    try:
        # 初始化
        await tester.setup()
        
        # 测试MT5连接
        logger.info("第一步: 测试MT5连接")
        mt5_success = await tester.test_mt5_connections()
        
        if not mt5_success:
            logger.error("❌ MT5连接失败，无法继续")
            return 1
        
        # 测试跟单逻辑
        logger.info("第二步: 测试跟单逻辑")
        logic_success = await tester.test_copy_trading_logic()
        
        if logic_success:
            logger.info("✅ 跟单逻辑测试通过")
        else:
            logger.warning("⚠️ 跟单逻辑测试失败")
        
        # 开始监控
        logger.info("第三步: 开始实时监控")
        await tester.monitor_accounts(30)
        
        logger.info("🎉 测试完成！")
        return 0
        
    except KeyboardInterrupt:
        logger.info("🛑 收到中断信号")
        return 0
    except Exception as e:
        logger.error(f"❌ 测试异常: {e}")
        return 1
    finally:
        await tester.cleanup()


if __name__ == "__main__":
    try:
        exit_code = asyncio.run(main())
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n🛑 测试已停止")
        sys.exit(0)
