"""
Core 模块 - 进程隔离架构
提供基础组件和MT5进程隔离客户端服务
"""

# 移除基类导入，使用具体实现类
# from .base_monitor import BaseMonitor
# from .base_executor import BaseExecutor
from .mt5_client import MT5Client, create_mt5_client
from .mt5_process_manager import MT5ProcessManager
from ..messaging.message_types import (
    CommandRequest, CommandResponse, ProcessStatus,
    TradeRequest, TradeResult, AccountInfo, Position,
    CommandType, ProcessState, TradeAction
)
from .exceptions import (
    MT5CopierException as MT5Error,
    MT5ConnectionError as ConnectionError,
    ConfigurationError,
    ValidationError,
    TimeoutError,
    MT5TradeError as ExecutionError
)

__all__ = [
    # 'BaseMonitor',  # 移除基类导出
    # 'BaseExecutor',  # 移除基类导出
    'MT5Client',
    'create_mt5_client',
    'MT5ProcessManager',
    'CommandRequest',
    'CommandResponse',
    'ProcessStatus',
    'TradeRequest',
    'TradeResult',
    'AccountInfo',
    'Position',
    'CommandType',
    'ProcessState',
    'TradeAction',
    'MT5Error',
    'ConnectionError',
    'ConfigurationError',
    'ValidationError',
    'TimeoutError',
    'ExecutionError'
]