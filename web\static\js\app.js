// MT5 Trading System JavaScript

class TradingSystem {
    constructor() {
        this.apiBaseUrl = '/api/v1';
        this.wsUrl = 'ws://localhost:8765';
        this.websocket = null;
        this.init();
    }

    init() {
        this.setupWebSocket();
        this.setupEventListeners();
        this.loadInitialData();
    }

    setupWebSocket() {
        try {
            this.websocket = new WebSocket(this.wsUrl);
            
            this.websocket.onopen = () => {
                console.log('WebSocket connection established');
                this.updateConnectionStatus(true);
            };

            this.websocket.onmessage = (event) => {
                const data = JSON.parse(event.data);
                this.handleWebSocketMessage(data);
            };

            this.websocket.onclose = () => {
                console.log('WebSocket connection closed');
                this.updateConnectionStatus(false);
                // Attempt to reconnect
                setTimeout(() => this.setupWebSocket(), 5000);
            };

            this.websocket.onerror = (error) => {
                console.error('WebSocket error:', error);
                this.updateConnectionStatus(false);
            };
        } catch (error) {
            console.error('Failed to establish WebSocket connection:', error);
        }
    }

    setupEventListeners() {
        // Add event listeners for UI interactions
        document.addEventListener('DOMContentLoaded', () => {
            this.bindUIEvents();
        });
    }

    bindUIEvents() {
        // Bind button clicks and other UI events
        const refreshBtn = document.getElementById('refresh-data');
        if (refreshBtn) {
            refreshBtn.addEventListener('click', () => this.loadInitialData());
        }
    }

    async loadInitialData() {
        try {
            const accounts = await this.fetchAccounts();
            this.updateAccountsDisplay(accounts);
        } catch (error) {
            console.error('Failed to load initial data:', error);
        }
    }

    async fetchAccounts() {
        const response = await fetch(`${this.apiBaseUrl}/accounts`);
        if (!response.ok) {
            throw new Error('Failed to fetch accounts');
        }
        return await response.json();
    }

    updateAccountsDisplay(accounts) {
        const container = document.getElementById('accounts-container');
        if (!container) return;

        container.innerHTML = accounts.map(account => `
            <div class="card">
                <h3>${account.name}</h3>
                <p>Balance: ${account.balance}</p>
                <p>Equity: ${account.equity}</p>
                <p>Grade: ${account.grade}</p>
                <span class="status-indicator ${account.status === 'online' ? 'status-online' : 'status-offline'}"></span>
                ${account.status}
            </div>
        `).join('');
    }

    updateConnectionStatus(isConnected) {
        const statusElement = document.getElementById('connection-status');
        if (statusElement) {
            statusElement.textContent = isConnected ? 'Connected' : 'Disconnected';
            statusElement.className = `status-indicator ${isConnected ? 'status-online' : 'status-offline'}`;
        }
    }

    handleWebSocketMessage(data) {
        switch (data.type) {
            case 'account_update':
                this.handleAccountUpdate(data.payload);
                break;
            case 'trade_update':
                this.handleTradeUpdate(data.payload);
                break;
            case 'pairing_update':
                this.handlePairingUpdate(data.payload);
                break;
            default:
                console.log('Unknown message type:', data.type);
        }
    }

    handleAccountUpdate(account) {
        // Update specific account in UI
        console.log('Account updated:', account);
    }

    handleTradeUpdate(trade) {
        // Update trade information in UI
        console.log('Trade updated:', trade);
    }

    handlePairingUpdate(pairing) {
        // Update pairing information in UI
        console.log('Pairing updated:', pairing);
    }
}

// Initialize the trading system when page loads
document.addEventListener('DOMContentLoaded', () => {
    new TradingSystem();
}); 