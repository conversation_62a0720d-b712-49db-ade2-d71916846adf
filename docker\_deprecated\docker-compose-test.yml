version: '3.8'

services:
  # Redis for state management
  redis:
    image: redis:7-alpine
    container_name: redis-test
    ports:
      - "6379:6379"
    command: redis-server --appendonly yes
    volumes:
      - redis_data:/data
    restart: unless-stopped

  # NATS with JetStream
  nats:
    image: nats:latest
    container_name: nats-test
    ports:
      - "4222:4222"
      - "8222:8222"
    command: ["--jetstream", "--store_dir", "/data"]
    volumes:
      - nats_data:/data
    restart: unless-stopped

  # MT5 Copy Trading System
  mt5-copy-trading:
    build:
      context: ..
      dockerfile: docker/Dockerfile
    container_name: mt5-copy-trading-test
    depends_on:
      - redis
      - nats
    volumes:
      - ../:/app
      - ../mt5_terminals:/app/mt5_terminals
      - ../config:/app/config
      - ../logs:/app/logs
    environment:
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - NATS_HOST=nats
      - NATS_PORT=4222
      - MT5_ACC001_PASSWORD=demo_password_001
      - MT5_ACC002_PASSWORD=demo_password_002
      - HOST_ID=UK-001
      - PYTHONPATH=/app
      - MT5_CONFIG_PATH=/app/config/optimized_system.yaml
      - MT5_MODE=hybrid
    working_dir: /app
    command: /entrypoint.sh hybrid
    restart: unless-stopped

volumes:
  redis_data:
  nats_data: