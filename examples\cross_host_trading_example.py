#!/usr/bin/env python3
"""
跨主机交易示例
演示如何使用MT5分布式系统进行跨主机跟单交易
"""
import asyncio
import sys
import time
from pathlib import Path
from typing import List, Dict

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent.parent))

from src.sdk.mt5_copier_sdk import (
    MT5CopierSDK, HostInfo, AccountInfo, 
    CrossHostPairingConfig, SystemHealthInfo
)
from src.utils.logger import get_logger, setup_logging


logger = get_logger(__name__)


class CrossHostTradingDemo:
    """跨主机交易演示"""
    
    def __init__(self, central_server: str):
        self.central_server = central_server
        self.sdk = MT5CopierSDK(f"http://{central_server}")
        
    async def run_demo(self):
        """运行演示"""
        logger.info("开始跨主机交易演示")
        
        async with self.sdk:
            # 1. 系统初始化检查
            await self.check_system_health()
            
            # 2. 注册主机
            await self.register_demo_hosts()
            
            # 3. 注册账户
            await self.register_demo_accounts()
            
            # 4. 创建跨主机跟单配置
            await self.create_demo_pairings()
            
            # 5. 监控系统状态
            await self.monitor_system(duration=60)  # 监控1分钟
            
            # 6. 清理演示数据
            await self.cleanup_demo()
        
        logger.info("跨主机交易演示完成")
    
    async def check_system_health(self):
        """检查系统健康状态"""
        logger.info("检查系统健康状态...")
        
        try:
            health = await self.sdk.get_system_health()
            
            logger.info("系统健康状态:")
            logger.info(f"  总主机数: {health.total_hosts}")
            logger.info(f"  在线主机: {health.online_hosts}")
            logger.info(f"  总账户数: {health.total_accounts}")
            logger.info(f"  活跃账户: {health.active_accounts}")
            logger.info(f"  跟单配置: {health.total_pairings}")
            logger.info(f"  消息吞吐量: {health.message_throughput:.2f}/s")
            logger.info(f"  平均延迟: {health.average_latency:.2f}ms")
            logger.info(f"  错误率: {health.error_rate:.2%}")
            
            if health.error_rate > 0.1:
                logger.warning("系统错误率较高，请检查")
            
        except Exception as e:
            logger.error(f"检查系统健康状态失败: {e}")
            raise
    
    async def register_demo_hosts(self):
        """注册演示主机"""
        logger.info("注册演示主机...")
        
        demo_hosts = [
            HostInfo(
                host_id="demo_master_singapore",
                hostname="singapore-demo-01",
                ip_address="*************",
                port=8000,
                capabilities=["master"]
            ),
            HostInfo(
                host_id="demo_slave_london",
                hostname="london-demo-01", 
                ip_address="*************",
                port=8000,
                capabilities=["slave"]
            ),
            HostInfo(
                host_id="demo_slave_newyork",
                hostname="newyork-demo-01",
                ip_address="*************", 
                port=8000,
                capabilities=["slave"]
            )
        ]
        
        for host in demo_hosts:
            try:
                success = await self.sdk.register_host(host)
                if success:
                    logger.info(f"主机注册成功: {host.host_id}")
                else:
                    logger.warning(f"主机注册失败: {host.host_id}")
            except Exception as e:
                logger.error(f"注册主机失败 {host.host_id}: {e}")
    
    async def register_demo_accounts(self):
        """注册演示账户"""
        logger.info("注册演示账户...")
        
        demo_accounts = [
            AccountInfo(
                account_id="demo_master_sg_001",
                login=********,
                password="demo_password_1",
                server="ICMarkets-Demo",
                host_id="demo_master_singapore",
                account_type="master",
                max_lot_size=10.0,
                risk_level="medium"
            ),
            AccountInfo(
                account_id="demo_slave_london_001",
                login=********,
                password="demo_password_2", 
                server="ICMarkets-Demo",
                host_id="demo_slave_london",
                account_type="slave",
                max_lot_size=5.0,
                risk_level="conservative"
            ),
            AccountInfo(
                account_id="demo_slave_ny_001",
                login=********,
                password="demo_password_3",
                server="ICMarkets-Demo", 
                host_id="demo_slave_newyork",
                account_type="slave",
                max_lot_size=3.0,
                risk_level="conservative"
            )
        ]
        
        for account in demo_accounts:
            try:
                success = await self.sdk.register_account(account)
                if success:
                    logger.info(f"账户注册成功: {account.account_id}")
                else:
                    logger.warning(f"账户注册失败: {account.account_id}")
            except Exception as e:
                logger.error(f"注册账户失败 {account.account_id}: {e}")
    
    async def create_demo_pairings(self):
        """创建演示跟单配置"""
        logger.info("创建跨主机跟单配置...")
        
        demo_pairings = [
            CrossHostPairingConfig(
                pairing_id="demo_sg_to_london",
                master_host="demo_master_singapore",
                master_account="demo_master_sg_001",
                slave_host="demo_slave_london", 
                slave_account="demo_slave_london_001",
                strategy="proportional",
                lot_multiplier=0.5,
                max_lot=5.0,
                min_lot=0.01,
                enabled=True
            ),
            CrossHostPairingConfig(
                pairing_id="demo_sg_to_newyork",
                master_host="demo_master_singapore",
                master_account="demo_master_sg_001",
                slave_host="demo_slave_newyork",
                slave_account="demo_slave_ny_001", 
                strategy="proportional",
                lot_multiplier=0.3,
                max_lot=3.0,
                min_lot=0.01,
                enabled=True
            )
        ]
        
        for pairing in demo_pairings:
            try:
                pairing_id = await self.sdk.create_cross_host_pairing(pairing)
                logger.info(f"跨主机跟单配置创建成功: {pairing_id}")
                logger.info(f"  主机: {pairing.master_host} -> {pairing.slave_host}")
                logger.info(f"  账户: {pairing.master_account} -> {pairing.slave_account}")
                logger.info(f"  策略: {pairing.strategy}, 倍数: {pairing.lot_multiplier}")
            except Exception as e:
                logger.error(f"创建跨主机跟单配置失败: {e}")
    
    async def monitor_system(self, duration: int = 60):
        """监控系统状态"""
        logger.info(f"开始监控系统状态 ({duration}秒)...")
        
        start_time = time.time()
        
        while time.time() - start_time < duration:
            try:
                # 获取系统健康状态
                health = await self.sdk.get_system_health()
                
                # 获取主机列表
                hosts = await self.sdk.get_host_list()
                online_hosts = [h for h in hosts if h.status == "online"]
                
                # 获取跨主机配置
                pairings = await self.sdk.get_cross_host_pairings()
                active_pairings = [p for p in pairings if p.get('enabled', False)]
                
                # 获取性能指标
                metrics = await self.sdk.get_performance_metrics()
                
                logger.info("系统状态监控:")
                logger.info(f"  在线主机: {len(online_hosts)}/{len(hosts)}")
                logger.info(f"  活跃账户: {health.active_accounts}/{health.total_accounts}")
                logger.info(f"  活跃跟单: {len(active_pairings)}/{len(pairings)}")
                logger.info(f"  消息吞吐: {health.message_throughput:.1f}/s")
                logger.info(f"  系统延迟: {health.average_latency:.1f}ms")
                logger.info(f"  错误率: {health.error_rate:.2%}")
                
                # 检查连通性
                await self.test_connectivity()
                
                await asyncio.sleep(10)  # 每10秒检查一次
                
            except Exception as e:
                logger.error(f"监控系统状态失败: {e}")
                await asyncio.sleep(5)
    
    async def test_connectivity(self):
        """测试跨主机连通性"""
        try:
            hosts = await self.sdk.get_host_list()
            
            for host in hosts:
                if host.host_id != "demo_master_singapore":  # 跳过本地主机
                    result = await self.sdk.test_cross_host_connectivity(host.host_id)
                    
                    if result.get('success'):
                        latency = result.get('latency', 0)
                        logger.debug(f"连通性测试 {host.host_id}: 成功 ({latency:.1f}ms)")
                    else:
                        error = result.get('error', 'Unknown error')
                        logger.warning(f"连通性测试 {host.host_id}: 失败 - {error}")
                        
        except Exception as e:
            logger.error(f"连通性测试失败: {e}")
    
    async def cleanup_demo(self):
        """清理演示数据"""
        logger.info("清理演示数据...")
        
        try:
            # 删除跨主机配置
            pairings = await self.sdk.get_cross_host_pairings()
            for pairing in pairings:
                if pairing.get('pairing_id', '').startswith('demo_'):
                    await self.sdk.delete_cross_host_pairing(pairing['pairing_id'])
                    logger.info(f"已删除跨主机配置: {pairing['pairing_id']}")
            
            # 注销演示主机
            demo_host_ids = [
                "demo_master_singapore",
                "demo_slave_london", 
                "demo_slave_newyork"
            ]
            
            for host_id in demo_host_ids:
                await self.sdk.unregister_host(host_id)
                logger.info(f"已注销主机: {host_id}")
            
            logger.info("演示数据清理完成")
            
        except Exception as e:
            logger.error(f"清理演示数据失败: {e}")


async def main():
    """主函数"""
    # 设置日志
    setup_logging({"level": "INFO"})
    
    # 中央服务器地址
    central_server = "localhost:8000"  # 根据实际情况修改
    
    # 创建演示实例
    demo = CrossHostTradingDemo(central_server)
    
    try:
        await demo.run_demo()
        logger.info("演示成功完成")
    except KeyboardInterrupt:
        logger.info("演示被用户中断")
    except Exception as e:
        logger.error(f"演示失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
