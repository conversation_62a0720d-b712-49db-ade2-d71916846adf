#!/usr/bin/env python3
"""
生产环境部署脚本
完全进程隔离，避免硬编码，从配置文件读取所有参数
"""

import asyncio
import sys
import os
import time
import yaml
import subprocess
from pathlib import Path
from typing import Dict, Any, List
from dataclasses import dataclass

# 添加项目路径
sys.path.insert(0, str(Path(__file__).parent.parent))

from src.utils.logger import get_logger, setup_logging
from src.core.copy_utils import UnifiedCopyProcessor

logger = get_logger(__name__)


@dataclass
class DeploymentConfig:
    """部署配置"""
    host_id: str
    config_path: str
    nats_servers: List[str]
    redis_host: str
    redis_port: int
    master_accounts: List[str]
    slave_accounts: List[str]
    copy_relationships: List[Dict[str, Any]]
    dry_run: bool = False


class ProductionDeployer:
    """生产环境部署器 - 工业级实现"""
    
    def __init__(self, config_path: str):
        self.config_path = config_path
        self.config = None
        self.deployment_config = None
        self.processes = {}
        
    def load_config(self) -> DeploymentConfig:
        """加载配置文件 - 避免硬编码"""
        try:
            logger.info(f"加载配置文件: {self.config_path}")
            
            with open(self.config_path, 'r', encoding='utf-8') as f:
                self.config = yaml.safe_load(f)
            
            # 解析基础配置
            host_id = self.config.get('host', {}).get('id', 'production_host_001')
            
            # NATS配置
            nats_servers = self.config.get('nats', {}).get('servers', ['nats://localhost:4222'])
            
            # Redis配置
            redis_config = self.config.get('redis', {})
            redis_host = redis_config.get('external_host', 'localhost')
            redis_port = redis_config.get('external_port', 6379)
            
            # 账户配置
            master_accounts = []
            slave_accounts = []
            copy_relationships = []
            
            # 从配置文件加载账户和跟单关系
            self._load_account_configs(master_accounts, slave_accounts, copy_relationships)
            
            self.deployment_config = DeploymentConfig(
                host_id=host_id,
                config_path=self.config_path,
                nats_servers=nats_servers,
                redis_host=redis_host,
                redis_port=redis_port,
                master_accounts=master_accounts,
                slave_accounts=slave_accounts,
                copy_relationships=copy_relationships,
                dry_run=self.config.get('deployment', {}).get('debug', False)
            )
            
            logger.info(f"配置加载完成: {self.deployment_config}")
            return self.deployment_config
            
        except Exception as e:
            logger.error(f"配置加载失败: {e}")
            raise
    
    def _load_account_configs(self, master_accounts: List[str], 
                            slave_accounts: List[str], 
                            copy_relationships: List[Dict[str, Any]]):
        """从配置文件加载账户配置"""
        try:
            # 加载账户配置目录
            accounts_dir = Path("config/accounts")
            if accounts_dir.exists():
                for account_file in accounts_dir.glob("*.yaml"):
                    with open(account_file, 'r', encoding='utf-8') as f:
                        account_config = yaml.safe_load(f)
                        
                    account_id = account_config.get('account_id')
                    if not account_id:
                        continue
                        
                    account_type = account_config.get('type', 'slave')
                    if account_type == 'master':
                        master_accounts.append(account_id)
                    else:
                        slave_accounts.append(account_id)
            
            # 加载跟单关系配置
            pairings_file = Path("config/pairings/dynamic_pairings.yaml")
            if pairings_file.exists():
                with open(pairings_file, 'r', encoding='utf-8') as f:
                    pairings_config = yaml.safe_load(f)
                    
                relationships = pairings_config.get('copy_relationships', [])
                copy_relationships.extend(relationships)
            
            logger.info(f"账户配置: 主账户={master_accounts}, 从账户={slave_accounts}")
            logger.info(f"跟单关系: {len(copy_relationships)}个配对")
            
        except Exception as e:
            logger.error(f"账户配置加载失败: {e}")
            # 使用默认配置
            master_accounts.extend(['ACC001'])
            slave_accounts.extend(['ACC002'])
            copy_relationships.append({
                'master_account': 'ACC001',
                'slave_account': 'ACC002',
                'copy_mode': 'forward',
                'copy_ratio': 1.0,
                'enabled': True
            })
    
    async def check_dependencies(self) -> bool:
        """检查依赖服务"""
        logger.info("检查依赖服务...")
        
        # 检查NATS
        nats_ok = await self._check_nats()
        if not nats_ok:
            logger.error("NATS服务不可用")
            return False
        
        # 检查Redis
        redis_ok = await self._check_redis()
        if not redis_ok:
            logger.error("Redis服务不可用")
            return False
        
        logger.info("✅ 所有依赖服务正常")
        return True
    
    async def _check_nats(self) -> bool:
        """检查NATS连接"""
        try:
            import socket
            for server in self.deployment_config.nats_servers:
                # 解析服务器地址
                if server.startswith('nats://'):
                    host_port = server[7:]  # 移除 nats:// 前缀
                    if ':' in host_port:
                        host, port = host_port.split(':')
                        port = int(port)
                    else:
                        host = host_port
                        port = 4222
                    
                    # 测试连接
                    sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                    sock.settimeout(5)
                    result = sock.connect_ex((host, port))
                    sock.close()
                    
                    if result == 0:
                        logger.info(f"✅ NATS服务器可用: {server}")
                        return True
                    else:
                        logger.warning(f"❌ NATS服务器不可用: {server}")
            
            return False
            
        except Exception as e:
            logger.error(f"NATS连接检查失败: {e}")
            return False
    
    async def _check_redis(self) -> bool:
        """检查Redis连接"""
        try:
            import socket
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(5)
            result = sock.connect_ex((self.deployment_config.redis_host, self.deployment_config.redis_port))
            sock.close()
            
            if result == 0:
                logger.info(f"✅ Redis服务器可用: {self.deployment_config.redis_host}:{self.deployment_config.redis_port}")
                return True
            else:
                logger.error(f"❌ Redis服务器不可用: {self.deployment_config.redis_host}:{self.deployment_config.redis_port}")
                return False
                
        except Exception as e:
            logger.error(f"Redis连接检查失败: {e}")
            return False
    
    async def deploy_system(self) -> bool:
        """部署系统 - 完全进程隔离"""
        try:
            logger.info("🚀 开始生产环境部署...")
            
            # 检查依赖
            if not await self.check_dependencies():
                return False
            
            # 启动主系统
            success = await self._start_main_system()
            if not success:
                logger.error("主系统启动失败")
                return False
            
            # 等待系统稳定
            await asyncio.sleep(5)
            
            # 验证系统状态
            if await self._verify_system_health():
                logger.info("🎉 生产环境部署成功！")
                return True
            else:
                logger.error("💥 系统健康检查失败")
                return False
                
        except Exception as e:
            logger.error(f"部署失败: {e}")
            return False
    
    async def _start_main_system(self) -> bool:
        """启动主系统"""
        try:
            logger.info("启动主交易系统...")
            
            # 构建启动命令
            cmd = [
                sys.executable, "main.py",
                "--host-id", self.deployment_config.host_id,
                "--config-path", self.deployment_config.config_path
            ]
            
            if self.deployment_config.dry_run:
                cmd.append("--dry-run")
            
            logger.info(f"启动命令: {' '.join(cmd)}")
            
            # 启动进程
            process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                bufsize=1,
                universal_newlines=True
            )
            
            self.processes['main_system'] = process
            
            # 等待启动
            await asyncio.sleep(3)
            
            # 检查进程状态
            if process.poll() is None:
                logger.info("✅ 主系统启动成功")
                return True
            else:
                stdout, stderr = process.communicate()
                logger.error(f"主系统启动失败: stdout={stdout}, stderr={stderr}")
                return False
                
        except Exception as e:
            logger.error(f"主系统启动异常: {e}")
            return False
    
    async def _verify_system_health(self) -> bool:
        """验证系统健康状态"""
        try:
            logger.info("验证系统健康状态...")
            
            # 检查主进程
            main_process = self.processes.get('main_system')
            if not main_process or main_process.poll() is not None:
                logger.error("主进程已退出")
                return False
            
            # 检查日志文件
            log_file = Path("logs/mt5_production.log")
            if log_file.exists():
                # 读取最近的日志
                with open(log_file, 'r', encoding='utf-8') as f:
                    lines = f.readlines()
                    recent_lines = lines[-20:] if len(lines) > 20 else lines
                    
                # 检查是否有启动成功的标志
                for line in recent_lines:
                    if "交易系统启动成功" in line or "系统启动成功" in line:
                        logger.info("✅ 系统启动成功标志已找到")
                        return True
            
            logger.warning("未找到系统启动成功标志，但进程正在运行")
            return True
            
        except Exception as e:
            logger.error(f"健康检查失败: {e}")
            return False
    
    def shutdown(self):
        """关闭所有进程"""
        logger.info("关闭系统...")
        
        for name, process in self.processes.items():
            if process and process.poll() is None:
                logger.info(f"终止进程: {name}")
                process.terminate()
                try:
                    process.wait(timeout=10)
                except subprocess.TimeoutExpired:
                    logger.warning(f"强制终止进程: {name}")
                    process.kill()


async def main():
    """主函数"""
    # 设置日志
    setup_logging({
        'level': 'INFO',
        'format': 'standard',
        'file': 'logs/production_deploy.log'
    })
    
    logger.info("=" * 60)
    logger.info("🚀 MT5分布式交易系统 - 生产环境部署")
    logger.info("=" * 60)
    
    # 配置文件路径
    config_path = "config/optimized_system.yaml"
    
    # 创建部署器
    deployer = ProductionDeployer(config_path)
    
    try:
        # 加载配置
        deployment_config = deployer.load_config()
        
        # 部署系统
        success = await deployer.deploy_system()
        
        if success:
            logger.info("🎉 部署成功！系统正在运行...")
            logger.info("按 Ctrl+C 停止系统")
            
            # 保持运行
            try:
                while True:
                    await asyncio.sleep(10)
                    # 可以在这里添加健康检查
                    
            except KeyboardInterrupt:
                logger.info("收到停止信号...")
        else:
            logger.error("💥 部署失败")
            return 1
            
    except Exception as e:
        logger.error(f"部署异常: {e}")
        return 1
    finally:
        deployer.shutdown()
    
    return 0


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
