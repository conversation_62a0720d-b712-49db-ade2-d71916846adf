# 账户配置模板
# 用于创建新账户时的默认配置
# 继承自主配置文件的通用设置

account:
  # 基础信息
  id: "${ACCOUNT_ID}"
  name: "${ACCOUNT_NAME}"
  enabled: true
  
  # MT5连接信息
  connection:
    login: "${MT5_LOGIN}"
    password: "${MT5_PASSWORD}"
    server: "${MT5_SERVER}"
    terminal_path: "${MT5_TERMINAL_PATH}"
  
  # 账户设置
  settings:
    initial_grade: 0
    timezone: "${ACCOUNT_TIMEZONE:-8}"  # 默认UTC+8
    currency: "${ACCOUNT_CURRENCY:-USD}"
    
  # 交易设置
  trading:
    allowed_symbols:
      - "EURUSD"
      - "GBPUSD"
      - "USDJPY"
      - "USDCAD"
      - "AUDUSD"
      - "NZDUSD"
      - "USDCHF"
    max_spread: 3.0
    slippage: 10
    enable_hedging: false
    
  # 风险管理（使用模板）
  risk_management:
    profile: "conservative"  # 引用风险配置模板
    max_positions: 5
    max_lot_size: 1.0
    max_daily_loss: "${MAX_DAILY_LOSS:-1000}"
    max_daily_trades: 20
    scale_factor: 1.0
    
  # 监控设置（继承主配置）
  monitoring:
    polling_interval: null  # 使用主配置的设置
    adaptive_polling: true
    alert_on_error: true
    
  # 通知设置
  notifications:
    telegram:
      enabled: "${TELEGRAM_ENABLED:-false}"
      chat_id: "${TELEGRAM_CHAT_ID}"
    email:
      enabled: "${EMAIL_ENABLED:-false}"
      address: "${NOTIFICATION_EMAIL}"
    
  # 等级系统
  grading:
    auto_grade: true
    manual_grade: null
    upgrade_threshold: 0.1
    downgrade_threshold: -0.05 