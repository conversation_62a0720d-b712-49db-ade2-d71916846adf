#!/usr/bin/env python3
"""
Docker Compose部署测试 - 核心测试文件
测试所有6个工业级服务的部署状态和功能验证

这是MT5分布式交易系统的核心基础设施测试，验证：
1. Redis Hash优化存储
2. NATS JetStream消息队列
3. Prometheus监控系统
4. Pushgateway指标收集
5. Grafana可视化仪表板
6. Redis Commander数据管理

使用方法:
    python tests/test_docker_deployment.py
"""
import sys
import time
import requests
import redis
import pytest
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))


class TestDockerDeployment:
    """Docker Compose部署测试类"""
    
    def test_redis_connection(self):
        """测试Redis连接和Hash操作"""
        client = redis.Redis(host='localhost', port=6379, decode_responses=True)
        
        # 测试连接
        assert client.ping() == True
        
        # 测试Hash操作
        test_key = 'test:deployment:redis'
        client.hset(test_key, 'status', 'success')
        client.hset(test_key, 'timestamp', str(int(time.time())))
        
        # 验证数据
        result = client.hget(test_key, 'status')
        assert result == 'success'
        
        # 清理测试数据
        client.delete(test_key)
    
    def test_nats_connection(self):
        """测试NATS JetStream监控端点"""
        response = requests.get('http://localhost:8222/varz', timeout=5)
        assert response.status_code == 200
        
        # 验证JetStream信息
        data = response.json()
        assert 'jetstream' in data
        assert data['jetstream']['config']['max_memory'] > 0
    
    def test_prometheus_connection(self):
        """测试Prometheus监控API"""
        response = requests.get('http://localhost:9090/api/v1/query?query=up', timeout=5)
        assert response.status_code == 200
        
        data = response.json()
        assert 'data' in data
        assert data['status'] == 'success'
    
    def test_pushgateway_connection(self):
        """测试Pushgateway指标推送"""
        # 推送测试指标
        test_metric = 'test_deployment_metric{job="test",instance="pytest"} 1\n'
        response = requests.post(
            'http://localhost:9091/metrics/job/test_deployment',
            data=test_metric,
            headers={'Content-Type': 'text/plain'},
            timeout=5
        )
        assert response.status_code == 200
        
        # 验证指标可以获取
        response = requests.get('http://localhost:9091/metrics', timeout=5)
        assert response.status_code == 200
        assert 'test_deployment_metric' in response.text
    
    def test_grafana_connection(self):
        """测试Grafana健康状态和API"""
        response = requests.get('http://localhost:3000/api/health', timeout=15)
        assert response.status_code == 200
        
        data = response.json()
        assert 'database' in data
        assert data['database'] == 'ok'
        assert 'version' in data
    
    def test_redis_commander_connection(self):
        """测试Redis Commander Web界面"""
        response = requests.get('http://localhost:8081/', timeout=5)
        assert response.status_code == 200
        assert 'redis' in response.text.lower()

    def test_redis_hash_manager_connection(self):
        """测试Redis Hash Manager API"""
        # 测试健康检查端点
        response = requests.get('http://localhost:8082/health', timeout=10)
        assert response.status_code == 200

        data = response.json()
        assert data['status'] == 'healthy'
        assert data['redis_connected'] == True
        assert data['service'] == 'redis-hash-manager'

        # 测试系统统计端点
        response = requests.get('http://localhost:8082/api/v1/stats', timeout=10)
        assert response.status_code == 200

        stats_data = response.json()
        assert 'data' in stats_data
        assert 'hosts' in stats_data['data']
        assert 'accounts' in stats_data['data']


def run_comprehensive_test():
    """运行全面的部署测试"""
    print("🚀 开始Docker Compose部署测试...")
    print("=" * 60)
    
    # 等待服务启动
    print("⏳ 等待服务完全启动...")
    time.sleep(3)
    
    test_instance = TestDockerDeployment()
    
    tests = [
        ("Redis Hash优化", test_instance.test_redis_connection),
        ("NATS JetStream", test_instance.test_nats_connection),
        ("Prometheus监控", test_instance.test_prometheus_connection),
        ("Pushgateway指标", test_instance.test_pushgateway_connection),
        ("Grafana仪表板", test_instance.test_grafana_connection),
        ("Redis Commander", test_instance.test_redis_commander_connection),
        ("Redis Hash Manager", test_instance.test_redis_hash_manager_connection),
    ]
    
    results = {}
    
    for service_name, test_func in tests:
        print(f"🔍 测试 {service_name}...", end=" ")
        try:
            test_func()
            results[service_name] = True
            print("✅ 通过")
        except Exception as e:
            results[service_name] = False
            print(f"❌ 失败: {e}")
    
    print("\n" + "=" * 60)
    print("📊 测试结果汇总:")
    print("=" * 60)
    
    passed = sum(1 for result in results.values() if result)
    total = len(results)
    
    for service, result in results.items():
        status = "✅ 正常" if result else "❌ 异常"
        print(f"  {service:<20} {status}")
    
    print(f"\n🎯 总体结果: {passed}/{total} 服务正常运行")

    if passed == total:
        print("🎉 所有工业级服务部署成功！")
        print("\n🌐 服务访问地址:")
        print("  • Grafana仪表板:     http://localhost:3000")
        print("  • Prometheus监控:    http://localhost:9090")
        print("  • Redis Commander:   http://localhost:8081")
        print("  • Pushgateway:       http://localhost:9091")
        print("  • NATS监控:          http://localhost:8222")
        print("  • Redis Hash Manager: http://localhost:8082")
        print("  • Redis:             localhost:6379")
        print("  • NATS:              localhost:4222")
        
        print("\n📋 部署验证完成:")
        print("  ✅ 所有6个工业级服务正常运行")
        print("  ✅ 功能测试全部通过")
        print("  ✅ 健康检查全部正常")
        print("  ✅ 网络连接全部可用")
        
        return True
    else:
        print("⚠️  部分服务存在问题，请检查Docker日志")
        return False


if __name__ == "__main__":
    success = run_comprehensive_test()
    sys.exit(0 if success else 1)
