# MT5 High-Frequency Trading System Configuration Guide

<div align="center">

English | [简体中文](README.md)

</div>

## 📋 Table of Contents

- [Configuration Overview](#configuration-overview)
- [Configuration Hierarchy](#configuration-hierarchy)
- [Quick Start](#quick-start)
- [Detailed Configuration](#detailed-configuration)
- [Environment Configuration](#environment-configuration)
- [Best Practices](#best-practices)
- [Troubleshooting](#troubleshooting)

## 🏗️ Configuration Overview

### Core Configuration Files

| File | Purpose | Priority |
|------|---------|----------|
| `main.yaml` | 🔥 **Main Configuration File** - Unified system configuration entry point | Highest |
| `system.yaml` | System-level configuration (legacy, recommend migrating to main.yaml) | Medium |
| `logging.yaml` | Logging system configuration | High |

### Specialized Configuration Directories

```
config/
├── main.yaml                    # 🔥 Main configuration file (recommended)
├── system.yaml                  # System configuration (to be deprecated)
├── logging.yaml                 # Logging configuration
├── accounts/                    # Account configurations
│   ├── ACC001.yaml             # Specific account configuration
│   └── README.md               # Account configuration documentation
├── templates/                   # Configuration templates
│   ├── account_template.yaml   # Account configuration template
│   ├── risk_profiles.yaml      # Risk configuration template
│   └── copy_modes.yaml         # Copy mode configuration
├── performance/                 # Performance configurations
│   ├── optimization.yaml       # Performance optimization configuration
│   └── resource_limits.yaml    # Resource limit configuration
├── security/                    # Security configurations
│   ├── api_permissions.yaml    # API permission configuration
│   └── encryption_keys.yaml    # Encryption key configuration
└── terminals/                   # Terminal configurations
    ├── terminal_mapping.yaml   # Terminal mapping configuration
    └── terminal_pools.yaml     # Terminal pool configuration
```

## 🎯 Configuration Hierarchy

### Configuration Priority (High to Low)

1. **Environment Variables** - Highest priority
2. **Command Line Arguments** - Runtime specified
3. **main.yaml** - Main configuration file
4. **Specialized Configuration Files** - Functional module configurations
5. **Template Configurations** - Default templates
6. **System Defaults** - Built-in default values

### Configuration Inheritance Relationship

```mermaid
graph TD
    A[main.yaml] --> B[accounts/]
    A --> C[performance/]
    A --> D[security/]
    A --> E[terminals/]
    
    F[templates/] --> B
    F --> C
    F --> D
    F --> E
    
    G[Environment Variables] --> A
    H[Command Line Arguments] --> A
```

## 🚀 Quick Start

### 1. Basic Configuration

```bash
# 1. Copy main configuration file (if not exists)
cp config/main.yaml.example config/main.yaml

# 2. Edit main configuration file
nano config/main.yaml

# 3. Set environment variables
export MT5_MASTER_PASSWORD="your_password"
export REDIS_PASSWORD="redis_password"
export JWT_SECRET="your_jwt_secret"
```

### 2. Account Configuration

```bash
# Create new account configuration
cp config/templates/account_template.yaml config/accounts/ACC002.yaml

# Edit account configuration
nano config/accounts/ACC002.yaml
```

### 3. Validate Configuration

```bash
# Use configuration validation tool
python scripts/validate_config.py

# Or directly start system for testing
./run.sh start
```

## 📖 Detailed Configuration

### Main.yaml - Main Configuration File

```yaml
# System basic information
system:
  name: "MT5 High-Frequency Trading System"
  version: "2.0.0"
  environment: "production"  # development, testing, production

# Monitoring configuration (unified monitoring settings)
monitoring:
  base_polling_interval: 0.001  # 1ms base polling interval
  adaptive_polling: true        # Adaptive polling
  performance_mode: "high_frequency"  # conservative, balanced, aggressive, high_frequency

# Message queue configuration (unified NATS configuration)
messaging:
  nats:
    servers:
      - "nats://localhost:4222"
    timeout: 5.0
    max_reconnect: 60

# Cache configuration (unified Redis configuration)
cache:
  redis:
    host: "localhost"
    port: 6379
    password: "${REDIS_PASSWORD}"
    max_connections: 50

# API configuration (unified API settings)
api:
  host: "0.0.0.0"
  port: 8000
  authentication:
    enabled: true
    jwt_secret: "${JWT_SECRET}"
```

### Account Configuration (accounts/ACC001.yaml)

```yaml
account:
  # Basic information
  id: "ACC001"
  name: "Demo Account 1"
  enabled: true
  
  # MT5 connection information
  connection:
    login: 5000001
    password: "${MT5_ACC001_PASSWORD}"
    server: "ICMarkets-Demo"
    terminal_path: "C:/Program Files/MetaTrader 5/terminal64.exe"
  
  # Risk management
  risk_management:
    profile: "conservative"     # Reference templates/risk_profiles.yaml
    max_positions: 5
    max_lot_size: 1.0
    max_daily_loss: 100
```

### Performance Configuration (performance/optimization.yaml)

```yaml
# Polling optimization
polling:
  adaptive:
    enabled: true
    algorithm: "ML_DRIVEN"
    
# Concurrency optimization
concurrency:
  thread_pool:
    core_size: 4
    max_size: 16
    
# Memory optimization
memory:
  object_pooling:
    enabled: true
    pool_sizes:
      signals: 1000
      orders: 500
```

## 🌍 Environment Configuration

### Development Environment

```yaml
# Environment configuration in config/main.yaml
environments:
  development:
    system:
      environment: "development"
    monitoring:
      base_polling_interval: 0.01  # Use lower frequency for development
    api:
      port: 8001
```

### Production Environment

```yaml
environments:
  production:
    system:
      environment: "production"
    monitoring:
      base_polling_interval: 0.001  # Use highest frequency for production
    security:
      tls:
        enabled: true
```

### Environment Variable Settings

```bash
# .env file
MT5_MASTER_PASSWORD=your_secure_password
MT5_ACC001_PASSWORD=account_password
REDIS_PASSWORD=redis_password
JWT_SECRET=your_jwt_secret_key
NATS_URL=nats://localhost:4222
GRAFANA_ADMIN_PASSWORD=admin_password

# Environment-specific variables
NODE_ENV=production
LOG_LEVEL=INFO
DEBUG=false
```

## 💡 Best Practices

### 1. Configuration Management

**✅ Recommended Practices**

```yaml
# Use environment variables for sensitive information
password: "${MT5_PASSWORD}"

# Use configuration references to avoid duplication
risk_management:
  profile: "conservative"  # Reference template

# Use reasonable default values
timezone: "${ACCOUNT_TIMEZONE:-8}"  # Default UTC+8
```

**❌ Avoid**

```yaml
# Don't hardcode passwords in configuration files
password: "MyPassword123"

# Don't repeat the same configuration blocks
```

### 2. Configuration Layering

```yaml
# Main configuration file contains only common settings
# main.yaml
system:
  name: "MT5 System"
  version: "2.0.0"

# Specialized configuration files contain specific settings
# accounts/ACC001.yaml
account:
  connection:
    login: 5000001
```

### 3. Configuration Validation

```python
# Use configuration validation script
python scripts/validate_config.py

# Checks include:
# - Required fields exist
# - Data types are correct
# - Referenced files exist
# - Environment variables are set
```

### 4. Configuration Backup

```bash
# Regular configuration backup
tar -czf config_backup_$(date +%Y%m%d).tar.gz config/

# Version control configuration files (excluding sensitive information)
git add config/*.yaml
git commit -m "Update configuration"
```

## 🔧 Troubleshooting

### Common Issues

#### 1. Configuration File Not Found

```bash
Error: FileNotFoundError: config/main.yaml not found

Solution:
# Check if file exists
ls -la config/
# Copy example configuration
cp config/main.yaml.example config/main.yaml
```

#### 2. Environment Variable Not Set

```bash
Error: KeyError: 'MT5_PASSWORD'

Solution:
# Set environment variable
export MT5_PASSWORD="your_password"
# Or create .env file
echo "MT5_PASSWORD=your_password" >> .env
```

#### 3. Configuration Format Error

```bash
Error: yaml.scanner.ScannerError: mapping values are not allowed here

Solution:
# Check YAML syntax
python -c "import yaml; yaml.safe_load(open('config/main.yaml'))"
# Use online YAML validator
```

#### 4. Configuration Conflicts

```bash
Error: Configuration conflict: multiple definitions found

Solution:
# Check configuration priority
# Remove duplicate configuration items
# Use configuration inheritance mechanism
```

### Configuration Debugging

```bash
# Show current configuration
python scripts/show_config.py

# Show configuration source
python scripts/show_config.py --show-source

# Validate configuration integrity
python scripts/validate_config.py --verbose
```

### Performance Tuning

```yaml
# High-frequency trading optimization
monitoring:
  base_polling_interval: 0.0001  # 0.1ms
  performance_mode: "high_frequency"

performance:
  scenarios:
    high_frequency:
      enabled: true
      polling_interval: 0.0001
      batch_size: 1
      threading: "single"
```

### Logging Configuration

```yaml
# logging.yaml
version: 1
formatters:
  default:
    format: '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    
handlers:
  console:
    class: logging.StreamHandler
    level: INFO
    formatter: default
    
  file:
    class: logging.handlers.RotatingFileHandler
    level: DEBUG
    formatter: default
    filename: logs/mt5_system.log
    maxBytes: 10485760  # 10MB
    backupCount: 5
```

## 📞 Getting Help

- **Configuration Issues**: See `config/README.md`
- **API Documentation**: http://localhost:8000/docs
- **Log Location**: `logs/` directory
- **Configuration Validation**: `python scripts/validate_config.py`

---

*Last Updated: 2025*  
*Configuration Version: 1.0.0*