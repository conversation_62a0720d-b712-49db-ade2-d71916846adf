#!/usr/bin/env python3
"""
关系管理Web API
提供RESTful API接口来管理交易关系
"""

import json
from datetime import datetime
from typing import Dict, List, Optional, Any
from dataclasses import asdict

try:
    from fastapi import FastAPI, HTTPException, Depends, Query, Body
    from pydantic import BaseModel, Field
    from fastapi.responses import JSONResponse
    FASTAPI_AVAILABLE = True
except ImportError:
    FASTAPI_AVAILABLE = False
    # 定义基础类用于类型提示
    class BaseModel:
        pass
    def Field(*args, **kwargs):
        return None

from .relationship_manager import (
    get_relationship_manager,
    RelationshipManager,
    TradingRelationship,
    RelationshipType,
    RelationshipStatus,
    VolumeMapping,
    SymbolFilter,
    TimeWindow,
    RiskConstraints
)
from ..utils.logger import get_logger

logger = get_logger(__name__)


# ============ Pydantic 模型 ============

class VolumeMapppingModel(BaseModel):
    """手数映射模型"""
    ratio: float = Field(1.0, description="基础比例")
    min_volume: float = Field(0.01, description="最小手数")
    max_volume: float = Field(10.0, description="最大手数")
    round_to: float = Field(0.01, description="手数取整到")
    use_dynamic_ratio: bool = Field(False, description="使用动态比例")
    balance_ratio_mode: str = Field("none", description="余额比例模式")


class SymbolFilterModel(BaseModel):
    """品种过滤器模型"""
    mode: str = Field("all", description="过滤模式: all, include, exclude")
    symbols: List[str] = Field(default_factory=list, description="品种列表")
    symbol_patterns: List[str] = Field(default_factory=list, description="品种模式")


class TimeWindowModel(BaseModel):
    """时间窗口模型"""
    start_time: str = Field("00:00", description="开始时间 HH:MM")
    end_time: str = Field("23:59", description="结束时间 HH:MM")
    timezone: str = Field("UTC", description="时区")
    days_of_week: List[int] = Field(default_factory=lambda: [0, 1, 2, 3, 4], description="工作日")
    exclude_holidays: bool = Field(True, description="排除节假日")


class RiskConstraintsModel(BaseModel):
    """风险约束模型"""
    max_daily_volume: float = Field(100.0, description="每日最大手数")
    max_concurrent_positions: int = Field(10, description="最大并发持仓")
    max_daily_loss: float = Field(1000.0, description="每日最大亏损")
    correlation_limit: float = Field(0.8, description="相关性限制")
    use_stop_loss: bool = Field(False, description="使用止损")
    stop_loss_pips: float = Field(50.0, description="止损点数")
    emergency_stop_loss: float = Field(5000.0, description="紧急止损")
    emergency_stop_drawdown: float = Field(0.20, description="紧急止损回撤")


class CreateRelationshipRequest(BaseModel):
    """创建关系请求"""
    source_account: str = Field(..., description="源账户")
    target_account: str = Field(..., description="目标账户")
    relationship_type: str = Field("forward", description="关系类型")
    name: Optional[str] = Field(None, description="关系名称")
    description: Optional[str] = Field(None, description="关系描述")
    volume_mapping: Optional[VolumeMapppingModel] = None
    symbol_filter: Optional[SymbolFilterModel] = None
    time_window: Optional[TimeWindowModel] = None
    risk_constraints: Optional[RiskConstraintsModel] = None
    tags: List[str] = Field(default_factory=list, description="标签")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="元数据")


class UpdateRelationshipRequest(BaseModel):
    """更新关系请求"""
    name: Optional[str] = None
    description: Optional[str] = None
    status: Optional[str] = None
    volume_mapping: Optional[VolumeMapppingModel] = None
    symbol_filter: Optional[SymbolFilterModel] = None
    time_window: Optional[TimeWindowModel] = None
    risk_constraints: Optional[RiskConstraintsModel] = None
    tags: Optional[List[str]] = None
    metadata: Optional[Dict[str, Any]] = None


class RelationshipResponse(BaseModel):
    """关系响应"""
    relationship_id: str
    name: str
    description: str
    source_account: str
    target_account: str
    relationship_type: str
    status: str
    volume_mapping: VolumeMapppingModel
    symbol_filter: SymbolFilterModel
    time_window: TimeWindowModel
    risk_constraints: RiskConstraintsModel
    created_at: str
    updated_at: str
    tags: List[str]
    metadata: Dict[str, Any]
    total_signals: int
    successful_copies: int
    failed_copies: int
    total_volume: float
    total_pnl: float


class BatchCreateRequest(BaseModel):
    """批量创建请求"""
    relationships: List[CreateRelationshipRequest]


class StatsResponse(BaseModel):
    """统计响应"""
    total_relationships: int
    active_relationships: int
    total_signals_processed: int
    last_updated: str


# ============ API 应用 ============

if FASTAPI_AVAILABLE:
    app = FastAPI(
        title="MT5跟单关系管理API",
        description="动态管理MT5账户之间的跟单关系",
        version="1.0.0"
    )


    def get_manager() -> RelationshipManager:
        """获取关系管理器依赖"""
        return get_relationship_manager()


    def _convert_relationship_to_response(relationship: TradingRelationship) -> RelationshipResponse:
        """转换关系对象为响应模型"""
        return RelationshipResponse(
            relationship_id=relationship.relationship_id,
            name=relationship.name,
            description=relationship.description,
            source_account=relationship.source_account,
            target_account=relationship.target_account,
            relationship_type=relationship.relationship_type.value,
            status=relationship.status.value,
            volume_mapping=VolumeMapppingModel(**asdict(relationship.volume_mapping)),
            symbol_filter=SymbolFilterModel(**asdict(relationship.symbol_filter)),
            time_window=TimeWindowModel(**asdict(relationship.time_window)),
            risk_constraints=RiskConstraintsModel(**asdict(relationship.risk_constraints)),
            created_at=relationship.created_at.isoformat(),
            updated_at=relationship.updated_at.isoformat(),
            tags=relationship.tags,
            metadata=relationship.metadata,
            total_signals=relationship.total_signals,
            successful_copies=relationship.successful_copies,
            failed_copies=relationship.failed_copies,
            total_volume=relationship.total_volume,
            total_pnl=relationship.total_pnl
        )


    # ============ 关系管理端点 ============

    @app.get("/relationships", response_model=List[RelationshipResponse])
    async def get_relationships(
        account: Optional[str] = Query(None, description="按账户过滤"),
        status: Optional[str] = Query(None, description="按状态过滤"),
        relationship_type: Optional[str] = Query(None, description="按类型过滤"),
        active_only: bool = Query(False, description="只返回激活的关系"),
        manager: RelationshipManager = Depends(get_manager)
    ):
        """获取关系列表"""
        try:
            if active_only:
                relationships = manager.get_active_relationships()
            else:
                relationships = list(manager.relationships.values())
            
            # 过滤
            if account:
                relationships = [r for r in relationships 
                               if r.source_account == account or r.target_account == account]
            
            if status:
                relationships = [r for r in relationships if r.status.value == status]
            
            if relationship_type:
                relationships = [r for r in relationships if r.relationship_type.value == relationship_type]
            
            return [_convert_relationship_to_response(r) for r in relationships]
            
        except Exception as e:
            logger.error(f"获取关系列表失败: {e}")
            raise HTTPException(status_code=500, detail=str(e))


    @app.post("/relationships", response_model=RelationshipResponse)
    async def create_relationship(
        request: CreateRelationshipRequest,
        manager: RelationshipManager = Depends(get_manager)
    ):
        """创建新关系"""
        try:
            # 转换参数
            kwargs = request.dict(exclude_unset=True)
            kwargs['relationship_type'] = RelationshipType(kwargs['relationship_type'])
            
            # 转换嵌套对象
            if 'volume_mapping' in kwargs and kwargs['volume_mapping']:
                kwargs['volume_mapping'] = VolumeMapping(**kwargs['volume_mapping'].dict())
            if 'symbol_filter' in kwargs and kwargs['symbol_filter']:
                kwargs['symbol_filter'] = SymbolFilter(**kwargs['symbol_filter'].dict())
            if 'time_window' in kwargs and kwargs['time_window']:
                kwargs['time_window'] = TimeWindow(**kwargs['time_window'].dict())
            if 'risk_constraints' in kwargs and kwargs['risk_constraints']:
                kwargs['risk_constraints'] = RiskConstraints(**kwargs['risk_constraints'].dict())
            
            relationship = manager.create_relationship(**kwargs)
            return _convert_relationship_to_response(relationship)
            
        except ValueError as e:
            raise HTTPException(status_code=400, detail=str(e))
        except Exception as e:
            logger.error(f"创建关系失败: {e}")
            raise HTTPException(status_code=500, detail=str(e))


    @app.get("/relationships/{relationship_id}", response_model=RelationshipResponse)
    async def get_relationship(
        relationship_id: str,
        manager: RelationshipManager = Depends(get_manager)
    ):
        """获取指定关系"""
        relationship = manager.get_relationship(relationship_id)
        if not relationship:
            raise HTTPException(status_code=404, detail="关系不存在")
        
        return _convert_relationship_to_response(relationship)


    @app.put("/relationships/{relationship_id}", response_model=RelationshipResponse)
    async def update_relationship(
        relationship_id: str,
        request: UpdateRelationshipRequest,
        manager: RelationshipManager = Depends(get_manager)
    ):
        """更新关系"""
        try:
            # 转换参数
            kwargs = request.dict(exclude_unset=True, exclude_none=True)
            
            if 'status' in kwargs:
                kwargs['status'] = RelationshipStatus(kwargs['status'])
            
            # 转换嵌套对象
            if 'volume_mapping' in kwargs and kwargs['volume_mapping']:
                kwargs['volume_mapping'] = VolumeMapping(**kwargs['volume_mapping'].dict())
            if 'symbol_filter' in kwargs and kwargs['symbol_filter']:
                kwargs['symbol_filter'] = SymbolFilter(**kwargs['symbol_filter'].dict())
            if 'time_window' in kwargs and kwargs['time_window']:
                kwargs['time_window'] = TimeWindow(**kwargs['time_window'].dict())
            if 'risk_constraints' in kwargs and kwargs['risk_constraints']:
                kwargs['risk_constraints'] = RiskConstraints(**kwargs['risk_constraints'].dict())
            
            relationship = manager.update_relationship(relationship_id, **kwargs)
            return _convert_relationship_to_response(relationship)
            
        except ValueError as e:
            raise HTTPException(status_code=400, detail=str(e))
        except Exception as e:
            logger.error(f"更新关系失败: {e}")
            raise HTTPException(status_code=500, detail=str(e))


    @app.delete("/relationships/{relationship_id}")
    async def delete_relationship(
        relationship_id: str,
        manager: RelationshipManager = Depends(get_manager)
    ):
        """删除关系"""
        success = manager.delete_relationship(relationship_id)
        if not success:
            raise HTTPException(status_code=404, detail="关系不存在")
        
        return {"message": "关系删除成功", "relationship_id": relationship_id}


    # ============ 关系操作端点 ============

    @app.post("/relationships/{relationship_id}/pause")
    async def pause_relationship(
        relationship_id: str,
        manager: RelationshipManager = Depends(get_manager)
    ):
        """暂停关系"""
        success = manager.pause_relationship(relationship_id)
        if not success:
            raise HTTPException(status_code=404, detail="关系不存在")
        
        return {"message": "关系暂停成功", "relationship_id": relationship_id}


    @app.post("/relationships/{relationship_id}/resume")
    async def resume_relationship(
        relationship_id: str,
        manager: RelationshipManager = Depends(get_manager)
    ):
        """恢复关系"""
        success = manager.resume_relationship(relationship_id)
        if not success:
            raise HTTPException(status_code=404, detail="关系不存在")
        
        return {"message": "关系恢复成功", "relationship_id": relationship_id}


    # ============ 批量操作端点 ============

    @app.post("/relationships/batch", response_model=List[RelationshipResponse])
    async def batch_create_relationships(
        request: BatchCreateRequest,
        manager: RelationshipManager = Depends(get_manager)
    ):
        """批量创建关系"""
        try:
            relationships_config = []
            for rel_req in request.relationships:
                config = rel_req.dict(exclude_unset=True)
                config['relationship_type'] = RelationshipType(config['relationship_type'])
                
                # 转换嵌套对象
                if 'volume_mapping' in config and config['volume_mapping']:
                    config['volume_mapping'] = VolumeMapping(**config['volume_mapping'])
                if 'symbol_filter' in config and config['symbol_filter']:
                    config['symbol_filter'] = SymbolFilter(**config['symbol_filter'])
                if 'time_window' in config and config['time_window']:
                    config['time_window'] = TimeWindow(**config['time_window'])
                if 'risk_constraints' in config and config['risk_constraints']:
                    config['risk_constraints'] = RiskConstraints(**config['risk_constraints'])
                
                relationships_config.append(config)
            
            relationships = manager.bulk_create_relationships(relationships_config)
            return [_convert_relationship_to_response(r) for r in relationships]
            
        except Exception as e:
            logger.error(f"批量创建关系失败: {e}")
            raise HTTPException(status_code=500, detail=str(e))


    @app.post("/relationships/batch/star")
    async def create_star_relationships(
        source_account: str = Body(..., description="源账户"),
        target_accounts: List[str] = Body(..., description="目标账户列表"),
        relationship_type: str = Body("forward", description="关系类型"),
        ratio: float = Body(1.0, description="手数比例"),
        manager: RelationshipManager = Depends(get_manager)
    ):
        """创建星型关系（一对多）"""
        try:
            created_relationships = []
            for target_account in target_accounts:
                relationship = manager.create_relationship(
                    source_account=source_account,
                    target_account=target_account,
                    relationship_type=RelationshipType(relationship_type),
                    name=f"Star: {source_account} -> {target_account}"
                )
                
                if ratio != 1.0:
                    relationship.volume_mapping.ratio = ratio
                    manager.update_relationship(relationship.relationship_id, 
                                              volume_mapping=relationship.volume_mapping)
                
                created_relationships.append(relationship)
            
            return {
                "message": f"创建星型关系成功",
                "source_account": source_account,
                "created_count": len(created_relationships),
                "relationships": [_convert_relationship_to_response(r) for r in created_relationships]
            }
            
        except Exception as e:
            logger.error(f"创建星型关系失败: {e}")
            raise HTTPException(status_code=500, detail=str(e))


    # ============ 查询端点 ============

    @app.get("/accounts/{account_id}/relationships", response_model=List[RelationshipResponse])
    async def get_account_relationships(
        account_id: str,
        role: Optional[str] = Query(None, description="角色: source, target"),
        manager: RelationshipManager = Depends(get_manager)
    ):
        """获取账户的所有关系"""
        relationships = []
        
        if role is None or role == "source":
            relationships.extend(manager.get_relationships_by_source(account_id))
        
        if role is None or role == "target":
            relationships.extend(manager.get_relationships_by_target(account_id))
        
        # 去重
        seen = set()
        unique_relationships = []
        for rel in relationships:
            if rel.relationship_id not in seen:
                seen.add(rel.relationship_id)
                unique_relationships.append(rel)
        
        return [_convert_relationship_to_response(r) for r in unique_relationships]


    @app.get("/symbols/{symbol}/relationships", response_model=List[RelationshipResponse])
    async def get_symbol_relationships(
        symbol: str,
        manager: RelationshipManager = Depends(get_manager)
    ):
        """获取支持指定品种的关系"""
        relationships = manager.get_relationships_for_symbol(symbol)
        return [_convert_relationship_to_response(r) for r in relationships]


    # ============ 统计端点 ============

    @app.get("/stats", response_model=StatsResponse)
    async def get_stats(manager: RelationshipManager = Depends(get_manager)):
        """获取统计信息"""
        stats = manager.get_stats()
        return StatsResponse(**stats)


    @app.get("/stats/accounts")
    async def get_account_stats(manager: RelationshipManager = Depends(get_manager)):
        """获取账户统计"""
        source_counts = {}
        target_counts = {}
        
        for rel in manager.relationships.values():
            source_counts[rel.source_account] = source_counts.get(rel.source_account, 0) + 1
            target_counts[rel.target_account] = target_counts.get(rel.target_account, 0) + 1
        
        return {
            "source_stats": source_counts,
            "target_stats": target_counts,
            "total_accounts": len(set(list(source_counts.keys()) + list(target_counts.keys())))
        }


    # ============ 导入导出端点 ============

    @app.get("/export")
    async def export_relationships(
        format: str = Query("yaml", description="导出格式: yaml, json"),
        manager: RelationshipManager = Depends(get_manager)
    ):
        """导出关系配置"""
        try:
            export_data = manager.export_relationships(format)
            
            if format == "yaml":
                return JSONResponse(
                    content=export_data,
                    media_type="application/x-yaml",
                    headers={"Content-Disposition": "attachment; filename=relationships.yaml"}
                )
            else:
                return JSONResponse(
                    content=json.loads(export_data),
                    headers={"Content-Disposition": "attachment; filename=relationships.json"}
                )
                
        except Exception as e:
            logger.error(f"导出关系失败: {e}")
            raise HTTPException(status_code=500, detail=str(e))


    @app.post("/import")
    async def import_relationships(
        data: str = Body(..., description="导入数据"),
        format: str = Body("yaml", description="数据格式: yaml, json"),
        overwrite: bool = Body(False, description="是否覆盖现有关系"),
        manager: RelationshipManager = Depends(get_manager)
    ):
        """导入关系配置"""
        try:
            imported_count = manager.import_relationships(data, format, overwrite)
            return {
                "message": "导入成功",
                "imported_count": imported_count
            }
            
        except Exception as e:
            logger.error(f"导入关系失败: {e}")
            raise HTTPException(status_code=500, detail=str(e))


    # ============ 健康检查 ============

    @app.get("/health")
    async def health_check():
        """健康检查"""
        return {
            "status": "healthy",
            "timestamp": datetime.now().isoformat(),
            "service": "MT5关系管理API"
        }


    # ============ 启动函数 ============

    def run_api(host: str = "0.0.0.0", port: int = 8000, debug: bool = False):
        """启动API服务"""
        try:
            import uvicorn
            uvicorn.run(app, host=host, port=port, debug=debug)
        except ImportError:
            logger.error("需要安装 uvicorn 来运行Web API: pip install uvicorn")
            raise


else:
    logger.warning("FastAPI 不可用，无法启动Web API。请安装: pip install fastapi uvicorn")
    
    def run_api(*args, **kwargs):
        raise ImportError("需要安装 FastAPI 和 uvicorn 来运行Web API")


if __name__ == "__main__":
    if FASTAPI_AVAILABLE:
        run_api(debug=True)
    else:
        print("请安装所需依赖: pip install fastapi uvicorn")