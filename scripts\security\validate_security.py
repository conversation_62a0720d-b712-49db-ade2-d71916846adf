#!/usr/bin/env python3
"""
MT5 Trading System Security Validation
企业级安全配置验证和测试工具
"""

import os
import sys
import json
import yaml
import hashlib
import secrets
import logging
import traceback
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Any, Tuple

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# 加载.env文件
def load_env_file():
    """加载.env文件中的环境变量"""
    env_file = Path('.env')
    if env_file.exists():
        logger.info(f"Loading environment variables from {env_file}")
        with open(env_file, 'r', encoding='utf-8') as f:
            for line in f:
                line = line.strip()
                if line and not line.startswith('#') and '=' in line:
                    key, value = line.split('=', 1)
                    os.environ[key] = value
        logger.info("Environment variables loaded successfully")
    else:
        logger.warning(".env file not found")

# 在模块导入时立即加载环境变量
load_env_file()

class SecurityValidator:
    """安全验证器"""
    
    def __init__(self):
        self.passed_tests = 0
        self.failed_tests = 0
        self.warnings = 0
        self.total_tests = 0
        self.test_results = []
        
    def run_test(self, test_name: str, test_func, *args, **kwargs) -> bool:
        """运行单个测试"""
        self.total_tests += 1
        try:
            result = test_func(*args, **kwargs)
            if result is True:
                self.passed_tests += 1
                logger.info(f"✅ {test_name}: PASSED")
                self.test_results.append({
                    "test": test_name,
                    "status": "PASSED",
                    "message": "Test completed successfully"
                })
                return True
            elif result is False:
                self.failed_tests += 1
                logger.error(f"❌ {test_name}: FAILED")
                self.test_results.append({
                    "test": test_name,
                    "status": "FAILED",
                    "message": "Test failed"
                })
                return False
            else:
                self.warnings += 1
                logger.warning(f"⚠️  {test_name}: WARNING - {result}")
                self.test_results.append({
                    "test": test_name,
                    "status": "WARNING",
                    "message": str(result)
                })
                return False
        except Exception as e:
            self.failed_tests += 1
            logger.error(f"💀 {test_name}: ERROR - {str(e)}")
            self.test_results.append({
                "test": test_name,
                "status": "ERROR",
                "message": str(e),
                "traceback": traceback.format_exc()
            })
            return False
    
    def validate_environment_variables(self) -> bool:
        """验证环境变量"""
        logger.info("🔍 Validating environment variables...")
        
        required_vars = [
            "JWT_SECRET",
            "MASTER_ENCRYPTION_KEY",
            "DB_ENCRYPTION_KEY"
        ]
        
        missing_vars = []
        weak_vars = []
        
        for var in required_vars:
            value = os.getenv(var)
            if not value:
                missing_vars.append(var)
            elif len(value) < 32:
                weak_vars.append(f"{var} (length: {len(value)})")
        
        if missing_vars:
            return f"Missing required environment variables: {', '.join(missing_vars)}"
        
        if weak_vars:
            return f"Weak environment variables: {', '.join(weak_vars)}"
        
        return True
    
    def validate_configuration_files(self) -> bool:
        """验证配置文件"""
        logger.info("🔍 Validating configuration files...")
        
        config_files = [
            "config/main.yaml",
            "config/security/api_permissions.yaml",
            "config/security/encryption_keys.yaml"
        ]
        
        for config_file in config_files:
            if not os.path.exists(config_file):
                return f"Missing configuration file: {config_file}"
            
            try:
                with open(config_file, 'r') as f:
                    yaml.safe_load(f)
            except yaml.YAMLError as e:
                return f"Invalid YAML in {config_file}: {str(e)}"
        
        return True
    
    def validate_file_permissions(self) -> bool:
        """验证文件权限"""
        logger.info("🔍 Validating file permissions...")
        
        sensitive_files = {
            "config/security/encryption_keys.yaml": 0o600,
            "config/security/api_permissions.yaml": 0o600,
            ".env": 0o600
        }
        
        permission_issues = []
        
        for file_path, expected_perm in sensitive_files.items():
            if os.path.exists(file_path):
                actual_perm = os.stat(file_path).st_mode & 0o777
                if actual_perm != expected_perm:
                    permission_issues.append(
                        f"{file_path}: {oct(actual_perm)} (expected: {oct(expected_perm)})"
                    )
        
        if permission_issues:
            return f"Incorrect file permissions: {'; '.join(permission_issues)}"
        
        return True
    
    def test_encryption_functionality(self) -> bool:
        """测试加密功能"""
        logger.info("🔍 Testing encryption functionality...")
        
        try:
            from cryptography.fernet import Fernet
            from cryptography.hazmat.primitives import hashes
            from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
            import base64
            
            # 测试基础加密
            key = Fernet.generate_key()
            f = Fernet(key)
            
            test_data = b"Test encryption data for MT5 trading system"
            encrypted = f.encrypt(test_data)
            decrypted = f.decrypt(encrypted)
            
            if decrypted != test_data:
                return "Basic encryption/decryption test failed"
            
            # 测试密钥派生
            password = b"test_password_for_mt5_system"
            salt = os.urandom(32)
            
            kdf = PBKDF2HMAC(
                algorithm=hashes.SHA256(),
                length=32,
                salt=salt,
                iterations=100000,
            )
            
            derived_key = kdf.derive(password)
            if len(derived_key) != 32:
                return "Key derivation test failed"
            
            # 测试哈希功能
            test_hash = hashlib.sha256(test_data).hexdigest()
            if len(test_hash) != 64:
                return "Hash function test failed"
            
            logger.info("🔐 All encryption tests passed")
            return True
            
        except ImportError as e:
            return f"Missing cryptography library: {str(e)}"
        except Exception as e:
            return f"Encryption test failed: {str(e)}"
    
    def test_jwt_functionality(self) -> bool:
        """测试JWT功能"""
        logger.info("🔍 Testing JWT functionality...")
        
        try:
            import jwt
            from datetime import datetime, timedelta
            
            # 测试JWT编码和解码
            secret = os.getenv("JWT_SECRET", "test_secret_key_for_validation")
            
            payload = {
                "user_id": "test_user",
                "roles": ["trader"],
                "exp": datetime.utcnow() + timedelta(minutes=30),
                "iat": datetime.utcnow(),
                "iss": "mt5-trading-system",
                "aud": "mt5-api"
            }
            
            # 编码
            token = jwt.encode(payload, secret, algorithm="HS256")
            
            # 解码
            decoded = jwt.decode(
                token, 
                secret, 
                algorithms=["HS256"],
                audience="mt5-api",
                issuer="mt5-trading-system"
            )
            
            if decoded["user_id"] != "test_user":
                return "JWT encoding/decoding test failed"
            
            # 测试过期令牌
            expired_payload = payload.copy()
            expired_payload["exp"] = datetime.utcnow() - timedelta(minutes=1)
            expired_token = jwt.encode(expired_payload, secret, algorithm="HS256")
            
            try:
                jwt.decode(expired_token, secret, algorithms=["HS256"])
                return "Expired JWT token was not rejected"
            except jwt.ExpiredSignatureError:
                pass  # 这是期望的行为
            
            logger.info("🎫 JWT functionality test passed")
            return True
            
        except ImportError as e:
            return f"Missing PyJWT library: {str(e)}"
        except Exception as e:
            return f"JWT test failed: {str(e)}"
    
    def test_api_key_generation(self) -> bool:
        """测试API密钥生成"""
        logger.info("🔍 Testing API key generation...")
        
        try:
            # 测试密钥生成
            api_key = secrets.token_urlsafe(32)
            if len(api_key) < 32:
                return "Generated API key is too short"
            
            # 测试密钥哈希
            key_hash = hashlib.sha256(api_key.encode()).hexdigest()
            if len(key_hash) != 64:
                return "API key hashing failed"
            
            # 测试密钥唯一性
            api_key2 = secrets.token_urlsafe(32)
            if api_key == api_key2:
                return "API key generation is not random"
            
            logger.info("🔑 API key generation test passed")
            return True
            
        except Exception as e:
            return f"API key generation test failed: {str(e)}"
    
    def validate_security_configuration(self) -> bool:
        """验证安全配置"""
        logger.info("🔍 Validating security configuration...")
        
        try:
            # 检查API权限配置
            api_permissions_file = "config/security/api_permissions.yaml"
            if os.path.exists(api_permissions_file):
                with open(api_permissions_file, 'r') as f:
                    api_config = yaml.safe_load(f)
                
                # 检查角色定义
                if "roles" not in api_config:
                    return "No roles defined in API permissions configuration"
                
                required_roles = ["admin", "trader", "analyst", "viewer"]
                for role in required_roles:
                    if role not in api_config["roles"]:
                        return f"Missing required role: {role}"
                
                # 检查JWT配置
                if "jwt" not in api_config:
                    return "No JWT configuration found"
                
                jwt_config = api_config["jwt"]
                if jwt_config.get("algorithm") != "HS256":
                    return "JWT algorithm should be HS256"
                
                if not jwt_config.get("secret", "").startswith("${"):
                    return "JWT secret should use environment variable"
            
            # 检查加密密钥配置
            encryption_keys_file = "config/security/encryption_keys.yaml"
            if os.path.exists(encryption_keys_file):
                with open(encryption_keys_file, 'r') as f:
                    crypto_config = yaml.safe_load(f)
                
                # 检查主密钥配置
                if "master_keys" not in crypto_config:
                    return "No master keys configuration found"
                
                # 检查算法配置
                algorithms = crypto_config.get("algorithms", {})
                symmetric = algorithms.get("symmetric", {})
                if symmetric.get("preferred") != "AES-256-GCM":
                    return "Preferred symmetric algorithm should be AES-256-GCM"
            
            logger.info("⚙️  Security configuration validation passed")
            return True
            
        except Exception as e:
            return f"Security configuration validation failed: {str(e)}"
    
    def test_password_strength_validation(self) -> bool:
        """测试密码强度验证"""
        logger.info("🔍 Testing password strength validation...")
        
        try:
            import re
            
            def check_password_strength(password: str) -> List[str]:
                issues = []
                if len(password) < 12:
                    issues.append("Too short (minimum 12 characters)")
                if not re.search(r'[A-Z]', password):
                    issues.append("Missing uppercase letters")
                if not re.search(r'[a-z]', password):
                    issues.append("Missing lowercase letters")
                if not re.search(r'[0-9]', password):
                    issues.append("Missing numbers")
                if not re.search(r'[!@#$%^&*(),.?":{}|<>]', password):
                    issues.append("Missing special characters")
                return issues
            
            # 测试弱密码
            weak_passwords = [
                "password",
                "123456",
                "admin",
                "Password",
                "Password123"
            ]
            
            for weak_pass in weak_passwords:
                issues = check_password_strength(weak_pass)
                if not issues:
                    return f"Weak password '{weak_pass}' was not detected"
            
            # 测试强密码
            strong_password = "MyStr0ng!P@ssw0rd#2024"
            issues = check_password_strength(strong_password)
            if issues:
                return f"Strong password was incorrectly flagged: {issues}"
            
            logger.info("🔐 Password strength validation test passed")
            return True
            
        except Exception as e:
            return f"Password strength validation test failed: {str(e)}"
    
    def test_rate_limiting_configuration(self) -> bool:
        """测试速率限制配置"""
        logger.info("🔍 Testing rate limiting configuration...")
        
        try:
            api_permissions_file = "config/security/api_permissions.yaml"
            if not os.path.exists(api_permissions_file):
                return "API permissions file not found"
            
            with open(api_permissions_file, 'r') as f:
                config = yaml.safe_load(f)
            
            # 检查全局速率限制
            rate_limiting = config.get("rate_limiting", {})
            global_limit = rate_limiting.get("global", {})
            
            if not global_limit.get("enabled"):
                return "Global rate limiting is not enabled"
            
            requests_per_minute = global_limit.get("requests_per_minute")
            if not requests_per_minute or requests_per_minute < 100:
                return "Global rate limit is too low or not set"
            
            # 检查端点级速率限制
            endpoints = config.get("endpoints", {})
            for endpoint, methods in endpoints.items():
                for method, settings in methods.get("methods", {}).items():
                    if "rate_limit" not in settings:
                        return f"No rate limit set for {method} {endpoint}"
            
            logger.info("🚦 Rate limiting configuration test passed")
            return True
            
        except Exception as e:
            return f"Rate limiting configuration test failed: {str(e)}"
    
    def test_audit_logging_configuration(self) -> bool:
        """测试审计日志配置"""
        logger.info("🔍 Testing audit logging configuration...")
        
        try:
            api_permissions_file = "config/security/api_permissions.yaml"
            if not os.path.exists(api_permissions_file):
                return "API permissions file not found"
            
            with open(api_permissions_file, 'r') as f:
                config = yaml.safe_load(f)
            
            # 检查审计配置
            audit = config.get("audit", {})
            if not audit.get("enabled"):
                return "Audit logging is not enabled"
            
            # 检查审计事件
            events = audit.get("events", {})
            required_events = [
                "authentication",
                "authorization", 
                "api_access",
                "permission_changes",
                "sensitive_operations"
            ]
            
            for event in required_events:
                if not events.get(event):
                    return f"Audit event '{event}' is not enabled"
            
            # 检查审计存储
            storage = audit.get("storage", {})
            if storage.get("type") not in ["file", "database", "syslog"]:
                return "Invalid audit storage type"
            
            logger.info("📝 Audit logging configuration test passed")
            return True
            
        except Exception as e:
            return f"Audit logging configuration test failed: {str(e)}"
    
    def run_all_tests(self) -> Dict[str, Any]:
        """运行所有安全测试"""
        logger.info("🚀 Starting comprehensive security validation...")
        logger.info("=" * 60)
        
        # 运行所有测试
        tests = [
            ("Environment Variables Validation", self.validate_environment_variables),
            ("Configuration Files Validation", self.validate_configuration_files),
            ("File Permissions Validation", self.validate_file_permissions),
            ("Encryption Functionality Test", self.test_encryption_functionality),
            ("JWT Functionality Test", self.test_jwt_functionality),
            ("API Key Generation Test", self.test_api_key_generation),
            ("Security Configuration Validation", self.validate_security_configuration),
            ("Password Strength Validation Test", self.test_password_strength_validation),
            ("Rate Limiting Configuration Test", self.test_rate_limiting_configuration),
            ("Audit Logging Configuration Test", self.test_audit_logging_configuration),
        ]
        
        for test_name, test_func in tests:
            self.run_test(test_name, test_func)
        
        # 生成报告
        return self.generate_report()
    
    def generate_report(self) -> Dict[str, Any]:
        """生成安全验证报告"""
        security_score = (self.passed_tests / self.total_tests * 100) if self.total_tests > 0 else 0
        
        if security_score >= 90:
            security_level = "EXCELLENT"
            emoji = "🔒"
        elif security_score >= 80:
            security_level = "GOOD"
            emoji = "🔐"
        elif security_score >= 70:
            security_level = "MODERATE"
            emoji = "⚠️ "
        elif security_score >= 60:
            security_level = "WEAK"
            emoji = "🔓"
        else:
            security_level = "CRITICAL"
            emoji = "💀"
        
        report = {
            "timestamp": datetime.utcnow().isoformat(),
            "summary": {
                "total_tests": self.total_tests,
                "passed_tests": self.passed_tests,
                "failed_tests": self.failed_tests,
                "warnings": self.warnings,
                "security_score": round(security_score, 2),
                "security_level": security_level
            },
            "test_results": self.test_results
        }
        
        # 打印报告
        logger.info("=" * 60)
        logger.info("📊 SECURITY VALIDATION REPORT")
        logger.info("=" * 60)
        logger.info(f"Total Tests: {self.total_tests}")
        logger.info(f"Passed: {self.passed_tests}")
        logger.info(f"Failed: {self.failed_tests}")
        logger.info(f"Warnings: {self.warnings}")
        logger.info(f"Security Score: {security_score:.1f}%")
        logger.info(f"Security Level: {emoji} {security_level}")
        logger.info("=" * 60)
        
        if self.failed_tests > 0:
            logger.error("❌ FAILED TESTS:")
            for result in self.test_results:
                if result["status"] in ["FAILED", "ERROR"]:
                    logger.error(f"  • {result['test']}: {result['message']}")
        
        if self.warnings > 0:
            logger.warning("⚠️  WARNINGS:")
            for result in self.test_results:
                if result["status"] == "WARNING":
                    logger.warning(f"  • {result['test']}: {result['message']}")
        
        # 保存报告
        report_file = f"security_validation_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(report_file, 'w') as f:
            json.dump(report, f, indent=2)
        
        logger.info(f"📄 Report saved to: {report_file}")
        
        return report

def main():
    """主函数"""
    try:
        validator = SecurityValidator()
        report = validator.run_all_tests()
        
        # 退出码
        if validator.failed_tests > 0:
            sys.exit(1)
        elif report["summary"]["security_score"] < 80:
            sys.exit(2)
        else:
            sys.exit(0)
            
    except KeyboardInterrupt:
        logger.info("Security validation interrupted by user")
        sys.exit(130)
    except Exception as e:
        logger.error(f"Security validation failed: {str(e)}")
        logger.debug(traceback.format_exc())
        sys.exit(1)

if __name__ == "__main__":
    main()