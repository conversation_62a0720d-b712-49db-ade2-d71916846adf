#!/usr/bin/env python3
"""
MT5账户执行器 - 严格职责分离架构
仅负责执行交易命令，不进行任何监控活动
接收消息系统的执行指令，将结果反馈给消息系统
"""

import asyncio
import time
from typing import Dict, Any, Optional, List
from dataclasses import dataclass
from datetime import datetime

# 移除基类依赖，直接实现执行功能
from ..messaging.message_types import TradeSignal, OrderTypeEnum, TradeAction
from ..utils.logger import get_logger
from ..messaging.jetstream_client import JetStreamClient

logger = get_logger(__name__)


@dataclass
class ExecutionResult:
    """执行结果"""
    command_id: str
    account_id: str
    success: bool
    result_data: Dict[str, Any]
    timestamp: float
    error_message: Optional[str] = None


@dataclass
class ExecutionCommand:
    """执行命令"""
    command_id: str
    command_type: str  # open, close, modify
    account_id: str
    symbol: str
    volume: float
    order_type: OrderTypeEnum
    price: Optional[float] = None
    sl: Optional[float] = None
    tp: Optional[float] = None
    position_id: Optional[int] = None
    metadata: Dict[str, Any] = None


class MT5AccountExecutor:
    """
    MT5账户执行器 - 严格职责分离实现
    
    职责：
    执行开仓命令
    执行平仓命令  
    执行修改命令
    反馈执行结果
    
    职责边界：
    不监控任何数据
    不主动生成信号
    不处理跟单逻辑
    只响应执行命令
    """
    
    def __init__(self, account_id: str, account_config: Dict[str, Any],
                 mt5_client, command_subscriber: JetStreamClient,
                 result_publisher: JetStreamClient, rpc_client=None):
        # 基础属性
        self.account_config = account_config
        self.running = False
        self.start_time = None
        self.account_id = account_id
        self.mt5_client = mt5_client  # 保留作为备用，逐步迁移到RPC
        self.command_subscriber = command_subscriber
        self.result_publisher = result_publisher

        # RPC客户端 - 新的架构核心
        self.rpc_client = rpc_client
        if self.rpc_client:
            logger.info(f"执行器使用RPC架构: {self.account_id}")
        else:
            logger.warning(f"执行器使用传统架构: {self.account_id}")
        
        # 执行状态
        self.pending_commands: Dict[str, ExecutionCommand] = {}
        # 增加队列容量并从配置获取
        queue_size = account_config.get('execution_queue_size', 500)  # 默认500，比原来的100大5倍
        self.command_queue = asyncio.Queue(maxsize=queue_size)
        self.queue_size = queue_size
        
        # 执行统计
        self.execution_stats = {
            'commands_received': 0,
            'commands_executed': 0,
            'commands_failed': 0,
            'execution_errors': 0,
            'last_execution_time': 0,
            'queue_full_count': 0,
            'queue_max_size': 0,
            'queue_current_size': 0
        }
        
        # 命令主题
        self.command_topic = f"MT5.EXECUTE.{self.account_id}"
        self.result_topic = f"MT5.RESULT.{self.account_id}"

        # 从统一监控配置获取执行器配置
        try:
            from .config_manager import get_config_manager
            config_mgr = get_config_manager()

            # 获取执行器相关配置
            self.execution_timeout = config_mgr.get('monitoring.execution_timeout', 30.0)
            self.retry_attempts = config_mgr.get('monitoring.retry_attempts', 3)
            self.sleep_interval = config_mgr.get('monitoring.sleep_interval', 0.2)

            logger.info(f"执行器配置加载: {self.account_id} - 超时:{self.execution_timeout}s, 重试:{self.retry_attempts}次, 睡眠:{self.sleep_interval}s")

        except Exception as e:
            logger.warning(f"执行器配置加载失败，使用默认值: {e}")
            self.execution_timeout = 30.0
            self.retry_attempts = 3
            self.sleep_interval = 0.2

        logger.info(f"MT5账户执行器初始化: {account_id} - 纯执行模式")

    def _parse_order_type(self, order_type_value):
        """解析订单类型"""
        try:
            # 如果已经是OrderTypeEnum实例，直接返回
            if isinstance(order_type_value, OrderTypeEnum):
                return order_type_value

            # 如果是字符串，尝试转换
            if isinstance(order_type_value, str):
                # 标准化字符串格式
                order_type_str = order_type_value.upper().strip()

                # 映射常见的订单类型
                type_mapping = {
                    'BUY': OrderTypeEnum.BUY,
                    'SELL': OrderTypeEnum.SELL,
                    'BUY_LIMIT': OrderTypeEnum.BUY_LIMIT,
                    'SELL_LIMIT': OrderTypeEnum.SELL_LIMIT,
                    'BUY_STOP': OrderTypeEnum.BUY_STOP,
                    'SELL_STOP': OrderTypeEnum.SELL_STOP,
                    'BUY_STOP_LIMIT': OrderTypeEnum.BUY_STOP_LIMIT,
                    'SELL_STOP_LIMIT': OrderTypeEnum.SELL_STOP_LIMIT,
                    # 兼容数字类型
                    '0': OrderTypeEnum.BUY,
                    '1': OrderTypeEnum.SELL,
                }

                if order_type_str in type_mapping:
                    return type_mapping[order_type_str]
                else:
                    return OrderTypeEnum(order_type_str)

            if isinstance(order_type_value, (int, float)):
                if order_type_value == 0:
                    return OrderTypeEnum.BUY
                elif order_type_value == 1:
                    return OrderTypeEnum.SELL
                else:
                    logger.warning(f"未知的数字订单类型: {order_type_value}, 默认使用BUY")
                    return OrderTypeEnum.BUY

            logger.warning(f"无法解析订单类型: {order_type_value}, 默认使用BUY")
            return OrderTypeEnum.BUY

        except Exception as e:
            logger.error(f"解析订单类型失败: {order_type_value}, 错误: {e}")
            return OrderTypeEnum.BUY

    async def start(self):
        """启动执行器 - 实现启动接口"""
        return await self.start_executor()
    
    async def _ensure_execution_ready(self) -> bool:
        """确保执行器就绪状态 - 通过进程隔离MT5客户端"""
        if not self.mt5_client:
            logger.error(f"MT5客户端未初始化: {self.account_id}")
            return False
        
        if hasattr(self.mt5_client, 'process_manager') and hasattr(self.mt5_client.process_manager, 'is_account_connected'):
            if not self.mt5_client.process_manager.is_account_connected(self.account_id):
                logger.warning(f"MT5连接未就绪，执行器将在连接建立后开始工作: {self.account_id}")
                # 不返回 False，而是继续启动执行器，在执行循环中等待连接
        
        # 额外检查：尝试获取账户信息验证连接健康（如果连接未就绪，跳过验证）
        try:
            account_info = await self.mt5_client.get_account_info()
            if not account_info:
                logger.warning(f"连接未完全建立，执行器将在后续循环中验证: {self.account_id}")
            else:
                logger.info(f"执行器连接验证成功: {self.account_id}")
            return True
        except Exception as e:
            logger.warning(f"连接验证跳过，将在后续循环中重试: {self.account_id} - {e}")
            return True  # 允许启动，在循环中重试
    
    async def start_executor(self) -> bool:
        """启动执行器 - 仅执行，不监控"""
        if self.running:
            logger.warning(f"执行器已在运行: {self.account_id}")
            return False
        
        try:
            if not await self._ensure_execution_ready():
                logger.error(f"执行器未就绪: {self.account_id}")
                return False
            
            self.running = True
            self.start_time = time.time()
            
            await self.command_subscriber.subscribe(
                subject=self.command_topic,
                callback=self._handle_execution_command
            )
            
            asyncio.create_task(self._execution_loop())
            asyncio.create_task(self._queue_monitor_loop())
            
            await self._publish_execution_result("executor_started", self.account_id, True, {
                "account_id": self.account_id,
                "start_time": self.start_time
            })
            
            logger.info(f"MT5账户执行器已启动: {self.account_id}")
            return True
            
        except Exception as e:
            logger.error(f"启动执行器失败: {e}")
            self.running = False
            return False
    
    async def stop_executor(self):
        """停止执行器"""
        if not self.running:
            return
        
        self.running = False
        
        while not self.command_queue.empty():
            try:
                command = await asyncio.wait_for(self.command_queue.get(), timeout=1.0)
                await self._execute_command(command)
            except asyncio.TimeoutError:
                break
        
        await self._publish_execution_result("executor_stopped", self.account_id, True, {
            "account_id": self.account_id,
            "stop_time": time.time(),
            "stats": self.execution_stats
        })
        
        logger.info(f"MT5账户执行器已停止: {self.account_id}")
    
    async def _handle_execution_command(self, msg):
        """处理执行命令消息"""
        try:
            command_data = msg.data if isinstance(msg.data, dict) else msg.data.decode('utf-8')
            
            # 解析执行命令
            command = ExecutionCommand(
                command_id=command_data.get('command_id', ''),
                command_type=command_data.get('command_type', ''),
                account_id=command_data.get('account_id', ''),
                symbol=command_data.get('symbol', ''),
                volume=float(command_data.get('volume', 0)),
                order_type=self._parse_order_type(command_data.get('order_type', 'BUY')),
                price=command_data.get('price'),
                sl=command_data.get('sl'),
                tp=command_data.get('tp'),
                position_id=command_data.get('position_id'),
                metadata=command_data.get('metadata', {})
            )
            
            # 验证命令
            if command.account_id != self.account_id:
                logger.warning(f"命令账户不匹配: {command.account_id} != {self.account_id}")
                await msg.nak()
                return
            
            try:
                self.command_queue.put_nowait(command)
                self.execution_stats['commands_received'] += 1
                await msg.ack()

                logger.debug(f"接收执行命令: {command.command_type} - {command.symbol} (队列: {self.command_queue.qsize()}/{self.queue_size})")

            except asyncio.QueueFull:
                current_size = self.command_queue.qsize()
                logger.warning(f"执行队列已满 ({current_size}/{self.queue_size})，尝试优先级处理")

                try:
                    await asyncio.wait_for(self.command_queue.put(command), timeout=0.5)
                    self.execution_stats['commands_received'] += 1
                    await msg.ack()
                    logger.info(f"延迟接收执行命令: {command.command_type} - {command.symbol}")
                except asyncio.TimeoutError:
                    logger.error(f"执行队列持续满载，拒绝命令: {command.command_type} - {command.symbol}")
                    await msg.nak()
                    self.execution_stats['commands_failed'] += 1
                
        except Exception as e:
            logger.error(f"处理执行命令失败: {e}")
            await msg.nak()
    
    async def _execution_loop(self):
        """执行主循环 - 纯执行逻辑"""
        logger.info(f"执行循环启动: {self.account_id}")
        
        while self.running:
            try:
                # 从队列获取命令
                command = await asyncio.wait_for(
                    self.command_queue.get(), 
                    timeout=1.0
                )
                
                # 执行命令
                await self._execute_command(command)
                
            except asyncio.TimeoutError:
                continue  # 队列为空，继续等待
            except Exception as e:
                logger.error(f"执行循环异常: {e}")
                self.execution_stats['execution_errors'] += 1
                await asyncio.sleep(1)

    async def _queue_monitor_loop(self):
        """队列监控循环"""
        logger.info(f"队列监控启动: {self.account_id}")

        while self.running:
            try:
                current_size = self.command_queue.qsize()
                self.execution_stats['queue_current_size'] = current_size
                self.execution_stats['queue_max_size'] = max(
                    self.execution_stats['queue_max_size'],
                    current_size
                )

                # 队列使用率警告
                usage_rate = current_size / self.queue_size
                if usage_rate > 0.8:
                    logger.warning(f"执行队列使用率高: {current_size}/{self.queue_size} ({usage_rate:.1%})")
                elif usage_rate > 0.5:
                    logger.info(f"执行队列使用率: {current_size}/{self.queue_size} ({usage_rate:.1%})")

                await asyncio.sleep(10)  # 每10秒检查一次

            except Exception as e:
                logger.error(f"队列监控异常: {e}")
                await asyncio.sleep(5)

    async def _execute_command(self, command: ExecutionCommand):
        """执行单个命令"""
        start_time = time.time()
        
        try:
            logger.info(f"执行命令: {command.command_type} {command.symbol} {command.volume}")
            
            if command.command_type == "open":
                result = await self._execute_open_position(command)
            elif command.command_type == "close":
                result = await self._execute_close_position(command)
            elif command.command_type == "modify":
                result = await self._execute_modify_position(command)
            else:
                result = ExecutionResult(
                    command_id=command.command_id,
                    account_id=command.account_id,
                    success=False,
                    result_data={},
                    timestamp=time.time(),
                    error_message=f"未知命令类型: {command.command_type}"
                )
            
            await self._publish_execution_result(
                command.command_type, 
                command.command_id, 
                result.success, 
                result.result_data,
                result.error_message
            )
            
            if result.success:
                self.execution_stats['commands_executed'] += 1
            else:
                self.execution_stats['commands_failed'] += 1
            
            self.execution_stats['last_execution_time'] = time.time()
            
            execution_time = time.time() - start_time
            logger.debug(f"命令执行完成: {command.command_id} - 耗时: {execution_time:.3f}s")
            
        except Exception as e:
            logger.error(f"执行命令异常: {e}")
            self.execution_stats['commands_failed'] += 1
            
            await self._publish_execution_result(
                command.command_type,
                command.command_id,
                False,
                {},
                str(e)
            )
    
    async def _execute_open_position(self, command: ExecutionCommand) -> ExecutionResult:
        """执行开仓"""
        try:
            execution_volume = await self._calculate_execution_volume(
                command.volume, 
                command.symbol
            )
            
            execution_price = await self._get_execution_price(
                command.symbol, 
                command.order_type.value
            )
            
            if self.rpc_client:
                order_type = 0 if command.order_type.value == 'BUY' else 1  # MT5 order types

                trade_result_dict = await self.rpc_client.send_order(
                    account_id=self.account_id,
                    symbol=command.symbol,
                    order_type=order_type,
                    volume=execution_volume,
                    price=execution_price,
                    sl=command.sl or 0.0,
                    tp=command.tp or 0.0,
                    comment=f"Execute_{command.command_id}"
                )

                if trade_result_dict.get("status") == "success":
                    trade_result = type('TradeResult', (), {
                        'retcode': trade_result_dict.get('retcode', 10020),
                        'order': trade_result_dict.get('order', 0),
                        'deal': trade_result_dict.get('deal', 0),
                        'price': trade_result_dict.get('price', execution_price),
                        'comment': trade_result_dict.get('comment', '')
                    })()
                else:
                    trade_result = type('TradeResult', (), {
                        'retcode': 10020,  # TRADE_RETCODE_ERROR
                        'order': 0,
                        'deal': 0,
                        'price': 0.0,
                        'comment': trade_result_dict.get('error', 'RPC call failed')
                    })()
            else:
                # 备用：使用传统MT5客户端
                from .mt5_client import TradeRequest, TradeAction, OrderType as MT5OrderType

                trade_request = TradeRequest(
                    action=TradeAction.DEAL,
                    symbol=command.symbol,
                    volume=execution_volume,
                    type=MT5OrderType.BUY if command.order_type.value == 'BUY' else MT5OrderType.SELL,
                    price=execution_price,
                    sl=command.sl,
                    tp=command.tp,
                    comment=f"Execute_{command.command_id}"
                )

                trade_result = await self.mt5_client.send_order(trade_request)
            
            return ExecutionResult(
                command_id=command.command_id,
                account_id=command.account_id,
                success=trade_result.retcode == 10009,  # TRADE_RETCODE_DONE
                result_data={
                    'ticket': trade_result.order,
                    'deal': trade_result.deal,
                    'volume': execution_volume,
                    'price': trade_result.price or execution_price,
                    'symbol': command.symbol,
                    'retcode': trade_result.retcode
                },
                timestamp=time.time(),
                error_message=trade_result.comment if trade_result.retcode != 10009 else None
            )
            
        except Exception as e:
            return ExecutionResult(
                command_id=command.command_id,
                account_id=command.account_id,
                success=False,
                result_data={},
                timestamp=time.time(),
                error_message=str(e)
            )
    
    async def _execute_close_position(self, command: ExecutionCommand) -> ExecutionResult:
        """执行平仓"""
        try:
            trade_result = await self.mt5_client.close_position(
                position_id=command.position_id,
                volume=command.volume
            )
            
            return ExecutionResult(
                command_id=command.command_id,
                account_id=command.account_id,
                success=trade_result.retcode == 10009,  # TRADE_RETCODE_DONE
                result_data={
                    'position_id': command.position_id,
                    'volume': command.volume,
                    'deal': trade_result.deal,
                    'retcode': trade_result.retcode
                },
                timestamp=time.time(),
                error_message=trade_result.comment if trade_result.retcode != 10009 else None
            )
            
        except Exception as e:
            return ExecutionResult(
                command_id=command.command_id,
                account_id=command.account_id,
                success=False,
                result_data={},
                timestamp=time.time(),
                error_message=str(e)
            )
    
    async def _execute_modify_position(self, command: ExecutionCommand) -> ExecutionResult:
        """执行修改持仓"""
        try:
            # 通过进程隔离MT5客户端执行修改持仓
            # 注意：MT5 API中修改持仓需要使用TradeRequest，这里需要特殊处理
            from .mt5_client import TradeRequest, TradeAction
            
            trade_request = TradeRequest(
                action=TradeAction.SLTP,
                position=command.position_id,
                sl=command.sl,
                tp=command.tp,
                comment=f"Modify_{command.command_id}"
            )
            
            trade_result = await self.mt5_client.send_order(trade_request)
            
            return ExecutionResult(
                command_id=command.command_id,
                account_id=command.account_id,
                success=trade_result.retcode == 10009,  # TRADE_RETCODE_DONE
                result_data={
                    'position_id': command.position_id,
                    'sl': command.sl,
                    'tp': command.tp,
                    'retcode': trade_result.retcode
                },
                timestamp=time.time(),
                error_message=trade_result.comment if trade_result.retcode != 10009 else None
            )
            
        except Exception as e:
            return ExecutionResult(
                command_id=command.command_id,
                account_id=command.account_id,
                success=False,
                result_data={},
                timestamp=time.time(),
                error_message=str(e)
            )
    
    async def _calculate_execution_volume(self, signal_volume: float, symbol: str) -> float:
        """计算执行手数 - 基于风控规则"""
        try:
            # 简单实现：直接使用信号手数
            # 实际生产中这里应该有复杂的风控逻辑
            return signal_volume
        except Exception as e:
            logger.error(f"计算执行手数失败: {e}")
            return 0.0
    
    async def _get_execution_price(self, symbol: str, order_type: str) -> Optional[float]:
        """获取执行价格 - 基于市价或配置"""
        try:
            # 对于市价单，返回None让MT5自动处理
            # 对于限价单，这里应该有价格计算逻辑
            return None
        except Exception as e:
            logger.error(f"获取执行价格失败: {e}")
            return None
    
    async def _publish_execution_result(self, command_type: str, command_id: str, 
                                      success: bool, data: Dict[str, Any], 
                                      error_message: Optional[str] = None):
        """发布执行结果"""
        try:
            result = {
                'command_type': command_type,
                'command_id': command_id,
                'account_id': self.account_id,
                'success': success,
                'data': data,
                'timestamp': time.time(),
                'error_message': error_message
            }
            
            await self.result_publisher.publish(
                subject=self.result_topic,
                data=result
            )
            
            logger.debug(f"发布执行结果: {command_type} - {success}")
            
        except Exception as e:
            logger.error(f"发布执行结果失败: {e}")
    
    def get_execution_stats(self) -> Dict[str, Any]:
        """获取执行统计"""
        uptime = time.time() - (self.start_time or time.time())

        return {
            **self.execution_stats,
            'account_id': self.account_id,
            'running': self.running,
            'uptime_seconds': uptime,
            'pending_commands': len(self.pending_commands),
            'queue_size': self.command_queue.qsize(),
            'command_topic': self.command_topic,
            'result_topic': self.result_topic,
            'rpc_enabled': self.rpc_client is not None
        }

    async def execute_order_rpc(self, symbol: str, order_type: str, volume: float,
                               price: float = 0.0, sl: float = 0.0, tp: float = 0.0,
                               comment: str = "") -> Dict[str, Any]:
        """使用RPC直接执行订单"""
        if not self.rpc_client:
            return {"status": "failed", "error": "RPC client not available"}

        try:
            # 转换订单类型
            mt5_order_type = 0 if order_type.upper() == 'BUY' else 1

            result = await self.rpc_client.send_order(
                account_id=self.account_id,
                symbol=symbol,
                order_type=mt5_order_type,
                volume=volume,
                price=price,
                sl=sl,
                tp=tp,
                comment=comment
            )

            return result
        except Exception as e:
            logger.error(f"RPC订单执行失败: {self.account_id}, 错误: {e}")
            return {"status": "failed", "error": str(e)}
    
    # 重写父类方法，确保只执行
    async def execute_trade(self, signal: TradeSignal) -> bool:
        """执行交易 - 父类接口实现"""
        command = ExecutionCommand(
            command_id=f"trade_{signal.signal_id}",
            command_type=signal.action.value.lower(),
            account_id=self.account_id,
            symbol=signal.symbol,
            volume=signal.volume,
            order_type=signal.order_type,
            price=signal.price,
            sl=signal.sl,
            tp=signal.tp,
            position_id=signal.position_id
        )
        
        await self.command_queue.put(command)
        return True