#!/usr/bin/env python3
"""
增强的故障恢复测试
测试网络中断恢复、验证数据一致性、测试组件重启机制
"""

import asyncio
import json
import os
import sys
import tempfile
import time
import signal
import subprocess
from pathlib import Path
from typing import Dict, Any, List
from dataclasses import dataclass

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# 导入真实组件
try:
    from src.core.mt5_account_monitor import MT5AccountMonitor
    from src.core.mt5_account_executor import MT5AccountExecutor
    from src.core.mt5_rpc_client import MT5RPCClient
    from src.core.mt5_request_handler import MT5RequestHandler
    from src.messaging.jetstream_client import JetStreamClient
    from src.messaging.message_types import MessageEnvelope, TradeSignal, SignalType, PositionSignalData
    
    REAL_COMPONENTS_AVAILABLE = True
    print("✅ 增强故障恢复测试组件导入成功")
    
except ImportError as e:
    print(f"❌ 导入组件失败: {e}")
    sys.exit(1)


@dataclass
class FaultEvent:
    """故障事件"""
    timestamp: float
    event_type: str  # 'network_disconnect', 'component_crash', 'service_restart'
    component: str
    description: str
    recovery_time: float = 0.0
    data_loss: bool = False


@dataclass
class ConsistencyCheck:
    """数据一致性检查"""
    timestamp: float
    check_type: str
    expected_data: Dict[str, Any]
    actual_data: Dict[str, Any]
    is_consistent: bool
    inconsistency_details: List[str]


class DataConsistencyValidator:
    """数据一致性验证器"""
    
    def __init__(self):
        self.expected_state = {}
        self.actual_state = {}
        self.consistency_checks = []
    
    def set_expected_state(self, component: str, state: Dict[str, Any]):
        """设置期望状态"""
        self.expected_state[component] = {
            'state': state,
            'timestamp': time.time()
        }
    
    def update_actual_state(self, component: str, state: Dict[str, Any]):
        """更新实际状态"""
        self.actual_state[component] = {
            'state': state,
            'timestamp': time.time()
        }
    
    def check_consistency(self, component: str) -> ConsistencyCheck:
        """检查数据一致性"""
        timestamp = time.time()
        
        if component not in self.expected_state:
            return ConsistencyCheck(
                timestamp=timestamp,
                check_type=f'{component}_consistency',
                expected_data={},
                actual_data=self.actual_state.get(component, {}).get('state', {}),
                is_consistent=False,
                inconsistency_details=['No expected state defined']
            )
        
        if component not in self.actual_state:
            return ConsistencyCheck(
                timestamp=timestamp,
                check_type=f'{component}_consistency',
                expected_data=self.expected_state[component]['state'],
                actual_data={},
                is_consistent=False,
                inconsistency_details=['No actual state available']
            )
        
        expected = self.expected_state[component]['state']
        actual = self.actual_state[component]['state']
        
        inconsistencies = []
        is_consistent = True
        
        # 检查关键字段
        for key, expected_value in expected.items():
            if key not in actual:
                inconsistencies.append(f'Missing key: {key}')
                is_consistent = False
            elif actual[key] != expected_value:
                inconsistencies.append(f'Value mismatch for {key}: expected {expected_value}, got {actual[key]}')
                is_consistent = False
        
        # 检查额外字段
        for key in actual:
            if key not in expected:
                inconsistencies.append(f'Unexpected key: {key}')
        
        check = ConsistencyCheck(
            timestamp=timestamp,
            check_type=f'{component}_consistency',
            expected_data=expected,
            actual_data=actual,
            is_consistent=is_consistent,
            inconsistency_details=inconsistencies
        )
        
        self.consistency_checks.append(check)
        return check
    
    def get_consistency_summary(self) -> Dict[str, Any]:
        """获取一致性检查总结"""
        if not self.consistency_checks:
            return {}
        
        total_checks = len(self.consistency_checks)
        consistent_checks = sum(1 for check in self.consistency_checks if check.is_consistent)
        
        return {
            'total_checks': total_checks,
            'consistent_checks': consistent_checks,
            'inconsistent_checks': total_checks - consistent_checks,
            'consistency_rate': (consistent_checks / total_checks * 100) if total_checks > 0 else 0,
            'checks': self.consistency_checks
        }


class NetworkSimulator:
    """网络故障模拟器"""
    
    def __init__(self):
        self.network_enabled = True
        self.latency_ms = 0
        self.packet_loss_rate = 0.0
    
    async def simulate_network_disconnect(self, duration: float):
        """模拟网络断开"""
        print(f"    🔌 模拟网络断开 {duration} 秒...")
        self.network_enabled = False
        await asyncio.sleep(duration)
        self.network_enabled = True
        print(f"    🔌 网络恢复连接")
    
    async def simulate_high_latency(self, latency_ms: int, duration: float):
        """模拟高延迟"""
        print(f"    🐌 模拟高延迟 {latency_ms}ms，持续 {duration} 秒...")
        original_latency = self.latency_ms
        self.latency_ms = latency_ms
        await asyncio.sleep(duration)
        self.latency_ms = original_latency
        print(f"    🐌 延迟恢复正常")
    
    async def simulate_packet_loss(self, loss_rate: float, duration: float):
        """模拟丢包"""
        print(f"    📦 模拟丢包率 {loss_rate*100:.1f}%，持续 {duration} 秒...")
        original_loss_rate = self.packet_loss_rate
        self.packet_loss_rate = loss_rate
        await asyncio.sleep(duration)
        self.packet_loss_rate = original_loss_rate
        print(f"    📦 丢包率恢复正常")
    
    def is_network_available(self) -> bool:
        """检查网络是否可用"""
        return self.network_enabled
    
    def get_network_conditions(self) -> Dict[str, Any]:
        """获取网络状况"""
        return {
            'enabled': self.network_enabled,
            'latency_ms': self.latency_ms,
            'packet_loss_rate': self.packet_loss_rate
        }


class EnhancedFaultRecoveryTest:
    """增强的故障恢复测试类"""
    
    def __init__(self):
        self.test_data_dir = Path(tempfile.mkdtemp(prefix="enhanced_fault_recovery_test_"))
        self.jetstream_clients = []
        self.components = []
        self.fault_events = []
        self.consistency_validator = DataConsistencyValidator()
        self.network_simulator = NetworkSimulator()
        print(f"测试数据目录: {self.test_data_dir}")
    
    def cleanup(self):
        """清理测试资源"""
        import shutil
        try:
            # 停止所有组件
            for component in self.components:
                if hasattr(component, 'running'):
                    component.running = False
            
            # 断开所有JetStream连接
            for client in self.jetstream_clients:
                if hasattr(client, 'disconnect'):
                    asyncio.create_task(client.disconnect())
            
            shutil.rmtree(self.test_data_dir)
            print("🧹 增强故障恢复测试环境清理完成")
        except Exception as e:
            print(f"⚠️ 清理警告: {e}")
    
    async def setup_fault_recovery_infrastructure(self):
        """设置故障恢复测试基础设施"""
        print("\n🏗️ 设置增强故障恢复测试基础设施...")
        
        try:
            # 创建主JetStream客户端
            main_config = {
                'servers': ['nats://localhost:4222'],
                'stream_name': 'ENHANCED_FAULT_RECOVERY_MAIN',
                'subjects': ['ENHANCED.FAULT.RECOVERY.>']
            }
            
            main_client = JetStreamClient(main_config)
            connected = await main_client.connect()
            
            if not connected:
                print("  ❌ 无法连接到NATS服务器")
                return None
            
            print("  ✅ 增强故障恢复基础设施设置成功")
            self.jetstream_clients.append(main_client)
            
            return main_client
            
        except Exception as e:
            print(f"  ❌ 增强故障恢复基础设施设置失败: {e}")
            return None
    
    async def test_network_interruption_recovery(self):
        """测试网络中断恢复"""
        print("\n🌐 测试网络中断恢复...")
        
        # 设置基础设施
        main_client = await self.setup_fault_recovery_infrastructure()
        if not main_client:
            return False
        
        try:
            # 创建测试组件
            rpc_client = MT5RPCClient(main_client)
            
            # 创建模拟的MT5ProcessManager
            class NetworkAwareMT5ProcessManager:
                def __init__(self, network_simulator):
                    self.network_simulator = network_simulator
                    self.request_count = 0
                    self.failed_requests = 0
                
                async def handle_request(self, request_data):
                    self.request_count += 1
                    
                    # 检查网络状况
                    if not self.network_simulator.is_network_available():
                        self.failed_requests += 1
                        raise Exception("Network unavailable")
                    
                    # 模拟网络延迟
                    if self.network_simulator.latency_ms > 0:
                        await asyncio.sleep(self.network_simulator.latency_ms / 1000)
                    
                    return {
                        "status": "success",
                        "data": f"Network-aware response #{self.request_count}",
                        "timestamp": time.time(),
                        "network_conditions": self.network_simulator.get_network_conditions()
                    }
            
            network_manager = NetworkAwareMT5ProcessManager(self.network_simulator)
            rpc_handler = MT5RequestHandler(main_client, network_manager)
            
            print("  ✅ 网络感知组件创建成功")
            self.components.extend([rpc_client, rpc_handler])
            
            # 启动RPC处理器
            await rpc_handler.start()
            await asyncio.sleep(1)
            
            # 测试正常网络条件下的通信
            print("  📡 测试正常网络条件...")
            
            normal_responses = []
            for i in range(3):
                try:
                    response = await rpc_client.call_rpc(
                        account_id='NETWORK_TEST_001',
                        method='network_test',
                        params={'test_id': i},
                        timeout=3.0
                    )
                    normal_responses.append(response)
                except Exception as e:
                    print(f"    ⚠️ 正常网络测试失败: {e}")
            
            print(f"  ✅ 正常网络通信: {len(normal_responses)}/3 成功")
            
            # 设置期望状态
            self.consistency_validator.set_expected_state('rpc_handler', {
                'status': 'running',
                'request_count': network_manager.request_count,
                'failed_requests': network_manager.failed_requests
            })
            
            # 模拟网络中断
            print("  💥 模拟网络中断...")
            
            fault_event = FaultEvent(
                timestamp=time.time(),
                event_type='network_disconnect',
                component='network',
                description='Simulated network disconnection'
            )
            self.fault_events.append(fault_event)
            
            # 启动网络中断任务
            network_task = asyncio.create_task(
                self.network_simulator.simulate_network_disconnect(3.0)
            )
            
            # 在网络中断期间尝试通信
            interrupted_responses = []
            interrupted_errors = 0
            
            for i in range(5):
                try:
                    response = await rpc_client.call_rpc(
                        account_id='NETWORK_TEST_001',
                        method='network_interrupt_test',
                        params={'test_id': i},
                        timeout=2.0
                    )
                    interrupted_responses.append(response)
                except Exception as e:
                    interrupted_errors += 1
                    print(f"    ⚠️ 网络中断期间通信失败 (预期): {type(e).__name__}")
                
                await asyncio.sleep(0.5)
            
            # 等待网络恢复
            await network_task
            
            # 记录恢复时间
            recovery_start = time.time()
            
            # 测试网络恢复后的通信
            print("  🔄 测试网络恢复后通信...")
            
            recovery_responses = []
            recovery_attempts = 0
            
            for i in range(5):
                recovery_attempts += 1
                try:
                    response = await rpc_client.call_rpc(
                        account_id='NETWORK_TEST_001',
                        method='network_recovery_test',
                        params={'test_id': i},
                        timeout=3.0
                    )
                    recovery_responses.append(response)
                    
                    if len(recovery_responses) == 1:
                        # 记录首次成功恢复的时间
                        fault_event.recovery_time = time.time() - recovery_start
                        print(f"    ✅ 网络恢复时间: {fault_event.recovery_time:.2f}秒")
                    
                except Exception as e:
                    print(f"    ⚠️ 恢复期间通信失败: {e}")
                
                await asyncio.sleep(0.5)
            
            # 更新实际状态
            self.consistency_validator.update_actual_state('rpc_handler', {
                'status': 'running',
                'request_count': network_manager.request_count,
                'failed_requests': network_manager.failed_requests
            })
            
            # 检查数据一致性
            consistency_check = self.consistency_validator.check_consistency('rpc_handler')
            
            # 停止RPC处理器
            await rpc_handler.stop()
            
            # 分析结果
            recovery_rate = len(recovery_responses) / recovery_attempts
            
            print(f"  📊 网络中断恢复测试结果:")
            print(f"    正常通信成功率: {len(normal_responses)}/3")
            print(f"    中断期间错误数: {interrupted_errors}/5 (预期)")
            print(f"    恢复后成功率: {len(recovery_responses)}/{recovery_attempts} ({recovery_rate*100:.1f}%)")
            print(f"    数据一致性: {'✅ 一致' if consistency_check.is_consistent else '❌ 不一致'}")
            
            # 成功条件：恢复率 >= 80%，数据一致
            return recovery_rate >= 0.8 and consistency_check.is_consistent
            
        except Exception as e:
            print(f"  ❌ 网络中断恢复测试失败: {e}")
            return False
    
    async def test_component_restart_mechanism(self):
        """测试组件重启机制"""
        print("\n🔄 测试组件重启机制...")
        
        # 设置基础设施
        main_client = await self.setup_fault_recovery_infrastructure()
        if not main_client:
            return False
        
        try:
            # 创建可重启的组件
            class RestartableComponent:
                def __init__(self, component_id: str):
                    self.component_id = component_id
                    self.running = False
                    self.restart_count = 0
                    self.operation_count = 0
                    self.last_operation_time = 0
                
                async def start(self):
                    """启动组件"""
                    self.running = True
                    self.last_operation_time = time.time()
                    print(f"    🚀 组件 {self.component_id} 启动")
                
                async def stop(self):
                    """停止组件"""
                    self.running = False
                    print(f"    🛑 组件 {self.component_id} 停止")
                
                async def restart(self):
                    """重启组件"""
                    print(f"    🔄 重启组件 {self.component_id}...")
                    await self.stop()
                    await asyncio.sleep(0.5)  # 重启延迟
                    await self.start()
                    self.restart_count += 1
                    print(f"    ✅ 组件 {self.component_id} 重启完成 (第{self.restart_count}次)")
                
                async def simulate_crash(self):
                    """模拟组件崩溃"""
                    print(f"    💥 组件 {self.component_id} 崩溃")
                    self.running = False
                
                async def perform_operation(self):
                    """执行操作"""
                    if not self.running:
                        raise Exception(f"Component {self.component_id} is not running")
                    
                    self.operation_count += 1
                    self.last_operation_time = time.time()
                    return {
                        'component_id': self.component_id,
                        'operation_count': self.operation_count,
                        'timestamp': self.last_operation_time
                    }
                
                def get_status(self) -> Dict[str, Any]:
                    """获取组件状态"""
                    return {
                        'component_id': self.component_id,
                        'running': self.running,
                        'restart_count': self.restart_count,
                        'operation_count': self.operation_count,
                        'last_operation_time': self.last_operation_time
                    }
            
            # 创建多个可重启组件
            components = [
                RestartableComponent(f'RESTART_COMP_{i:03d}')
                for i in range(3)
            ]
            
            # 启动所有组件
            for comp in components:
                await comp.start()
            
            print(f"  ✅ 创建并启动了 {len(components)} 个可重启组件")
            
            # 设置期望状态
            for comp in components:
                self.consistency_validator.set_expected_state(comp.component_id, {
                    'running': True,
                    'restart_count': 0,
                    'operation_count': 0
                })
            
            # 正常操作阶段
            print("  📊 正常操作阶段...")
            
            for _ in range(5):
                for comp in components:
                    try:
                        result = await comp.perform_operation()
                        print(f"    ✅ {comp.component_id} 操作成功: #{result['operation_count']}")
                    except Exception as e:
                        print(f"    ❌ {comp.component_id} 操作失败: {e}")
                
                await asyncio.sleep(0.2)
            
            # 模拟组件崩溃
            print("  💥 模拟组件崩溃...")
            
            crashed_component = components[1]  # 选择第二个组件崩溃
            
            fault_event = FaultEvent(
                timestamp=time.time(),
                event_type='component_crash',
                component=crashed_component.component_id,
                description='Simulated component crash'
            )
            self.fault_events.append(fault_event)
            
            await crashed_component.simulate_crash()
            
            # 尝试操作崩溃的组件
            print("  🧪 测试崩溃组件操作...")
            
            crash_errors = 0
            for _ in range(3):
                try:
                    await crashed_component.perform_operation()
                except Exception as e:
                    crash_errors += 1
                    print(f"    ⚠️ 崩溃组件操作失败 (预期): {e}")
            
            # 重启崩溃的组件
            print("  🔄 重启崩溃的组件...")
            
            restart_start = time.time()
            await crashed_component.restart()
            fault_event.recovery_time = time.time() - restart_start
            
            # 测试重启后的操作
            print("  🧪 测试重启后操作...")
            
            restart_operations = 0
            for _ in range(5):
                try:
                    result = await crashed_component.perform_operation()
                    restart_operations += 1
                    print(f"    ✅ 重启后操作成功: #{result['operation_count']}")
                except Exception as e:
                    print(f"    ❌ 重启后操作失败: {e}")
                
                await asyncio.sleep(0.2)
            
            # 更新实际状态并检查一致性
            for comp in components:
                self.consistency_validator.update_actual_state(comp.component_id, comp.get_status())
                consistency_check = self.consistency_validator.check_consistency(comp.component_id)
                
                if not consistency_check.is_consistent:
                    print(f"    ⚠️ {comp.component_id} 数据不一致: {consistency_check.inconsistency_details}")
            
            # 停止所有组件
            for comp in components:
                await comp.stop()
            
            # 分析结果
            restart_success = (crashed_component.restart_count > 0 and restart_operations >= 4)
            
            print(f"  📊 组件重启机制测试结果:")
            print(f"    崩溃前错误数: {crash_errors}/3 (预期)")
            print(f"    重启次数: {crashed_component.restart_count}")
            print(f"    重启时间: {fault_event.recovery_time:.2f}秒")
            print(f"    重启后成功操作: {restart_operations}/5")
            print(f"    重启机制: {'✅ 有效' if restart_success else '❌ 无效'}")
            
            return restart_success
            
        except Exception as e:
            print(f"  ❌ 组件重启机制测试失败: {e}")
            return False
    
    async def test_data_consistency_validation(self):
        """测试数据一致性验证"""
        print("\n🔍 测试数据一致性验证...")
        
        try:
            # 创建测试数据
            test_components = ['COMP_A', 'COMP_B', 'COMP_C']
            
            # 设置期望状态
            for i, comp in enumerate(test_components):
                expected_state = {
                    'id': comp,
                    'value': i * 10,
                    'status': 'active',
                    'last_update': time.time()
                }
                self.consistency_validator.set_expected_state(comp, expected_state)
            
            print(f"  ✅ 设置了 {len(test_components)} 个组件的期望状态")
            
            # 模拟正常状态更新
            print("  📊 模拟正常状态更新...")
            
            for i, comp in enumerate(test_components):
                actual_state = {
                    'id': comp,
                    'value': i * 10,  # 与期望一致
                    'status': 'active',
                    'last_update': time.time()
                }
                self.consistency_validator.update_actual_state(comp, actual_state)
            
            # 检查一致性
            consistent_checks = 0
            for comp in test_components:
                check = self.consistency_validator.check_consistency(comp)
                if check.is_consistent:
                    consistent_checks += 1
                    print(f"    ✅ {comp} 数据一致")
                else:
                    print(f"    ❌ {comp} 数据不一致: {check.inconsistency_details}")
            
            # 模拟数据不一致情况
            print("  💥 模拟数据不一致...")
            
            # 故意创建不一致的状态
            inconsistent_state = {
                'id': 'COMP_B',
                'value': 999,  # 与期望不一致
                'status': 'error',  # 与期望不一致
                'last_update': time.time(),
                'extra_field': 'unexpected'  # 额外字段
            }
            self.consistency_validator.update_actual_state('COMP_B', inconsistent_state)
            
            # 检查不一致性
            inconsistent_check = self.consistency_validator.check_consistency('COMP_B')
            
            print(f"    📊 不一致检查结果:")
            print(f"      一致性: {'✅ 一致' if inconsistent_check.is_consistent else '❌ 不一致'}")
            if not inconsistent_check.is_consistent:
                for detail in inconsistent_check.inconsistency_details:
                    print(f"      - {detail}")
            
            # 获取一致性总结
            summary = self.consistency_validator.get_consistency_summary()
            
            print(f"  📊 数据一致性验证总结:")
            print(f"    总检查数: {summary['total_checks']}")
            print(f"    一致检查数: {summary['consistent_checks']}")
            print(f"    不一致检查数: {summary['inconsistent_checks']}")
            print(f"    一致性率: {summary['consistency_rate']:.1f}%")
            
            # 成功条件：能够正确检测一致性和不一致性
            detection_success = (
                consistent_checks == len(test_components) - 1 and  # COMP_A和COMP_C应该一致
                not inconsistent_check.is_consistent  # COMP_B应该不一致
            )
            
            return detection_success
            
        except Exception as e:
            print(f"  ❌ 数据一致性验证测试失败: {e}")
            return False
    
    async def run_all_tests(self):
        """运行所有增强故障恢复测试"""
        print("🚀 开始增强故障恢复测试...")
        
        test_results = {}
        
        # 运行各项测试
        test_results['network_interruption_recovery'] = await self.test_network_interruption_recovery()
        test_results['component_restart_mechanism'] = await self.test_component_restart_mechanism()
        test_results['data_consistency_validation'] = await self.test_data_consistency_validation()
        
        # 统计结果
        passed = sum(1 for result in test_results.values() if result)
        total = len(test_results)
        success_rate = (passed / total) * 100
        
        print(f"\n📊 增强故障恢复测试结果:")
        print(f"总测试数: {total}")
        print(f"通过: {passed}")
        print(f"失败: {total - passed}")
        print(f"成功率: {success_rate:.1f}%")
        
        print(f"\n📋 详细结果:")
        for test_name, result in test_results.items():
            status = "✅ 通过" if result else "❌ 失败"
            print(f"  {test_name}: {status}")
        
        # 输出故障事件总结
        if self.fault_events:
            print(f"\n🚨 故障事件总结:")
            for event in self.fault_events:
                print(f"  {event.event_type}: {event.component}")
                print(f"    描述: {event.description}")
                if event.recovery_time > 0:
                    print(f"    恢复时间: {event.recovery_time:.2f}秒")
                print(f"    数据丢失: {'是' if event.data_loss else '否'}")
        
        return success_rate >= 66  # 66%以上算成功


async def main():
    """主函数"""
    if not REAL_COMPONENTS_AVAILABLE:
        print("❌ 增强故障恢复测试组件不可用，无法运行测试")
        return False
    
    test_suite = EnhancedFaultRecoveryTest()
    
    try:
        success = await test_suite.run_all_tests()
        
        if success:
            print("\n🎉 增强故障恢复测试成功!")
        else:
            print("\n⚠️ 增强故障恢复测试部分失败")
        
        return success
        
    finally:
        test_suite.cleanup()


if __name__ == '__main__':
    result = asyncio.run(main())
    sys.exit(0 if result else 1)
