"""
事件处理器

负责处理MT5原始事件、系统事件，并转换为标准信号。
"""

import asyncio
from dataclasses import dataclass
from datetime import datetime
from decimal import Decimal
from typing import Dict, List, Optional, Callable, Any
from enum import Enum
import logging
import Meta<PERSON>rader5 as mt5

from ..models import Signal, SignalType, SignalPriority, Trade, TradeType
from ...communication.messaging import NatsClient
from ...storage.cache import CacheManager
from ...utils.decorators import async_timed


logger = logging.getLogger(__name__)


class EventType(Enum):
    """事件类型"""
    # MT5事件
    MT5_CONNECTED = "mt5_connected"
    MT5_DISCONNECTED = "mt5_disconnected"
    MT5_TRADE_TRANSACTION = "mt5_trade_transaction"
    MT5_BOOK_EVENT = "mt5_book_event"
    MT5_TICK_EVENT = "mt5_tick_event"
    
    # 交易事件
    ORDER_PLACED = "order_placed"
    ORDER_MODIFIED = "order_modified"
    ORDER_CANCELLED = "order_cancelled"
    POSITION_OPENED = "position_opened"
    POSITION_MODIFIED = "position_modified"
    POSITION_CLOSED = "position_closed"
    
    # 账户事件
    BALANCE_CHANGED = "balance_changed"
    MARGIN_LEVEL_CHANGED = "margin_level_changed"
    EQUITY_CHANGED = "equity_changed"
    
    # 系统事件
    SYSTEM_STARTUP = "system_startup"
    SYSTEM_SHUTDOWN = "system_shutdown"
    HEARTBEAT = "heartbeat"
    ERROR = "error"


@dataclass
class Event:
    """基础事件类"""
    id: str
    type: EventType
    timestamp: datetime
    source: str  # 事件源（terminal_id, system等）
    data: Dict[str, Any]
    priority: int = 1


class EventProcessor:
    """事件处理器 - 处理系统和MT5原始事件"""
    
    def __init__(self, 
                 cache_manager: CacheManager,
                 nats_client: NatsClient):
        
        self.cache = cache_manager
        self.nats = nats_client
        
        # 事件队列
        self.event_queue: asyncio.Queue = asyncio.Queue(maxsize=10000)
        
        # 事件处理器注册
        self.handlers: Dict[EventType, List[Callable]] = {}
        self._register_default_handlers()
        
        # MT5事件监听器
        self.mt5_listeners: Dict[str, bool] = {}  # terminal_id -> active
        
        # 事件统计
        self.event_stats = {
            'total_received': 0,
            'total_processed': 0,
            'by_type': {},
            'errors': 0
        }
        
        # 工作器
        self.workers: List[asyncio.Task] = []
        self.worker_count = 3
    
    async def start(self):
        """启动事件处理器"""
        # 启动事件处理工作器
        for i in range(self.worker_count):
            worker = asyncio.create_task(self._event_worker(i))
            self.workers.append(worker)
        
        # 订阅系统事件
        await self._subscribe_system_events()
        
        logger.info(f"Event processor started with {self.worker_count} workers")
    
    async def stop(self):
        """停止事件处理器"""
        # 停止MT5监听
        for terminal_id in list(self.mt5_listeners.keys()):
            await self.stop_mt5_listener(terminal_id)
        
        # 取消工作器
        for worker in self.workers:
            worker.cancel()
        
        await asyncio.gather(*self.workers, return_exceptions=True)
        
        logger.info("Event processor stopped")
    
    def register_handler(self, event_type: EventType, handler: Callable):
        """注册事件处理器"""
        if event_type not in self.handlers:
            self.handlers[event_type] = []
        
        self.handlers[event_type].append(handler)
        logger.info(f"Registered handler for {event_type.value}")
    
    async def emit_event(self, event: Event):
        """发出事件"""
        try:
            # 更新统计
            self.event_stats['total_received'] += 1
            self.event_stats['by_type'][event.type.value] = \
                self.event_stats['by_type'].get(event.type.value, 0) + 1
            
            # 放入队列
            await self.event_queue.put(event)
            
        except asyncio.QueueFull:
            logger.error(f"Event queue full, dropping event {event.id}")
    
    async def start_mt5_listener(self, terminal_id: str, terminal_path: str):
        """启动MT5事件监听"""
        if terminal_id in self.mt5_listeners:
            logger.warning(f"MT5 listener already active for {terminal_id}")
            return
        
        self.mt5_listeners[terminal_id] = True
        
        # 启动监听任务
        listener_task = asyncio.create_task(
            self._mt5_event_listener(terminal_id, terminal_path)
        )
        self.workers.append(listener_task)
        
        logger.info(f"Started MT5 listener for terminal {terminal_id}")
    
    async def stop_mt5_listener(self, terminal_id: str):
        """停止MT5事件监听"""
        if terminal_id in self.mt5_listeners:
            self.mt5_listeners[terminal_id] = False
            logger.info(f"Stopped MT5 listener for terminal {terminal_id}")
    
    def _register_default_handlers(self):
        """注册默认事件处理器"""
        # MT5事件处理器
        self.register_handler(EventType.MT5_TRADE_TRANSACTION, self._handle_mt5_trade)
        self.register_handler(EventType.MT5_CONNECTED, self._handle_mt5_connection)
        self.register_handler(EventType.MT5_DISCONNECTED, self._handle_mt5_disconnection)
        
        # 交易事件处理器
        self.register_handler(EventType.POSITION_OPENED, self._handle_position_opened)
        self.register_handler(EventType.POSITION_CLOSED, self._handle_position_closed)
        self.register_handler(EventType.ORDER_PLACED, self._handle_order_placed)
        
        # 账户事件处理器
        self.register_handler(EventType.BALANCE_CHANGED, self._handle_balance_changed)
        self.register_handler(EventType.MARGIN_LEVEL_CHANGED, self._handle_margin_level_changed)
        
        # 系统事件处理器
        self.register_handler(EventType.HEARTBEAT, self._handle_heartbeat)
        self.register_handler(EventType.ERROR, self._handle_error)
    
    async def _event_worker(self, worker_id: int):
        """事件处理工作器"""
        logger.info(f"Event worker {worker_id} started")
        
        while True:
            try:
                # 获取事件
                event = await self.event_queue.get()
                
                # 处理事件
                await self._process_event(event)
                
                # 更新统计
                self.event_stats['total_processed'] += 1
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Event worker {worker_id} error: {e}")
                self.event_stats['errors'] += 1
                await asyncio.sleep(1)
    
    @async_timed
    async def _process_event(self, event: Event):
        """处理单个事件"""
        # 获取该类型的所有处理器
        handlers = self.handlers.get(event.type, [])
        
        if not handlers:
            logger.warning(f"No handlers for event type {event.type.value}")
            return
        
        # 执行所有处理器
        for handler in handlers:
            try:
                if asyncio.iscoroutinefunction(handler):
                    await handler(event)
                else:
                    handler(event)
                    
            except Exception as e:
                logger.error(f"Handler error for event {event.id}: {e}")
    
    async def _mt5_event_listener(self, terminal_id: str, terminal_path: str):
        """MT5事件监听器"""
        logger.info(f"MT5 event listener started for {terminal_id}")
        
        while self.mt5_listeners.get(terminal_id, False):
            try:
                # 这里应该实现实际的MT5事件监听
                # 示例：轮询交易历史变化
                
                # 检查新交易
                await self._check_new_trades(terminal_id)
                
                # 检查账户变化
                await self._check_account_changes(terminal_id)
                
                # 检查连接状态
                await self._check_connection_status(terminal_id)
                
                # 休眠以避免过度轮询
                await asyncio.sleep(0.1)
                
            except Exception as e:
                logger.error(f"MT5 listener error for {terminal_id}: {e}")
                await asyncio.sleep(1)
        
        logger.info(f"MT5 event listener stopped for {terminal_id}")
    
    async def _check_new_trades(self, terminal_id: str):
        """检查新交易（示例）"""
        # 获取上次检查时间
        cache_key = f"last_trade_check:{terminal_id}"
        last_check = await self.cache.get(cache_key)
        
        # 这里应该调用MT5 API获取新交易
        # 示例代码
        
        # 更新检查时间
        await self.cache.set(cache_key, datetime.utcnow().isoformat(), ttl=3600)
    
    async def _check_account_changes(self, terminal_id: str):
        """检查账户变化"""
        # 获取缓存的账户信息
        cache_key = f"account_info:{terminal_id}"
        cached_info = await self.cache.get(cache_key)
        
        # 获取当前账户信息（示例）
        current_info = {
            'balance': 10000,
            'equity': 10500,
            'margin': 500,
            'margin_level': 2100
        }
        
        if cached_info:
            # 比较变化
            if current_info['balance'] != cached_info.get('balance'):
                await self.emit_event(Event(
                    id=f"balance_{terminal_id}_{datetime.utcnow().timestamp()}",
                    type=EventType.BALANCE_CHANGED,
                    timestamp=datetime.utcnow(),
                    source=terminal_id,
                    data={
                        'old_balance': cached_info.get('balance'),
                        'new_balance': current_info['balance']
                    }
                ))
        
        # 更新缓存
        await self.cache.set(cache_key, current_info, ttl=300)
    
    async def _check_connection_status(self, terminal_id: str):
        """检查连接状态"""
        # 这里应该检查MT5连接状态
        pass
    
    # 事件处理器实现
    async def _handle_mt5_trade(self, event: Event):
        """处理MT5交易事件"""
        # 转换为标准信号
        trade_data = event.data
        
        signal_type = SignalType.POSITION_OPENED
        if trade_data.get('type') == 'DEAL_TYPE_SELL':
            signal_type = SignalType.POSITION_CLOSED
        
        signal = Signal(
            type=signal_type,
            source_account_id=trade_data.get('account_id'),
            symbol=trade_data.get('symbol'),
            trade_type='BUY' if trade_data.get('type') == 'DEAL_TYPE_BUY' else 'SELL',
            volume=Decimal(str(trade_data.get('volume', 0))),
            price=Decimal(str(trade_data.get('price', 0))),
            reference_ticket=trade_data.get('position_id'),
            data=trade_data
        )
        
        # 发布信号
        await self.nats.publish(
            f"signals.trade.{signal_type.value}",
            signal.to_nats_message()
        )
    
    async def _handle_mt5_connection(self, event: Event):
        """处理MT5连接事件"""
        terminal_id = event.source
        
        # 更新终端状态
        await self.cache.set(
            f"terminal_status:{terminal_id}",
            "connected",
            ttl=300
        )
        
        logger.info(f"Terminal {terminal_id} connected")
    
    async def _handle_mt5_disconnection(self, event: Event):
        """处理MT5断开连接事件"""
        terminal_id = event.source
        
        # 更新终端状态
        await self.cache.set(
            f"terminal_status:{terminal_id}",
            "disconnected",
            ttl=300
        )
        
        # 发送告警
        logger.warning(f"Terminal {terminal_id} disconnected")
    
    async def _handle_position_opened(self, event: Event):
        """处理开仓事件"""
        # 生成开仓信号
        signal = Signal(
            type=SignalType.POSITION_OPENED,
            source_account_id=event.data.get('account_id'),
            symbol=event.data.get('symbol'),
            trade_type=event.data.get('type'),
            volume=Decimal(str(event.data.get('volume', 0))),
            price=Decimal(str(event.data.get('price', 0))),
            sl=Decimal(str(event.data.get('sl', 0))) if event.data.get('sl') else None,
            tp=Decimal(str(event.data.get('tp', 0))) if event.data.get('tp') else None,
            reference_ticket=event.data.get('ticket')
        )
        
        # 发布信号
        await self.nats.publish(
            "signals.trade.position_opened",
            signal.to_nats_message()
        )
    
    async def _handle_position_closed(self, event: Event):
        """处理平仓事件"""
        # 生成平仓信号
        signal = Signal(
            type=SignalType.POSITION_CLOSED,
            source_account_id=event.data.get('account_id'),
            reference_ticket=event.data.get('ticket'),
            data={
                'profit': event.data.get('profit'),
                'close_price': event.data.get('close_price'),
                'close_time': event.data.get('close_time')
            }
        )
        
        # 发布信号
        await self.nats.publish(
            "signals.trade.position_closed",
            signal.to_nats_message()
        )
    
    async def _handle_order_placed(self, event: Event):
        """处理下单事件"""
        # 生成下单信号
        signal = Signal(
            type=SignalType.PLACE_ORDER,
            source_account_id=event.data.get('account_id'),
            symbol=event.data.get('symbol'),
            trade_type=event.data.get('type'),
            volume=Decimal(str(event.data.get('volume', 0))),
            price=Decimal(str(event.data.get('price', 0))),
            data=event.data
        )
        
        # 发布信号
        await self.nats.publish(
            "signals.order.place",
            signal.to_nats_message()
        )
    
    async def _handle_balance_changed(self, event: Event):
        """处理余额变化事件"""
        # 生成余额更新信号