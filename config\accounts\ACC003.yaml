# ACC003 - 从账户配置（跨主机部署）
# 只包含账户特定的配置，主机分配和跟单关系在其他配置文件中定义

# ============================================================================
# 账户基础信息
# ============================================================================
account:
  id: "ACC003"
  name: "从账户3 - 跨主机部署"
  enabled: false                    # 当前禁用
  description: "跨主机部署的交易账户，支持高延迟环境"
  
  # 账户能力定义（角色由关系管理系统动态分配）
  capabilities:
    can_generate_signals: true      # 能否生成交易信号
    can_follow_signals: true        # 能否跟随交易信号
    signal_priority: "low"          # 信号优先级（跨主机延迟高）
    max_followers: 5                # 最大跟随者数量
    max_follow_sources: 2           # 最大跟随信号源数量
    latency_tolerance: "high"       # 延迟容忍度

# ============================================================================
# MT5连接配置
# ============================================================================
mt5:
  connection:
    login: ********
    password: ${MT5_ACC003_PASSWORD}  # 从环境变量读取
    server: "TradeMaxGlobal-Demo"
    terminal_path: "D:/MetaTrader5/v3-mt5/terminal64.exe"
    
  # 连接参数（跨主机优化）
  connection_params:
    timeout: 15                     # 跨主机增加超时
    retry_attempts: 5               # 增加重试次数
    retry_delay: 10                 # 增加重试延迟
    auto_restart: true
    max_restarts: 10                # 增加重启次数
    
  # 连接池配置
  connection_pool:
    enabled: true
    max_connections: 2              # 跨主机减少连接数
    idle_timeout: 600               # 增加空闲超时

# ============================================================================
# 交易配置（跨主机优化）
# ============================================================================
trading:
  # 基础交易参数
  parameters:
    magic_number: 30003             # 独特的Magic Number
    slippage: 15                    # 跨主机增加滑点容忍
    max_deviation: 30               # 增加偏差容忍
    
  # 手数限制
  volume_limits:
    max_volume: 100.0                 # 比主账户小
    min_volume: 0.01
    default_volume: 1.0
    
  # 允许的交易品种（限制主要品种）
  allowed_symbols:
    mode: "all"                     # all/whitelist/blacklist
    symbols: []                     # 空数组表示所有品种
    
  # 交易时间限制
  trading_hours:
    enabled: true                   # 限制交易时间
    start_time: "08:00"
    end_time: "18:00"
    timezone: "UTC"
    exclude_weekends: true
    
  # 交易模式（从账户特定）
  trading_mode:
    manual_override: false          # 从账户禁止手动交易
    auto_trading: true              # 只接受跟单信号
    copy_signal_generation: false   # 从账户不生成信号
    copy_signal_execution: true     # 执行跟单信号

# ============================================================================
# 风险管理配置（保守策略）
# ============================================================================
risk_management:
  # 基础风险限制（保守设置）
  limits:
    max_daily_loss: 3000.0          # 中等风险水平
    max_positions: 8                # 适中持仓数
    max_lot_size: 8.0               # 适中手数
    max_daily_trades: 80            # 适中交易次数
    max_drawdown: 0.12              # 12%，较严格
    
  # 止损止盈设置
  stop_loss:
    default_enabled: false
    default_pips: 50
    trailing_stop: false
    
  take_profit:
    default_enabled: false
    default_pips: 100
    
  # 紧急停止
  emergency_stop:
    enabled: true
    loss_threshold: 5000.0
    drawdown_threshold: 0.20        # 20%
    
  # 风险检查
  risk_checks:
    pre_trade: true
    real_time: true
    post_trade: true
    copy_trade_validation: true     # 从账户特有的验证

# ============================================================================
# 跟单执行配置（跨主机优化）
# ============================================================================
copy_execution:
  # 执行参数（跨主机调整）
  execution_settings:
    max_execution_delay_ms: 8000    # 跨主机增加延迟容忍
    retry_on_failure: true
    max_retries: 5                  # 增加重试次数
    retry_delay_ms: 1500            # 增加重试延迟
    
  # 信号过滤（更严格）
  signal_filtering:
    enabled: true
    min_confidence: 0.85            # 更高的信号置信度要求
    max_signal_age_ms: 15000        # 跨主机增加信号年龄容忍
    
  # 执行验证
  execution_validation:
    verify_account_balance: true
    verify_margin_level: true
    verify_symbol_availability: true
    verify_network_latency: true    # 跨主机特有验证
    
  # 网络优化
  network_optimization:
    compression_enabled: true
    batch_processing: true
    priority_queue: true
    

# ============================================================================
# 通知配置
# ============================================================================
notifications:
  # Telegram通知
  telegram:
    enabled: true
    chat_id: "**********"
    
  # 通知级别
  levels:
    trade_opened: true
    trade_closed: true
    copy_executed: true
    copy_failed: true
    network_issues: true            # 跨主机特有
    emergency_stop: true
    risk_alert: true
    system_error: true
    daily_summary: true

# ============================================================================
# 数据存储配置
# ============================================================================
data_storage:
  # 本地数据路径
  local_paths:
    data_directory: "./data/accounts/ACC003"
    logs_directory: "./logs/accounts/ACC003"
    backup_directory: "./backups/accounts/ACC003"
    
  # 数据保留
  retention:
    trade_history_days: 90
    log_files_days: 30
    backup_files_days: 14           # 跨主机增加备份保留
    copy_signals_days: 30
    network_logs_days: 7            # 网络日志保留
    
  # 缓存配置（跨主机优化）
  cache:
    enabled: true
    ttl_seconds: 600                # 10分钟，增加缓存时间
    max_size_mb: 80

# ============================================================================
# 高级功能配置
# ============================================================================
advanced:
  # EA支持
  expert_advisors:
    enabled: false
    allowed_eas: []
    
  # 脚本支持
  scripts:
    enabled: false
    allowed_scripts: []
    
  # 自定义指标
  custom_indicators:
    enabled: false
    
  # 跨主机特有功能
  cross_host_features:
    enabled: true
    network_optimization: true
    latency_compensation: true
    connection_pooling: true

# ============================================================================
# 环境特定配置
# ============================================================================
environment_overrides:
  development:
    account:
      enabled: false                # 开发环境默认禁用
    trading:
      volume_limits:
        max_volume: 0.2
    risk_management:
      limits:
        max_daily_loss: 20.0
        
  testing:
    account:
      enabled: true                 # 测试环境启用
    trading:
      volume_limits:
        max_volume: 0.1
    risk_management:
      limits:
        max_daily_loss: 10.0
        
  production:
    # 生产环境使用默认配置
    pass

# ============================================================================
# 元数据
# ============================================================================
metadata:
  created_at: "2024-01-01T00:00:00Z"
  updated_at: "2024-01-01T00:00:00Z"
  version: "2.0.0"
  config_schema_version: "1.0"
  tags: ["slave", "cross-host", "conservative", "disabled"]