#!/usr/bin/env python3
"""
MT5协调器 - 硬迁移兼容层
为了保持向后兼容性，此文件将旧的DistributedMT5Coordinator映射到新的UnifiedMT5Coordinator
硬迁移：零向后兼容，强制统一，SSOT

注意：此文件仅为迁移过渡期使用，最终将完全移除
"""
import asyncio
import logging
from typing import Dict, Any

from .unified_coordinator import UnifiedMT5Coordinator

logger = logging.getLogger(__name__)

class DistributedMT5Coordinator:
    """
    分布式MT5协调器 - 迁移兼容层
    
    警告：此类已被弃用，仅用于向后兼容
    建议直接使用 UnifiedMT5Coordinator
    """
    
    def __init__(self, host_id: str, config_path: str):
        logger.warning("🚨 使用已弃用的DistributedMT5Coordinator，建议迁移到UnifiedMT5Coordinator")
        
        # 内部使用新的统一协调器
        self._unified_coordinator = UnifiedMT5Coordinator(host_id, config_path)
        
        # 保持旧接口属性的映射
        self.host_id = host_id
        self.config_path = config_path
        self.running = False
        
        logger.info(f"🔄 兼容层: DistributedMT5Coordinator -> UnifiedMT5Coordinator (主机: {host_id})")
    
    async def start(self):
        """启动协调器（兼容层）"""
        logger.info("🔄 通过兼容层启动统一协调器")
        
        success = await self._unified_coordinator.start()
        self.running = self._unified_coordinator.running
        
        if success:
            logger.info("✅ 协调器启动成功（通过兼容层）")
        else:
            logger.error("❌ 协调器启动失败（通过兼容层）")
        
        return success
    
    async def stop(self):
        """停止协调器（兼容层）"""
        logger.info("🔄 通过兼容层停止统一协调器")
        
        await self._unified_coordinator.stop()
        self.running = self._unified_coordinator.running
        
        logger.info("✅ 协调器已停止（通过兼容层）")
    
    async def get_system_status(self) -> Dict[str, Any]:
        """获取系统状态（兼容层）"""
        status = await self._unified_coordinator.get_system_status()
        
        # 添加兼容层标识
        status['compatibility_layer'] = {
            'active': True,
            'deprecated_class': 'DistributedMT5Coordinator',
            'target_class': 'UnifiedMT5Coordinator',
            'migration_status': 'compatibility_mode'
        }
        
        return status
    
    # 其他可能被外部代码使用的属性/方法映射
    @property
    def local_accounts(self):
        """本地账户映射"""
        return getattr(self._unified_coordinator, 'local_accounts', {})
    
    @property 
    def initialization_successful(self):
        """初始化成功状态映射"""
        return getattr(self._unified_coordinator, 'initialization_successful', False)
    
    @property
    def degraded_mode(self):
        """降级模式状态映射"""
        return getattr(self._unified_coordinator, 'degraded_mode', False)

# 向后兼容的导入别名
MT5Coordinator = DistributedMT5Coordinator

# 迁移提示函数
def create_unified_coordinator(host_id: str, config_path: str) -> UnifiedMT5Coordinator:
    """
    创建统一协调器的推荐方法
    
    使用此函数代替直接实例化DistributedMT5Coordinator
    """
    logger.info("✨ 创建统一协调器（推荐方式）")
    return UnifiedMT5Coordinator(host_id, config_path)

def get_migration_guide() -> str:
    """获取迁移指南"""
    return """
    🔄 MT5协调器迁移指南
    
    旧代码:
        from src.core.mt5_coordinator import DistributedMT5Coordinator
        coordinator = DistributedMT5Coordinator(host_id, config_path)
    
    新代码:
        from src.core.unified_coordinator import UnifiedMT5Coordinator
        coordinator = UnifiedMT5Coordinator(host_id, config_path)
    
    或使用便利函数:
        from src.core.mt5_coordinator import create_unified_coordinator
        coordinator = create_unified_coordinator(host_id, config_path)
    
    主要改进:
    ✅ 依赖注入容器管理组件生命周期
    ✅ 异步初始化模式，允许部分失败
    ✅ 健康检查和服务发现机制
    ✅ 支持组件热插拔和动态重启
    ✅ 零拷贝消息传递和直接信号路由
    ✅ 统一内存池管理
    """

if __name__ == "__main__":
    print(get_migration_guide())