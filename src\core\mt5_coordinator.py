# src/core/mt5_coordinator_enhanced.py
"""
分布式MT5协调器 - 第4层系统协调器 
完全符合4层架构，支持动态主从分配和pairing配置
"""
import asyncio
import os
import sys
import yaml
import multiprocessing as mp
import logging
from typing import Dict, List, Optional, Any
from pathlib import Path
import time
from dotenv import load_dotenv

load_dotenv()

from .mt5_process_manager import MT5ProcessManager
from .separated_process_runners import run_account_monitor_process, run_account_executor_process
from .mt5_configuration import AccountConfigManager
from .signal_router import SignalRouter
from .message_routing_coordinator import MessageRoutingCoordinator, LocalMessageRouter
from .mt5_rpc_client import MT5RPCClient
from .mt5_request_handler import MT5RequestHandler
from ..messaging.nats_manager import NATSManager
from ..messaging.hybrid_message_router import HybridMessageRouter

logger = logging.getLogger(__name__)


class DistributedMT5Coordinator:
    """
    分布式MT5协调器 - 第4层系统协调器
    """
    
    def __init__(self, host_id: str, config_path: str):
        self.host_id = host_id
        self.config_path = config_path
        self.config = self._load_config()
        
        self.mt5_process_manager = MT5ProcessManager(config_path)
        
        self.monitor_processes: Dict[str, mp.Process] = {}
        self.executor_processes: Dict[str, mp.Process] = {}
        self.monitor_queues: Dict[str, mp.Queue] = {}
        self.executor_queues: Dict[str, mp.Queue] = {}
        
        config_root = str(Path(config_path).parent.parent) if config_path else "config"
        self.account_manager = AccountConfigManager(config_dir=config_root)

        # 新架构组件
        self.optimized_nats_manager: Optional[NATSManager] = None
        self.hybrid_message_router: Optional[HybridMessageRouter] = None
        
        # 本地模式组件
        self.message_routing_coordinator = None
        
        # 核心组件
        self.jetstream = None
        self.state_manager = None
        self.running = False
        self.local_accounts: Dict[str, Dict] = {}
        
        # 架构模式标识
        self.architecture_mode = 'optimized'  # 'optimized' 或 'legacy'
        
        logger.info(f"DistributedMT5Coordinator初始化完成，主机: {host_id}")
    
    def _load_config(self) -> Dict:
        """Load configuration file"""
        try:
            from .config_manager import get_config_manager
            config_mgr = get_config_manager()
            all_config = config_mgr.get_all()

            nats_config = config_mgr.get_nats_config()
            if nats_config:
                all_config['nats'] = nats_config

            return all_config
        except Exception as e:
            logger.error(f"Failed to load configuration file: {e}")
            return {}
    
    async def start(self):
        """启动协调器"""
        self.running = True
        logger.info(f"启动分布式MT5协调器 - 主机: {self.host_id}")
        
        try:
            await self._init_distributed_components()
            await self._discover_local_accounts()
            await self._start_account_processes_enhanced()

            asyncio.create_task(self._monitor_processes())
            asyncio.create_task(self._heartbeat_task())
            
            logger.info("分布式MT5协调器启动完成")
            
        except Exception as e:
            logger.error(f"协调器启动失败: {e}")
            raise
    
    async def _init_distributed_components(self):
        """初始化分布式组件 - 优化版"""
        try:
            # 优先尝试初始化优化架构
            if self.architecture_mode == 'optimized' and 'nats' in self.config:
                success = await self._init_optimized_architecture()
                if success:
                    logger.info("优化架构初始化成功")
                    return
                else:
                    logger.warning("优化架构初始化失败，回退到传统架构")
                    self.architecture_mode = 'legacy'
            
            # 传统架构初始化（向后兼容）
            await self._init_legacy_architecture()
            
        except Exception as e:
            logger.error(f"分布式组件初始化失败: {e}")
            # 最后降级到本地模式
            await self._init_local_mode()
    
    async def _init_optimized_architecture(self) -> bool:
        """初始化优化架构"""
        try:
            logger.info("初始化优化NATS架构")
            
            # 1. 创建增强的JetStream客户端
            from ..messaging.jetstream_client import JetStreamClient
            
            self.jetstream = JetStreamClient(self.config['nats'])
            
            # 2. 初始化分层流架构 (使用新的initialize方法)
            success = await self.jetstream.initialize(host_id=self.host_id, use_layered_streams=True)
            if not success:
                raise RuntimeError("JetStream分层架构初始化失败")
            logger.info("JetStream分层架构初始化成功")
            
            # 3. 设置优先级消费者
            await self.jetstream.setup_priority_consumers()
            
            # 4. 创建优化NATS管理器
            self.optimized_nats_manager = NATSManager(
                host_id=self.host_id,
                jetstream_client=self.jetstream,
                config=self.config
            )
            
            # 5. 初始化优化架构
            success = await self.optimized_nats_manager.initialize_optimized_architecture()
            if not success:
                raise RuntimeError("优化NATS管理器初始化失败")
            
            # 6. 创建混合消息路由器
            self.hybrid_message_router = HybridMessageRouter(self.optimized_nats_manager)
            await self.hybrid_message_router.start()
            logger.info("混合消息路由器启动成功")
            
            # 7. 注册账户信息到路由表
            await self._register_accounts_to_optimized_manager()
            
            # 8. 注册消息处理器
            await self._register_optimized_handlers()
            
            # 9. 初始化RPC组件
            await self._init_rpc_components()
            
            logger.info("优化架构初始化完成")
            return True
            
        except Exception as e:
            logger.error(f"优化架构初始化失败: {e}")
            # 清理资源
            if self.hybrid_message_router:
                await self.hybrid_message_router.stop()
                self.hybrid_message_router = None
            if self.optimized_nats_manager:
                await self.optimized_nats_manager.shutdown()
                self.optimized_nats_manager = None
            if self.jetstream:
                await self.jetstream.disconnect()
                self.jetstream = None
            return False
    
 
    
    async def _init_local_mode(self):
        """初始化本地模式 """
        logger.info("初始化本地模式")
        
        try:
            if 'nats' in self.config:
                from ..messaging.jetstream_client import JetStreamClient
                
                self.jetstream = JetStreamClient(self.config['nats'])
                await self.jetstream.connect()
                logger.info("本地模式JetStream连接成功")
                
                await self._init_rpc_components()
            else:
                logger.warning("没有NATS配置，无法创建RPC组件")

            # 创建基本的消息路由器
            self.message_routing_coordinator = LocalMessageRouter()
            await self.message_routing_coordinator.start_routing()
            logger.info("本地消息路由器启动成功")
            
        except Exception as e:
            logger.error(f"本地模式初始化失败: {e}")
    
    async def _init_rpc_components(self):
        """初始化RPC组件（公共逻辑）"""
        try:
            if self.jetstream:
                self.rpc_handler = MT5RequestHandler(self.jetstream)
                if await self.rpc_handler.start():
                    logger.info("RPC Handler启动成功")
                else:
                    logger.error("RPC Handler启动失败")

                self.rpc_client = MT5RPCClient(self.jetstream)
                logger.info("RPC Client创建成功")
        except Exception as e:
            logger.error(f"RPC组件初始化失败: {e}")
    
    async def _register_accounts_to_optimized_manager(self):
        """注册账户到优化管理器"""
        if not self.optimized_nats_manager:
            return
        
        try:
            # 注册本地账户
            for account_id in self.local_accounts:
                self.optimized_nats_manager.register_account(account_id, self.host_id)
            
            logger.info(f"已注册 {len(self.local_accounts)} 个本地账户到优化管理器")
        except Exception as e:
            logger.error(f"注册账户失败: {e}")
    
    async def _register_optimized_handlers(self):
        """注册优化架构的消息处理器"""
        if not self.optimized_nats_manager:
            return
        
        try:
            from ..messaging.priority_queue import MessagePriority
            
            # 注册信号处理器
            for priority in MessagePriority:
                self.optimized_nats_manager.register_signal_handler(
                    priority, 
                    self._create_optimized_signal_handler(priority)
                )
            
            # 注册RPC处理器
            self.optimized_nats_manager.register_rpc_handler(
                'get_account_info', self._handle_get_account_info
            )
            self.optimized_nats_manager.register_rpc_handler(
                'send_order', self._handle_send_order
            )
            self.optimized_nats_manager.register_rpc_handler(
                'get_positions', self._handle_get_positions
            )
            
            logger.info("优化架构消息处理器注册完成")
            
        except Exception as e:
            logger.error(f"注册优化处理器失败: {e}")
    
    def _create_optimized_signal_handler(self, priority):
        """创建优化信号处理器"""
        async def handler(signal_data: Dict[str, Any]):
            try:
                account_id = signal_data.get('target_account')
                signal_type = signal_data.get('signal_type', 'trade_signal')
                
                logger.debug(f"处理{priority.name}优先级信号: {signal_type} -> {account_id}")
                
                # 根据信号类型分发到相应的执行器
                if account_id in self.executor_queues:
                    message = {
                        'type': 'trade_signal',
                        'priority': priority.name,
                        'data': signal_data,
                        'timestamp': time.time()
                    }
                    self.executor_queues[account_id].put_nowait(message)
                    logger.debug(f"信号已发送到执行器: {account_id}")
                else:
                    logger.warning(f"执行器队列不存在: {account_id}")
                    
            except Exception as e:
                logger.error(f"优化信号处理失败: {e}")
        
        return handler
    
    async def _handle_get_account_info(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """处理获取账户信息RPC"""
        try:
            account_id = params.get('account_id')
            if account_id and self.rpc_client:
                result = await self.rpc_client.get_account_info(account_id)
                return result
            return {'error': 'Invalid account_id or RPC client not available'}
        except Exception as e:
            return {'error': str(e)}
    
    async def _handle_send_order(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """处理发送订单RPC"""
        try:
            account_id = params.get('account_id')
            if account_id and self.rpc_client:
                result = await self.rpc_client.send_order(account_id, params)
                return result
            return {'error': 'Invalid account_id or RPC client not available'}
        except Exception as e:
            return {'error': str(e)}
    
    async def _handle_get_positions(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """处理获取持仓RPC"""
        try:
            account_id = params.get('account_id')
            if account_id and self.rpc_client:
                result = await self.rpc_client.get_positions(account_id)
                return result
            return {'error': 'Invalid account_id or RPC client not available'}
        except Exception as e:
            return {'error': str(e)}
    
    async def _discover_local_accounts(self):
        """发现本主机负责的账户"""
        try:
            all_accounts = self.account_manager.load_all_accounts()

            self.local_accounts = {}

            for account_id, account_config in all_accounts.items():
                account_host = account_config.host_id

                if account_host == self.host_id or not account_host:
                    config_dict = {
                        'login': account_config.login,
                        'password': account_config.password,  # 添加密码字段
                        'server': account_config.server,
                        'terminal_path': account_config.terminal_path,
                        'account_name': account_config.name,
                        'account_type': 'demo',  # 默认为demo类型
                        'magic_number': account_config.magic_number,
                        'max_volume': account_config.max_volume,
                        'min_volume': account_config.min_volume,
                        'allowed_symbols': account_config.allowed_symbols,
                        'max_daily_loss': account_config.max_daily_loss,
                        'max_positions': account_config.max_positions
                    }

                    self.local_accounts[account_id] = config_dict
                    logger.info(f"发现本地账户: {account_id} (主机: {account_config.host_id})")

            logger.info(f"本主机负责 {len(self.local_accounts)} 个账户")

        except Exception as e:
            logger.error(f"发现本地账户失败: {e}")
            raise
    
    async def _load_routing_rules(self):
        """加载消息路由规则"""
        try:
            if not self.message_routing_coordinator:
                return
            
            from .config_manager import get_config_manager
            config_mgr = get_config_manager(self.config_path)
            copy_relationships = config_mgr.get_copy_relationships()
            
            if copy_relationships:
                relationships_dict = {}
                for rel in copy_relationships:
                    master = rel.get('master_account')
                    slave = rel.get('slave_account')
                    if master and slave and rel.get('enabled', True):
                        if master not in relationships_dict:
                            relationships_dict[master] = []
                        relationships_dict[master].append({
                            'account_id': slave,
                            'copy_mode': rel.get('copy_mode', 'forward'),
                            'volume_ratio': rel.get('copy_ratio', 1.0)
                        })
                
                self.message_routing_coordinator.load_routing_rules_from_config({
                    'copy_relationships': relationships_dict
                })
                logger.info(f"消息路由规则加载完成，共 {len(copy_relationships)} 个跟单关系")
            else:
                logger.warning("未找到跟单关系配置")
                
        except Exception as e:
            logger.error(f"加载路由规则失败: {e}")
    
    async def _start_account_processes_enhanced(self):
        """启动账户进程"""
        for account_id, account_config in self.local_accounts.items():
            try:
                logger.info(f"启动账户: {account_id}")
                
                try:
                    terminal_started = await self.mt5_process_manager.start_terminal_process_new(
                        account_id, account_config
                    )
                    
                    if not terminal_started:
                        logger.error(f"终端启动失败，跳过账户: {account_id}")
                        continue
                        
                    logger.info(f"终端进程启动成功: {account_id}")
                except Exception as terminal_error:
                    logger.error(f"启动终端进程异常 {account_id}: {terminal_error}")
                    continue
                
                await asyncio.sleep(1)
                
                try:
                    terminal_status = self.mt5_process_manager.get_terminal_status_new(account_id)
                    logger.info(f"终端状态: {terminal_status}")
                except Exception as status_error:
                    logger.warning(f"获取终端状态失败 {account_id}: {status_error}")
                
                try:
                    logger.info(f"开始创建分离进程: {account_id}")
                    
                    await asyncio.wait_for(
                        self._create_separated_account_processes(account_id, account_config),
                        timeout=30  # 30秒超时
                    )
                    logger.info(f"分离进程创建成功: {account_id}")
                    
                except asyncio.TimeoutError:
                    logger.error(f"创建分离进程超时 {account_id}: 30秒超时")
                    continue
                except Exception as proc_error:
                    logger.error(f"创建分离进程失败 {account_id}: {proc_error}")

                    import traceback
                    logger.error(f"详细错误信息: {traceback.format_exc()}")
                    continue
                
                logger.info(f"账户启动成功: {account_id}")
                
            except Exception as e:
                logger.error(f"启动账户失败 {account_id}: {e}")
                continue
        
        logger.info(f"账户进程启动完成，成功启动 {len(self.monitor_processes)} 个监控器和 {len(self.executor_processes)} 个执行器")
    
    async def _create_separated_account_processes(self, account_id: str, account_config: Dict):
        """创建分离的账户进程 - 监控器和执行器"""
        try:
            process_config = {
                'login': account_config.get('login'),
                'password': account_config.get('password'),  
                'server': account_config.get('server'),
                'terminal_path': account_config.get('terminal_path'),
                'host_id': self.host_id,
                'nats': self.config.get('nats', {}),
                'account_name': account_config.get('account_name'),
                'account_type': account_config.get('account_type'),
                'magic_number': account_config.get('magic_number', 12345),
                'max_volume': account_config.get('max_volume', 1.0),
                'min_volume': account_config.get('min_volume', 0.01)
            }
            
            logger.info(f"账户 {account_id} 配置传递:")
            logger.info(f"  login: {process_config.get('login')}")
            logger.info(f"  password: {'***' if process_config.get('password') else 'None'}")
            logger.info(f"  server: {process_config.get('server')}")
            logger.info(f"  terminal_path: {process_config.get('terminal_path')}")
            
            nats_config = self.config.get('nats', {})
            logger.info(f"协调器NATS配置: {nats_config}")
            logger.info(f"传递给进程的NATS配置: {process_config.get('nats', {})}")
            
            await self._create_monitor_process(account_id, process_config)
            await self._create_executor_process(account_id, process_config)

            await self._create_rpc_service(account_id, account_config)

            logger.info(f"分离的账户进程已启动: {account_id} (监控器 + 执行器 + RPC服务)")
            
            if self.state_manager:
                monitor_pid = self.monitor_processes[account_id].pid if account_id in self.monitor_processes else None
                executor_pid = self.executor_processes[account_id].pid if account_id in self.executor_processes else None
                
                await self.state_manager.set(
                    f"account:{account_id}:processes",
                    {
                        'host_id': self.host_id,
                        'monitor_pid': monitor_pid,
                        'executor_pid': executor_pid,
                        'status': 'running',
                        'architecture': 'separated_processes',
                        'started_at': time.time()
                    },
                    ttl=60
                )
            
        except Exception as e:
            logger.error(f"创建分离进程失败 {account_id}: {e}")
            raise

    async def _create_monitor_process(self, account_id: str, process_config: Dict):
        """创建监控器进程"""
        try:
            monitor_queue = mp.Queue()
            self.monitor_queues[account_id] = monitor_queue
            
            monitor_process = mp.Process(
                target=run_account_monitor_process,
                args=(account_id, process_config, monitor_queue),
                name=f"mt5_monitor_{account_id}"
            )
            
            monitor_process.start()
            self.monitor_processes[account_id] = monitor_process
            
            logger.info(f"监控器进程启动: {account_id} (PID: {monitor_process.pid})")
            
            await asyncio.sleep(0.5)
            
        except Exception as e:
            logger.error(f"创建监控器进程失败 {account_id}: {e}")
            raise

    async def _create_executor_process(self, account_id: str, process_config: Dict):
        """创建执行器进程"""
        try:
            executor_queue = mp.Queue()
            self.executor_queues[account_id] = executor_queue
            
            executor_process = mp.Process(
                target=run_account_executor_process,
                args=(account_id, process_config, executor_queue),
                name=f"mt5_executor_{account_id}"
            )
            
            executor_process.start()
            self.executor_processes[account_id] = executor_process
            
            logger.info(f"执行器进程启动: {account_id} (PID: {executor_process.pid})")
            
            await asyncio.sleep(0.5)
            
        except Exception as e:
            logger.error(f"创建执行器进程失败 {account_id}: {e}")
            raise

    async def _create_rpc_service(self, account_id: str, account_config: Dict):
        """创建账户的RPC服务 """
        try:
            login = account_config.get('login')
            password = account_config.get('password')
            server = account_config.get('server')
            terminal_path = account_config.get('terminal_path')

            if not all([login, password, server, terminal_path]):
                logger.error(f"账户配置不完整，无法创建RPC服务: {account_id}")
                return

            success = await self.rpc_handler.add_account_service(
                account_id=account_id,
                login=int(login),
                password=password,
                server=server,
                terminal_path=terminal_path
            )

            if success:
                logger.info(f"RPC服务创建成功: {account_id}")
            else:
                logger.error(f"RPC服务创建失败: {account_id}")

        except Exception as e:
            logger.error(f"创建RPC服务失败 {account_id}: {e}")
            raise
    
    async def _monitor_processes(self):
        """监控分离的进程状态"""
        logger.info("启动分离进程监控")
        
        monitor_interval = 30   
        startup_grace_period = 60  
        startup_time = asyncio.get_event_loop().time()
        
        while self.running:
            try:
                current_time = asyncio.get_event_loop().time()
                is_startup_period = (current_time - startup_time) < startup_grace_period
                
                total_processes = len(self.monitor_processes) + len(self.executor_processes)
                if total_processes == 0 and is_startup_period:
                    logger.debug("启动期间：等待账户进程启动...")
                    await asyncio.sleep(5)  # 启动期间更频繁检查
                    continue
                
                logger.info(f"监控循环: 监控器进程={len(self.monitor_processes)}, 执行器进程={len(self.executor_processes)}")
                
                for account_id, monitor_process in list(self.monitor_processes.items()):
                    if not monitor_process.is_alive():
                        logger.warning(f"监控器进程异常退出: {account_id}")
                        # TODO: 实现自动重启监控器逻辑
                
                for account_id, executor_process in list(self.executor_processes.items()):
                    if not executor_process.is_alive():
                        logger.warning(f"执行器进程异常退出: {account_id}")
                        # TODO: 实现自动重启执行器逻辑
                
                try:
                    terminal_status = self.mt5_process_manager.get_all_process_status()
                    for account_id, status_info in terminal_status.items():
                        status = status_info.status if hasattr(status_info, 'status') else 'unknown'
                        if status not in ['running', 'connecting', 'unknown']:
                            logger.warning(f"终端进程状态异常: {account_id} - {status}")
                except Exception as e:
                    logger.warning(f"获取终端状态时出错: {e}")
                
                if total_processes > 0:
                    monitor_alive = sum(1 for p in self.monitor_processes.values() if p.is_alive())
                    executor_alive = sum(1 for p in self.executor_processes.values() if p.is_alive())
                    logger.info(f"进程状态: 监控器 {monitor_alive}/{len(self.monitor_processes)}, 执行器 {executor_alive}/{len(self.executor_processes)}")
                
                if total_processes == 0 and not is_startup_period:
                    logger.warning("没有任何账户进程在运行，系统可能存在配置问题")
                
                await asyncio.sleep(monitor_interval)
                
            except Exception as e:
                logger.error(f"进程监控异常: {e}")
                await asyncio.sleep(monitor_interval)
    
    async def _heartbeat_task(self):
        """心跳任务 """
        while self.running:
            try:
                # 发送系统心跳
                heartbeat = {
                    'host_id': self.host_id,
                    'timestamp': time.time(),
                    'accounts': list(self.local_accounts.keys()),
                    'architecture': 'separated_processes',
                    'monitor_processes': {
                        account_id: process.is_alive()
                        for account_id, process in self.monitor_processes.items()
                    },
                    'executor_processes': {
                        account_id: process.is_alive()
                        for account_id, process in self.executor_processes.items()
                    }
                }
                
                # TODO: 发送心跳到分布式系统
                logger.info(f"系统心跳: 主机={self.host_id}, 账户={len(self.local_accounts)}, 监控器={len([p for p in self.monitor_processes.values() if p.is_alive()])}, 执行器={len([p for p in self.executor_processes.values() if p.is_alive()])}")
                
                await asyncio.sleep(60)  # 1分钟心跳间隔
                
            except Exception as e:
                logger.error(f"心跳异常: {e}")
    
    async def stop(self):
        """停止协调器"""
        self.running = False
        logger.info("停止分布式MT5协调器 (分离进程)")
        
        try:
            # 停止新架构组件
            if self.hybrid_message_router:
                logger.info("停止混合消息路由器")
                await self.hybrid_message_router.stop()
                self.hybrid_message_router = None
            
            if self.optimized_nats_manager:
                logger.info("停止优化NATS管理器")
                await self.optimized_nats_manager.shutdown()
                self.optimized_nats_manager = None
            
            # 停止RPC组件
            if hasattr(self, 'rpc_handler') and self.rpc_handler:
                logger.info("停止RPC处理器")
                await self.rpc_handler.stop()

            for account_id, monitor_process in self.monitor_processes.items():
                if monitor_process.is_alive():
                    logger.info(f"停止监控器进程: {account_id}")
                    if account_id in self.monitor_queues:
                        try:
                            self.monitor_queues[account_id].put_nowait({'type': 'stop'})
                        except:
                            pass
                    
                    monitor_process.terminate()
                    monitor_process.join(timeout=10)
                    
                    if monitor_process.is_alive():
                        logger.warning(f"强制结束监控器进程: {account_id}")
                        monitor_process.kill()
            
            for account_id, executor_process in self.executor_processes.items():
                if executor_process.is_alive():
                    logger.info(f"停止执行器进程: {account_id}")
                    if account_id in self.executor_queues:
                        try:
                            self.executor_queues[account_id].put_nowait({'type': 'stop'})
                        except:
                            pass
                    
                    executor_process.terminate()
                    executor_process.join(timeout=10)
                    
                    if executor_process.is_alive():
                        logger.warning(f"强制结束执行器进程: {account_id}")
                        executor_process.kill()
            
            logger.info("停止所有终端进程")
            await asyncio.to_thread(self.mt5_process_manager.shutdown)
            
            if self.message_routing_coordinator:
                await self.message_routing_coordinator.stop_routing()
                logger.info("消息路由协调器已停止")
            
            # 旧架构组件已清理

            if self.jetstream:
                await self.jetstream.disconnect()

            if self.state_manager:
                await self.state_manager.stop()
            
            logger.info("分布式MT5协调器已完全停止 (分离进程)")
            
        except Exception as e:
            logger.error(f"停止协调器时出错: {e}")
    
    async def get_system_status(self) -> Dict[str, Any]:
        """获取系统状态"""
        return {
            'host_id': self.host_id,
            'running': self.running,
            'local_accounts': list(self.local_accounts.keys()),
            'architecture': 'separated_processes',
            'monitor_processes': {
                account_id: {
                    'pid': process.pid,
                    'alive': process.is_alive(),
                    'name': process.name
                }
                for account_id, process in self.monitor_processes.items()
            },
            'executor_processes': {
                account_id: {
                    'pid': process.pid,
                    'alive': process.is_alive(),
                    'name': process.name
                }
                for account_id, process in self.executor_processes.items()
            },
            'terminal_processes': self.mt5_process_manager.get_all_process_status(),
            'architecture_layer': '4-layer separated architecture',
            'features': [
                'Strict responsibility separation',
                'Monitor/Executor split',
                'Message-based communication',
                'Dynamic role assignment',
                'Pairing-based configuration',
                'Process isolation',
                'Distributed coordination'
            ],
            'process_summary': {
                'total_monitors': len(self.monitor_processes),
                'alive_monitors': sum(1 for p in self.monitor_processes.values() if p.is_alive()),
                'total_executors': len(self.executor_processes),
                'alive_executors': sum(1 for p in self.executor_processes.values() if p.is_alive())
            },
            'message_routing': self.message_routing_coordinator.get_routing_stats() if self.message_routing_coordinator else None
        }
    
    def send_message_to_monitor(self, account_id: str, message: Dict[str, Any]):
        """向监控器进程发送消息"""
        if account_id in self.monitor_queues:
            try:
                self.monitor_queues[account_id].put_nowait(message)
                logger.info(f"消息已发送到监控器 {account_id}: {message.get('type', 'unknown')}")
            except Exception as e:
                logger.error(f"发送消息到监控器失败 {account_id}: {e}")
        else:
            logger.warning(f"监控器消息队列不存在: {account_id}")
    
    def send_message_to_executor(self, account_id: str, message: Dict[str, Any]):
        """向执行器进程发送消息"""
        if account_id in self.executor_queues:
            try:
                self.executor_queues[account_id].put_nowait(message)
                logger.info(f"消息已发送到执行器 {account_id}: {message.get('type', 'unknown')}")
            except Exception as e:
                logger.error(f"发送消息到执行器失败 {account_id}: {e}")
        else:
            logger.warning(f"执行器消息队列不存在: {account_id}")
    
    def send_message_to_account(self, account_id: str, message: Dict[str, Any], target: str = 'both'):
        """向账户进程发送消息"""
        if target in ['both', 'monitor']:
            self.send_message_to_monitor(account_id, message)
        if target in ['both', 'executor']:
            self.send_message_to_executor(account_id, message)
    
    async def reload_pairing_config(self):
        """重新加载配对配置"""
        logger.info("重新加载配对配置 (分离进程)")
        
        reload_message = {'type': 'reload_config'}
        
        for account_id in self.monitor_processes:
            self.send_message_to_account(account_id, reload_message, 'both')
        
        logger.info("配对配置重载消息已发送到所有分离进程")
