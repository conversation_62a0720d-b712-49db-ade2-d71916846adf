# MT5分布式交易系统 - 开发环境
# 轻量级配置，适合本地开发和调试

version: '3.8'

# ============================================================================
# 开发环境服务配置
# ============================================================================
services:
  # 基础设施服务（轻量配置）
  redis:
    image: redis:7-alpine
    container_name: mt5-redis-dev
    ports:
      - "6379:6379"
    volumes:
      - redis_dev_data:/data
      - ../../configs/redis/redis-dev.conf:/etc/redis/redis.conf:ro
    command: redis-server /etc/redis/redis.conf
    environment:
      - REDIS_OPTIMIZATION_ENABLED=false
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
    restart: unless-stopped
    networks:
      - mt5-dev-network

  nats:
    image: nats:2.10-alpine
    container_name: mt5-nats-dev
    ports:
      - "4222:4222"
      - "8222:8222"
    volumes:
      - nats_dev_data:/data
      - ../../../logs:/logs
      - ../../configs/nats/nats-dev.conf:/etc/nats/nats.conf:ro
    command: ["--config=/etc/nats/nats.conf"]
    healthcheck:
      test: ["CMD", "sh", "-c", "echo 'PING' | nc localhost 4222 | grep -q PONG || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3
    restart: unless-stopped
    networks:
      - mt5-dev-network

  # 开发环境监控（可选）
  prometheus:
    image: prom/prometheus:latest
    container_name: mt5-prometheus-dev
    ports:
      - "9090:9090"
    volumes:
      - ../../configs/prometheus/prometheus-dev.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus_dev_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--storage.tsdb.retention.time=1d'  # 开发环境短保留期
      - '--web.enable-lifecycle'
    restart: unless-stopped
    networks:
      - mt5-dev-network
    profiles:
      - "monitoring"  # 可选启用

  grafana:
    image: grafana/grafana:latest
    container_name: mt5-grafana-dev
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin
      - GF_USERS_ALLOW_SIGN_UP=true
      - GF_AUTH_ANONYMOUS_ENABLED=true
      - GF_AUTH_ANONYMOUS_ORG_ROLE=Admin
    volumes:
      - grafana_dev_data:/var/lib/grafana
    depends_on:
      - prometheus
    restart: unless-stopped
    networks:
      - mt5-dev-network
    profiles:
      - "monitoring"

  # MT5应用服务（开发配置）
  mt5-coordinator:
    build:
      context: ../../..
      dockerfile: docker/Dockerfile
    image: mt5-trading-system:dev
    container_name: mt5-coordinator-dev
    ports:
      - "8000:8000"  # API端口
      - "8001:8001"  # 健康检查端口
    volumes:
      - ../../../config:/app/config:ro
      - ../../../logs:/app/logs
      - ../../../src:/app/src:ro  # 开发环境挂载源码
      - ./.env.development:/app/.env:ro
    environment:
      - PYTHONPATH=/app
      - MT5_ENVIRONMENT=development
      - MT5_CONFIG_PATH=/app/config/core/system.yaml
      - HOST_ID=docker-dev
      - MT5_NODE_TYPE=coordinator
      - MT5_DEPLOYMENT_MODE=development
      - MT5_DEBUG=true
      - MT5_LOG_LEVEL=DEBUG
      
      # 基础设施连接
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_DB=1  # 开发环境使用DB1
      - NATS_HOST=nats
      - NATS_PORT=4222
      
      # 开发环境账户密码（使用测试密码）
      - MT5_ACC001_PASSWORD=${MT5_ACC001_PASSWORD:-test123}
      - MT5_ACC002_PASSWORD=${MT5_ACC002_PASSWORD:-test123}
      - MT5_ACC003_PASSWORD=${MT5_ACC003_PASSWORD:-test123}
      
    depends_on:
      - redis
      - nats
    restart: unless-stopped
    networks:
      - mt5-dev-network
    labels:
      - "mt5.environment=development"
      - "mt5.service=coordinator"
    command: ["python", "main.py", "--host-id", "docker-dev", "--config", "/app/config/core/system.yaml"]
    
    # 开发环境健康检查
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8001/health"]
      interval: 60s
      timeout: 15s
      retries: 3
      start_period: 120s

  # Redis Commander（开发工具）
  redis-commander:
    image: rediscommander/redis-commander:latest
    container_name: mt5-redis-commander-dev
    ports:
      - "8081:8081"
    environment:
      - REDIS_HOSTS=redis:redis:6379
    depends_on:
      - redis
    restart: unless-stopped
    networks:
      - mt5-dev-network
    profiles:
      - "tools"

# ============================================================================
# 开发环境网络
# ============================================================================
networks:
  mt5-dev-network:
    driver: bridge
    name: mt5-dev-network
    labels:
      - "mt5.environment=development"

# ============================================================================
# 开发环境数据卷
# ============================================================================
volumes:
  redis_dev_data:
    driver: local
    labels:
      - "mt5.environment=development"
      - "mt5.data=redis"
      
  nats_dev_data:
    driver: local
    labels:
      - "mt5.environment=development"
      - "mt5.data=nats"
      
  prometheus_dev_data:
    driver: local
    labels:
      - "mt5.environment=development"
      - "mt5.data=prometheus"
      
  grafana_dev_data:
    driver: local
    labels:
      - "mt5.environment=development"
      - "mt5.data=grafana"