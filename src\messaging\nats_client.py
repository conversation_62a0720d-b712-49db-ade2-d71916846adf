"""
NATS客户端封装
"""
import asyncio
import json
import time
from typing import Callable, Optional, Dict, Any, List
from dataclasses import dataclass
import nats
from nats.aio.client import Client as NATS
from nats.aio.errors import ErrConnectionClosed, ErrTimeout, ErrNoServers

from .message_types import MessageEnvelope, MessageBatch, SubscriptionConfig, PublishConfig
from ..utils.logger import get_logger
from ..utils.metrics import get_metrics_collector


logger = get_logger(__name__)
metrics = get_metrics_collector()


@dataclass
class NATSConfig:
    """NATS配置"""
    servers: List[str]
    name: str = "mt5-copier"
    user: Optional[str] = None
    password: Optional[str] = None
    token: Optional[str] = None
    max_reconnect_attempts: int = 10
    reconnect_time_wait: float = 2.0
    connect_timeout: float = 10.0
    drain_timeout: float = 30.0
    ping_interval: float = 120.0
    max_outstanding_pings: int = 2
    no_echo: bool = False
    verbose: bool = False
    pedantic: bool = False
    allow_reconnect: bool = True


class NATSClient:
    """NATS客户端封装"""
    
    def __init__(self, config: NATSConfig):
        self.config = config
        self.nc: Optional[NATS] = None
        self._subscriptions: Dict[str, Any] = {}
        self._connection_status = "disconnected"
        self._last_error = None
        self._message_handlers: Dict[str, Callable] = {}
        self._reconnect_count = 0
        
    async def connect(self) -> bool:
        """连接到NATS服务器"""
        if self.nc and self.nc.is_connected:
            return True
        
        try:
            self.nc = NATS()
            
            await self.nc.connect(
                servers=self.config.servers,
                name=self.config.name,
                user=self.config.user,
                password=self.config.password,
                token=self.config.token,
                max_reconnect_attempts=self.config.max_reconnect_attempts,
                reconnect_time_wait=self.config.reconnect_time_wait,
                connect_timeout=self.config.connect_timeout,
                drain_timeout=self.config.drain_timeout,
                ping_interval=self.config.ping_interval,
                max_outstanding_pings=self.config.max_outstanding_pings,
                no_echo=self.config.no_echo,
                verbose=self.config.verbose,
                pedantic=self.config.pedantic,
                allow_reconnect=self.config.allow_reconnect,
                error_cb=self._error_cb,
                disconnected_cb=self._disconnected_cb,
                reconnected_cb=self._reconnected_cb,
                closed_cb=self._closed_cb
            )
            
            self._connection_status = "connected"
            logger.info(f"NATS连接成功: {self.config.servers}")
            metrics.increment("nats_connections_total")
            metrics.set_gauge("nats_connection_status", 1)
            
            return True
            
        except Exception as e:
            self._last_error = str(e)
            logger.error(f"NATS连接失败: {e}")
            metrics.increment("nats_connection_errors_total")
            metrics.set_gauge("nats_connection_status", 0)
            return False
    
    async def disconnect(self):
        """断开连接"""
        if self.nc and self.nc.is_connected:
            try:
                await self.nc.drain()
                await self.nc.close()
                self._connection_status = "disconnected"
                logger.info("NATS连接已断开")
                metrics.set_gauge("nats_connection_status", 0)
            except Exception as e:
                logger.error(f"断开NATS连接失败: {e}")
    
    async def publish(self, subject: str, data: Dict[str, Any], headers: Dict[str, str] = None) -> bool:
        """发布消息"""
        if not self.nc or not self.nc.is_connected:
            logger.error("NATS未连接")
            return False
        
        try:
            start_time = time.perf_counter()
            
            # 序列化数据
            payload = json.dumps(data, default=str).encode('utf-8')
            
            # 发布消息
            await self.nc.publish(subject, payload, headers=headers)
            
            # 记录指标
            publish_time = (time.perf_counter() - start_time) * 1000
            metrics.observe("nats_publish_duration_ms", publish_time)
            metrics.increment("nats_messages_published_total")
            
            logger.debug(f"消息发布成功: {subject}")
            return True
            
        except Exception as e:
            logger.error(f"消息发布失败: {e}")
            metrics.increment("nats_publish_errors_total")
            return False
    
    async def publish_message(self, message: MessageEnvelope) -> bool:
        """发布消息信封"""
        headers = message.headers.copy()
        headers.update({
            'message-id': message.id,
            'timestamp': str(message.timestamp)
        })
        
        if message.ttl:
            headers['ttl'] = str(message.ttl)
        
        return await self.publish(message.subject, message.payload, headers)
    
    async def publish_batch(self, messages: List[MessageEnvelope]) -> int:
        """批量发布消息"""
        if not messages:
            return 0
        
        success_count = 0
        batch_start = time.perf_counter()
        
        # 创建发布任务
        tasks = []
        for message in messages:
            task = self.publish_message(message)
            tasks.append(task)
        
        # 并行执行
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # 统计结果
        for result in results:
            if isinstance(result, Exception):
                logger.error(f"批量发布消息失败: {result}")
                metrics.increment("nats_batch_publish_errors_total")
            elif result:
                success_count += 1
        
        # 记录指标
        batch_time = (time.perf_counter() - batch_start) * 1000
        metrics.observe("nats_batch_publish_duration_ms", batch_time)
        metrics.record("nats_batch_size", len(messages))
        
        logger.debug(f"批量发布完成: {success_count}/{len(messages)}")
        return success_count
    
    async def subscribe(self, subject: str, callback: Callable, queue_group: str = None) -> bool:
        """订阅消息"""
        if not self.nc or not self.nc.is_connected:
            logger.error("NATS未连接")
            return False
        
        try:
            # 包装回调函数
            async def message_handler(msg):
                start_time = time.perf_counter()
                try:
                    # 解析消息
                    data = json.loads(msg.data.decode('utf-8'))
                    
                    # 创建消息信封
                    headers = msg.headers or {}
                    message = MessageEnvelope(
                        id=headers.get('message-id', ''),
                        subject=msg.subject,
                        payload=data,
                        headers=dict(headers),
                        timestamp=float(headers.get('timestamp', time.time()))
                    )
                    
                    # 调用回调
                    await callback(message)
                    
                    # 记录指标
                    process_time = (time.perf_counter() - start_time) * 1000
                    metrics.observe("nats_message_process_duration_ms", process_time)
                    metrics.increment("nats_messages_processed_total")
                    
                except Exception as e:
                    logger.error(f"消息处理失败: {e}")
                    metrics.increment("nats_message_process_errors_total")
                    raise
            
            # 创建订阅
            subscription = await self.nc.subscribe(
                subject=subject,
                cb=message_handler,
                queue=queue_group
            )
            
            self._subscriptions[subject] = subscription
            logger.info(f"订阅成功: {subject}")
            metrics.increment("nats_subscriptions_total")
            
            return True
            
        except Exception as e:
            logger.error(f"订阅失败: {e}")
            metrics.increment("nats_subscription_errors_total")
            return False
    
    async def unsubscribe(self, subject: str) -> bool:
        """取消订阅"""
        if subject not in self._subscriptions:
            return True
        
        try:
            subscription = self._subscriptions[subject]
            await subscription.unsubscribe()
            del self._subscriptions[subject]
            
            logger.info(f"取消订阅成功: {subject}")
            metrics.increment("nats_unsubscriptions_total")
            return True
            
        except Exception as e:
            logger.error(f"取消订阅失败: {e}")
            return False
    
    async def request(self, subject: str, data: Dict[str, Any], timeout: float = 10.0) -> Optional[MessageEnvelope]:
        """请求-响应模式"""
        if not self.nc or not self.nc.is_connected:
            logger.error("NATS未连接")
            return None
        
        try:
            start_time = time.perf_counter()
            
            # 序列化数据
            payload = json.dumps(data, default=str).encode('utf-8')
            
            # 发送请求
            response = await self.nc.request(subject, payload, timeout=timeout)
            
            # 解析响应
            response_data = json.loads(response.data.decode('utf-8'))
            
            # 记录指标
            request_time = (time.perf_counter() - start_time) * 1000
            metrics.observe("nats_request_duration_ms", request_time)
            metrics.increment("nats_requests_total")
            
            return MessageEnvelope(
                id="",
                subject=response.subject,
                payload=response_data,
                headers=dict(response.headers or {}),
                timestamp=time.time()
            )
            
        except asyncio.TimeoutError:
            logger.error(f"请求超时: {subject}")
            metrics.increment("nats_request_timeouts_total")
            return None
        except Exception as e:
            logger.error(f"请求失败: {e}")
            metrics.increment("nats_request_errors_total")
            return None
    
    async def _error_cb(self, e):
        """错误回调"""
        self._last_error = str(e)
        logger.error(f"NATS错误: {e}")
        metrics.increment("nats_errors_total")
    
    async def _disconnected_cb(self):
        """断开连接回调"""
        self._connection_status = "disconnected"
        logger.warning("NATS连接已断开")
        metrics.set_gauge("nats_connection_status", 0)
    
    async def _reconnected_cb(self):
        """重新连接回调"""
        self._connection_status = "connected"
        self._reconnect_count += 1
        logger.info("NATS重新连接成功")
        metrics.increment("nats_reconnections_total")
        metrics.set_gauge("nats_connection_status", 1)
    
    async def _closed_cb(self):
        """连接关闭回调"""
        self._connection_status = "closed"
        logger.info("NATS连接已关闭")
        metrics.set_gauge("nats_connection_status", 0)
    
    def is_connected(self) -> bool:
        """检查连接状态"""
        return self.nc is not None and self.nc.is_connected
    
    def get_status(self) -> Dict[str, Any]:
        """获取客户端状态"""
        return {
            'connection_status': self._connection_status,
            'is_connected': self.is_connected(),
            'last_error': self._last_error,
            'reconnect_count': self._reconnect_count,
            'subscriptions': list(self._subscriptions.keys()),
            'servers': self.config.servers
        }
    
    async def health_check(self) -> bool:
        """健康检查"""
        if not self.is_connected():
            return False
        
        try:
            # 发送测试消息
            test_subject = "health.check"
            test_data = {"timestamp": time.time()}
            
            await self.publish(test_subject, test_data)
            return True
            
        except Exception as e:
            logger.error(f"健康检查失败: {e}")
            return False


class NATSClientPool:
    """NATS客户端池"""
    
    def __init__(self, config: NATSConfig, pool_size: int = 5):
        self.config = config
        self.pool_size = pool_size
        self.clients: List[NATSClient] = []
        self.current_index = 0
        self._lock = asyncio.Lock()
    
    async def initialize(self):
        """初始化客户端池"""
        async with self._lock:
            for i in range(self.pool_size):
                client = NATSClient(self.config)
                if await client.connect():
                    self.clients.append(client)
                else:
                    logger.error(f"客户端池初始化失败: 客户端 {i}")
    
    async def get_client(self) -> Optional[NATSClient]:
        """获取可用客户端"""
        async with self._lock:
            if not self.clients:
                return None
            
            # 轮询选择客户端
            for _ in range(len(self.clients)):
                client = self.clients[self.current_index]
                self.current_index = (self.current_index + 1) % len(self.clients)
                
                if client.is_connected():
                    return client
            
            return None
    
    async def close_all(self):
        """关闭所有客户端"""
        async with self._lock:
            for client in self.clients:
                await client.disconnect()
            self.clients.clear()