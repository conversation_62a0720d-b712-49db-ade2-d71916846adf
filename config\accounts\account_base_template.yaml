# 账户基础配置模板
# 此文件包含账户的基础信息，不包含角色相关配置
# 角色配置在运行时动态管理

# ============================================================================
# 账户基础信息 (不可变)
# ============================================================================
account:
  id: "${ACCOUNT_ID}"
  name: "${ACCOUNT_NAME}"
  enabled: true
  
  # MT5连接信息
  connection:
    login: ${MT5_LOGIN}
    password: "${MT5_PASSWORD}"  # 从环境变量读取
    server: "${MT5_SERVER}"
    terminal_path: "${MT5_TERMINAL_PATH}"
    
  # 账户属性
  properties:
    currency: "USD"
    timezone: 0  # UTC+0
    account_type: "demo"  # demo/real
    broker: "${BROKER_NAME}"
    
# ============================================================================
# 交易配置 (可在运行时调整)
# ============================================================================
trading:
  # 允许的交易品种
  allowed_symbols:
    - "EURUSD"
    - "GBPUSD"
    - "USDJPY"
    - "XAUUSD"
    - "BTCUSD"
    
  # 交易限制
  limits:
    max_spread: 3.0
    slippage: 10
    max_lot_size: 10.0
    min_lot_size: 0.01
    
  # 交易时间
  trading_hours:
    enabled: true
    start_time: "00:00"  # UTC时间
    end_time: "23:59"
    exclude_weekends: true
    
# ============================================================================
# 风险管理配置 (角色无关的基础风险设置)
# ============================================================================
risk_management:
  # 基础风险限制
  base_limits:
    max_daily_loss: 4000.0
    max_daily_trades: 50
    max_positions: 10
    max_drawdown: 0.2  # 20%
    
  # 紧急停止
  emergency_stop:
    enabled: true
    loss_threshold: 4200.0
    drawdown_threshold: 0.3  # 30%
    
  # 风险检查
  checks:
    pre_trade: true
    real_time: true
    post_trade: true
    
# ============================================================================
# 监控配置
# ============================================================================
monitoring:
  # 性能监控
  performance:
    enabled: true
    polling_interval: 0.1  # 100ms
    adaptive_polling: true
    
  # 连接监控
  connection:
    health_check_interval: 30
    reconnect_attempts: 5
    reconnect_delay: 5
    
  # 指标收集
  metrics:
    enabled: true
    collection_interval: 60
    retention_days: 30
    
# ============================================================================
# 通知配置
# ============================================================================
notifications:
  # Telegram通知
  telegram:
    enabled: false
    chat_id: "${TELEGRAM_CHAT_ID}"
    token: "${TELEGRAM_BOT_TOKEN}"
    
  # 邮件通知
  email:
    enabled: false
    address: "${EMAIL_ADDRESS}"
    smtp_server: "${SMTP_SERVER}"
    
  # Webhook通知
  webhook:
    enabled: false
    url: "${WEBHOOK_URL}"
    
  # 通知级别
  levels:
    error: true
    warning: true
    info: false
    debug: false
    
# ============================================================================
# 本地配置 (主机相关)
# ============================================================================
local:
  # 数据存储
  data_path: "./data/accounts/${ACCOUNT_ID}"
  logs_path: "./logs/accounts/${ACCOUNT_ID}"
  
  # 缓存配置
  cache:
    enabled: true
    ttl: 300  # 5分钟
    max_size: 1000
    
  # 备份配置
  backup:
    enabled: true
    interval: 3600  # 1小时
    retention_days: 7
    
# ============================================================================
# 扩展配置 (可选)
# ============================================================================
extensions:
  # 自定义指标
  custom_metrics:
    enabled: false
    metrics: []
    
  # 插件配置
  plugins:
    enabled: false
    plugins: []
    
  # API配置
  api:
    enabled: false
    port: 0  # 0表示自动分配
    
# ============================================================================
# 元数据
# ============================================================================
metadata:
  created_at: "${CREATED_AT}"
  updated_at: "${UPDATED_AT}"
  version: "3.0.0"
  tags: []
  description: ""
