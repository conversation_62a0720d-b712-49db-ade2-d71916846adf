# MT5 分布式跟单系统 - 部署配置
# 替代 entrypoint.sh 的 YAML 配置文件
# 基于正确架构设计原则的自动化部署配置

deployment:
  name: "MT5 分布式跟单系统"
  version: "2.0.0"
  mode: "production"
  architecture: "process-isolated-multi-account"

# 服务依赖配置
dependencies:
  redis:
    host: "${REDIS_HOST:-redis}"
    port: "${REDIS_PORT:-6379}"
    timeout: 60
    health_check: true
  
  nats:
    host: "${NATS_HOST:-nats}"
    port: "${NATS_PORT:-4222}"
    timeout: 60
    health_check: true
  
  prometheus:
    pushgateway_url: "${PROMETHEUS_PUSHGATEWAY_URL:-http://pushgateway:9091}"
    enabled: true

# 环境变量验证
required_env_vars:
  - MT5_CONFIG_PATH
  - COPY_RELATIONSHIPS_CONFIG
  - ACCOUNT_ID  # 对于 master/slave 节点
  - HOST_ID
  - MT5_NODE_TYPE

# 服务类型配置
service_types:
  master:
    description: "主账户监控器"
    required_env:
      - ACCOUNT_ID
    startup_command: "python main.py --config ${MT5_CONFIG_PATH} --host-id ${HOST_ID} --distributed --masters ${ACCOUNT_ID}"
    health_check:
      endpoint: "/health/master/${ACCOUNT_ID}"
      interval: 30
    
  slave:
    description: "从账户执行器"
    required_env:
      - ACCOUNT_ID
    startup_command: "python main.py --config ${MT5_CONFIG_PATH} --host-id ${HOST_ID} --distributed --slaves ${ACCOUNT_ID}"
    health_check:
      endpoint: "/health/slave/${ACCOUNT_ID}"
      interval: 30
  
  api:
    description: "API 网关服务"
    required_env: []  # API 服务不需要 ACCOUNT_ID
    startup_command: "python -m uvicorn src.api.wrapper_api:app --host 0.0.0.0 --port 8000 --workers 1 --loop asyncio"
    health_check:
      endpoint: "/health"
      interval: 15
    ports:
      - "8000:8000"
  
  coordinator:
    description: "分布式系统协调器"
    required_env: []  # coordinator 不需要 ACCOUNT_ID
    startup_command: "python main.py --config ${MT5_CONFIG_PATH} --host-id ${HOST_ID} --distributed"
    health_check:
      endpoint: "/health/coordinator"
      interval: 30

# 账户配置映射
accounts:
  ACC001:
    type: "master"
    description: "主账户 - 信号源"
    container_name: "mt5-master-acc001"
    host_id: "docker-master-acc001"
    
  ACC002:
    type: "slave"
    description: "从账户 - 正向跟单"
    container_name: "mt5-slave-acc002"
    host_id: "docker-slave-acc002"
    copy_mode: "forward"
    copy_ratio: 1.0
    
  ACC003:
    type: "slave"
    description: "从账户 - 反向跟单"
    container_name: "mt5-slave-acc003"
    host_id: "docker-slave-acc003"
    copy_mode: "reverse"
    copy_ratio: 0.5

# 启动顺序配置
startup_order:
  1:
    - redis
    - nats
    - pushgateway
  2:
    - mt5-master-acc001
  3:
    - mt5-slave-acc002
    - mt5-slave-acc003
  4:
    - mt5-coordinator
  5:
    - mt5-api

# 健康检查配置
health_checks:
  startup_timeout: 300  # 5分钟
  readiness_timeout: 60  # 1分钟
  liveness_interval: 30  # 30秒
  failure_threshold: 3

# 日志配置
logging:
  level: "INFO"
  format: "json"
  rotation: "daily"
  retention: "7d"
  
# 监控配置
monitoring:
  metrics_enabled: true
  tracing_enabled: false
  prometheus_metrics: true
  custom_metrics:
    - "mt5.connections.active"
    - "mt5.trades.executed"
    - "mt5.signals.processed"
    - "mt5.latency.signal_to_execution"

# 安全配置
security:
  process_isolation: true
  resource_limits:
    cpu: "1000m"  # 1 CPU core
    memory: "1Gi"  # 1GB RAM
  network_policies:
    enabled: false  # 在生产环境中应启用

# 故障恢复配置
recovery:
  auto_restart: true
  max_restart_attempts: 3
  restart_delay: "30s"
  circuit_breaker:
    enabled: true
    failure_threshold: 5
    recovery_timeout: "60s"

# 配置文件路径
config_files:
  system_config: "/app/config/optimized_system.yaml"
  copy_relationships: "/app/config/copy_relationships.yaml"
  deployment_config: "/app/docker/deployment-config.yaml"

# 容器标签
labels:
  system:
    - "mt5.system=distributed-copy-trading"
    - "mt5.architecture=process-isolated"
    - "mt5.version=2.0.0"
  
  master:
    - "mt5.service=master-monitor"
    - "mt5.node.type=master"
    
  slave:
    - "mt5.service=slave-executor"
    - "mt5.node.type=slave"
    
  api:
    - "mt5.service=api-gateway"
    - "mt5.api.type=unified-management"
    
  coordinator:
    - "mt5.service=distributed-coordinator"
    - "mt5.node.type=coordinator"
