#!/bin/bash

# TLS证书生成脚本
# 为MT5交易系统生成自签名证书

set -e

CERT_DIR="/mnt/d/github/mt5-python/certs"
DAYS=365
KEY_SIZE=2048

# 创建证书目录
mkdir -p "$CERT_DIR"
cd "$CERT_DIR"

echo "生成TLS证书..."

# 生成CA私钥
echo "1. 生成CA私钥..."
openssl genrsa -out ca-key.pem $KEY_SIZE

# 生成CA证书
echo "2. 生成CA证书..."
openssl req -new -x509 -days $DAYS -key ca-key.pem -sha256 -out ca.pem -subj "/C=US/ST=State/L=City/O=MT5Trading/OU=Security/CN=MT5-CA"

# 生成服务器私钥
echo "3. 生成服务器私钥..."
openssl genrsa -out server-key.pem $KEY_SIZE

# 生成服务器证书请求
echo "4. 生成服务器证书请求..."
openssl req -subj "/C=US/ST=State/L=City/O=MT5Trading/OU=Security/CN=mt5-server" -sha256 -new -key server-key.pem -out server.csr

# 生成服务器证书扩展文件
echo "5. 生成服务器证书扩展..."
cat > server-extfile.cnf <<EOF
subjectAltName = DNS:mt5-server,DNS:localhost,DNS:mt5-nats,DNS:mt5-redis,IP:127.0.0.1,IP:***********,IP:***********,IP:***********
extendedKeyUsage = serverAuth
EOF

# 生成服务器证书
echo "6. 生成服务器证书..."
openssl x509 -req -days $DAYS -sha256 -in server.csr -CA ca.pem -CAkey ca-key.pem -out server-cert.pem -extfile server-extfile.cnf -CAcreateserial

# 生成客户端私钥
echo "7. 生成客户端私钥..."
openssl genrsa -out client-key.pem $KEY_SIZE

# 生成客户端证书请求
echo "8. 生成客户端证书请求..."
openssl req -subj "/C=US/ST=State/L=City/O=MT5Trading/OU=Security/CN=mt5-client" -new -key client-key.pem -out client.csr

# 生成客户端证书扩展文件
echo "9. 生成客户端证书扩展..."
cat > client-extfile.cnf <<EOF
extendedKeyUsage = clientAuth
EOF

# 生成客户端证书
echo "10. 生成客户端证书..."
openssl x509 -req -days $DAYS -sha256 -in client.csr -CA ca.pem -CAkey ca-key.pem -out client-cert.pem -extfile client-extfile.cnf -CAcreateserial

# 设置证书权限
echo "11. 设置证书权限..."
chmod 400 ca-key.pem server-key.pem client-key.pem
chmod 444 ca.pem server-cert.pem client-cert.pem

# 清理临时文件
echo "12. 清理临时文件..."
rm server.csr client.csr server-extfile.cnf client-extfile.cnf

echo "TLS证书生成完成！"
echo "证书位置: $CERT_DIR"
echo ""
echo "文件列表:"
ls -la "$CERT_DIR"
echo ""
echo "证书验证:"
echo "CA证书信息:"
openssl x509 -in ca.pem -text -noout | grep -E "(Subject|Issuer|Not Before|Not After)"
echo ""
echo "服务器证书信息:"
openssl x509 -in server-cert.pem -text -noout | grep -E "(Subject|Issuer|Not Before|Not After|DNS:|IP Address:)"
echo ""
echo "客户端证书信息:"
openssl x509 -in client-cert.pem -text -noout | grep -E "(Subject|Issuer|Not Before|Not After)"