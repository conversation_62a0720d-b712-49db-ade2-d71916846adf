"""
批量处理器
优化信号处理延迟和吞吐量
"""
import asyncio
import time
import logging
from typing import List, Dict, Any, Callable, Optional, TypeVar, Generic
from dataclasses import dataclass
from collections import deque
import threading

logger = logging.getLogger(__name__)

T = TypeVar('T')


@dataclass
class BatchItem:
    """批量处理项"""
    data: Any
    timestamp: float
    callback: Optional[Callable] = None
    metadata: Dict[str, Any] = None


class BatchProcessor(Generic[T]):
    """批量处理器"""
    
    def __init__(self, 
                 batch_size: int = 100,
                 batch_timeout: float = 1.0,
                 max_queue_size: int = 10000,
                 worker_count: int = 4):
        self.batch_size = batch_size
        self.batch_timeout = batch_timeout
        self.max_queue_size = max_queue_size
        self.worker_count = worker_count
        
        # 队列和状态
        self.queue: asyncio.Queue = asyncio.Queue(maxsize=max_queue_size)
        self.running = False
        self.workers: List[asyncio.Task] = []
        self.processor_func: Optional[Callable] = None
        
        # 统计信息
        self.stats = {
            'items_processed': 0,
            'batches_processed': 0,
            'items_dropped': 0,
            'processing_errors': 0,
            'average_batch_size': 0.0,
            'average_processing_time': 0.0,
            'start_time': None
        }
        
        # 性能监控
        self.processing_times = deque(maxlen=1000)
        self.batch_sizes = deque(maxlen=1000)
        
    def set_processor(self, processor_func: Callable[[List[T]], Any]):
        """设置批量处理函数"""
        self.processor_func = processor_func
    
    async def add_item(self, item: T, callback: Callable = None, 
                      metadata: Dict[str, Any] = None) -> bool:
        """添加项到批量处理队列"""
        if not self.running:
            logger.warning("批量处理器未运行，无法添加项")
            return False
        
        batch_item = BatchItem(
            data=item,
            timestamp=time.time(),
            callback=callback,
            metadata=metadata or {}
        )
        
        try:
            # 非阻塞添加
            self.queue.put_nowait(batch_item)
            return True
        except asyncio.QueueFull:
            logger.warning("批量处理队列已满，丢弃项")
            self.stats['items_dropped'] += 1
            return False
    
    async def start(self):
        """启动批量处理器"""
        if self.running:
            logger.warning("批量处理器已在运行")
            return
        
        if not self.processor_func:
            raise ValueError("必须设置处理函数")
        
        self.running = True
        self.stats['start_time'] = time.time()
        
        # 启动工作线程
        for i in range(self.worker_count):
            worker = asyncio.create_task(self._worker_loop(f"worker-{i}"))
            self.workers.append(worker)
        
        logger.info(f"批量处理器启动成功，工作线程数: {self.worker_count}")
    
    async def stop(self, timeout: float = 30.0):
        """停止批量处理器"""
        if not self.running:
            return
        
        self.running = False
        
        # 等待队列清空
        start_time = time.time()
        while not self.queue.empty() and (time.time() - start_time) < timeout:
            await asyncio.sleep(0.1)
        
        # 取消工作线程
        for worker in self.workers:
            worker.cancel()
        
        # 等待工作线程结束
        if self.workers:
            await asyncio.gather(*self.workers, return_exceptions=True)
        
        self.workers.clear()
        logger.info("批量处理器已停止")
    
    async def _worker_loop(self, worker_name: str):
        """工作线程循环"""
        logger.debug(f"工作线程启动: {worker_name}")
        
        while self.running:
            try:
                # 收集批量项
                batch = await self._collect_batch()
                
                if not batch:
                    continue
                
                # 处理批量
                await self._process_batch(batch, worker_name)
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"工作线程异常 {worker_name}: {e}")
                self.stats['processing_errors'] += 1
                await asyncio.sleep(1)  # 避免快速重试
        
        logger.debug(f"工作线程结束: {worker_name}")
    
    async def _collect_batch(self) -> List[BatchItem]:
        """收集批量项"""
        batch = []
        start_time = time.time()
        
        # 等待第一个项
        try:
            first_item = await asyncio.wait_for(
                self.queue.get(), 
                timeout=self.batch_timeout
            )
            batch.append(first_item)
        except asyncio.TimeoutError:
            return batch
        
        # 收集更多项直到达到批量大小或超时
        while len(batch) < self.batch_size:
            remaining_time = self.batch_timeout - (time.time() - start_time)
            if remaining_time <= 0:
                break
            
            try:
                item = await asyncio.wait_for(
                    self.queue.get(),
                    timeout=remaining_time
                )
                batch.append(item)
            except asyncio.TimeoutError:
                break
        
        return batch
    
    async def _process_batch(self, batch: List[BatchItem], worker_name: str):
        """处理批量"""
        if not batch:
            return
        
        start_time = time.time()
        
        try:
            # 提取数据
            items = [item.data for item in batch]
            
            # 调用处理函数
            if asyncio.iscoroutinefunction(self.processor_func):
                result = await self.processor_func(items)
            else:
                result = self.processor_func(items)
            
            # 调用回调函数
            for i, batch_item in enumerate(batch):
                if batch_item.callback:
                    try:
                        if asyncio.iscoroutinefunction(batch_item.callback):
                            await batch_item.callback(
                                batch_item.data, 
                                result[i] if isinstance(result, list) else result
                            )
                        else:
                            batch_item.callback(
                                batch_item.data,
                                result[i] if isinstance(result, list) else result
                            )
                    except Exception as e:
                        logger.error(f"回调函数异常: {e}")
            
            # 更新统计信息
            processing_time = time.time() - start_time
            self.processing_times.append(processing_time)
            self.batch_sizes.append(len(batch))
            
            self.stats['items_processed'] += len(batch)
            self.stats['batches_processed'] += 1
            self.stats['average_batch_size'] = sum(self.batch_sizes) / len(self.batch_sizes)
            self.stats['average_processing_time'] = sum(self.processing_times) / len(self.processing_times)
            
            logger.debug(f"批量处理完成 {worker_name}: {len(batch)}项, {processing_time:.3f}秒")
            
        except Exception as e:
            logger.error(f"批量处理失败 {worker_name}: {e}")
            self.stats['processing_errors'] += 1
    
    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        stats = self.stats.copy()
        
        if stats['start_time']:
            stats['uptime_seconds'] = time.time() - stats['start_time']
        
        stats['queue_size'] = self.queue.qsize()
        stats['running'] = self.running
        stats['worker_count'] = len(self.workers)
        
        return stats
    
    def get_performance_metrics(self) -> Dict[str, float]:
        """获取性能指标"""
        if not self.processing_times:
            return {}
        
        processing_times = list(self.processing_times)
        batch_sizes = list(self.batch_sizes)
        
        return {
            'avg_processing_time': sum(processing_times) / len(processing_times),
            'min_processing_time': min(processing_times),
            'max_processing_time': max(processing_times),
            'avg_batch_size': sum(batch_sizes) / len(batch_sizes),
            'min_batch_size': min(batch_sizes),
            'max_batch_size': max(batch_sizes),
            'throughput_items_per_second': self.stats['items_processed'] / max(1, time.time() - (self.stats['start_time'] or time.time()))
        }


class SignalBatchProcessor:
    """信号批量处理器（基于流式批量处理器）"""

    def __init__(self, batch_size=75, flush_interval=0.004):
        self.stream_processor = StreamBatchProcessor(batch_size, flush_interval)
        self.signal_router = None

    def set_signal_router(self, signal_router):
        """设置信号路由器"""
        self.signal_router = signal_router
        self.stream_processor.set_processor(self._process_signal_batch)

    async def process_item(self, signal):
        """处理单个信号"""
        await self.stream_processor.process_item(signal)

    async def flush(self):
        """刷新批次"""
        await self.stream_processor.flush()

    def get_stats(self):
        """获取统计信息"""
        return self.stream_processor.get_stats()
    
    async def _process_signal_batch(self, signals: List[Any]) -> List[bool]:
        """批量处理信号"""
        if not self.signal_router:
            logger.error("信号路由器未设置")
            return [False] * len(signals)
        
        results = []
        
        # 按主题分组信号
        signal_groups = {}
        for signal in signals:
            topic = getattr(signal, 'topic', 'default')
            if topic not in signal_groups:
                signal_groups[topic] = []
            signal_groups[topic].append(signal)
        
        # 批量发布每个主题的信号
        for topic, topic_signals in signal_groups.items():
            try:
                # 这里可以实现批量发布逻辑
                for signal in topic_signals:
                    success = await self.signal_router.publish_trade_signal(signal)
                    results.append(success)
            except Exception as e:
                logger.error(f"批量发布信号失败 {topic}: {e}")
                results.extend([False] * len(topic_signals))
        
        return results


class TradeBatchProcessor:
    """交易批量处理器（基于流式批量处理器）"""

    def __init__(self, batch_size=50, flush_interval=0.005):
        self.stream_processor = StreamBatchProcessor(batch_size, flush_interval)
        self.trade_executor = None

    def set_trade_executor(self, trade_executor):
        """设置交易执行器"""
        self.trade_executor = trade_executor
        self.stream_processor.set_processor(self._process_trade_batch)

    async def process_item(self, trade):
        """处理单个交易"""
        await self.stream_processor.process_item(trade)

    async def flush(self):
        """刷新批次"""
        await self.stream_processor.flush()

    def get_stats(self):
        """获取统计信息"""
        return self.stream_processor.get_stats()
    
    async def _process_trade_batch(self, trades: List[Any]) -> List[Dict[str, Any]]:
        """批量处理交易"""
        if not self.trade_executor:
            logger.error("交易执行器未设置")
            return [{'success': False, 'error': 'No executor'}] * len(trades)
        
        results = []
        
        # 按账户分组交易
        trade_groups = {}
        for trade in trades:
            account_id = getattr(trade, 'account_id', 'default')
            if account_id not in trade_groups:
                trade_groups[account_id] = []
            trade_groups[account_id].append(trade)
        
        # 批量执行每个账户的交易
        for account_id, account_trades in trade_groups.items():
            try:
                # 这里可以实现批量交易执行逻辑
                for trade in account_trades:
                    result = await self.trade_executor.execute_trade(trade)
                    results.append(result)
            except Exception as e:
                logger.error(f"批量执行交易失败 {account_id}: {e}")
                results.extend([{'success': False, 'error': str(e)}] * len(account_trades))
        
        return results



class StreamBatchProcessor:
    """流式批量处理器 - 更适合高频信号处理"""
    
    def __init__(self, 
                 batch_size: int = 50,
                 flush_interval: float = 0.005):  # 5ms刷新间隔
        self.batch_size = batch_size
        self.flush_interval = flush_interval
        
        self._current_batch = []
        self._processor_func: Optional[Callable] = None
        self._last_flush = time.time()
        
        # 统计信息
        self.stats = {
            'items_processed': 0,
            'batches_processed': 0,
            'start_time': time.time()
        }
    
    def set_processor(self, processor_func: Callable[[List[Any]], Any]):
        """设置批量处理函数"""
        self._processor_func = processor_func
    
    async def process_item(self, item: Any):
        """处理单个项目（立即处理或加入批次）"""
        if not self._processor_func:
            raise ValueError("必须先设置处理函数")
        
        self._current_batch.append(item)
        
        # 检查是否需要刷新批次
        current_time = time.time()
        should_flush = (
            len(self._current_batch) >= self.batch_size or
            (current_time - self._last_flush) >= self.flush_interval
        )
        
        if should_flush:
            await self._flush_batch()
    
    async def _flush_batch(self):
        """刷新当前批次"""
        if not self._current_batch:
            return
        
        batch = self._current_batch.copy()
        self._current_batch.clear()
        self._last_flush = time.time()
        
        try:
            # 处理批次
            if asyncio.iscoroutinefunction(self._processor_func):
                await self._processor_func(batch)
            else:
                self._processor_func(batch)
            
            # 更新统计
            self.stats['items_processed'] += len(batch)
            self.stats['batches_processed'] += 1
            
        except Exception as e:
            logger.error(f"流式批次处理错误: {e}")
    
    async def flush(self):
        """强制刷新当前批次"""
        await self._flush_batch()
    
    def get_stats(self) -> dict:
        """获取统计信息"""
        runtime = time.time() - self.stats['start_time']
        # 防止除零错误，确保runtime至少为一个很小的正数
        safe_runtime = max(runtime, 0.000001)  # 1微秒
        return {
            **self.stats,
            'runtime_seconds': runtime,
            'throughput_per_second': self.stats['items_processed'] / safe_runtime,
            'current_batch_size': len(self._current_batch)
        }



# 全局批量处理器实例
_signal_batch_processor: Optional[SignalBatchProcessor] = None
_trade_batch_processor: Optional[TradeBatchProcessor] = None


def get_signal_batch_processor() -> SignalBatchProcessor:
    """获取信号批量处理器（使用配置中心配置）"""
    global _signal_batch_processor
    if _signal_batch_processor is None:
        # Get configuration from unified config manager
        try:
            from ..core.config_manager import get_config_manager
            config_mgr = get_config_manager()
            batch_size = config_mgr.get("batch.signal_batch_size", 75)
            flush_interval = config_mgr.get("batch.signal_flush_interval", 0.004)
        except ImportError:
            # Fallback to default configuration
            batch_size = 75
            flush_interval = 0.004
        
        _signal_batch_processor = SignalBatchProcessor(
            batch_size=batch_size,
            flush_interval=flush_interval
        )
        logger.info(f"信号批处理器初始化: batch_size={batch_size}, flush_interval={flush_interval}")
    return _signal_batch_processor


def get_trade_batch_processor() -> TradeBatchProcessor:
    """获取交易批量处理器（使用配置中心配置）"""
    global _trade_batch_processor
    if _trade_batch_processor is None:
        # Get configuration from unified config manager
        try:
            from ..core.config_manager import get_config_manager
            config_mgr = get_config_manager()
            batch_size = config_mgr.get("batch.trade_batch_size", 50)
            flush_interval = config_mgr.get("batch.trade_flush_interval", 0.005)
        except ImportError:
            # Fallback to default configuration
            batch_size = 50
            flush_interval = 0.005
        
        _trade_batch_processor = TradeBatchProcessor(
            batch_size=batch_size,
            flush_interval=flush_interval
        )
        logger.info(f"交易批处理器初始化: batch_size={batch_size}, flush_interval={flush_interval}")
    return _trade_batch_processor


class DistributedBatchProcessor:
    """分布式批量处理器 - 支持跨主机批处理"""
    
    def __init__(self, 
                 processor_type: str = "signal",
                 host_id: str = None):
        self.processor_type = processor_type
        self.host_id = host_id or self._get_host_id()
        
        # Get configuration from unified config manager
        try:
            from ..core.config_manager import get_config_manager
            config_mgr = get_config_manager()
            if processor_type == "signal":
                self.batch_size = config_mgr.get("batch.signal_batch_size", 75)
                self.flush_interval = config_mgr.get("batch.signal_flush_interval", 0.004)
            else:  # trade
                self.batch_size = config_mgr.get("batch.trade_batch_size", 50)
                self.flush_interval = config_mgr.get("batch.trade_flush_interval", 0.005)
        except ImportError:
            self.batch_size = 75 if processor_type == "signal" else 50
            self.flush_interval = 0.004 if processor_type == "signal" else 0.005
        
        # 创建本地流式批处理器
        self.local_processor = StreamBatchProcessor(
            batch_size=self.batch_size,
            flush_interval=self.flush_interval
        )
        
        # 跨主机批处理队列
        self.cross_host_batches = {}  # {target_host: batch_items}
        self.cross_host_flush_times = {}  # {target_host: last_flush_time}
        
        logger.info(f"分布式批处理器初始化: {processor_type}, host={self.host_id}")
    
    def _get_host_id(self) -> str:
        """Get host ID"""
        try:
            from ..core.config_manager import get_config_manager
            config_mgr = get_config_manager()
            return config_mgr.get("system.host_id", "localhost")
        except:
            import os
            return os.getenv('HOSTNAME', 'localhost')
    
    async def process_item(self, item: Any, target_host: str = None):
        """处理项目 - 支持跨主机路由"""
        if target_host is None or target_host == self.host_id:
            # 本地处理
            await self.local_processor.process_item(item)
        else:
            # 跨主机处理 - 加入跨主机批次
            await self._add_to_cross_host_batch(item, target_host)
    
    async def _add_to_cross_host_batch(self, item: Any, target_host: str):
        """添加到跨主机批次"""
        if target_host not in self.cross_host_batches:
            self.cross_host_batches[target_host] = []
            self.cross_host_flush_times[target_host] = time.time()
        
        self.cross_host_batches[target_host].append(item)
        
        # 检查是否需要刷新
        current_time = time.time()
        batch = self.cross_host_batches[target_host]
        last_flush = self.cross_host_flush_times[target_host]
        
        should_flush = (
            len(batch) >= self.batch_size or
            (current_time - last_flush) >= self.flush_interval
        )
        
        if should_flush:
            await self._flush_cross_host_batch(target_host)
    
    async def _flush_cross_host_batch(self, target_host: str):
        """刷新跨主机批次"""
        if target_host not in self.cross_host_batches or not self.cross_host_batches[target_host]:
            return
        
        batch = self.cross_host_batches[target_host].copy()
        self.cross_host_batches[target_host].clear()
        self.cross_host_flush_times[target_host] = time.time()
        
        try:
            # 发送批次到目标主机
            await self._send_batch_to_host(batch, target_host)
            logger.debug(f"跨主机批次发送成功: {len(batch)}项 -> {target_host}")
        except Exception as e:
            logger.error(f"跨主机批次发送失败 -> {target_host}: {e}")
    
    async def _send_batch_to_host(self, batch: List[Any], target_host: str):
        """发送批次到目标主机"""
        # 这里应该通过NATS或其他消息系统发送
        # 暂时使用日志记录
        logger.info(f"发送批次到 {target_host}: {len(batch)} 项")
        # TODO: 实现实际的跨主机发送逻辑
    
    def set_processor(self, processor_func):
        """设置处理函数"""
        self.local_processor.set_processor(processor_func)
    
    async def flush_all(self):
        """刷新所有批次"""
        # 刷新本地批次
        await self.local_processor.flush()
        
        # 刷新所有跨主机批次
        for target_host in list(self.cross_host_batches.keys()):
            await self._flush_cross_host_batch(target_host)
    
    def get_stats(self) -> dict:
        """获取统计信息"""
        local_stats = self.local_processor.get_stats()
        
        # 添加跨主机统计
        cross_host_stats = {}
        for host, batch in self.cross_host_batches.items():
            cross_host_stats[host] = len(batch)
        
        return {
            **local_stats,
            'host_id': self.host_id,
            'processor_type': self.processor_type,
            'cross_host_pending': cross_host_stats,
            'total_cross_host_pending': sum(cross_host_stats.values())
        }
