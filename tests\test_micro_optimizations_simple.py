#!/usr/bin/env python3
"""
简化的微优化功能测试
避免复杂的导入依赖，直接测试核心功能
"""
import pytest
import asyncio
import time
import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 直接导入需要测试的模块
from src.utils.unified_batch_processor import AdvancedMemoryPool, BatchConfig
from src.messaging.protobuf_codec import LRUMessageCache, ProtobufCodec, ProtobufConfig
from src.infrastructure.connection_pool import BatchCommand, BatchResult


class TestMemoryPoolSimple:
    """简化的内存池测试"""
    
    def test_memory_pool_basic_operations(self):
        """测试内存池基本操作"""
        pool = AdvancedMemoryPool(max_pool_size=10)
        
        # 测试对象创建
        obj1 = pool.get_optimized_object('list')
        assert isinstance(obj1, list)
        assert pool.pool_stats['total_created'] == 1
        
        # 测试对象归还和复用
        pool.return_object('list', obj1)
        obj2 = pool.get_optimized_object('list')
        assert obj2 is obj1  # 应该是同一个对象
        assert pool.pool_stats['total_reused'] == 1
        
        print("✅ 内存池基本操作测试通过")

    def test_memory_pool_stats(self):
        """测试内存池统计功能"""
        pool = AdvancedMemoryPool(max_pool_size=5)
        
        # 创建并归还一些对象
        objects = []
        for i in range(3):
            obj = pool.get_optimized_object('batch_container')
            objects.append(obj)
        
        for obj in objects:
            pool.return_object('batch_container', obj)
        
        # 复用对象
        reused_obj = pool.get_optimized_object('batch_container')
        
        stats = pool.get_pool_stats()
        assert stats['total_created'] == 3
        assert stats['total_reused'] == 1
        assert stats['hit_rate_percent'] > 0
        
        print(f"✅ 内存池统计测试通过，命中率: {stats['hit_rate_percent']:.1f}%")


class TestLRUCacheSimple:
    """简化的LRU缓存测试"""
    
    def test_lru_cache_basic_operations(self):
        """测试LRU缓存基本操作"""
        cache = LRUMessageCache(max_size=5, ttl_seconds=300)
        
        data = b"test message"
        result = {"decoded": "test result"}
        
        # 测试缓存miss
        cached = cache.get(data)
        assert cached is None
        assert cache.stats['misses'] == 1
        
        # 测试缓存put和hit
        cache.put(data, result)
        cached = cache.get(data)
        assert cached == result
        assert cache.stats['hits'] == 1
        
        print("✅ LRU缓存基本操作测试通过")

    def test_lru_cache_eviction(self):
        """测试LRU缓存淘汰机制"""
        cache = LRUMessageCache(max_size=3, ttl_seconds=300)
        
        # 填满缓存
        for i in range(3):
            data = f"message_{i}".encode()
            cache.put(data, f"result_{i}")
        
        assert len(cache.cache) == 3
        
        # 访问第一个，使其成为最近使用
        cache.get(b"message_0")
        
        # 添加新项目，应该淘汰message_1
        cache.put(b"message_3", "result_3")
        
        assert cache.get(b"message_0") is not None  # 应该还在
        assert cache.get(b"message_1") is None      # 应该被淘汰
        assert cache.get(b"message_3") is not None  # 新加入的应该在
        
        print("✅ LRU缓存淘汰机制测试通过")

    def test_lru_cache_ttl_expiration(self):
        """测试TTL过期机制"""
        cache = LRUMessageCache(max_size=10, ttl_seconds=0.1)  # 0.1秒TTL
        
        data = b"test message"
        result = "test result"
        
        cache.put(data, result)
        assert cache.get(data) == result  # 立即获取应该成功
        
        time.sleep(0.15)  # 等待过期
        assert cache.get(data) is None    # 过期后应该返回None
        
        print("✅ LRU缓存TTL过期测试通过")


class TestProtobufCodecWithCache:
    """测试带缓存的Protobuf编解码器"""
    
    def test_codec_with_cache(self):
        """测试启用缓存的编解码器"""
        config = ProtobufConfig(
            cache_enabled=True,
            cache_max_size=50,
            cache_ttl_seconds=300,
            use_fallback_json=True
        )
        codec = ProtobufCodec(config)
        
        assert codec.cache is not None
        
        # 测试编解码
        signal_data = {
            'signal_id': 'test_001',
            'symbol': 'EURUSD',
            'action': 'BUY',
            'volume': 0.1
        }
        
        # 编码
        encoded = codec.encode_trade_signal(signal_data)
        assert encoded is not None
        
        # 第一次解码（miss）
        decoded1 = codec.decode_trade_signal(encoded)
        assert decoded1 is not None
        
        # 第二次解码（hit）
        decoded2 = codec.decode_trade_signal(encoded)
        assert decoded2 == decoded1
        
        # 检查缓存统计
        cache_stats = codec.get_cache_stats()
        assert cache_stats['hits'] >= 1
        assert cache_stats['misses'] >= 1
        
        print(f"✅ Protobuf缓存测试通过，命中率: {cache_stats['hit_rate_percent']:.1f}%")


class TestBatchCommandStructures:
    """测试批量命令结构"""
    
    def test_batch_command_creation(self):
        """测试批量命令创建"""
        command = BatchCommand(
            command_id="test_cmd_001",
            command_type="order_send",
            parameters={'symbol': 'EURUSD', 'volume': 0.1},
            priority=1,
            timeout=30.0
        )
        
        assert command.command_id == "test_cmd_001"
        assert command.command_type == "order_send"
        assert command.parameters['symbol'] == 'EURUSD'
        assert command.retry_count == 0
        assert command.max_retries == 3
        
        print("✅ 批量命令创建测试通过")

    def test_batch_result_creation(self):
        """测试批量结果创建"""
        result = BatchResult(
            command_id="test_cmd_001",
            success=True,
            result={'retcode': 10009},
            execution_time=0.025
        )
        
        assert result.command_id == "test_cmd_001"
        assert result.success is True
        assert result.result['retcode'] == 10009
        assert result.error is None
        
        print("✅ 批量结果创建测试通过")


class TestIntegratedPerformance:
    """集成性能测试"""
    
    def test_memory_pool_performance(self):
        """测试内存池性能影响"""
        pool = AdvancedMemoryPool(max_pool_size=100)
        
        # 不使用内存池的情况
        start_time = time.time()
        objects_without_pool = []
        for i in range(1000):
            obj = {'items': [], 'metadata': {}, 'processed': False}
            objects_without_pool.append(obj)
        time_without_pool = time.time() - start_time
        
        # 使用内存池的情况
        start_time = time.time()
        objects_with_pool = []
        for i in range(1000):
            obj = pool.get_optimized_object('batch_container')
            objects_with_pool.append(obj)
            # 每10个归还一些以触发复用
            if i % 10 == 0 and i > 0:
                for j in range(5):
                    if j < len(objects_with_pool):
                        pool.return_object('batch_container', objects_with_pool[j])
        time_with_pool = time.time() - start_time
        
        pool_stats = pool.get_pool_stats()
        
        print(f"✅ 内存池性能测试:")
        print(f"   不使用内存池: {time_without_pool:.4f}s")
        print(f"   使用内存池: {time_with_pool:.4f}s")
        print(f"   复用率: {pool_stats['hit_rate_percent']:.1f}%")

    def test_cache_performance(self):
        """测试缓存性能影响"""
        config = ProtobufConfig(cache_enabled=True, use_fallback_json=True)
        codec = ProtobufCodec(config)
        
        # 准备测试数据
        signals = []
        for i in range(10):
            signal = {
                'signal_id': f'perf_test_{i}',
                'symbol': 'EURUSD',
                'action': 'BUY'
            }
            signals.append(signal)
        
        # 编码所有信号
        encoded_signals = []
        for signal in signals:
            encoded = codec.encode_trade_signal(signal)
            encoded_signals.append(encoded)
        
        # 多次解码测试（利用缓存）
        start_time = time.time()
        for _ in range(50):  # 每个信号解码5次
            for encoded in encoded_signals:
                decoded = codec.decode_trade_signal(encoded)
        decode_time = time.time() - start_time
        
        cache_stats = codec.get_cache_stats()
        
        print(f"✅ 缓存性能测试:")
        print(f"   解码时间: {decode_time:.4f}s (500次解码)")
        print(f"   平均每次: {decode_time/500*1000:.2f}ms")
        print(f"   缓存命中率: {cache_stats['hit_rate_percent']:.1f}%")


def test_all_micro_optimizations():
    """运行所有微优化测试"""
    print("\n🚀 开始微优化功能测试...")
    
    # 内存池测试
    print("\n📦 内存池测试:")
    memory_tests = TestMemoryPoolSimple()
    memory_tests.test_memory_pool_basic_operations()
    memory_tests.test_memory_pool_stats()
    
    # LRU缓存测试
    print("\n🎯 LRU缓存测试:")
    cache_tests = TestLRUCacheSimple()
    cache_tests.test_lru_cache_basic_operations()
    cache_tests.test_lru_cache_eviction()
    cache_tests.test_lru_cache_ttl_expiration()
    
    # 编解码器缓存测试
    print("\n📡 编解码器缓存测试:")
    codec_tests = TestProtobufCodecWithCache()
    codec_tests.test_codec_with_cache()
    
    # 批量命令测试
    print("\n⚡ 批量命令测试:")
    batch_tests = TestBatchCommandStructures()
    batch_tests.test_batch_command_creation()
    batch_tests.test_batch_result_creation()
    
    # 性能测试
    print("\n📊 集成性能测试:")
    perf_tests = TestIntegratedPerformance()
    perf_tests.test_memory_pool_performance()
    perf_tests.test_cache_performance()
    
    print("\n✅ 所有微优化测试完成！")


if __name__ == '__main__':
    test_all_micro_optimizations()