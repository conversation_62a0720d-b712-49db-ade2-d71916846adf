groups:
- name: mt5_trading_alerts
  rules:
  - alert: MT5SystemDown
    annotations:
      description: "MT5\u4EA4\u6613\u7CFB\u7EDF\u5DF2\u79BB\u7EBF\u8D85\u8FC71\u5206\
        \u949F"
      summary: "MT5\u4EA4\u6613\u7CFB\u7EDF\u79BB\u7EBF"
    expr: up{job="mt5-system"} == 0
    for: 1m
    labels:
      severity: critical
  - alert: HighSignalLatency
    annotations:
      description: "P95\u5EF6\u8FDF\u8D85\u8FC7100ms\uFF0C\u5F53\u524D\u503C: {{ $value\
        \ }}s"
      summary: "\u4EA4\u6613\u4FE1\u53F7\u5EF6\u8FDF\u8FC7\u9AD8"
    expr: histogram_quantile(0.95, rate(mt5_signal_latency_seconds_bucket[5m])) >
      0.1
    for: 2m
    labels:
      severity: warning
  - alert: HighErrorRate
    annotations:
      description: "\u7CFB\u7EDF\u9519\u8BEF\u7387\u8D85\u8FC7\u9608\u503C: {{ $value\
        \ }}/s"
      summary: "\u9519\u8BEF\u7387\u8FC7\u9AD8"
    expr: rate(mt5_errors_total[5m]) > 0.1
    for: 1m
    labels:
      severity: warning
  - alert: NATSJetStreamDown
    annotations:
      description: "NATS JetStream\u670D\u52A1\u4E0D\u53EF\u7528"
      summary: "NATS JetStream\u79BB\u7EBF"
    expr: up{job="nats-jetstream"} == 0
    for: 30s
    labels:
      severity: critical
