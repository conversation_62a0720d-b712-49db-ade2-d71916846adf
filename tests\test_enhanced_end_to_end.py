#!/usr/bin/env python3
"""
增强的端到端测试
修复消息格式问题，完善RPC调用测试，添加实际消息传递验证
"""

import asyncio
import json
import os
import sys
import tempfile
import time
import uuid
from pathlib import Path
from typing import Dict, Any, List

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# 导入真实组件
try:
    from src.core.mt5_account_monitor import MT5AccountMonitor
    from src.core.mt5_account_executor import MT5AccountExecutor
    from src.core.mt5_rpc_client import MT5RPCClient
    from src.core.mt5_request_handler import MT5RequestHandler
    from src.messaging.jetstream_client import JetStreamClient
    from src.messaging.message_types import MessageEnvelope, TradeSignal, SignalType, PositionSignalData
    from src.messaging.message_codec import MessageCodec

    REAL_COMPONENTS_AVAILABLE = True
    print("✅ 增强端到端测试组件导入成功")

except ImportError as e:
    print(f"❌ 导入组件失败: {e}")
    sys.exit(1)


class EnhancedEndToEndTest:
    """增强的端到端测试类"""
    
    def __init__(self):
        self.test_data_dir = Path(tempfile.mkdtemp(prefix="enhanced_e2e_test_"))
        self.jetstream_clients = []
        self.components = []
        self.received_messages = []
        self.rpc_responses = []
        print(f"测试数据目录: {self.test_data_dir}")
    
    def cleanup(self):
        """清理测试资源"""
        import shutil
        try:
            # 停止所有组件
            for component in self.components:
                if hasattr(component, 'stop') and asyncio.iscoroutinefunction(component.stop):
                    asyncio.create_task(component.stop())
                elif hasattr(component, 'running'):
                    component.running = False
            
            # 断开所有JetStream连接
            for client in self.jetstream_clients:
                if hasattr(client, 'disconnect'):
                    asyncio.create_task(client.disconnect())
            
            shutil.rmtree(self.test_data_dir)
            print("🧹 增强端到端测试环境清理完成")
        except Exception as e:
            print(f"⚠️ 清理警告: {e}")
    
    async def setup_enhanced_infrastructure(self):
        """设置增强的基础设施"""
        print("\n🏗️ 设置增强的端到端基础设施...")
        
        try:
            # 创建主JetStream客户端
            main_config = {
                'servers': ['nats://localhost:4222'],
                'stream_name': 'ENHANCED_E2E_MAIN',
                'subjects': ['ENHANCED.E2E.>']
            }
            
            main_client = JetStreamClient(main_config)
            connected = await main_client.connect()
            
            if not connected:
                print("  ❌ 无法连接到NATS服务器")
                return None, None, None
            
            print("  ✅ 主JetStream客户端连接成功")
            self.jetstream_clients.append(main_client)
            
            # 创建监控器专用客户端
            monitor_config = {
                'servers': ['nats://localhost:4222'],
                'stream_name': 'ENHANCED_E2E_MONITOR',
                'subjects': ['ENHANCED.MONITOR.>']
            }
            
            monitor_client = JetStreamClient(monitor_config)
            await monitor_client.connect()
            print("  ✅ 监控器JetStream客户端连接成功")
            self.jetstream_clients.append(monitor_client)
            
            # 创建执行器专用客户端
            executor_config = {
                'servers': ['nats://localhost:4222'],
                'stream_name': 'ENHANCED_E2E_EXECUTOR',
                'subjects': ['ENHANCED.EXECUTOR.>']
            }
            
            executor_client = JetStreamClient(executor_config)
            await executor_client.connect()
            print("  ✅ 执行器JetStream客户端连接成功")
            self.jetstream_clients.append(executor_client)
            
            return main_client, monitor_client, executor_client
            
        except Exception as e:
            print(f"  ❌ 增强基础设施设置失败: {e}")
            return None, None, None
    
    async def test_message_envelope_flow(self):
        """测试MessageEnvelope消息流"""
        print("\n📨 测试MessageEnvelope消息流...")
        
        # 设置基础设施
        main_client, monitor_client, executor_client = await self.setup_enhanced_infrastructure()
        if not main_client:
            return False
        
        try:
            # 创建测试交易信号
            signal_data = PositionSignalData(
                symbol='EURUSD',
                volume=0.1,
                action='BUY',
                price=1.1234,
                sl=1.1200,
                tp=1.1300,
                magic=12345,
                account_id='ENHANCED_MASTER_001',
                copy_magic_number=12345
            )

            test_signal = TradeSignal(
                type=SignalType.POSITION_OPEN,
                master_id='ENHANCED_MASTER_001',
                ticket=1000001,
                data=signal_data,
                timestamp=time.time()
            )
            
            # 创建消息信封
            message_envelope = MessageEnvelope.create_trade_signal(
                signal=test_signal,
                subject='ENHANCED.MONITOR.TRADE_SIGNAL'
            )
            
            print(f"  ✅ 创建消息信封: {message_envelope.id}")
            
            # 设置消息接收器
            received_messages = []
            
            async def message_handler(msg_envelope):
                received_messages.append(msg_envelope)
                print(f"  📥 接收到消息: {msg_envelope.id}, 主题: {msg_envelope.subject}")
            
            # 订阅消息
            await executor_client.subscribe(
                subject='ENHANCED.MONITOR.TRADE_SIGNAL',
                callback=message_handler
            )
            
            print("  ✅ 执行器订阅消息成功")
            
            # 发布消息
            success = await monitor_client.publish_message(message_envelope)
            
            if success:
                print("  ✅ 监控器发布消息成功")
            else:
                print("  ❌ 监控器发布消息失败")
                return False
            
            # 等待消息传递
            await asyncio.sleep(2)
            
            # 验证消息接收
            if received_messages:
                received_msg = received_messages[0]
                print(f"  ✅ 消息传递成功: 发送ID={message_envelope.id}, 接收ID={received_msg.id}")
                
                # 验证消息内容
                if received_msg.payload.get('symbol') == 'EURUSD':
                    print("  ✅ 消息内容验证成功")
                    return True
                else:
                    print("  ❌ 消息内容验证失败")
                    return False
            else:
                print("  ⚠️ 未接收到消息")
                return False
                
        except Exception as e:
            print(f"  ❌ MessageEnvelope消息流测试失败: {e}")
            return False
    
    async def test_enhanced_rpc_communication(self):
        """测试增强的RPC通信"""
        print("\n🔌 测试增强的RPC通信...")
        
        # 设置基础设施
        main_client, _, _ = await self.setup_enhanced_infrastructure()
        if not main_client:
            return False
        
        try:
            # 创建增强的MT5ProcessManager
            class EnhancedMT5ProcessManager:
                def __init__(self):
                    self.request_count = 0
                    self.request_history = []

                def is_account_available(self, account_id: str) -> bool:
                    """检查账户是否可用"""
                    return True  # 测试环境中总是返回True
                
                async def handle_request(self, request_data):
                    self.request_count += 1
                    self.request_history.append(request_data)

                    # 支持新的请求格式 (account_id + command)
                    method = request_data.get('command', request_data.get('method', 'unknown'))
                    params = request_data.get('params', {})
                    account_id = request_data.get('account_id', params.get('account_id', 'unknown'))
                    
                    print(f"    📞 处理RPC请求: {method} for {account_id}")
                    
                    # 模拟不同方法的详细响应
                    if method == 'get_account_info':
                        return {
                            "status": "success",
                            "data": {
                                "account_id": account_id,
                                "balance": 10000.0 + self.request_count * 10,
                                "equity": 10000.0 + self.request_count * 10,
                                "margin": 0.0,
                                "free_margin": 10000.0 + self.request_count * 10,
                                "server": "Enhanced-Demo",
                                "currency": "USD",
                                "leverage": 100
                            },
                            "timestamp": time.time(),
                            "request_id": request_data.get('request_id', '')
                        }
                    elif method == 'get_positions':
                        return {
                            "status": "success",
                            "data": {
                                "positions": [
                                    {
                                        "ticket": 1000000 + self.request_count,
                                        "symbol": "EURUSD",
                                        "type": "BUY",
                                        "volume": 0.1,
                                        "open_price": 1.1234,
                                        "current_price": 1.1240,
                                        "profit": 6.0
                                    }
                                ],
                                "count": 1
                            },
                            "timestamp": time.time()
                        }
                    elif method == 'place_order':
                        return {
                            "status": "success",
                            "data": {
                                "order_id": f"ORDER_{self.request_count:06d}",
                                "ticket": 2000000 + self.request_count,
                                "symbol": params.get('symbol', 'EURUSD'),
                                "volume": params.get('volume', 0.1),
                                "price": params.get('price', 1.1234),
                                "execution_time": time.time()
                            }
                        }
                    else:
                        return {
                            "status": "error",
                            "error": f"Unknown method: {method}",
                            "timestamp": time.time()
                        }
            
            enhanced_manager = EnhancedMT5ProcessManager()
            
            # 创建RPC组件
            rpc_client = MT5RPCClient(main_client)
            rpc_handler = MT5RequestHandler(main_client, enhanced_manager)
            
            print("  ✅ 增强RPC组件创建成功")
            self.components.extend([rpc_client, rpc_handler])
            
            # 启动RPC处理器
            await rpc_handler.start()
            print("  ✅ RPC处理器启动成功")
            
            # 等待处理器完全启动
            await asyncio.sleep(1)
            
            # 测试多种RPC调用
            test_calls = [
                {
                    'account_id': 'ENHANCED_TEST_001',
                    'method': 'get_account_info',
                    'params': {'account_id': 'ENHANCED_TEST_001'},
                    'expected_status': 'success'
                },
                {
                    'account_id': 'ENHANCED_TEST_001',
                    'method': 'get_positions',
                    'params': {'account_id': 'ENHANCED_TEST_001'},
                    'expected_status': 'success'
                },
                {
                    'account_id': 'ENHANCED_TEST_001',
                    'method': 'place_order',
                    'params': {
                        'account_id': 'ENHANCED_TEST_001',
                        'symbol': 'GBPUSD',
                        'action': 'BUY',
                        'volume': 0.2,
                        'price': 1.2500
                    },
                    'expected_status': 'success'
                }
            ]
            
            successful_calls = 0
            
            for i, test_call in enumerate(test_calls):
                try:
                    print(f"  📞 RPC调用 {i+1}: {test_call['method']}")
                    
                    response = await rpc_client.call_rpc(
                        account_id=test_call['account_id'],
                        method=test_call['method'],
                        params=test_call['params'],
                        timeout=5.0
                    )
                    
                    if response and response.get('status') == test_call['expected_status']:
                        print(f"    ✅ 调用成功: {response.get('status')}")
                        if 'data' in response:
                            print(f"    📊 响应数据: {list(response['data'].keys())}")
                        successful_calls += 1
                    else:
                        print(f"    ⚠️ 调用结果异常: {response}")
                        
                except asyncio.TimeoutError:
                    print(f"    ⚠️ 调用超时: {test_call['method']}")
                except Exception as e:
                    print(f"    ❌ 调用失败: {e}")
            
            # 停止RPC处理器
            await rpc_handler.stop()
            
            success_rate = successful_calls / len(test_calls)
            print(f"  📊 增强RPC调用成功率: {successful_calls}/{len(test_calls)} ({success_rate*100:.1f}%)")
            print(f"  📊 处理器处理请求数: {enhanced_manager.request_count}")
            
            return success_rate >= 0.66  # 66%以上算成功
            
        except Exception as e:
            print(f"  ❌ 增强RPC通信测试失败: {e}")
            return False
    
    async def test_actual_message_verification(self):
        """测试实际消息传递验证"""
        print("\n🔍 测试实际消息传递验证...")
        
        # 设置基础设施
        main_client, monitor_client, executor_client = await self.setup_enhanced_infrastructure()
        if not main_client:
            return False
        
        try:
            # 创建消息验证器
            class MessageVerifier:
                def __init__(self):
                    self.sent_messages = {}
                    self.received_messages = {}
                    self.verification_results = []
                
                def record_sent(self, message_id: str, content: Dict[str, Any]):
                    self.sent_messages[message_id] = {
                        'content': content,
                        'sent_time': time.time()
                    }
                
                def record_received(self, message_id: str, content: Dict[str, Any]):
                    self.received_messages[message_id] = {
                        'content': content,
                        'received_time': time.time()
                    }
                
                def verify_message(self, message_id: str) -> Dict[str, Any]:
                    if message_id not in self.sent_messages:
                        return {'status': 'error', 'reason': 'message_not_sent'}
                    
                    if message_id not in self.received_messages:
                        return {'status': 'error', 'reason': 'message_not_received'}
                    
                    sent = self.sent_messages[message_id]
                    received = self.received_messages[message_id]
                    
                    # 验证内容一致性
                    content_match = sent['content'] == received['content']
                    
                    # 计算传输延迟
                    latency = received['received_time'] - sent['sent_time']
                    
                    result = {
                        'status': 'success' if content_match else 'content_mismatch',
                        'content_match': content_match,
                        'latency_ms': latency * 1000,
                        'sent_time': sent['sent_time'],
                        'received_time': received['received_time']
                    }
                    
                    self.verification_results.append(result)
                    return result
                
                def get_summary(self) -> Dict[str, Any]:
                    total_sent = len(self.sent_messages)
                    total_received = len(self.received_messages)
                    total_verified = len(self.verification_results)
                    
                    successful_verifications = sum(1 for r in self.verification_results if r['status'] == 'success')
                    
                    if self.verification_results:
                        avg_latency = sum(r['latency_ms'] for r in self.verification_results) / len(self.verification_results)
                        max_latency = max(r['latency_ms'] for r in self.verification_results)
                        min_latency = min(r['latency_ms'] for r in self.verification_results)
                    else:
                        avg_latency = max_latency = min_latency = 0
                    
                    return {
                        'total_sent': total_sent,
                        'total_received': total_received,
                        'total_verified': total_verified,
                        'successful_verifications': successful_verifications,
                        'success_rate': (successful_verifications / total_verified * 100) if total_verified > 0 else 0,
                        'avg_latency_ms': avg_latency,
                        'max_latency_ms': max_latency,
                        'min_latency_ms': min_latency
                    }
            
            verifier = MessageVerifier()
            
            # 设置消息接收器
            async def verified_message_handler(msg_envelope):
                verifier.record_received(msg_envelope.id, msg_envelope.payload)
                print(f"    📥 验证接收: {msg_envelope.id}")
            
            # 订阅消息
            await executor_client.subscribe(
                subject='ENHANCED.VERIFICATION.>',
                callback=verified_message_handler
            )
            
            print("  ✅ 消息验证器设置完成")
            
            # 发送多条测试消息
            test_messages = []
            for i in range(10):
                signal_data = PositionSignalData(
                    symbol=['EURUSD', 'GBPUSD', 'USDJPY'][i % 3],
                    volume=0.01 * (i + 1),
                    action=['BUY', 'SELL'][i % 2],
                    price=1.1000 + i * 0.0001,
                    sl=1.0900 + i * 0.0001,
                    tp=1.1100 + i * 0.0001,
                    magic=12345 + i,
                    account_id='VERIFICATION_MASTER',
                    copy_magic_number=12345 + i
                )

                signal = TradeSignal(
                    type=SignalType.POSITION_OPEN,
                    master_id='VERIFICATION_MASTER',
                    ticket=1000000 + i,
                    data=signal_data,
                    timestamp=time.time()
                )
                
                envelope = MessageEnvelope.create_trade_signal(
                    signal=signal,
                    subject=f'ENHANCED.VERIFICATION.MSG_{i:03d}'
                )
                
                test_messages.append(envelope)
            
            # 发送消息并记录
            for envelope in test_messages:
                verifier.record_sent(envelope.id, envelope.payload)
                success = await monitor_client.publish_message(envelope)
                if success:
                    print(f"    📤 发送成功: {envelope.id}")
                else:
                    print(f"    ❌ 发送失败: {envelope.id}")
            
            # 等待消息传递
            await asyncio.sleep(3)
            
            # 验证所有消息
            for envelope in test_messages:
                result = verifier.verify_message(envelope.id)
                if result['status'] == 'success':
                    print(f"    ✅ 验证成功: {envelope.id}, 延迟: {result['latency_ms']:.2f}ms")
                else:
                    print(f"    ❌ 验证失败: {envelope.id}, 原因: {result.get('reason', 'unknown')}")
            
            # 获取验证总结
            summary = verifier.get_summary()
            print(f"  📊 消息验证总结:")
            print(f"    发送: {summary['total_sent']}, 接收: {summary['total_received']}")
            print(f"    验证成功率: {summary['success_rate']:.1f}%")
            print(f"    平均延迟: {summary['avg_latency_ms']:.2f}ms")
            print(f"    延迟范围: {summary['min_latency_ms']:.2f}-{summary['max_latency_ms']:.2f}ms")
            
            return summary['success_rate'] >= 80  # 80%成功率算通过
            
        except Exception as e:
            print(f"  ❌ 实际消息传递验证测试失败: {e}")
            return False
    
    async def run_all_tests(self):
        """运行所有增强端到端测试"""
        print("🚀 开始增强端到端测试...")
        
        test_results = {}
        
        # 运行各项测试
        test_results['message_envelope_flow'] = await self.test_message_envelope_flow()
        test_results['enhanced_rpc_communication'] = await self.test_enhanced_rpc_communication()
        test_results['actual_message_verification'] = await self.test_actual_message_verification()
        
        # 统计结果
        passed = sum(1 for result in test_results.values() if result)
        total = len(test_results)
        success_rate = (passed / total) * 100
        
        print(f"\n📊 增强端到端测试结果:")
        print(f"总测试数: {total}")
        print(f"通过: {passed}")
        print(f"失败: {total - passed}")
        print(f"成功率: {success_rate:.1f}%")
        
        print(f"\n📋 详细结果:")
        for test_name, result in test_results.items():
            status = "✅ 通过" if result else "❌ 失败"
            print(f"  {test_name}: {status}")
        
        return success_rate >= 66  # 66%以上算成功


async def main():
    """主函数"""
    if not REAL_COMPONENTS_AVAILABLE:
        print("❌ 增强端到端测试组件不可用，无法运行测试")
        return False
    
    test_suite = EnhancedEndToEndTest()
    
    try:
        success = await test_suite.run_all_tests()
        
        if success:
            print("\n🎉 增强端到端测试成功!")
        else:
            print("\n⚠️ 增强端到端测试部分失败")
        
        return success
        
    finally:
        test_suite.cleanup()


if __name__ == '__main__':
    result = asyncio.run(main())
    sys.exit(0 if result else 1)
