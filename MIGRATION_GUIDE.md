# 🚨 消息队列子系统硬迁移指南

## ⚠️ 重要通知

**自本次架构更新起，消息队列子系统已重构为单一权威实现 `HybridQueueManager`。**

### 📌 零向后兼容策略 (Zero Backward Compatibility)

- ❌ **不保留** 任何旧队列实现的兼容接口
- ❌ **不支持** 灰度或并行运行旧消息队列
- ❌ **不提供** 旧接口的fallback机制
- ✅ **强制统一** 所有消息传递路径经过 `HybridQueueManager`

## 🗑️ 已弃用组件清单

### 立即废弃的文件 (DEPRECATED FILES)

以下文件将在本次合并后**立即删除**，不再维护：

```
src/messaging/nats_manager.py          → 已废弃，使用 HybridQueueManager
src/messaging/nats_client.py           → 已废弃，使用 HybridQueueManager  
src/messaging/jetstream_client.py      → 已废弃，使用 HybridQueueManager
src/messaging/message_codec.py         → 已废弃，功能合并到新实现
src/messaging/protobuf_codec.py        → 已废弃，功能合并到新实现
```

### 立即废弃的类和接口

```python
# ❌ 已废弃 - 不再使用
from src.messaging.nats_manager import NATSManager
from src.messaging.nats_client import NATSClient
from src.messaging.jetstream_client import JetStreamClient
from src.messaging.message_codec import MessageCodec
from src.messaging.protobuf_codec import ProtobufCodec

# ✅ 新的统一实现 - 强制使用
from src.messaging.hybrid_queue_manager import HybridQueueManager
from src.messaging.message_queue_interface import MessageQueueInterface
```

## 🔄 强制替换映射表

### 核心组件替换

| 旧组件 | 新组件 | 说明 |
|--------|--------|------|
| `NATSManager` | `HybridQueueManager` | 统一队列管理 |
| `NATSClient` | `HybridQueueManager.backends[NATS]` | 通过管理器访问 |
| `JetStreamClient` | `HybridQueueManager.backends[NATS]` | 通过管理器访问 |
| `MessageCodec` | `MessageEnvelope` | 统一消息格式 |
| `ProtobufCodec` | `MessageEnvelope` | 统一消息格式 |

### 方法映射

| 旧方法调用 | 新方法调用 |
|------------|------------|
| `nats_manager.publish_with_priority(data, priority)` | `hybrid_manager.publish(subject, message, priority)` |
| `jetstream.subscribe(subject, callback)` | `hybrid_manager.subscribe(subject, callback)` |
| `nats_client.request(subject, data)` | `hybrid_manager.request(subject, message)` |
| `codec.encode(message)` | `MessageEnvelope(payload=message)` |
| `codec.decode(data)` | `message.payload` |

## 📋 迁移检查清单

### 必须完成的强制替换

- [ ] **核心协调器** (`src/core/mt5_coordinator.py`)
  - 替换 NATSManager 为 HybridQueueManager
  - 更新初始化逻辑
  
- [ ] **账户执行器** (`src/core/mt5_account_executor.py`)
  - 替换 JetStreamClient 为 HybridQueueManager
  - 更新消息发布逻辑
  
- [ ] **账户监控器** (`src/core/mt5_account_monitor.py`)
  - 替换 JetStreamClient 为 HybridQueueManager
  - 更新订阅逻辑
  
- [ ] **进程运行器** (`src/core/separated_process_runners.py`)
  - 替换所有 JetStreamClient 实例化
  - 更新消息传递逻辑
  
- [ ] **分布式组件** (`src/distributed/`)
  - service_registry.py
  - failover_manager.py  
  - account_registry.py
  
- [ ] **多终端管理** (`src/multi_terminal/`)
  - terminal_manager.py
  - independent_role_manager.py
  - base_service.py
  
- [ ] **API层** (`src/api/`, `src/web/`)
  - role_management.py
  - terminal_management_api.py
  
- [ ] **基础设施** (`src/infrastructure/`)
  - production_optimizer.py
  - environment_manager.py

### 配置文件更新

- [ ] **删除旧配置项**
  ```yaml
  # ❌ 删除这些配置
  nats:
    servers: [...]
  redis:
    host: localhost
  jetstream:
    streams: [...]
  ```

- [ ] **使用新配置**
  ```yaml
  # ✅ 使用统一配置
  queue_manager:
    primary_backend: "nats"
    backup_backends: ["redis_streams"]
    enable_dual_write: true
  ```

### 测试文件更新

- [ ] **更新所有测试文件** (20+ files)
  - 替换旧组件导入
  - 更新测试用例
  - 修复断言逻辑

### 脚本和工具更新

- [ ] **工具脚本** (`scripts/`)
  - health_check.py
  - collect_logs.py
  - mt5_launcher.py
  
- [ ] **示例代码** (`examples/`)
  - manual_role_example.py
  - multi_terminal_example.py

## 🚫 禁止行为

### 严格禁止的代码模式

```python
# ❌ 绝对禁止 - 直接导入旧组件
from src.messaging.nats_manager import NATSManager

# ❌ 绝对禁止 - 绕过抽象层
jetstream_client = JetStreamClient()
jetstream_client.publish(...)

# ❌ 绝对禁止 - 旧配置项
config = {'nats_servers': [...]}

# ❌ 绝对禁止 - 兼容性适配器
if hasattr(self, 'old_nats_manager'):
    # 兼容逻辑
```

### CI/CD 检查点

以下检查将在CI中强制执行：

```bash
# 检查禁止导入
grep -r "from.*nats_manager import" src/ && exit 1
grep -r "from.*nats_client import" src/ && exit 1  
grep -r "from.*jetstream_client import" src/ && exit 1

# 检查禁止实例化
grep -r "NATSManager(" src/ && exit 1
grep -r "JetStreamClient(" src/ && exit 1
```

## ✅ 验证迁移完成

### 验证命令

```bash
# 1. 验证无旧组件引用
python scripts/validate_migration_complete.py

# 2. 验证新组件正常工作  
python scripts/validate_hybrid_queue_config.py --test-connections

# 3. 运行完整测试套件
pytest tests/ -v

# 4. 验证配置正确性
python -c "
from src.messaging.hybrid_queue_manager import HybridQueueManager
print('✅ 迁移验证通过')
"
```

### 成功标准

- ✅ 无任何旧组件直接引用
- ✅ 所有消息传递通过 HybridQueueManager
- ✅ 测试套件100%通过
- ✅ 配置验证通过
- ✅ 生产环境健康检查通过

## 🆘 故障排除

### 常见迁移问题

1. **导入错误**
   ```
   ImportError: cannot import name 'NATSManager'
   ```
   **解决**: 替换为 `from src.messaging.hybrid_queue_manager import HybridQueueManager`

2. **方法不存在**
   ```
   AttributeError: 'HybridQueueManager' object has no attribute 'old_method'
   ```
   **解决**: 查看方法映射表，使用新的统一接口

3. **配置错误**
   ```
   KeyError: 'nats_servers'
   ```
   **解决**: 更新为新的配置结构

### 紧急回退

**注意**: 本次迁移不提供回退机制。如遇到严重问题：

1. 立即停止部署
2. 联系架构团队
3. 基于备份环境恢复服务
4. 在开发环境完成修复后重新部署

## 📞 支持联系

- **架构问题**: 联系系统架构师
- **迁移问题**: 查看本文档或提交Issue
- **紧急故障**: 遵循生产环境故障响应流程

---

**⚠️ 重要提醒**: 这是一次不可逆的硬迁移。请确保在生产环境部署前完成所有验证步骤。