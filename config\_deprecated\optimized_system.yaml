imports:
  - "distributed/hosts.yaml"
  - "pairings/dynamic_pairings.yaml"
  - "accounts/*.yaml" # 导入所有账户配置

accounts:
  auto_discovery: true
  config_dir: config/accounts
control_plane:
  coordinator:
    failure_detection_timeout: 90
    health_check_interval: 30
    heartbeat_interval: 15
    latency_check_interval: 120
    latency_threshold_ms: 100
    max_missed_heartbeats: 3
    metrics_collection_interval: 60
  failover:
    detection_interval: 30
    enabled: true
    recovery_timeout: 300
    strategies:
    - backup_master
    - load_balancing
    - graceful_degradation
data_plane:
  master_monitor:
    batch_size: 50
    batch_timeout_ms: 50
    compression_enabled: true
    compression_threshold: 1024
    flush_interval_ms: 100
    heartbeat_interval: 10
    metrics_interval: 30
    signal_buffer_size: 100
  slave_executor:
    ack_wait: 30
    execution_batch_size: 5
    execution_queue_size: 1000
    execution_timeout: 30
    max_concurrent_executions: 10
    retry_attempts: 3
    retry_delay: 1
    subscription_timeout: 60
deployment:
  debug: false
  environment: production
  graceful_shutdown:
    enabled: true
    timeout: 30
  health_check:
    enabled: true
    endpoint: /health
    interval: 30
    timeout: 10
  resources:
    cpu_limit: '2.0'
    memory_limit: 4Gi
distributed:
  hosts_config: config/distributed/hosts.yaml
  pairing_config: config/distributed/active_pairings.yaml
extensions:
  custom:
    trading_hours:
      end: 23:00
      start: 22:00
      timezone: Europe/London
  plugins:
    enabled: []
host:
  datacenter: ${DATACENTER:-primary}
  id: ${HOST_ID:-production_host_001}
  ip_address: ${HOST_IP:-*************}
  region: ${HOST_REGION:-asia-east}
monitoring:
  logging:
    backup_count: 5
    file: logs/mt5_production.log
    filters:
    - exclude_debug
    - exclude_third_party
    format: json
    level: INFO
    max_size_mb: 100
  prometheus:
    enabled: true
    metrics:
    - trading_signals_total
    - execution_latency_seconds
    - error_rate
    - system_health
    - memory_usage
    - cpu_usage
    path: /metrics
    port: 8000
# NATS配置 - 支持混合部署架构
nats:
  connection:
    max_pings_out: 3
    max_reconnect: 60
    ping_interval: 30
    reconnect_wait: 2
    timeout: 10
  jetstream:
    control_plane_subjects:
    - MT5.CONTROL.HEARTBEAT.*
    - MT5.CONTROL.COMMAND.*
    - MT5.CONTROL.METRICS.*
    - MT5.CONTROL.PING.*
    - MT5.CONTROL.PONG.*
    - MT5.CONTROL.FAILOVER.*
    data_plane_subjects:
    - MT5.TRADES.*
    - MT5.SIGNALS.*
    - MT5.EXECUTION.*
    duplicate_window: 120
    max_age: 14400
    max_bytes: 21474836480
    max_msg_size: 1048576
    max_msgs: 20000000
    replicas: 1
    retention: limits
    storage: file
    stream_name: MT5_PRODUCTION_STREAM
  servers:
  # 主要NATS服务器
  - nats://localhost:4222
  # 备用服务器（如果有）
  - nats://127.0.0.1:4222
  # Docker环境服务器
  external_servers:
  - nats://nats:4222
optimization:
  optimizations_applied:
  - system_resources
  - performance_parameters
  - network_configuration
  - storage_configuration
  system_info:
    cpu_count: 12
    disk_free_gb: 750
    memory_gb: 15
  timestamp: '**********.5479157'
performance:
  asyncio:
    event_loop_policy: uvloop
    max_workers: 20
  memory:
    gc_threshold:
    - 700
    - 10
    - 10
    max_memory_usage_mb: 3840
  messaging:
    compression_level: 6
    max_message_size: 1048576
    serialization_format: msgpack
  network:
    socket_timeout: 30
    tcp_keepalive: true
    tcp_nodelay: true
# Redis配置 - 支持混合部署架构
redis:
  cache:
    account_info_ttl: 1800
    default_ttl: 3600
    signal_cache_ttl: 300
  connection_pool:
    max_connections: 100
    retry_on_timeout: true
    socket_connect_timeout: 5
    socket_keepalive: true
    socket_keepalive_options: {}
    socket_timeout: 5
  db: 0
  decode_responses: true
  encoding: utf-8
  # Docker环境：容器内使用容器名
  host: ${REDIS_HOST:-redis}
  password: ${REDIS_PASSWORD:-}
  port: ${REDIS_PORT:-6379}
  # 宿主机环境：MT5客户端使用localhost连接Docker暴露的端口
  external_host: localhost
  external_port: 6379
security:
  access_control:
    enabled: false
    whitelist_ips: []
  authentication:
    enabled: false
    token_ttl: 3600
  encryption:
    algorithm: AES-256-GCM
    enabled: false

# ============================================================================
# 跟单关系配置
# ============================================================================
copy_relationships:
  - master_account: "ACC001"
    slave_account: "ACC002"
    copy_mode: "forward"
    copy_ratio: 1.0
    max_volume: 100.0
    min_volume: 0.001  # 修复：允许更小的手数
    risk_multiplier: 1.0
    reverse_sl_tp: false
    enabled: true
