#!/usr/bin/env python3
"""
分布式配置验证工具
验证分布式系统配置的完整性和一致性
使用方法: python scripts/validate_distributed_config.py
"""

import sys
import os
import json
from pathlib import Path
from typing import Dict, List, Optional, Any, Tuple
import re

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent.parent))

from ...src.utils.logger import get_logger, MT5Logger, setup_logging


# 设置日志
setup_logging(
    level="INFO",
    log_to_file=False,
    format_type="simple"
)
logger = get_logger("config_validator")


class DistributedConfigValidator:
    """分布式配置验证器"""
    
    def __init__(self):
        self.project_root = Path(__file__).parent.parent
        self.config_dir = self.project_root / "config"
        self.accounts_dir = self.config_dir / "accounts"
        
        self.errors = []
        self.warnings = []
        self.recommendations = []
        
        # 验证结果
        self.validation_result = {
            'passed': False,
            'errors': [],
            'warnings': [],
            'recommendations': [],
            'summary': {}
        }
    
    def _add_error(self, category: str, message: str, file_path: str = None):
        """添加错误"""
        error = {
            'category': category,
            'message': message,
            'file': file_path,
            'severity': 'error'
        }
        self.errors.append(error)
        logger.error(f"❌ [{category}] {message}" + (f" ({file_path})" if file_path else ""))
    
    def _add_warning(self, category: str, message: str, file_path: str = None):
        """添加警告"""
        warning = {
            'category': category,
            'message': message,
            'file': file_path,
            'severity': 'warning'
        }
        self.warnings.append(warning)
        logger.warning(f"⚠️  [{category}] {message}" + (f" ({file_path})" if file_path else ""))
    
    def _add_recommendation(self, category: str, message: str, file_path: str = None):
        """添加建议"""
        recommendation = {
            'category': category,
            'message': message,
            'file': file_path,
            'severity': 'info'
        }
        self.recommendations.append(recommendation)
        logger.info(f"💡 [{category}] {message}" + (f" ({file_path})" if file_path else ""))
    
    def _load_yaml_file(self, file_path: Path) -> Optional[Dict[str, Any]]:
        """安全加载YAML文件"""
        try:
            import yaml
            with open(file_path, 'r', encoding='utf-8') as f:
                return yaml.safe_load(f)
        except FileNotFoundError:
            self._add_error("文件系统", f"配置文件不存在: {file_path}", str(file_path))
            return None
        except yaml.YAMLError as e:
            self._add_error("YAML格式", f"YAML格式错误: {e}", str(file_path))
            return None
        except Exception as e:
            self._add_error("文件读取", f"读取文件失败: {e}", str(file_path))
            return None
    
    def validate_system_config(self) -> bool:
        """验证系统配置"""
        logger.info("🔍 验证系统配置...")
        
        system_config_path = self.config_dir / "system.yaml"
        config = self._load_yaml_file(system_config_path)
        
        if not config:
            return False
        
        success = True
        
        # 验证分布式配置
        if not self._validate_distributed_settings(config):
            success = False
        
        # 验证消息配置
        if not self._validate_messaging_config(config):
            success = False
        
        # 验证存储配置
        if not self._validate_storage_config(config):
            success = False
        
        # 验证网络配置
        if not self._validate_network_config(config):
            success = False
        
        return success
    
    def _validate_distributed_settings(self, config: Dict[str, Any]) -> bool:
        """验证分布式设置"""
        distributed_config = config.get('system', {}).get('distributed', {})
        
        if not distributed_config.get('enabled'):
            self._add_warning("分布式配置", "分布式模式未启用")
            return True
        
        success = True
        
        # 检查中央服务器IP
        central_server = distributed_config.get('central_server')
        if not central_server or central_server == "REPLACE_WITH_CENTRAL_SERVER_IP":
            self._add_error("分布式配置", "必须设置实际的中央服务器IP地址")
            success = False
        elif not self._is_valid_ip_or_hostname(central_server):
            self._add_error("分布式配置", f"中央服务器地址格式无效: {central_server}")
            success = False
        
        # 检查集群配置
        cluster_config = distributed_config.get('cluster', {})
        if not cluster_config.get('name'):
            self._add_warning("分布式配置", "建议设置集群名称")
        
        return success
    
    def _validate_messaging_config(self, config: Dict[str, Any]) -> bool:
        """验证消息配置"""
        messaging_config = config.get('messaging', {})
        
        if not messaging_config:
            self._add_error("消息配置", "缺少消息配置")
            return False
        
        success = True
        
        # 验证NATS配置
        nats_config = messaging_config.get('nats', {})
        servers = nats_config.get('servers', [])
        
        if not servers:
            self._add_error("NATS配置", "必须配置至少一个NATS服务器")
            success = False
        else:
            for server in servers:
                if "REPLACE_WITH_CENTRAL_SERVER_IP" in server:
                    self._add_error("NATS配置", f"必须替换服务器地址中的占位符: {server}")
                    success = False
                elif not self._is_valid_nats_url(server):
                    self._add_error("NATS配置", f"NATS服务器URL格式无效: {server}")
                    success = False
        
        # 验证JetStream配置
        jetstream_config = nats_config.get('jetstream', {})
        if not jetstream_config.get('enabled'):
            self._add_warning("NATS配置", "建议启用JetStream以获得更好的可靠性")
        
        return success
    
    def _validate_storage_config(self, config: Dict[str, Any]) -> bool:
        """验证存储配置"""
        storage_config = config.get('storage', {})
        
        if not storage_config:
            self._add_error("存储配置", "缺少存储配置")
            return False
        
        success = True
        
        # 验证缓存配置
        cache_config = storage_config.get('cache', {})
        provider = cache_config.get('provider')
        
        if provider == 'redis':
            redis_config = cache_config.get('redis', {})
            host = redis_config.get('host')
            
            if not host or host == "REPLACE_WITH_CENTRAL_SERVER_IP":
                self._add_error("Redis配置", "必须设置实际的Redis服务器地址")
                success = False
            elif not self._is_valid_ip_or_hostname(host):
                self._add_error("Redis配置", f"Redis服务器地址格式无效: {host}")
                success = False
        
        elif provider == 'redis_sentinel':
            sentinel_config = cache_config.get('redis_sentinel', {})
            sentinels = sentinel_config.get('sentinels', [])
            
            if not sentinels:
                self._add_error("Redis Sentinel配置", "必须配置至少一个Sentinel节点")
                success = False
            else:
                for sentinel in sentinels:
                    host = sentinel.get('host')
                    if not host or host == "REPLACE_WITH_CENTRAL_SERVER_IP":
                        self._add_error("Redis Sentinel配置", "必须设置实际的Sentinel地址")
                        success = False
        
        return success
    
    def _validate_network_config(self, config: Dict[str, Any]) -> bool:
        """验证网络配置"""
        success = True
        
        # 检查端口冲突
        used_ports = []
        
        # 组件端口
        components = config.get('components', {})
        monitoring = components.get('monitoring', {})
        metrics = monitoring.get('metrics', {})
        
        if metrics.get('enabled'):
            port = metrics.get('port', 8080)
            used_ports.append(('Metrics', port))
        
        communication = components.get('communication', {})
        if communication.get('enabled'):
            ws_port = communication.get('websocket_port', 8765)
            used_ports.append(('WebSocket', ws_port))
        
        # 检查端口重复
        port_numbers = [port for _, port in used_ports]
        if len(port_numbers) != len(set(port_numbers)):
            self._add_error("网络配置", "检测到端口冲突")
            success = False
        
        return success
    
    def validate_account_configs(self) -> bool:
        """验证账户配置"""
        logger.info("🔍 验证账户配置...")
        
        if not self.accounts_dir.exists():
            self._add_error("账户配置", f"账户配置目录不存在: {self.accounts_dir}")
            return False
        
        account_files = list(self.accounts_dir.glob("*.yaml"))
        account_files = [f for f in account_files if f.name not in ["README.md", "README_EN.md"]]
        
        if not account_files:
            self._add_warning("账户配置", "未找到账户配置文件")
            return True
        
        success = True
        accounts_by_host = {}
        master_accounts = []
        slave_accounts = []
        
        for account_file in account_files:
            account_config = self._load_yaml_file(account_file)
            if not account_config:
                success = False
                continue
            
            account_id = account_file.stem.upper()
            
            # 验证单个账户配置
            if not self._validate_single_account_config(account_config, account_id, str(account_file)):
                success = False
                continue
            
            # 收集统计信息
            account_info = account_config.get('account', {})
            role = account_info.get('role', 'unknown')
            
            if role == 'master':
                master_accounts.append(account_id)
            elif role == 'slave':
                slave_accounts.append(account_id)
            
            # 按主机分组
            deployment = account_config.get('deployment', {})
            host_id = deployment.get('host_id', 'unknown')
            
            if host_id not in accounts_by_host:
                accounts_by_host[host_id] = []
            accounts_by_host[host_id].append({
                'id': account_id,
                'role': role,
                'file': str(account_file)
            })
        
        # 验证账户关系和分布
        if not self._validate_account_relationships(accounts_by_host, master_accounts, slave_accounts):
            success = False
        
        return success
    
    def _validate_single_account_config(self, config: Dict[str, Any], account_id: str, file_path: str) -> bool:
        """验证单个账户配置"""
        success = True
        
        account_info = config.get('account', {})
        if not account_info:
            self._add_error("账户配置", f"缺少account配置段", file_path)
            return False
        
        # 验证必需字段
        required_fields = ['id', 'role', 'connection']
        for field in required_fields:
            if field not in account_info:
                self._add_error("账户配置", f"缺少必需字段: {field}", file_path)
                success = False
        
        # 验证账户ID一致性
        if account_info.get('id') != account_id:
            self._add_error("账户配置", f"文件名与账户ID不匹配: 文件={account_id}, 配置={account_info.get('id')}", file_path)
            success = False
        
        # 验证角色
        role = account_info.get('role')
        if role not in ['master', 'slave']:
            self._add_error("账户配置", f"无效的账户角色: {role}", file_path)
            success = False
        
        # 验证连接配置
        connection = account_info.get('connection', {})
        required_connection_fields = ['login', 'password', 'server']
        for field in required_connection_fields:
            if field not in connection:
                self._add_error("账户配置", f"缺少连接字段: {field}", file_path)
                success = False
        
        # 验证部署配置
        deployment = config.get('deployment', {})
        if not deployment:
            self._add_warning("账户配置", "建议添加deployment配置以支持分布式部署", file_path)
        else:
            if not deployment.get('host_id'):
                self._add_warning("账户配置", "建议设置host_id以明确主机分配", file_path)
        
        # 验证分布式配置
        distributed = config.get('distributed', {})
        if role == 'master' and distributed.get('enabled'):
            target_slaves = distributed.get('target_slaves', [])
            if not target_slaves:
                self._add_warning("账户配置", "主账户启用分布式但未配置目标从账户", file_path)
            else:
                for target in target_slaves:
                    if not target.get('account_id') or not target.get('host_id'):
                        self._add_error("账户配置", "目标从账户配置不完整", file_path)
                        success = False
        
        return success
    
    def _validate_account_relationships(self, accounts_by_host: Dict[str, List], 
                                       master_accounts: List[str], 
                                       slave_accounts: List[str]) -> bool:
        """验证账户关系和分布"""
        success = True
        
        # 检查是否有主账户
        if not master_accounts:
            self._add_warning("账户关系", "未找到主账户（master）")
        
        # 检查是否有从账户
        if not slave_accounts:
            self._add_warning("账户关系", "未找到从账户（slave）")
        
        # 检查主机分布
        logger.info("📊 账户分布统计:")
        for host_id, accounts in accounts_by_host.items():
            masters = [acc for acc in accounts if acc['role'] == 'master']
            slaves = [acc for acc in accounts if acc['role'] == 'slave']
            
            logger.info(f"   主机 {host_id}: {len(masters)} 主账户, {len(slaves)} 从账户")
            
            if len(masters) > 1:
                self._add_warning("账户分布", f"主机 {host_id} 有多个主账户，可能影响性能")
            
            if len(accounts) == 0:
                self._add_warning("账户分布", f"主机 {host_id} 未分配任何账户")
        
        # 检查跨主机配置的一致性
        logger.info("🔗 验证跨主机关系...")
        
        # 这里可以添加更复杂的关系验证逻辑
        # 例如：检查主账户配置的target_slaves是否都存在
        
        return success
    
    def validate_docker_config(self) -> bool:
        """验证Docker配置"""
        logger.info("🔍 验证Docker配置...")
        
        docker_compose_path = self.project_root / "docker" / "docker-compose-cross-host.yml"
        
        if not docker_compose_path.exists():
            self._add_error("Docker配置", f"Docker Compose文件不存在: {docker_compose_path}")
            return False
        
        try:
            import yaml
            with open(docker_compose_path, 'r', encoding='utf-8') as f:
                docker_config = yaml.safe_load(f)
        except Exception as e:
            self._add_error("Docker配置", f"读取Docker Compose文件失败: {e}")
            return False
        
        success = True
        
        # 检查必需的服务
        services = docker_config.get('services', {})
        required_services = ['nats', 'redis']
        
        for service in required_services:
            if service not in services:
                self._add_error("Docker配置", f"缺少必需的服务: {service}")
                success = False
        
        # 检查端口暴露
        for service_name, service_config in services.items():
            ports = service_config.get('ports', [])
            if not ports and service_name in required_services:
                self._add_warning("Docker配置", f"服务 {service_name} 未暴露端口")
        
        return success
    
    def validate_required_files(self) -> bool:
        """验证必需文件是否存在"""
        logger.info("🔍 验证必需文件...")
        
        success = True
        
        required_files = [
            self.config_dir / "system.yaml",
            self.project_root / "docker" / "docker-compose-cross-host.yml",
            self.project_root / "scripts" / "test_connection.py",
        ]
        
        for file_path in required_files:
            if not file_path.exists():
                self._add_error("文件检查", f"必需文件不存在: {file_path}")
                success = False
        
        # 检查配置文件目录结构
        required_dirs = [
            self.config_dir,
            self.accounts_dir,
            self.project_root / "docker",
            self.project_root / "scripts",
        ]
        
        for dir_path in required_dirs:
            if not dir_path.exists():
                self._add_error("目录检查", f"必需目录不存在: {dir_path}")
                success = False
        
        return success
    
    def run_full_validation(self) -> Dict[str, Any]:
        """运行完整验证"""
        logger.info("=" * 60)
        logger.info("🚀 开始分布式配置验证")
        logger.info("=" * 60)
        
        # 清空之前的结果
        self.errors.clear()
        self.warnings.clear()
        self.recommendations.clear()
        
        validation_steps = [
            ("必需文件检查", self.validate_required_files),
            ("系统配置验证", self.validate_system_config),
            ("账户配置验证", self.validate_account_configs),
            ("Docker配置验证", self.validate_docker_config),
        ]
        
        all_passed = True
        
        for step_name, validation_func in validation_steps:
            logger.info(f"\n📋 {step_name}...")
            try:
                if not validation_func():
                    all_passed = False
            except Exception as e:
                self._add_error("验证异常", f"{step_name}发生异常: {e}")
                all_passed = False
        
        # 生成验证报告
        self._generate_validation_report(all_passed)
        
        return self.validation_result
    
    def _generate_validation_report(self, all_passed: bool):
        """生成验证报告"""
        logger.info("=" * 60)
        logger.info("📋 验证结果总结")
        logger.info("=" * 60)
        
        # 统计
        error_count = len(self.errors)
        warning_count = len(self.warnings)
        recommendation_count = len(self.recommendations)
        
        logger.info(f"错误: {error_count}")
        logger.info(f"警告: {warning_count}")
        logger.info(f"建议: {recommendation_count}")
        
        # 更新验证结果
        self.validation_result.update({
            'passed': all_passed and error_count == 0,
            'errors': self.errors,
            'warnings': self.warnings,
            'recommendations': self.recommendations,
            'summary': {
                'error_count': error_count,
                'warning_count': warning_count,
                'recommendation_count': recommendation_count,
                'overall_status': 'PASSED' if (all_passed and error_count == 0) else 'FAILED'
            }
        })
        
        # 显示详细结果
        if error_count > 0:
            logger.info(f"\n❌ 发现 {error_count} 个错误，必须修复后才能部署:")
            for i, error in enumerate(self.errors, 1):
                logger.info(f"   {i}. [{error['category']}] {error['message']}")
        
        if warning_count > 0:
            logger.info(f"\n⚠️  发现 {warning_count} 个警告，建议处理:")
            for i, warning in enumerate(self.warnings, 1):
                logger.info(f"   {i}. [{warning['category']}] {warning['message']}")
        
        if recommendation_count > 0:
            logger.info(f"\n💡 提供 {recommendation_count} 个优化建议:")
            for i, rec in enumerate(self.recommendations, 1):
                logger.info(f"   {i}. [{rec['category']}] {rec['message']}")
        
        # 最终状态
        if self.validation_result['passed']:
            logger.info("\n🎉 所有验证通过！配置可以用于分布式部署。")
        else:
            logger.info("\n❌ 验证失败！请修复错误后重新验证。")
    
    def save_report(self, output_file: Optional[str] = None):
        """保存验证报告"""
        if not output_file:
            output_file = f"config_validation_report_{int(time.time())}.json"
        
        try:
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(self.validation_result, f, indent=2, ensure_ascii=False)
            logger.info(f"📁 验证报告已保存到: {output_file}")
        except Exception as e:
            logger.error(f"保存验证报告失败: {e}")
    
    @staticmethod
    def _is_valid_ip_or_hostname(address: str) -> bool:
        """验证IP地址或主机名格式"""
        # 简单的IP地址验证
        ip_pattern = r'^(\d{1,3}\.){3}\d{1,3}$'
        hostname_pattern = r'^[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?(\.[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?)*$'
        
        return bool(re.match(ip_pattern, address) or re.match(hostname_pattern, address))
    
    @staticmethod
    def _is_valid_nats_url(url: str) -> bool:
        """验证NATS URL格式"""
        nats_pattern = r'^nats://[a-zA-Z0-9\.\-]+:\d+$'
        return bool(re.match(nats_pattern, url))


def main():
    """主函数"""
    import time
    
    validator = DistributedConfigValidator()
    
    try:
        # 运行验证
        result = validator.run_full_validation()
        
        # 保存报告
        validator.save_report()
        
        # 返回适当的退出码
        return 0 if result['passed'] else 1
        
    except KeyboardInterrupt:
        logger.info("\n⏹️  验证被用户中断")
        return 1
    except Exception as e:
        logger.error(f"验证过程中发生异常: {e}")
        return 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)