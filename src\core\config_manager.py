#!/usr/bin/env python3
"""
Configuration Manager - Unified configuration management for MT5 trading system
Eliminates all hardcoded configurations, implements true configuration-driven system
All configuration parameters are read from configuration files, no hardcoded default values

Features:
- Unified configuration file management
- Environment variable override support
- Multi-environment configuration support
- Configuration validation support
- Integration with AccountConfigManager
- Replaces all other configuration managers
"""

import logging
import os
from typing import Dict, Any, Optional, List
from pathlib import Path
import yaml
from pydantic import BaseModel, Field

logger = logging.getLogger(__name__)


class ConfigDefaults(BaseModel):
    """Configuration defaults - loaded from config files, not hardcoded"""

    # These values will be loaded from config/core/system_defaults.yaml
    copy_ratio: Optional[float] = Field(default=None, description="Default copy ratio")
    max_volume: Optional[float] = Field(default=None, description="Default maximum volume")
    min_volume: Optional[float] = Field(default=None, description="Default minimum volume")
    risk_multiplier: Optional[float] = Field(default=None, description="Default risk multiplier")
    max_positions: Optional[int] = Field(default=None, description="Default maximum positions")

    def __init__(self, **data):
        """Initialize and load default values from config file"""
        super().__init__(**data)
        if any(v is None for v in [self.copy_ratio, self.max_volume, self.min_volume,
                                   self.risk_multiplier, self.max_positions]):
            self._load_from_config()

    def _load_from_config(self):
        """Load default values from configuration file"""
        try:
            defaults_file = Path("config/core/system_defaults.yaml")
            if defaults_file.exists():
                with open(defaults_file, 'r', encoding='utf-8') as f:
                    defaults = yaml.safe_load(f)
                
                copy_trading = defaults.get('copy_trading', {})
                self.copy_ratio = copy_trading.get('default_copy_ratio', 1.0)
                self.max_volume = copy_trading.get('default_max_volume', 10.0)
                self.min_volume = copy_trading.get('default_min_volume', 0.01)
                self.risk_multiplier = copy_trading.get('default_risk_multiplier', 1.0)
                self.max_positions = copy_trading.get('default_max_positions', 10)
                
                logger.info(f"Loaded default values from config file: {defaults_file}")
            else:
                self.copy_ratio = 1.0
                self.max_volume = 10.0
                self.min_volume = 0.01
                self.risk_multiplier = 1.0
                self.max_positions = 10

                logger.warning(f"Config file not found, using temporary defaults: {defaults_file}")

        except Exception as e:
            logger.error(f"Failed to load config defaults: {e}")

            self.copy_ratio = 1.0
            self.max_volume = 1.0  # Safe small value
            self.min_volume = 0.01
            self.risk_multiplier = 1.0
            self.max_positions = 1  # Safe small value


class ConfigManager:
    """
    Unified Configuration Manager - System's single configuration manager
    """

    def __init__(self, config_root: str = "config", environment: str = None):
        self.config_root = Path(config_root)
        self.environment = environment or os.getenv('APP_ENVIRONMENT', 'development')
        self.defaults = ConfigDefaults()
        self._config_cache = {}
        self._watchers = []
        self._load_all_configs()
    
    def _load_all_configs(self):
        """加载所有配置文件"""
        try:
            self._load_core_configs()
            self._load_copy_trading_config()
            self._load_risk_management_config()
            self._load_account_configs()
            self._load_environment_config()
            self._apply_environment_overrides()

            logger.info(f"统一配置管理器初始化完成 ({self.environment}环境)")

        except Exception as e:
            logger.error(f"配置加载失败: {e}")

    def _load_core_configs(self):
        """加载核心系统配置"""
        system_config_file = self.config_root / "core" / "system.yaml"
        if system_config_file.exists():
            with open(system_config_file, 'r', encoding='utf-8') as f:
                self._config_cache['system'] = yaml.safe_load(f)

        infra_config_file = self.config_root / "core" / "infrastructure.yaml"
        if infra_config_file.exists():
            with open(infra_config_file, 'r', encoding='utf-8') as f:
                self._config_cache['infrastructure'] = yaml.safe_load(f)

        monitoring_config_file = self.config_root / "core" / "monitoring.yaml"
        if monitoring_config_file.exists():
            with open(monitoring_config_file, 'r', encoding='utf-8') as f:
                self._config_cache['monitoring'] = yaml.safe_load(f)

    def _load_environment_config(self):
        """加载环境特定配置"""
        env_config_file = self.config_root / "environments" / f"{self.environment}.yaml"
        if env_config_file.exists():
            with open(env_config_file, 'r', encoding='utf-8') as f:
                env_config = yaml.safe_load(f)
                self._config_cache['environment'] = env_config

                self._merge_environment_config(env_config)

    def _merge_environment_config(self, env_config: Dict[str, Any]):
        """合并环境配置到主配置"""
        for section, config in env_config.items():
            if section in self._config_cache:
                self._deep_merge(self._config_cache[section], config)
            else:
                self._config_cache[section] = config

    def _deep_merge(self, base: Dict[str, Any], override: Dict[str, Any]):
        """深度合并配置字典"""
        for key, value in override.items():
            if key in base and isinstance(base[key], dict) and isinstance(value, dict):
                self._deep_merge(base[key], value)
            else:
                base[key] = value

    def _apply_environment_overrides(self):
        """应用环境变量覆盖"""
        env_overrides = {
            'NATS_SERVERS': ('infrastructure', 'nats', 'servers'),
            'MT5_TERMINAL_PATH': ('system', 'mt5', 'terminal_path'),
            'LOG_LEVEL': ('monitoring', 'logging', 'level'),
            'MAX_VOLUME': ('copy_trading', 'default_max_volume'),
            'COPY_RATIO': ('copy_trading', 'default_copy_ratio'),
        }

        for env_var, config_path in env_overrides.items():
            env_value = os.getenv(env_var)
            if env_value:
                self._set_config_value(config_path, env_value)

    def _set_config_value(self, path: tuple, value: str):
        """设置配置值"""
        try:
            if value.lower() in ('true', 'false'):
                value = value.lower() == 'true'
            elif value.isdigit():
                value = int(value)
            elif '.' in value and value.replace('.', '').isdigit():
                value = float(value)

            config = self._config_cache
            for key in path[:-1]:
                if key not in config:
                    config[key] = {}
                config = config[key]
            config[path[-1]] = value

        except Exception as e:
            logger.warning(f"设置配置值失败 {path}: {e}")
    
    def _load_copy_trading_config(self):
        """加载跟单配置"""
        config_file = self.config_root / "relationships" / "copy_trading.yaml"
        if config_file.exists():
            with open(config_file, 'r', encoding='utf-8') as f:
                self._config_cache['copy_trading'] = yaml.safe_load(f)
    
    def _load_risk_management_config(self):
        """加载风险管理配置"""
        config_file = self.config_root / "risk" / "risk_management.yaml"
        if config_file.exists():
            with open(config_file, 'r', encoding='utf-8') as f:
                self._config_cache['risk_management'] = yaml.safe_load(f)
    
    def _load_account_configs(self):
        """加载账户配置"""
        accounts_dir = self.config_root / "accounts"
        if accounts_dir.exists():
            self._config_cache['accounts'] = {}
            for config_file in accounts_dir.glob("*.yaml"):
                account_id = config_file.stem
                with open(config_file, 'r', encoding='utf-8') as f:
                    self._config_cache['accounts'][account_id] = yaml.safe_load(f)
    
    def get_copy_ratio(self, master_account: str, slave_account: str) -> float:
        """获取跟单比例"""
        try:
            relationships = self._config_cache.get('copy_trading', {}).get('relationships', [])
            for rel in relationships:
                if (rel.get('master_account') == master_account and 
                    rel.get('slave_account') == slave_account):
                    return rel.get('copy_ratio', self.defaults.copy_ratio)
            
            return self.defaults.copy_ratio
            
        except Exception as e:
            logger.error(f"获取跟单比例失败: {e}")
            return self.defaults.copy_ratio
    
    def get_volume_limits(self, account_id: str) -> Dict[str, float]:
        """
        获取手数限制 
        优先从跟单关系配置读取，然后是账户配置
        """
        try:
            relationships = self._config_cache.get('copy_trading', {}).get('relationships', [])
            for rel in relationships:
                if rel.get('slave_account') == account_id:
                    limits = rel.get('limits', {})
                    if 'max_volume_per_trade' in limits and 'min_volume_per_trade' in limits:
                        return {
                            'max_volume': limits['max_volume_per_trade'],
                            'min_volume': limits['min_volume_per_trade']
                        }

            account_config = self._config_cache.get('accounts', {}).get(account_id, {})
            trading_config = account_config.get('trading', {})
            volume_limits = trading_config.get('volume_limits', {})

            return {
                'max_volume': volume_limits.get('max_volume', self.defaults.max_volume),
                'min_volume': volume_limits.get('min_volume', self.defaults.min_volume)
            }

        except Exception as e:
            logger.error(f"获取手数限制失败: {e}")
            return {
                'max_volume': self.defaults.max_volume,
                'min_volume': self.defaults.min_volume
            }
    
    def get_risk_multiplier(self, master_account: str, slave_account: str) -> float:
        """获取风险倍数"""
        try:
            relationships = self._config_cache.get('copy_trading', {}).get('relationships', [])
            for rel in relationships:
                if (rel.get('master_account') == master_account and 
                    rel.get('slave_account') == slave_account):
                    risk_mgmt = rel.get('risk_management', {})
                    return risk_mgmt.get('risk_multiplier', self.defaults.risk_multiplier)
            
            return self.defaults.risk_multiplier
            
        except Exception as e:
            logger.error(f"获取风险倍数失败: {e}")
            return self.defaults.risk_multiplier
    
    def get_max_positions(self, account_id: str) -> int:
        """获取最大持仓数"""
        try:
            relationships = self._config_cache.get('copy_trading', {}).get('relationships', [])
            for rel in relationships:
                if rel.get('slave_account') == account_id:
                    limits = rel.get('limits', {})
                    if 'max_positions' in limits:
                        return limits['max_positions']
            
            account_config = self._config_cache.get('accounts', {}).get(account_id, {})
            risk_config = account_config.get('risk_management', {})
            limits = risk_config.get('limits', {})
            
            return limits.get('max_positions', self.defaults.max_positions)
            
        except Exception as e:
            logger.error(f"获取最大持仓数失败: {e}")
            return self.defaults.max_positions
    
    def get_relationship_config(self, master_account: str, slave_account: str) -> Dict[str, Any]:
        """获取完整的跟单关系配置"""
        try:
            relationships = self._config_cache.get('copy_trading', {}).get('relationships', [])
            for rel in relationships:
                if (rel.get('master_account') == master_account and 
                    rel.get('slave_account') == slave_account):

                    config = rel.copy()
                    config.setdefault('copy_ratio', self.defaults.copy_ratio)
                    config.setdefault('limits', {})
                    config['limits'].setdefault('max_volume_per_trade', self.defaults.max_volume)
                    config['limits'].setdefault('min_volume_per_trade', self.defaults.min_volume)
                    config['limits'].setdefault('max_positions', self.defaults.max_positions)
                    config.setdefault('risk_management', {})
                    config['risk_management'].setdefault('risk_multiplier', self.defaults.risk_multiplier)
                    
                    return config
            
            return {
                'master_account': master_account,
                'slave_account': slave_account,
                'copy_ratio': self.defaults.copy_ratio,
                'limits': {
                    'max_volume_per_trade': self.defaults.max_volume,
                    'min_volume_per_trade': self.defaults.min_volume,
                    'max_positions': self.defaults.max_positions
                },
                'risk_management': {
                    'risk_multiplier': self.defaults.risk_multiplier
                },
                'enabled': False  # 未配置的关系默认禁用
            }
            
        except Exception as e:
            logger.error(f"获取关系配置失败: {e}")
            return {}
    
    def validate_no_hardcoded_values(self) -> List[str]:
        """验证系统中没有硬编码值"""
        issues = []
        
        if not self._config_cache:
            issues.append("配置缓存为空，可能存在硬编码值")
        
        defaults_file = Path("config/core/system_defaults.yaml")
        if not defaults_file.exists():
            issues.append(f"默认值配置文件不存在: {defaults_file}")
        
        return issues

    def reload_configs(self):
        """重新加载所有配置"""
        self._config_cache.clear()
        self.defaults = ConfigDefaults()
        self._load_all_configs()
        logger.info("配置已重新加载")

    # ==================== 通用配置获取方法 ====================

    def get(self, key: str, default: Any = None) -> Any:
        """
        获取配置值
        """
        env_key = key.upper().replace('.', '_')
        env_value = os.getenv(env_key)
        if env_value is not None:
            try:
                if env_value.lower() in ('true', 'false'):
                    return env_value.lower() == 'true'
                elif env_value.isdigit():
                    return int(env_value)
                elif '.' in env_value and env_value.replace('.', '').isdigit():
                    return float(env_value)
                else:
                    return env_value
            except:
                return env_value

        keys = key.split('.')

        # 创建配置缓存的副本以避免迭代时修改字典
        config_cache_copy = self._config_cache.copy()
        for section_name, section_config in config_cache_copy.items():
            if isinstance(section_config, dict):
                value = section_config
                found = True
                for k in keys:
                    if isinstance(value, dict) and k in value:
                        value = value[k]
                    else:
                        found = False
                        break

                if found:
                    return value

        return default

    def get_all(self) -> Dict[str, Any]:
        """获取所有配置"""
        return self._config_cache.copy()

    def get_accounts(self) -> Dict[str, Any]:
        """获取所有账户配置"""
        return self._config_cache.get('accounts', {})

    def get_account_config(self, account_id: str) -> Dict[str, Any]:
        """获取特定账户配置"""
        accounts = self.get_accounts()
        return accounts.get(account_id, {})

    def is_account_enabled(self, account_id: str) -> bool:
        """检查账户是否启用"""
        account_config = self.get_account_config(account_id)
        return account_config.get('account', {}).get('enabled', False)

    def get_nats_config(self) -> Dict[str, Any]:
        """获取NATS配置"""
        infra_config = self._config_cache.get('infrastructure', {})
        nats_config = infra_config.get('nats', {})

        if not nats_config:
            system_config = self._config_cache.get('system', {})
            nats_config = system_config.get('nats', {})

        return nats_config

    def get_monitoring_config(self) -> Dict[str, Any]:
        """获取监控配置"""
        return self._config_cache.get('monitoring', {})

    # ==================== 与AccountConfigManager集成 ====================

    def get_account_config_manager(self, enable_hot_reload: bool = True, enable_validation: bool = True):
        """获取专业的账户配置管理器"""
        from .mt5_configuration import AccountConfigManager
        return AccountConfigManager(
            config_dir=str(self.config_root),
            enable_hot_reload=enable_hot_reload,
            enable_validation=enable_validation
        )

    def get_account_by_host(self, host_id: str) -> Dict[str, Any]:
        """获取指定主机上的账户"""
        account_manager = self.get_account_config_manager()
        accounts_list = account_manager.get_accounts_by_host(host_id)
        return {
            account_config.account_id: account_config.__dict__
            for account_config in accounts_list
        }

    def get_account_by_tag(self, tag: str) -> Dict[str, Any]:
        """获取指定标签的账户"""
        account_manager = self.get_account_config_manager()
        accounts_list = account_manager.get_accounts_by_tag(tag)
        return {
            account_config.account_id: account_config.__dict__
            for account_config in accounts_list
        }

    def validate_account_configs(self) -> Dict[str, List[str]]:
        """验证所有账户配置"""
        account_manager = self.get_account_config_manager()
        return account_manager.validate_all_configs()


_config_manager = None


def get_config_manager() -> ConfigManager:
    """获取全局配置管理器"""
    global _config_manager
    if _config_manager is None:
        _config_manager = ConfigManager()
    return _config_manager


# 向后兼容的别名
def get_unified_config_manager() -> ConfigManager:
    """获取统一配置管理器 - 向后兼容别名"""
    return get_config_manager()


def get_config_value(key_path: str, account_id: str = None,
                    master_account: str = None, slave_account: str = None) -> Any:
    """
    统一的配置值获取接口
    """
    manager = get_config_manager()

    if key_path == "copy_ratio" and master_account and slave_account:
        return manager.get_copy_ratio(master_account, slave_account)
    elif key_path == "max_volume" and account_id:
        return manager.get_volume_limits(account_id)['max_volume']
    elif key_path == "min_volume" and account_id:
        return manager.get_volume_limits(account_id)['min_volume']
    elif key_path == "risk_multiplier" and master_account and slave_account:
        return manager.get_risk_multiplier(master_account, slave_account)
    elif key_path == "max_positions" and account_id:
        return manager.get_max_positions(account_id)
    elif key_path == "default_volume_round_to":
        try:
            defaults = manager._config_cache.get('copy_trading', {})
            return defaults.get('default_volume_round_to', 0.01)
        except:
            return 0.01
    else:
        logger.warning(f"未知的配置键路径: {key_path}")
        return None


# ==================== 流配置管理器 ====================

class StreamConfigManager:
    """
    统一流配置管理器 - 单一数据源
    管理NATS JetStream的分层流配置
    """

    def __init__(self, config_manager: ConfigManager):
        self.config_manager = config_manager
        self.host_id = os.getenv('HOST_ID', 'localhost')
        self._stream_cache = {}
        self._consumer_cache = {}
        self._subject_patterns = {}
        self._load_stream_configs()

    def _load_stream_configs(self):
        """加载流配置"""
        try:
            # 从infrastructure.yaml加载流配置
            jetstream_config = self.config_manager.get('nats.jetstream', {})

            # 加载流定义
            streams_config = jetstream_config.get('streams', {})
            for stream_type, stream_config in streams_config.items():
                self._stream_cache[stream_type] = stream_config

            # 加载消费者配置
            consumers_config = jetstream_config.get('consumers', {})
            for consumer_type, consumer_config in consumers_config.items():
                self._consumer_cache[consumer_type] = consumer_config

            # 加载主题模式
            self._subject_patterns = jetstream_config.get('subject_patterns', {})

            logger.info(f"流配置加载完成: {len(self._stream_cache)}个流, {len(self._consumer_cache)}个消费者类型")

        except Exception as e:
            logger.error(f"流配置加载失败: {e}")
            self._load_default_stream_configs()

    def _load_default_stream_configs(self):
        """加载默认流配置（后备方案）"""
        logger.warning("使用默认流配置")

        # 默认分层流配置
        self._stream_cache = {
            'local': {
                'name_template': 'MT5_LOCAL_{host_id}',
                'subjects': ['MT5.LOCAL.{host_id}.*'],
                'storage': 'memory',
                'retention': 'workqueue',
                'max_age': 300,
                'replicas': 1
            },
            'signals': {
                'name': 'MT5_SIGNALS',
                'subjects': ['MT5.SIGNALS.*', 'MT5.COPY.*'],
                'storage': 'file',
                'retention': 'limits',
                'max_age': 3600,
                'replicas': 3
            }
        }

    def get_stream_config(self, stream_type: str) -> Dict[str, Any]:
        """获取流配置"""
        if stream_type not in self._stream_cache:
            logger.error(f"未找到流类型: {stream_type}")
            return {}

        config = self._stream_cache[stream_type].copy()

        # 处理模板变量
        if 'name_template' in config:
            config['name'] = config['name_template'].format(host_id=self.host_id)
            del config['name_template']

        # 处理主题模板
        if 'subjects' in config:
            processed_subjects = []
            for subject in config['subjects']:
                processed_subjects.append(subject.format(host_id=self.host_id))
            config['subjects'] = processed_subjects

        return config

    def get_consumer_config(self, consumer_type: str, consumer_name: str = None) -> Dict[str, Any]:
        """获取消费者配置"""
        if consumer_type not in self._consumer_cache:
            logger.error(f"未找到消费者类型: {consumer_type}")
            return {}

        config = self._consumer_cache[consumer_type].copy()

        # 处理嵌套配置
        if isinstance(config, dict) and consumer_name:
            if consumer_name in config:
                config = config[consumer_name].copy()
            else:
                # 取第一个配置作为默认
                config = next(iter(config.values())).copy()

        # 处理模板变量
        if 'name_template' in config:
            config['name'] = config['name_template'].format(host_id=self.host_id)
            del config['name_template']

        return config

    def get_subject_pattern(self, pattern_name: str, **kwargs) -> str:
        """获取主题模式"""
        if pattern_name not in self._subject_patterns:
            logger.error(f"未找到主题模式: {pattern_name}")
            return ""

        pattern = self._subject_patterns[pattern_name]

        # 添加默认参数
        format_kwargs = {'host_id': self.host_id}
        format_kwargs.update(kwargs)

        try:
            return pattern.format(**format_kwargs)
        except KeyError as e:
            logger.error(f"主题模式格式化失败: {pattern}, 缺少参数: {e}")
            return pattern

    def get_all_streams(self) -> Dict[str, Dict[str, Any]]:
        """获取所有流配置"""
        return {
            stream_type: self.get_stream_config(stream_type)
            for stream_type in self._stream_cache.keys()
        }

    def get_all_consumers(self) -> Dict[str, Dict[str, Any]]:
        """获取所有消费者配置"""
        result = {}
        for consumer_type, consumer_config in self._consumer_cache.items():
            if isinstance(consumer_config, dict):
                for consumer_name in consumer_config.keys():
                    key = f"{consumer_type}.{consumer_name}"
                    result[key] = self.get_consumer_config(consumer_type, consumer_name)
            else:
                result[consumer_type] = self.get_consumer_config(consumer_type)
        return result

    def validate_stream_config(self, stream_type: str) -> List[str]:
        """验证流配置"""
        issues = []
        config = self.get_stream_config(stream_type)

        if not config:
            issues.append(f"流配置为空: {stream_type}")
            return issues

        # 必需字段检查
        required_fields = ['name', 'subjects', 'storage', 'retention']
        for field in required_fields:
            if field not in config:
                issues.append(f"流配置缺少必需字段 {field}: {stream_type}")

        # 主题格式检查
        if 'subjects' in config:
            for subject in config['subjects']:
                if not subject or not isinstance(subject, str):
                    issues.append(f"无效的主题格式: {subject} in {stream_type}")

        return issues

    def reload_configs(self):
        """重新加载流配置"""
        self._stream_cache.clear()
        self._consumer_cache.clear()
        self._subject_patterns.clear()
        self._load_stream_configs()
        logger.info("流配置已重新加载")


# 全局流配置管理器实例
_stream_config_manager = None

def get_stream_config_manager(config_manager: ConfigManager = None) -> StreamConfigManager:
    """获取全局流配置管理器实例"""
    global _stream_config_manager
    if _stream_config_manager is None:
        if config_manager is None:
            config_manager = ConfigManager()
        _stream_config_manager = StreamConfigManager(config_manager)
    return _stream_config_manager
