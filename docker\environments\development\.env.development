# MT5分布式交易系统 - 开发环境变量配置

# ============================================================================
# 环境标识
# ============================================================================
MT5_ENVIRONMENT=development
MT5_DEPLOYMENT_MODE=development
MT5_DEBUG=true
MT5_LOG_LEVEL=DEBUG

# ============================================================================
# 主机配置
# ============================================================================
MT5_HOST_ID=docker-dev
HOST_IP=************

# ============================================================================
# 应用配置
# ============================================================================
MT5_MAX_WORKERS=4
MT5_MEMORY_LIMIT=1024
MT5_CONFIG_PATH=/app/config/core/system.yaml

# ============================================================================
# 基础设施端口配置
# ============================================================================
REDIS_EXTERNAL_PORT=6379
REDIS_DB=1
NATS_CLIENT_PORT=4222
NATS_HTTP_PORT=8222
PROMETHEUS_PORT=9090
GRAFANA_PORT=3000
REDIS_COMMANDER_PORT=8081

# ============================================================================
# 应用端口配置
# ============================================================================
MT5_API_PORT=8000
MT5_HEALTH_PORT=8001
HASH_MANAGER_PORT=8082

# ============================================================================
# 监控配置
# ============================================================================
PROMETHEUS_RETENTION=1d
GRAFANA_ADMIN_PASSWORD=admin
GRAFANA_ANONYMOUS=true

# ============================================================================
# 安全配置（开发环境）
# ============================================================================
AUTH_TOKEN_SECRET=dev-secret-key-change-in-production
MT5_ENABLE_TLS=false
MT5_ENABLE_AUTH=false

# ============================================================================
# 账户密码（开发环境 - 使用测试密码）
# ============================================================================
# 注意：生产环境中这些应该设置为真实密码
MT5_ACC001_PASSWORD=Jingenqi0724@
MT5_ACC002_PASSWORD=Jingenqi0724@
MT5_ACC003_PASSWORD=Jingenqi0724@

# ============================================================================
# 功能开关（开发环境）
# ============================================================================
MT5_TESTING_MODE=false
MT5_MOCK_TRADING=false
MT5_HOT_RELOAD=true

# ============================================================================
# 日志配置
# ============================================================================
LOG_TO_FILE=true
LOG_TO_CONSOLE=true
LOG_JSON_FORMAT=true

# ============================================================================
# Docker配置
# ============================================================================
COMPOSE_PROJECT_NAME=mt5-dev
COMPOSE_FILE=docker-compose.yml

# ============================================================================
# 数据库配置
# ============================================================================
DATABASE_URL=sqlite:///data/mt5_development.db

# ============================================================================
# 外部服务配置（开发环境可选）
# ============================================================================
# Jaeger追踪（可选）
JAEGER_AGENT_HOST=localhost
JAEGER_AGENT_PORT=6831

# 第三方API（开发环境可以使用模拟）
TELEGRAM_BOT_TOKEN=
TELEGRAM_CHAT_ID=7861723903

# ============================================================================
# 性能配置（开发环境优化）
# ============================================================================
REDIS_MAX_CONNECTIONS=50
NATS_MAX_CONNECTIONS=20
HTTP_TIMEOUT=30
CONNECTION_TIMEOUT=10

# ============================================================================
# 开发工具配置
# ============================================================================
ENABLE_TOOLS_PROFILE=true
ENABLE_MONITORING_PROFILE=false

# ============================================================================
# 容器配置
# ============================================================================
RESTART_POLICY=unless-stopped
HEALTHCHECK_INTERVAL=60s
HEALTHCHECK_TIMEOUT=15s
HEALTHCHECK_RETRIES=3