#!/usr/bin/env python3
"""
LRU消息缓存功能单元测试
"""
import pytest
import time
import hashlib
import threading
from unittest.mock import Mock, patch

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.messaging.protobuf_codec import (
    LRUMessageCache, ProtobufCodec, ProtobufConfig,
    get_protobuf_codec, get_cache_stats, get_cache_health, clear_cache
)


class TestLRUMessageCache:
    """测试LRU消息缓存功能"""

    def test_cache_initialization(self):
        """测试缓存初始化"""
        cache = LRUMessageCache(max_size=100, ttl_seconds=300)
        
        assert cache.max_size == 100
        assert cache.ttl_seconds == 300
        assert len(cache.cache) == 0
        assert len(cache.access_times) == 0
        assert cache.stats['hits'] == 0
        assert cache.stats['misses'] == 0

    def test_cache_key_generation(self):
        """测试缓存键生成"""
        cache = LRUMessageCache()
        
        data1 = b"test message 1"
        data2 = b"test message 2"
        data3 = b"test message 1"  # 与data1相同
        
        key1 = cache._generate_cache_key(data1)
        key2 = cache._generate_cache_key(data2)
        key3 = cache._generate_cache_key(data3)
        
        assert key1 != key2  # 不同数据应该有不同的键
        assert key1 == key3  # 相同数据应该有相同的键
        assert len(key1) == 16  # SHA256前16位
        assert isinstance(key1, str)

    def test_basic_cache_operations(self):
        """测试基本缓存操作"""
        cache = LRUMessageCache(max_size=10, ttl_seconds=300)
        
        data = b"test message"
        decoded_result = {"signal_id": "123", "symbol": "EURUSD"}
        
        # 首次获取（应该miss）
        result = cache.get(data)
        assert result is None
        assert cache.stats['misses'] == 1
        assert cache.stats['hits'] == 0
        
        # 放入缓存
        cache.put(data, decoded_result)
        assert cache.stats['total_size'] == 1
        
        # 再次获取（应该hit）
        result = cache.get(data)
        assert result == decoded_result
        assert cache.stats['hits'] == 1
        assert cache.stats['misses'] == 1

    def test_lru_eviction(self):
        """测试LRU淘汰机制"""
        cache = LRUMessageCache(max_size=3, ttl_seconds=300)
        
        # 添加3个项目
        data1 = b"message1"
        data2 = b"message2"
        data3 = b"message3"
        
        cache.put(data1, "result1")
        cache.put(data2, "result2")
        cache.put(data3, "result3")
        
        assert len(cache.cache) == 3
        
        # 访问第一个项目（应该移到最后）
        cache.get(data1)
        
        # 添加第四个项目（应该淘汰data2，因为它是最久未使用的）
        data4 = b"message4"
        cache.put(data4, "result4")
        
        assert len(cache.cache) == 3
        assert cache.get(data1) == "result1"  # 应该还在
        assert cache.get(data2) is None       # 应该被淘汰
        assert cache.get(data3) == "result3"  # 应该还在
        assert cache.get(data4) == "result4"  # 新加入的应该在
        
        assert cache.stats['evictions'] >= 1

    def test_ttl_expiration(self):
        """测试TTL过期机制"""
        cache = LRUMessageCache(max_size=10, ttl_seconds=0.1)  # 0.1秒TTL
        
        data = b"test message"
        result = "test result"
        
        # 添加到缓存
        cache.put(data, result)
        
        # 立即获取（应该成功）
        cached_result = cache.get(data)
        assert cached_result == result
        
        # 等待过期
        time.sleep(0.15)
        
        # 再次获取（应该过期）
        expired_result = cache.get(data)
        assert expired_result is None
        assert cache.stats['expired'] >= 1

    def test_cache_size_limit(self):
        """测试缓存大小限制"""
        cache = LRUMessageCache(max_size=5, ttl_seconds=300)
        
        # 添加超过限制的项目
        for i in range(10):
            data = f"message{i}".encode()
            result = f"result{i}"
            cache.put(data, result)
        
        # 缓存大小不应超过限制
        assert len(cache.cache) <= 5
        assert cache.stats['evictions'] > 0

    def test_thread_safety(self):
        """测试线程安全"""
        cache = LRUMessageCache(max_size=100, ttl_seconds=300)
        results = []
        errors = []
        
        def worker_thread(thread_id):
            try:
                for i in range(50):
                    data = f"thread_{thread_id}_message_{i}".encode()
                    result = f"thread_{thread_id}_result_{i}"
                    
                    # 放入缓存
                    cache.put(data, result)
                    
                    # 立即获取
                    cached_result = cache.get(data)
                    if cached_result != result:
                        errors.append(f"Thread {thread_id}: Expected {result}, got {cached_result}")
                    
                    # 短暂休眠
                    time.sleep(0.001)
                
                results.append(f"thread_{thread_id}_completed")
            except Exception as e:
                errors.append(f"thread_{thread_id}_error: {e}")
        
        # 启动多个线程
        threads = []
        for i in range(5):
            thread = threading.Thread(target=worker_thread, args=(i,))
            threads.append(thread)
            thread.start()
        
        # 等待所有线程完成
        for thread in threads:
            thread.join()
        
        # 检查结果
        assert len(errors) == 0, f"Thread errors: {errors}"
        assert len(results) == 5

    def test_cache_statistics(self):
        """测试缓存统计信息"""
        cache = LRUMessageCache(max_size=10, ttl_seconds=300)
        
        # 执行一些操作
        data1 = b"message1"
        data2 = b"message2"
        
        cache.put(data1, "result1")
        cache.get(data1)  # hit
        cache.get(data2)  # miss
        cache.put(data2, "result2")
        cache.get(data1)  # hit
        
        stats = cache.get_stats()
        
        # 检查统计字段
        assert 'hits' in stats
        assert 'misses' in stats
        assert 'evictions' in stats
        assert 'expired' in stats
        assert 'total_size' in stats
        assert 'hit_rate_percent' in stats
        assert 'total_requests' in stats
        assert 'cache_utilization_percent' in stats
        assert 'memory_estimate_kb' in stats
        
        # 检查数值
        assert stats['hits'] == 2
        assert stats['misses'] == 1
        assert stats['total_requests'] == 3
        assert abs(stats['hit_rate_percent'] - 66.67) < 0.1

    def test_cache_health_status(self):
        """测试缓存健康状态"""
        cache = LRUMessageCache(max_size=10, ttl_seconds=300)
        
        # 执行一些操作以获得健康分数
        for i in range(5):
            data = f"message{i}".encode()
            cache.put(data, f"result{i}")
            cache.get(data)  # 增加命中率
        
        health = cache.get_health_status()
        
        # 检查健康状态字段
        assert 'status' in health
        assert 'score' in health
        assert 'recommendations' in health
        
        # 检查状态值
        assert health['status'] in ['excellent', 'good', 'fair', 'poor']
        assert 0 <= health['score'] <= 100
        assert isinstance(health['recommendations'], list)

    def test_cache_recommendations(self):
        """测试缓存优化建议"""
        cache = LRUMessageCache(max_size=5, ttl_seconds=0.1)
        
        # 创建低命中率场景
        for i in range(20):
            data = f"unique_message_{i}".encode()
            cache.put(data, f"result_{i}")
        
        # 等待一些项目过期
        time.sleep(0.15)
        
        # 尝试获取一些不存在的项目
        for i in range(10):
            cache.get(f"missing_{i}".encode())
        
        health = cache.get_health_status()
        recommendations = health['recommendations']
        
        # 应该有一些建议
        assert len(recommendations) > 0

    def test_cache_clear(self):
        """测试缓存清理"""
        cache = LRUMessageCache(max_size=10, ttl_seconds=300)
        
        # 添加一些项目
        for i in range(5):
            data = f"message{i}".encode()
            cache.put(data, f"result{i}")
        
        assert len(cache.cache) == 5
        assert cache.stats['total_size'] == 5
        
        # 清理缓存
        cache.clear()
        
        assert len(cache.cache) == 0
        assert len(cache.access_times) == 0
        assert cache.stats['total_size'] == 0
        assert cache.stats['hits'] == 0
        assert cache.stats['misses'] == 0


class TestProtobufCodecWithCache:
    """测试带缓存的Protobuf编解码器"""

    def test_codec_with_cache_enabled(self):
        """测试启用缓存的编解码器"""
        config = ProtobufConfig(
            cache_enabled=True,
            cache_max_size=100,
            cache_ttl_seconds=300
        )
        codec = ProtobufCodec(config)
        
        assert codec.cache is not None
        assert codec.config.cache_enabled is True

    def test_codec_with_cache_disabled(self):
        """测试禁用缓存的编解码器"""
        config = ProtobufConfig(cache_enabled=False)
        codec = ProtobufCodec(config)
        
        assert codec.cache is None
        assert codec.config.cache_enabled is False

    def test_decode_signal_with_cache(self):
        """测试带缓存的信号解码"""
        config = ProtobufConfig(cache_enabled=True, use_fallback_json=True)
        codec = ProtobufCodec(config)
        
        # 准备测试数据
        signal_data = {
            'signal_id': 'test_001',
            'account_id': 'ACC001',
            'symbol': 'EURUSD',
            'action': 'BUY',
            'volume': 0.1
        }
        
        # 编码
        encoded_data = codec.encode_trade_signal(signal_data)
        
        # 第一次解码（应该miss）
        decoded_1 = codec.decode_trade_signal(encoded_data)
        assert decoded_1 is not None
        
        # 第二次解码（应该hit）
        decoded_2 = codec.decode_trade_signal(encoded_data)
        assert decoded_2 == decoded_1
        
        # 检查缓存统计
        cache_stats = codec.get_cache_stats()
        assert cache_stats is not None
        assert cache_stats['hits'] >= 1
        assert cache_stats['misses'] >= 1

    def test_decode_batch_signals_with_cache(self):
        """测试带缓存的批量信号解码"""
        config = ProtobufConfig(cache_enabled=True, use_fallback_json=True)
        codec = ProtobufCodec(config)
        
        # 准备测试数据
        signals = [
            {'signal_id': 'test_001', 'symbol': 'EURUSD'},
            {'signal_id': 'test_002', 'symbol': 'GBPUSD'}
        ]
        
        # 编码
        encoded_data = codec.encode_batch_signals(signals)
        
        # 第一次解码（应该miss）
        decoded_1 = codec.decode_batch_signals(encoded_data)
        assert decoded_1 is not None
        assert len(decoded_1) == 2
        
        # 第二次解码（应该hit）
        decoded_2 = codec.decode_batch_signals(encoded_data)
        assert decoded_2 == decoded_1
        
        # 检查缓存统计
        cache_stats = codec.get_cache_stats()
        assert cache_stats['hits'] >= 1

    def test_cache_performance_impact(self):
        """测试缓存对性能的影响"""
        config = ProtobufConfig(cache_enabled=True, use_fallback_json=True)
        codec = ProtobufCodec(config)
        
        # 准备测试数据
        signal_data = {
            'signal_id': 'perf_test',
            'symbol': 'EURUSD',
            'action': 'BUY'
        }
        encoded_data = codec.encode_trade_signal(signal_data)
        
        # 第一次解码（无缓存）
        start_time = time.time()
        decoded_1 = codec.decode_trade_signal(encoded_data)
        first_decode_time = time.time() - start_time
        
        # 第二次解码（有缓存）
        start_time = time.time()
        decoded_2 = codec.decode_trade_signal(encoded_data)
        second_decode_time = time.time() - start_time
        
        # 缓存命中应该更快（虽然在测试中差异可能很小）
        assert decoded_1 == decoded_2
        # 注意：在简单的测试中，时间差异可能不明显

    def test_cache_error_handling(self):
        """测试缓存错误处理"""
        config = ProtobufConfig(cache_enabled=True, use_fallback_json=True)
        codec = ProtobufCodec(config)
        
        # 测试无效数据
        invalid_data = b"invalid data"
        result = codec.decode_trade_signal(invalid_data)
        # 应该返回None而不是崩溃
        assert result is None
        
        # 检查缓存统计（应该记录miss）
        cache_stats = codec.get_cache_stats()
        assert cache_stats['misses'] >= 1

    def test_global_cache_functions(self):
        """测试全局缓存函数"""
        # 清理全局状态
        clear_cache()
        
        # 配置并使用全局编解码器
        config = ProtobufConfig(cache_enabled=True, use_fallback_json=True)
        codec = get_protobuf_codec(config)
        
        # 编码解码一些数据
        signal_data = {'signal_id': 'global_test', 'symbol': 'EURUSD'}
        encoded_data = codec.encode_trade_signal(signal_data)
        decoded_data = codec.decode_trade_signal(encoded_data)
        
        # 检查全局缓存统计
        stats = get_cache_stats()
        assert stats is not None
        assert stats['total_requests'] > 0
        
        # 检查全局缓存健康状态
        health = get_cache_health()
        assert health is not None
        assert 'status' in health
        
        # 清理全局缓存
        clear_cache()
        
        # 验证清理后的状态
        stats_after_clear = get_cache_stats()
        assert stats_after_clear['total_size'] == 0

    def test_performance_metrics(self):
        """测试性能指标"""
        config = ProtobufConfig(cache_enabled=True)
        codec = ProtobufCodec(config)
        
        metrics = codec.get_performance_metrics()
        
        # 检查性能指标字段
        assert 'protobuf_available' in metrics
        assert 'cache_enabled' in metrics
        assert 'compression_enabled' in metrics
        assert 'cache_hit_rate' in metrics
        assert 'cache_size' in metrics
        assert 'cache_utilization' in metrics

    def test_cache_with_large_data(self):
        """测试大数据缓存"""
        cache = LRUMessageCache(max_size=100, ttl_seconds=300)
        
        # 创建大数据
        large_data = b"x" * 10000  # 10KB数据
        large_result = {"large": True, "size": len(large_data)}
        
        # 放入缓存
        cache.put(large_data, large_result)
        
        # 获取并验证
        cached_result = cache.get(large_data)
        assert cached_result == large_result

    def test_cache_memory_estimation(self):
        """测试缓存内存估算"""
        cache = LRUMessageCache(max_size=10, ttl_seconds=300)
        
        # 添加一些项目
        for i in range(5):
            data = f"message_{i}".encode()
            cache.put(data, f"result_{i}")
        
        stats = cache.get_stats()
        
        # 内存估算应该大于0
        assert stats['memory_estimate_kb'] > 0
        assert isinstance(stats['memory_estimate_kb'], (int, float))


if __name__ == '__main__':
    pytest.main([__file__])