# src/core/signal_router.py
"""
信号路由器 - 纯净版
只负责信号的路由和分发，不包含跟单关系管理
跟单关系由CopyRelationshipManager管理
"""
import asyncio
import logging
from typing import Dict, List, Optional, Any
from ..messaging.message_types import TradeSignal, SignalType
from datetime import datetime
 
logger = logging.getLogger(__name__)


class SignalRouter:
    """
    信号路由器 
    只负责信号的发布和订阅，不管理跟单关系
    """

    def __init__(self, jetstream_client):
        self.js = jetstream_client
        self.signal_cache: Dict[str, TradeSignal] = {}
        self.routing_stats = {
            'signals_published': 0,
            'signals_received': 0,
            'routing_errors': 0
        }

    async def publish_trade_signal(self, signal: TradeSignal):
        """发布交易信号到NATS"""
        try:
            signal_id = f"{signal.master_id}_{signal.ticket}_{int(signal.timestamp)}"
            account_id = signal.master_id
            signal_data = signal.model_dump()

            self.signal_cache[signal_id] = signal

            # 使用新的分层流优先级路由
            from ..messaging.priority_queue import PriorityAnalyzer, MessagePriority
            
            # 分析信号优先级
            priority = PriorityAnalyzer.get_command_priority('send_order', signal_data)
            
            # 使用新的信号流主题
            topic = f"MT5.SIGNALS.{priority.name}.{account_id}"
            await self.js.publish(topic, signal_data)

            self.routing_stats['signals_published'] += 1

            position_data = signal.data
            logger.info(
                f"发布交易信号: {account_id} -> {topic} "
                f"{position_data.symbol} {signal.type.value} {position_data.volume}"
            )

        except Exception as e:
            self.routing_stats['routing_errors'] += 1
            logger.error(f"发布交易信号异常: {e}")

    async def publish_copy_signal(self, target_account: str, signal):
        """发布跟单信号到特定账户"""
        try:
            # 使用新的跨主机信号主题
            topic = f"MT5.SIGNALS.HIGH.{target_account}"

            if hasattr(signal, 'to_dict'):
                signal_data = signal.to_dict()
            else:
                signal_data = signal

            await self.js.publish(topic, signal_data)

            if hasattr(signal, 'master_account'):
                account_id = signal.master_account
                symbol = signal.symbol
                action = signal.action.value if hasattr(signal.action, 'value') else signal.action
                volume = signal.volume
            else:
                account_id = signal.get('master_account', 'UNKNOWN')
                symbol = signal.get('symbol', 'UNKNOWN')
                action = signal.get('action', 'UNKNOWN')
                volume = signal.get('volume', 0.0)

            logger.info(
                f"发布跟单信号: {account_id} -> {target_account} "
                f"{symbol} {action} {volume}"
            )

        except Exception as e:
            self.routing_stats['routing_errors'] += 1
            logger.error(f"发布跟单信号异常: {e}")

    async def subscribe_trade_signals(self, account_id: str, callback):
        """订阅交易信号"""
        try:
            # 使用新的监控流主题
            topic = f"MT5.MONITOR.{account_id}"
            await self.js.subscribe(topic, callback)

            logger.info(f"订阅交易信号: {topic}")

        except Exception as e:
            logger.error(f"订阅交易信号异常: {e}")

    async def subscribe_copy_signals(self, account_id: str, callback):
        """订阅跟单信号"""
        try:
            # 使用新的信号流订阅所有优先级
            topic = f"MT5.SIGNALS.*.{account_id}"
            await self.js.subscribe(topic, callback)

            logger.info(f"订阅跟单信号: {topic}")

        except Exception as e:
            logger.error(f"订阅跟单信号异常: {e}")

    async def subscribe_all_trade_signals(self, callback):
        """订阅所有交易信号（用于跟单关系管理器）"""
        try:
            # 使用新的信号流订阅所有信号
            topic = "MT5.SIGNALS.*"
            await self.js.subscribe(topic, callback)

            logger.info(f"订阅所有交易信号: {topic}")

        except Exception as e:
            logger.error(f"订阅所有交易信号异常: {e}")

    def get_routing_stats(self) -> Dict[str, Any]:
        """获取路由统计"""
        return {
            **self.routing_stats,
            'cached_signals': len(self.signal_cache)
        }

    def clear_signal_cache(self, max_age_seconds: int = 3600):
        """清理过期信号缓存"""
        current_time = datetime.now()
        expired_signals = []

        for signal_id, signal in self.signal_cache.items():
            age = (current_time - signal.timestamp).total_seconds()
            if age > max_age_seconds:
                expired_signals.append(signal_id)

        for signal_id in expired_signals:
            del self.signal_cache[signal_id]

        if expired_signals:
            logger.info(f"清理了 {len(expired_signals)} 个过期信号")


class SignalValidator:
    """信号验证器"""

    def __init__(self):
        self.validation_rules = {
            'volume_min': 0.01,
            'volume_max': 100.0,
            'required_fields': ['signal_id', 'account_id', 'symbol', 'action']
        }

    def validate_signal(self, signal: TradeSignal) -> tuple[bool, List[str]]:
        """验证信号有效性"""
        errors = []

        # 检查必需字段
        for field in self.validation_rules['required_fields']:
            if not getattr(signal, field, None):
                errors.append(f"缺少必需字段: {field}")

        # 检查成交量
        if signal.volume < self.validation_rules['volume_min']:
            errors.append(f"成交量过小: {signal.volume}")
        if signal.volume > self.validation_rules['volume_max']:
            errors.append(f"成交量过大: {signal.volume}")

        # 检查价格合理性
        if signal.price is not None and signal.price <= 0:
            errors.append("价格必须大于0")

        return len(errors) == 0, errors
