"""
专属消费者管理器
确保每个账户的消息只被一个终端消费，防止重复处理
"""
import asyncio
import time
from typing import Dict, Set, Optional, Callable, Any
from dataclasses import dataclass, field
from datetime import datetime, timedelta
import hashlib

from ..utils.logger import get_logger
from .state_manager import StateManager
from ..messaging.jetstream_client import JetStreamClient

logger = get_logger(__name__)


@dataclass
class ConsumerRegistration:
    """消费者注册信息"""
    consumer_id: str
    account_id: str
    terminal_id: str
    subject: str
    callback: Callable
    created_at: datetime = field(default_factory=datetime.now)
    last_heartbeat: datetime = field(default_factory=datetime.now)
    message_count: int = 0
    error_count: int = 0
    is_active: bool = True


@dataclass
class MessageDeduplication:
    """消息去重信息"""
    message_id: str
    account_id: str
    processed_at: datetime
    consumer_id: str
    checksum: str


class ExclusiveConsumerManager:
    """
    专属消费者管理器
    
    核心功能：
    1. 确保每个账户只有一个活跃消费者
    2. 消息去重处理
    3. 消费者健康监控
    4. 故障转移支持
    """
    
    def __init__(self, jetstream_client: JetStreamClient, state_manager: StateManager, 
                 terminal_id: str, host_id: str):
        self.jetstream = jetstream_client
        self.state_manager = state_manager
        self.terminal_id = terminal_id
        self.host_id = host_id
        
        # 消费者管理
        self.registered_consumers: Dict[str, ConsumerRegistration] = {}
        self.active_subscriptions: Dict[str, Any] = {}
        
        # 去重管理
        self.processed_messages: Dict[str, MessageDeduplication] = {}
        self.deduplication_window = 300  # 5分钟去重窗口
        
        # 监控
        self.heartbeat_interval = 30  # 心跳间隔
        self.consumer_timeout = 120   # 消费者超时
        self.monitoring_task: Optional[asyncio.Task] = None
        self.running = False
        
        # Redis键前缀
        self.redis_prefix = "nats:exclusive_consumers"
    
    async def start(self):
        """启动专属消费者管理器"""
        if self.running:
            return
        
        self.running = True
        
        # 启动监控任务
        self.monitoring_task = asyncio.create_task(self._monitoring_loop())
        
        logger.info(f"专属消费者管理器已启动: {self.terminal_id}@{self.host_id}")
    
    async def stop(self):
        """停止专属消费者管理器"""
        self.running = False
        
        if self.monitoring_task:
            self.monitoring_task.cancel()
            try:
                await self.monitoring_task
            except asyncio.CancelledError:
                pass
        
        # 注销所有消费者
        await self._unregister_all_consumers()
        
        logger.info("专属消费者管理器已停止")
    
    async def register_exclusive_consumer(self, account_id: str, subject: str, 
                                        callback: Callable) -> Optional[str]:
        """
        注册专属消费者
        返回consumer_id，如果注册失败返回None
        """
        try:
            # 生成消费者ID
            consumer_id = self._generate_consumer_id(account_id, subject)
            
            # 检查是否已有活跃消费者
            existing_consumer = await self._get_active_consumer(account_id, subject)
            if existing_consumer:
                if existing_consumer['terminal_id'] == self.terminal_id:
                    logger.debug(f"消费者已存在: {account_id} -> {consumer_id}")
                    return existing_consumer['consumer_id']
                else:
                    logger.warning(f"账户 {account_id} 已有其他终端的活跃消费者: {existing_consumer['terminal_id']}")
                    return None
            
            # 尝试获取消费者锁
            lock_acquired = await self._acquire_consumer_lock(account_id, subject, consumer_id)
            if not lock_acquired:
                logger.warning(f"无法获取消费者锁: {account_id}")
                return None
            
            # 🔧 创建专属消费者 - 避免消费者类型冲突
            durable_name = f"exclusive_{self.terminal_id}_{account_id}_{hashlib.md5(subject.encode()).hexdigest()[:8]}"

            # 🔧 首先清理可能存在的冲突消费者
            await self._cleanup_conflicting_consumer(durable_name)

            # 包装回调函数以支持去重
            wrapped_callback = self._create_deduplication_wrapper(account_id, consumer_id, callback)

            # 🔧 使用直接的 JetStream 订阅而不是 subscribe_durable
            subscription = await self._create_push_subscription(
                subject=subject,
                callback=wrapped_callback,
                consumer_name=durable_name,
                account_id=account_id
            )
            
            if not subscription:
                await self._release_consumer_lock(account_id, subject)
                return None
            
            # 注册消费者
            registration = ConsumerRegistration(
                consumer_id=consumer_id,
                account_id=account_id,
                terminal_id=self.terminal_id,
                subject=subject,
                callback=callback
            )
            
            self.registered_consumers[consumer_id] = registration
            self.active_subscriptions[consumer_id] = subscription
            
            # 保存到Redis
            await self._save_consumer_registration(registration)
            
            logger.info(f"专属消费者注册成功: {account_id} -> {consumer_id}")
            return consumer_id
            
        except Exception as e:
            logger.error(f"注册专属消费者失败: {e}")
            return None
    
    async def unregister_consumer(self, consumer_id: str):
        """注销消费者"""
        try:
            if consumer_id not in self.registered_consumers:
                return
            
            registration = self.registered_consumers[consumer_id]
            
            # 取消订阅
            if consumer_id in self.active_subscriptions:
                subscription = self.active_subscriptions[consumer_id]
                try:
                    await subscription.unsubscribe()
                except Exception as e:
                    logger.warning(f"取消订阅失败: {e}")
                
                del self.active_subscriptions[consumer_id]
            
            # 释放锁
            await self._release_consumer_lock(registration.account_id, registration.subject)
            
            # 从Redis删除
            await self._remove_consumer_registration(consumer_id)
            
            # 从内存删除
            del self.registered_consumers[consumer_id]
            
            logger.info(f"消费者注销成功: {consumer_id}")
            
        except Exception as e:
            logger.error(f"注销消费者失败: {e}")
    
    def _generate_consumer_id(self, account_id: str, subject: str) -> str:
        """生成消费者ID"""
        data = f"{self.terminal_id}_{self.host_id}_{account_id}_{subject}_{int(time.time())}"
        return hashlib.md5(data.encode()).hexdigest()
    
    def _create_deduplication_wrapper(self, account_id: str, consumer_id: str, 
                                    original_callback: Callable) -> Callable:
        """创建去重包装器"""
        async def deduplication_wrapper(msg):
            try:
                # 生成消息校验和
                message_checksum = self._calculate_message_checksum(msg)
                message_id = getattr(msg, 'headers', {}).get('Nats-Msg-Id', f"msg_{int(time.time())}")
                
                # 检查是否已处理
                if await self._is_message_processed(account_id, message_id, message_checksum):
                    logger.debug(f"消息已处理，跳过: {message_id}")
                    await msg.ack()
                    return
                
                # 记录消息处理
                await self._record_message_processing(account_id, message_id, message_checksum, consumer_id)
                
                # 调用原始回调
                await original_callback(msg)
                
                # 更新统计
                if consumer_id in self.registered_consumers:
                    self.registered_consumers[consumer_id].message_count += 1
                    self.registered_consumers[consumer_id].last_heartbeat = datetime.now()
                
            except Exception as e:
                logger.error(f"消息处理失败: {e}")
                
                # 更新错误统计
                if consumer_id in self.registered_consumers:
                    self.registered_consumers[consumer_id].error_count += 1
                
                # 重新抛出异常让JetStream处理
                raise
        
        return deduplication_wrapper
    
    def _calculate_message_checksum(self, msg) -> str:
        """计算消息校验和"""
        try:
            # 使用消息数据和主题生成校验和
            data = f"{msg.subject}_{msg.data.decode() if msg.data else ''}"
            return hashlib.md5(data.encode()).hexdigest()
        except Exception:
            return hashlib.md5(str(time.time()).encode()).hexdigest()
    
    async def _is_message_processed(self, account_id: str, message_id: str, checksum: str) -> bool:
        """检查消息是否已处理"""
        try:
            key = f"{self.redis_prefix}:processed:{account_id}:{message_id}"
            stored_checksum = await self.state_manager.get(key)  # 🔧 修复方法名

            return stored_checksum == checksum

        except Exception as e:
            logger.error(f"检查消息处理状态失败: {e}")
            return False
    
    async def _record_message_processing(self, account_id: str, message_id: str, 
                                       checksum: str, consumer_id: str):
        """记录消息处理"""
        try:
            key = f"{self.redis_prefix}:processed:{account_id}:{message_id}"
            await self.state_manager.set(key, checksum, ttl=self.deduplication_window)
            
            # 记录处理详情
            detail_key = f"{self.redis_prefix}:details:{account_id}:{message_id}"
            detail_data = {
                'consumer_id': consumer_id,
                'terminal_id': self.terminal_id,
                'host_id': self.host_id,
                'processed_at': datetime.now().isoformat(),
                'checksum': checksum
            }
            await self.state_manager.set(detail_key, detail_data, ttl=self.deduplication_window)
            
        except Exception as e:
            logger.error(f"记录消息处理失败: {e}")
    
    async def _acquire_consumer_lock(self, account_id: str, subject: str, consumer_id: str) -> bool:
        """获取消费者锁"""
        try:
            lock_key = f"{self.redis_prefix}:locks:{account_id}:{hashlib.md5(subject.encode()).hexdigest()}"
            lock_data = {
                'consumer_id': consumer_id,
                'terminal_id': self.terminal_id,
                'host_id': self.host_id,
                'acquired_at': datetime.now().isoformat()
            }

            # 🔧 实现 set_if_not_exists 逻辑
            # 首先检查锁是否已存在
            existing_lock = await self.state_manager.get(lock_key)
            if existing_lock is not None:
                logger.debug(f"获取消费者锁失败: {account_id} - 锁已存在")
                return False

            # 如果不存在，则设置锁
            success = await self.state_manager.set(lock_key, lock_data, ttl=self.consumer_timeout)

            if success:
                logger.debug(f"获取消费者锁成功: {account_id}")
                return True
            else:
                logger.debug(f"获取消费者锁失败: {account_id}")
                return False

        except Exception as e:
            logger.error(f"获取消费者锁异常: {e}")
            return False
    
    async def _release_consumer_lock(self, account_id: str, subject: str):
        """释放消费者锁"""
        try:
            lock_key = f"{self.redis_prefix}:locks:{account_id}:{hashlib.md5(subject.encode()).hexdigest()}"
            await self.state_manager.delete(lock_key)
            
        except Exception as e:
            logger.error(f"释放消费者锁失败: {e}")
    
    async def _get_active_consumer(self, account_id: str, subject: str) -> Optional[Dict]:
        """获取活跃消费者信息"""
        try:
            lock_key = f"{self.redis_prefix}:locks:{account_id}:{hashlib.md5(subject.encode()).hexdigest()}"
            lock_data = await self.state_manager.get(lock_key)  # 🔧 修复方法名

            return lock_data if lock_data else None

        except Exception as e:
            logger.error(f"获取活跃消费者失败: {e}")
            return None

    async def _cleanup_conflicting_consumer(self, consumer_name: str):
        """清理可能存在的冲突消费者"""
        try:
            # 尝试删除可能存在的同名消费者
            stream_name = self.jetstream.config.stream_name
            await self.jetstream.js.delete_consumer(stream_name, consumer_name)
            logger.debug(f"清理冲突消费者成功: {consumer_name}")
        except Exception as e:
            # 消费者不存在是正常的
            logger.debug(f"清理消费者（可能不存在）: {consumer_name} - {e}")

    async def _create_push_subscription(self, subject: str, callback, consumer_name: str, account_id: str):
        """创建 PUSH 订阅，避免消费者类型冲突"""
        try:
            # 🔧 简化方案：直接使用 jetstream 的 subscribe_durable 方法
            # 这个方法会自动处理消费者创建和类型匹配
            stream_name = self.jetstream.config.stream_name
            logger.info(f"🔧 创建订阅: 流={stream_name}, 主题={subject}, 消费者={consumer_name}")

            # 使用 JetStreamClient 的 subscribe_durable 方法
            # 注意：subscribe_durable 返回 bool，不是订阅对象
            success = await self.jetstream.subscribe_durable(
                subject=subject,
                callback=callback,
                consumer_name=consumer_name
            )

            if success:
                logger.info(f"✅ 专属订阅创建成功: {consumer_name}")
                # 返回一个标识符，表示订阅成功
                return consumer_name
            else:
                logger.error(f"❌ 专属订阅创建失败: {consumer_name} - subscribe_durable返回False")
                return None

        except Exception as e:
            logger.error(f"❌ 创建专属订阅失败: {e}")
            return None

    async def _save_consumer_registration(self, registration: ConsumerRegistration):
        """保存消费者注册信息到Redis"""
        try:
            key = f"{self.redis_prefix}:registrations:{registration.consumer_id}"
            data = {
                'consumer_id': registration.consumer_id,
                'account_id': registration.account_id,
                'terminal_id': registration.terminal_id,
                'subject': registration.subject,
                'created_at': registration.created_at.isoformat(),
                'last_heartbeat': registration.last_heartbeat.isoformat(),
                'message_count': registration.message_count,
                'error_count': registration.error_count,
                'is_active': registration.is_active,
                'host_id': self.host_id
            }

            await self.state_manager.set(key, data, ttl=self.consumer_timeout * 2)

        except Exception as e:
            logger.error(f"保存消费者注册信息失败: {e}")

    async def _remove_consumer_registration(self, consumer_id: str):
        """从Redis删除消费者注册信息"""
        try:
            key = f"{self.redis_prefix}:registrations:{consumer_id}"
            await self.state_manager.delete(key)

        except Exception as e:
            logger.error(f"删除消费者注册信息失败: {e}")

    async def _unregister_all_consumers(self):
        """注销所有消费者"""
        for consumer_id in list(self.registered_consumers.keys()):
            await self.unregister_consumer(consumer_id)

    async def _monitoring_loop(self):
        """监控循环"""
        while self.running:
            try:
                # 更新心跳
                await self._update_heartbeats()

                # 检查消费者健康状态
                await self._check_consumer_health()

                # 清理过期的处理记录
                await self._cleanup_processed_messages()

                # 等待下次检查
                await asyncio.sleep(self.heartbeat_interval)

            except Exception as e:
                logger.error(f"监控循环异常: {e}")
                await asyncio.sleep(5)

    async def _update_heartbeats(self):
        """更新心跳"""
        for consumer_id, registration in self.registered_consumers.items():
            try:
                registration.last_heartbeat = datetime.now()
                await self._save_consumer_registration(registration)

            except Exception as e:
                logger.error(f"更新心跳失败: {e}")

    async def _check_consumer_health(self):
        """检查消费者健康状态"""
        current_time = datetime.now()
        unhealthy_consumers = []

        for consumer_id, registration in self.registered_consumers.items():
            # 检查超时
            if (current_time - registration.last_heartbeat).total_seconds() > self.consumer_timeout:
                unhealthy_consumers.append(consumer_id)
                continue

            # 检查错误率
            if registration.message_count > 0:
                error_rate = registration.error_count / registration.message_count
                if error_rate > 0.1:  # 错误率超过10%
                    logger.warning(f"消费者错误率过高: {consumer_id}, 错误率: {error_rate:.2%}")

        # 清理不健康的消费者
        for consumer_id in unhealthy_consumers:
            logger.warning(f"清理不健康的消费者: {consumer_id}")
            await self.unregister_consumer(consumer_id)

    async def _cleanup_processed_messages(self):
        """清理过期的处理记录"""
        try:
            # 清理内存中的记录
            current_time = datetime.now()
            expired_messages = []

            for message_id, dedup_info in self.processed_messages.items():
                if (current_time - dedup_info.processed_at).total_seconds() > self.deduplication_window:
                    expired_messages.append(message_id)

            for message_id in expired_messages:
                del self.processed_messages[message_id]

            if expired_messages:
                logger.debug(f"清理过期消息记录: {len(expired_messages)}条")

        except Exception as e:
            logger.error(f"清理过期消息记录失败: {e}")

    async def get_consumer_stats(self) -> Dict[str, Any]:
        """获取消费者统计信息"""
        stats = {
            'total_consumers': len(self.registered_consumers),
            'active_subscriptions': len(self.active_subscriptions),
            'processed_messages_cache': len(self.processed_messages),
            'consumers': []
        }

        for consumer_id, registration in self.registered_consumers.items():
            consumer_stats = {
                'consumer_id': consumer_id,
                'account_id': registration.account_id,
                'subject': registration.subject,
                'message_count': registration.message_count,
                'error_count': registration.error_count,
                'error_rate': registration.error_count / max(registration.message_count, 1),
                'uptime_seconds': (datetime.now() - registration.created_at).total_seconds(),
                'last_heartbeat': registration.last_heartbeat.isoformat(),
                'is_active': registration.is_active
            }
            stats['consumers'].append(consumer_stats)

        return stats
