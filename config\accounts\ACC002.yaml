# ACC002 - 从账户配置
# 只包含账户特定的配置，主机分配和跟单关系在其他配置文件中定义

# ============================================================================
# 账户基础信息
# ============================================================================
account:
  id: "ACC002"
  name: "跟单账户2"
  enabled: true
  description: "跟单交易账户，具备信号跟随能力"
  
  # 账户能力定义（角色由关系管理系统动态分配）
  capabilities:
    can_generate_signals: false     # 能否生成交易信号
    can_follow_signals: true        # 能否跟随交易信号
    signal_priority: "medium"       # 信号优先级
    max_follow_sources: 3           # 最大跟随信号源数量

# ============================================================================
# MT5连接配置
# ============================================================================
mt5:
  connection:
    login: ********
    password:  ${MT5_ACC002_PASSWORD} # Jingenqi0724@ # ${MT5_ACC002_PASSWORD} 
    server: "TradeMaxGlobal-Demo"
    terminal_path: "D:/MetaTrader5/v2-mt5/terminal64.exe"
    
  # 连接参数
  connection_params:
    timeout: 10
    retry_attempts: 3
    retry_delay: 5
    auto_restart: true
    max_restarts: 5
    
  # 连接池配置
  connection_pool:
    enabled: true
    max_connections: 3              # 从账户连接数较少
    idle_timeout: 300

# ============================================================================
# 交易配置
# ============================================================================
trading:
  # 基础交易参数
  parameters:
    magic_number: 12346             # 与主账户不同的Magic Number
    slippage: 3
    max_deviation: 20
    
  # 手数限制（比主账户保守）
  volume_limits:
    max_volume: 100.0                # 比主账户小
    min_volume: 0.01
    default_volume: 1.0
    
  # 允许的交易品种
  allowed_symbols:
    mode: "all"                     # all/whitelist/blacklist
    symbols: []                     # 空数组表示所有品种
    
  # 交易时间限制
  trading_hours:
    enabled: false                  # 不限制交易时间
    start_time: "00:00"
    end_time: "24:00"
    timezone: "UTC"
    exclude_weekends: true
    
  # 交易模式（从账户特定）
  trading_mode:
    manual_override: True         # 从账户禁止手动交易
    auto_trading: true              # 只接受跟单信号
    copy_signal_generation: false   # 从账户不生成信号
    copy_signal_execution: true     # 执行跟单信号

# ============================================================================
# 风险管理配置（更保守）
# ============================================================================


# ============================================================================
# 跟单执行配置
# ============================================================================
copy_execution:
  # 执行参数
  execution_settings:
    max_execution_delay_ms: 5000    # 最大执行延迟
    retry_on_failure: true
    max_retries: 3
    retry_delay_ms: 1000
    
  # 信号过滤
  signal_filtering:
    enabled: true
    min_confidence: 0.8             # 最小信号置信度
    max_signal_age_ms: 10000        # 信号最大年龄10秒
    
  # 执行验证
  execution_validation:
    verify_account_balance: true
    verify_margin_level: true
    verify_symbol_availability: true
    

# ============================================================================
# 通知配置
# ============================================================================
notifications:
  # Telegram通知
  telegram:
    enabled: true
    chat_id: "**********"
    
  # 通知级别（从账户特定）
  levels:
    trade_opened: true
    trade_closed: true
    copy_executed: true             # 跟单执行通知
    copy_failed: true               # 跟单失败通知
    emergency_stop: true
    risk_alert: true
    system_error: true
    daily_summary: true

# ============================================================================
# 数据存储配置
# ============================================================================
data_storage:
  # 本地数据路径
  local_paths:
    data_directory: "./data/accounts/ACC002"
    logs_directory: "./logs/accounts/ACC002"
    backup_directory: "./backups/accounts/ACC002"
    
  # 数据保留
  retention:
    trade_history_days: 90
    log_files_days: 30
    backup_files_days: 7
    copy_signals_days: 30           # 跟单信号保留
    
  # 缓存配置
  cache:
    enabled: true
    ttl_seconds: 300                # 5分钟，比主账户短
    max_size_mb: 50                 # 比主账户小

# ============================================================================
# 高级功能配置
# ============================================================================
advanced:
  # EA支持（从账户通常不需要）
  expert_advisors:
    enabled: false
    allowed_eas: []
    
  # 脚本支持
  scripts:
    enabled: false
    allowed_scripts: []
    
  # 自定义指标
  custom_indicators:
    enabled: false

# ============================================================================
# 部署配置
# ============================================================================
deployment:
  host_id: "uk-001"                    # 部署到的主机ID
  role: "secondary"                    # 部署角色
  priority: 2                          # 部署优先级

# ============================================================================
# 环境特定配置
# ============================================================================
environment_overrides:
  development:
    trading:
      volume_limits:
        max_volume: 0.5             # 开发环境更小手数
    risk_management:
      limits:
        max_daily_loss: 50.0        # 开发环境限制亏损
        
  testing:
    trading:
      volume_limits:
        max_volume: 0.05            # 测试环境极小手数
    risk_management:
      limits:
        max_daily_loss: 20.0
        
  production:
    # 生产环境使用默认配置
    pass

# ============================================================================
# 元数据
# ============================================================================
metadata:
  created_at: "2024-01-01T00:00:00Z"
  updated_at: "2024-01-01T00:00:00Z"
  version: "2.0.0"
  config_schema_version: "1.0"
  tags: ["copy-trading", "conservative"]