[tool:pytest]
markers =
    asyncio: mark test as an asyncio test
    slow: mark test as slow running
    integration: mark test as integration test
    unit: mark test as unit test
    performance: mark test as performance test

asyncio_mode = auto

testpaths = tests

python_files = test_*.py
python_classes = Test*
python_functions = test_*

addopts = 
    -v
    --tb=short
    --strict-markers
    --disable-warnings
    
filterwarnings =
    ignore::DeprecationWarning
    ignore::PendingDeprecationWarning
    ignore::ResourceWarning