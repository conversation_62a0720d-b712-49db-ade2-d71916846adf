# MT5分布式交易系统 - 跟单关系配置
# 统一定义所有跟单关系，这是跟单逻辑的唯一权威来源
# 移除所有其他地方的跟单关系定义

# ============================================================================
# 跟单关系定义
# ============================================================================
relationships:
  # 主要跟单关系：ACC001 -> ACC002
  - relationship_id: "ACC001_to_ACC002"
    enabled: true
    priority: 1
    
    # 主从账户
    master_account: "ACC001"
    slave_account: "ACC002"
    
    # 跟单模式
    copy_mode: "forward"      # forward/reverse/bidirectional
    copy_ratio: 1.0           # 手数比例
    
    # 交易限制
    limits:
      max_volume_per_trade: 10.0
      min_volume_per_trade: 0.01
      max_positions: 10
      max_daily_volume: 100.0
      max_daily_trades: 50
      
    # 风险管理
    risk_management:
      risk_multiplier: 1.0
      max_drawdown: 0.15      # 15%
      stop_loss_multiplier: 1.0
      take_profit_multiplier: 1.0
      reverse_sl_tp: false
      
    # 时间限制
    time_restrictions:
      active_hours:
        start: "00:00"
        end: "24:00"
        timezone: "UTC"
      
      active_days:
        - "monday"
        - "tuesday" 
        - "wednesday"
        - "thursday"
        - "friday"
        
      exclude_news_events: false
      
    # 品种过滤
    symbol_filter:
      mode: "whitelist"       # whitelist/blacklist/all
      symbols: []             # 空数组表示所有品种
      
    # 执行配置
    execution:
      delay_ms: 100           # 执行延迟
      timeout_ms: 5000        # 执行超时
      retry_attempts: 3
      retry_delay_ms: 1000
      
    # 监控配置
    monitoring:
      track_performance: true
      alert_on_failure: true
      log_all_trades: true
      
  # 潜在的第二个跟单关系：ACC001 -> ACC003（当前禁用）
  - relationship_id: "ACC001_to_ACC003"
    enabled: false            # 当前禁用
    priority: 2
    
    master_account: "ACC001"
    slave_account: "ACC003"
    
    copy_mode: "forward"
    copy_ratio: 0.5           # 50%手数
    
    limits:
      max_volume_per_trade: 5.0
      min_volume_per_trade: 0.01
      max_positions: 5
      max_daily_volume: 50.0
      max_daily_trades: 30
      
    risk_management:
      risk_multiplier: 0.8
      max_drawdown: 0.10      # 10%
      reverse_sl_tp: false
      
    time_restrictions:
      active_hours:
        start: "08:00"
        end: "18:00"
        timezone: "UTC"
        
      active_days:
        - "monday"
        - "tuesday"
        - "wednesday"
        - "thursday"
        - "friday"
        
    symbol_filter:
      mode: "whitelist"
      symbols:
        - "EURUSD"
        - "GBPUSD"
        - "USDJPY"
        
    execution:
      delay_ms: 200
      timeout_ms: 8000
      retry_attempts: 5
      retry_delay_ms: 1500
      
    monitoring:
      track_performance: true
      alert_on_failure: true
      log_all_trades: true

# ============================================================================
# 全局跟单设置
# ============================================================================
global_settings:
  # 系统级别限制
  system_limits:
    max_concurrent_relationships: 10
    max_total_positions: 50
    emergency_stop_enabled: true
    
  # 性能优化
  performance:
    batch_processing: true
    batch_size: 5
    batch_timeout_ms: 1000
    
  # 监控和统计
  monitoring:
    collect_statistics: true
    statistics_interval: 300  # 5分钟
    performance_tracking: true
    
  # 故障处理
  fault_tolerance:
    continue_on_error: true
    max_consecutive_failures: 5
    cooldown_period_ms: 30000  # 30秒
    
  # 安全设置
  security:
    require_confirmation: false
    audit_trail: true
    encrypt_signals: false

# ============================================================================
# 跟单策略配置
# ============================================================================
strategies:
  # 标准跟单策略
  standard:
    name: "Standard Copy Trading"
    description: "基础跟单策略，1:1复制交易"
    
    parameters:
      copy_ratio: 1.0
      delay_ms: 100
      risk_multiplier: 1.0
      
  # 保守跟单策略
  conservative:
    name: "Conservative Copy Trading"
    description: "保守跟单策略，降低风险"
    
    parameters:
      copy_ratio: 0.5
      delay_ms: 200
      risk_multiplier: 0.8
      max_drawdown: 0.10
      
  # 激进跟单策略
  aggressive:
    name: "Aggressive Copy Trading"
    description: "激进跟单策略，追求收益"
    
    parameters:
      copy_ratio: 1.5
      delay_ms: 50
      risk_multiplier: 1.2
      max_drawdown: 0.20

# ============================================================================
# 动态调整规则
# ============================================================================
dynamic_adjustment:
  enabled: false  # 当前关闭，未来功能
  
  # 性能调整
  performance_based:
    enabled: false
    adjustment_interval: 3600  # 1小时
    
    rules:
      - condition: "win_rate < 0.4"
        action: "reduce_copy_ratio"
        factor: 0.8
        
      - condition: "drawdown > 0.15"
        action: "pause_copying"
        duration: 1800  # 30分钟
        
  # 市场条件调整
  market_condition:
    enabled: false
    
    rules:
      - condition: "high_volatility"
        action: "increase_delay"
        value: 500  # ms
        
      - condition: "news_event"
        action: "pause_copying"
        duration: 300  # 5分钟

# ============================================================================
# 验证规则
# ============================================================================
validation:
  # 关系验证
  relationship_validation:
    - "master_account_exists"
    - "slave_account_exists"
    - "no_circular_dependencies"
    - "no_duplicate_relationships"
    - "accounts_on_same_server"        # 当前系统约束
    
  # 配置验证
  configuration_validation:
    - "copy_ratio_positive"
    - "volume_limits_valid"
    - "time_restrictions_valid"
    - "risk_parameters_reasonable"
    
  # 运行时验证
  runtime_validation:
    - "account_balance_sufficient"
    - "network_connectivity_ok"
    - "service_health_good"
    - "no_conflicting_trades"