#!/usr/bin/env python3
"""
MT5 分布式跟单系统 - Python 启动脚本
替代 entrypoint.sh，使用 YAML 配置文件
基于正确架构设计原则的自动化部署
"""

import os
import sys
import time
import socket
import yaml
import logging
from typing import Dict, List

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class MT5DeploymentManager:
    """MT5 部署管理器"""
    
    def __init__(self, config_path: str = "/app/docker/deployment-config.yaml"):
        self.config_path = config_path
        self.config = self.load_config()
        self.service_type = os.getenv('MT5_NODE_TYPE', 'unknown')
        
    def load_config(self) -> Dict:
        """加载部署配置"""
        try:
            with open(self.config_path, 'r', encoding='utf-8') as f:
                return yaml.safe_load(f)
        except Exception as e:
            logger.error(f"加载配置文件失败: {e}")
            sys.exit(1)
    
    def check_dependencies(self) -> bool:
        """检查服务依赖"""
        logger.info("检查服务依赖...")
        
        dependencies = self.config.get('dependencies', {})
        
        for service, config in dependencies.items():
            if not config.get('health_check', False):
                continue
                
            host = os.path.expandvars(config['host'])
            port = config['port']
            timeout = config.get('timeout', 60)
            
            if not self.wait_for_service(host, port, service, timeout):
                logger.error(f"依赖服务 {service} 不可用")
                return False
        
        logger.info("所有依赖服务检查通过")
        return True
    
    def wait_for_service(self, host: str, port: int, service: str, timeout: int = 60) -> bool:
        """等待服务启动"""
        logger.info(f"等待 {service} 服务启动 ({host}:{port})...")
        
        start_time = time.time()
        while time.time() - start_time < timeout:
            try:
                with socket.create_connection((host, port), timeout=1):
                    logger.info(f"{service} 服务已启动")
                    return True
            except (socket.error, ConnectionRefusedError):
                time.sleep(1)
        
        logger.error(f"{service} 服务启动超时")
        return False
    
    def validate_environment(self) -> bool:
        """验证环境变量"""
        logger.info("验证环境变量...")
        
        # 只检查通用必需变量，不包括 ACCOUNT_ID（某些服务不需要）
        required_vars = ['MT5_CONFIG_PATH', 'COPY_RELATIONSHIPS_CONFIG', 'HOST_ID', 'MT5_NODE_TYPE']
        service_config = self.config.get('service_types', {}).get(self.service_type, {})
        service_required = service_config.get('required_env', [])

        all_required = set(service_required)  # 只检查服务特定的必需变量
        missing_vars = []
        
        for var in all_required:
            if not os.getenv(var):
                missing_vars.append(var)
        
        if missing_vars:
            logger.error(f"缺少必需的环境变量: {missing_vars}")
            return False
        
        logger.info("环境变量验证通过")
        return True
    
    def setup_environment(self):
        """设置环境"""
        logger.info("设置运行环境...")
        
        # 设置默认配置路径
        if not os.getenv('MT5_CONFIG_PATH'):
            os.environ['MT5_CONFIG_PATH'] = self.config['config_files']['system_config']
        
        if not os.getenv('COPY_RELATIONSHIPS_CONFIG'):
            os.environ['COPY_RELATIONSHIPS_CONFIG'] = self.config['config_files']['copy_relationships']
        
        # 设置 Python 路径
        if not os.getenv('PYTHONPATH'):
            os.environ['PYTHONPATH'] = '/app'
        
        logger.info("环境设置完成")
    
    def get_startup_command(self) -> List[str]:
        """获取启动命令"""
        service_config = self.config.get('service_types', {}).get(self.service_type, {})
        
        if not service_config:
            logger.error(f"未知的服务类型: {self.service_type}")
            sys.exit(1)
        
        command_template = service_config.get('startup_command', '')
        if not command_template:
            logger.error(f"服务类型 {self.service_type} 没有配置启动命令")
            sys.exit(1)
        
        # 展开环境变量
        command = os.path.expandvars(command_template)
        return command.split()
    
    def print_startup_info(self):
        """打印启动信息"""
        print("=" * 60)
        print(f"MT5 分布式跟单系统启动")
        print("=" * 60)
        print(f"启动时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"服务类型: {self.service_type}")
        print(f"主机ID: {os.getenv('HOST_ID', 'unknown')}")
        print(f"账户ID: {os.getenv('ACCOUNT_ID', 'N/A')}")
        print(f"部署模式: {os.getenv('MT5_DEPLOYMENT_MODE', 'development')}")
        print(f"配置文件: {os.getenv('MT5_CONFIG_PATH', 'N/A')}")
        print(f"跟单配置: {os.getenv('COPY_RELATIONSHIPS_CONFIG', 'N/A')}")
        print("=" * 60)
    
    def run(self):
        """运行服务"""
        try:
            # 打印启动信息
            self.print_startup_info()
            
            # 验证环境变量
            if not self.validate_environment():
                sys.exit(1)
            
            # 设置环境
            self.setup_environment()
            
            # 检查依赖服务
            if not self.check_dependencies():
                sys.exit(1)
            
            # 获取启动命令
            command = self.get_startup_command()
            
            logger.info(f"启动服务: {' '.join(command)}")
            
            # 启动服务
            os.execvp(command[0], command)
            
        except KeyboardInterrupt:
            logger.info("收到停止信号，正在关闭...")
            sys.exit(0)
        except Exception as e:
            logger.error(f"启动失败: {e}")
            sys.exit(1)

def main():
    """主函数"""
    if len(sys.argv) < 2:
        print("用法: python entrypoint.py <service_type>")
        print("服务类型: master, slave, api, coordinator")
        sys.exit(1)
    
    service_type = sys.argv[1]
    os.environ['MT5_NODE_TYPE'] = service_type
    
    manager = MT5DeploymentManager()
    manager.run()

if __name__ == "__main__":
    main()
