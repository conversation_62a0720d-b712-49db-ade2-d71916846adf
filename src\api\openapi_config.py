"""
OpenAPI配置和文档增强
"""
from typing import Dict, Any, List
from fastapi import FastAPI
from fastapi.openapi.utils import get_openapi
from fastapi.openapi.docs import get_swagger_ui_html
from fastapi.responses import HTMLResponse


def create_custom_openapi(app: FastAPI) -> Dict[str, Any]:
    """创建自定义OpenAPI配置"""
    if app.openapi_schema:
        return app.openapi_schema
    
    openapi_schema = get_openapi(
        title="MT5高频交易复制系统",
        version="2.0.0",
        description="""
## 🚀 MT5高频交易复制系统 API

这是一个分布式、高频、低延迟的MetaTrader5交易复制系统，具有以下特性：

### ✨ 核心功能
- **高频信号处理**：支持1ms-100ms的信号传输延迟
- **智能批量处理**：动态调整批量大小（5-100个信号）
- **内存池管理**：优化内存分配和回收
- **连接池复用**：NATS、Redis连接池管理
- **分布式架构**：跨主机账户配对和管理

### 🛠️ 技术特性
- **异步处理**：基于asyncio的高性能异步架构
- **智能调度**：自适应批量大小和频率调整
- **实时监控**：集成Prometheus指标和健康检查
- **故障恢复**：自动重连和故障转移机制

### 📊 性能指标
- **信号延迟**：< 15ms
- **吞吐量**：> 1000 signals/second
- **可用性**：99.9%
- **内存效率**：对象池复用 > 90%

### 🔧 架构组件
- **主监控器**：高频轮询MT5状态变化
- **从执行器**：接收信号并执行交易
- **批量处理器**：智能信号批处理
- **分布式协调**：多主机协调管理
- **连接管理**：连接池和会话管理

### 🌐 部署支持
- **Docker容器化**：完整的Docker部署方案
- **SystemD服务**：Linux系统服务管理
- **集群部署**：多节点分布式部署
- **监控集成**：Prometheus + Grafana

### 🔐 安全特性
- **API密钥认证**：JWT token验证
- **CORS支持**：跨域请求控制
- **数据加密**：敏感数据加密传输
- **访问控制**：基于角色的权限管理

### 📈 监控和调试
- **实时指标**：性能和业务指标
- **健康检查**：系统健康状态监控
- **日志管理**：结构化日志记录
- **错误追踪**：详细的错误堆栈
        """,
        routes=app.routes,
        tags=[
            {
                "name": "系统管理",
                "description": "系统状态、健康检查、主机管理"
            },
            {
                "name": "账户管理", 
                "description": "MT5账户注册、配置、状态管理"
            },
            {
                "name": "交易配对",
                "description": "主从账户配对、跟单策略管理"
            },
            {
                "name": "分布式协调",
                "description": "跨主机协调、负载均衡、故障转移"
            },
            {
                "name": "性能监控",
                "description": "实时性能指标、统计分析"
            },
            {
                "name": "配置管理",
                "description": "系统配置、参数调整"
            }
        ]
    )
    
    # 添加自定义信息
    openapi_schema["info"]["x-logo"] = {
        "url": "https://fastapi.tiangolo.com/img/logo-margin/logo-teal.png",
        "altText": "MT5 Trading System"
    }
    
    # 添加服务器信息
    openapi_schema["servers"] = [
        {
            "url": "http://localhost:8000",
            "description": "开发环境"
        },
        {
            "url": "https://api.mt5trading.com",
            "description": "生产环境"
        }
    ]
    
    # 添加安全方案
    if "components" not in openapi_schema:
        openapi_schema["components"] = {}
    if "securitySchemes" not in openapi_schema["components"]:
        openapi_schema["components"]["securitySchemes"] = {}
    
    openapi_schema["components"]["securitySchemes"] = {
        "ApiKeyAuth": {
            "type": "apiKey",
            "in": "header",
            "name": "X-API-Key",
            "description": "API密钥认证"
        },
        "BearerAuth": {
            "type": "http",
            "scheme": "bearer",
            "bearerFormat": "JWT",
            "description": "JWT Bearer Token"
        }
    }
    
    # 添加全局安全要求
    openapi_schema["security"] = [
        {"ApiKeyAuth": []},
        {"BearerAuth": []}
    ]
    
    # 添加响应示例
    if "examples" not in openapi_schema["components"]:
        openapi_schema["components"]["examples"] = {}
    
    openapi_schema["components"]["examples"] = {
        "SuccessResponse": {
            "summary": "成功响应",
            "value": {
                "success": True,
                "message": "操作成功",
                "data": {},
                "timestamp": 1641024000
            }
        },
        "ErrorResponse": {
            "summary": "错误响应",
            "value": {
                "success": False,
                "message": "操作失败",
                "error": "详细错误信息",
                "timestamp": 1641024000
            }
        }
    }
    
    # 添加扩展信息
    openapi_schema["x-tagGroups"] = [
        {
            "name": "核心功能",
            "tags": ["系统管理", "账户管理", "交易配对"]
        },
        {
            "name": "高级功能", 
            "tags": ["分布式协调", "性能监控", "配置管理"]
        }
    ]
    
    app.openapi_schema = openapi_schema
    return app.openapi_schema


def create_custom_swagger_ui_html(openapi_url: str = "/openapi.json") -> HTMLResponse:
    """创建自定义Swagger UI HTML"""
    html_content = f"""
<!DOCTYPE html>
<html>
<head>
    <title>MT5 Trading System API</title>
    <link rel="stylesheet" type="text/css" href="https://unpkg.com/swagger-ui-dist@3.52.5/swagger-ui.css" />
    <link rel="icon" type="image/png" href="https://fastapi.tiangolo.com/img/favicon.png" />
    <style>
        .swagger-ui .topbar {{
            background-color: #1f2937;
            border-bottom: 1px solid #374151;
        }}
        .swagger-ui .topbar .download-url-wrapper {{
            display: none;
        }}
        .swagger-ui .info .title {{
            color: #10b981;
        }}
        .swagger-ui .info .description {{
            color: #6b7280;
        }}
        .swagger-ui .scheme-container {{
            background: #f9fafb;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            padding: 16px;
            margin: 16px 0;
        }}
        .swagger-ui .opblock.opblock-get {{
            border-color: #10b981;
        }}
        .swagger-ui .opblock.opblock-post {{
            border-color: #3b82f6;
        }}
        .swagger-ui .opblock.opblock-put {{
            border-color: #f59e0b;
        }}
        .swagger-ui .opblock.opblock-delete {{
            border-color: #ef4444;
        }}
        .custom-header {{
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            text-align: center;
            margin-bottom: 20px;
        }}
        .custom-header h1 {{
            margin: 0;
            font-size: 2.5em;
        }}
        .custom-header p {{
            margin: 10px 0 0 0;
            font-size: 1.2em;
            opacity: 0.9;
        }}
        .performance-badges {{
            display: flex;
            justify-content: center;
            gap: 15px;
            margin-top: 15px;
        }}
        .badge {{
            background: rgba(255, 255, 255, 0.2);
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 0.9em;
        }}
    </style>
</head>
<body>
    <div class="custom-header">
        <h1>🚀 MT5 Trading System API</h1>
        <p>高频分布式交易复制系统</p>
        <div class="performance-badges">
            <span class="badge">⚡ &lt; 15ms 延迟</span>
            <span class="badge">🔄 1000+ signals/s</span>
            <span class="badge">🌐 分布式架构</span>
            <span class="badge">🛡️ 高可用性</span>
        </div>
    </div>
    <div id="swagger-ui"></div>
    <script src="https://unpkg.com/swagger-ui-dist@3.52.5/swagger-ui-bundle.js"></script>
    <script>
        const ui = SwaggerUIBundle({{
            url: '{openapi_url}',
            dom_id: '#swagger-ui',
            presets: [
                SwaggerUIBundle.presets.apis,
                SwaggerUIBundle.presets.standalone
            ],
            layout: "BaseLayout",
            deepLinking: true,
            showExtensions: true,
            showCommonExtensions: true,
            docExpansion: "none",
            filter: true,
            tryItOutEnabled: true,
            requestInterceptor: function(request) {{
                // 添加自定义请求头
                request.headers['X-Client'] = 'SwaggerUI';
                return request;
            }},
            responseInterceptor: function(response) {{
                // 处理响应
                console.log('API Response:', response);
                return response;
            }}
        }});
    </script>
</body>
</html>
    """
    return HTMLResponse(content=html_content, status_code=200)


def add_openapi_enhancements(app: FastAPI):
    """添加OpenAPI增强功能"""
    
    # 设置自定义OpenAPI生成器
    app.openapi = lambda: create_custom_openapi(app)
    
    # 添加自定义文档路由
    @app.get("/docs", include_in_schema=False)
    async def custom_swagger_ui_html():
        return create_custom_swagger_ui_html()
    
    # 添加API信息端点
    @app.get("/api/info", 
             summary="获取API信息",
             description="获取系统API的基本信息和版本",
             tags=["系统管理"],
             response_model=Dict[str, Any])
    async def get_api_info():
        """获取API信息"""
        return {
            "name": "MT5 Trading System API",
            "version": "2.0.0",
            "description": "高频分布式MT5交易复制系统",
            "author": "MT5 Trading Team",
            "documentation": "/docs",
            "openapi_spec": "/openapi.json",
            "features": [
                "高频信号处理",
                "智能批量处理", 
                "内存池管理",
                "连接池复用",
                "分布式架构"
            ],
            "performance": {
                "signal_latency": "< 15ms",
                "throughput": "> 1000 signals/second",
                "availability": "99.9%",
                "memory_efficiency": "> 90%"
            },
            "endpoints": {
                "total": len(app.routes),
                "health_check": "/health",
                "metrics": "/metrics", 
                "documentation": "/docs"
            }
        }
    
    # 添加API统计端点
    @app.get("/api/stats",
             summary="获取API统计信息", 
             description="获取API使用统计和性能指标",
             tags=["性能监控"],
             response_model=Dict[str, Any])
    async def get_api_stats():
        """获取API统计信息"""
        from ..utils.metrics import get_metrics_collector
        
        metrics = get_metrics_collector()
        
        return {
            "uptime": time.time() - app.state.start_time if hasattr(app.state, 'start_time') else 0,
            "requests": {
                "total": metrics.get_counter_value("api_requests_total"),
                "errors": metrics.get_counter_value("api_errors_total"),
                "avg_duration": metrics.get_histogram_avg("api_request_duration_seconds")
            },
            "system": {
                "memory_pools": metrics.get_gauge_value("memory_pool_total_allocated"),
                "connection_pools": metrics.get_gauge_value("connection_pool_active"),
                "batch_processor": metrics.get_gauge_value("batch_processor_avg_batch_size")
            },
            "trading": {
                "signals_processed": metrics.get_counter_value("signals_sent_total"),
                "signals_failed": metrics.get_counter_value("signals_failed_total"),
                "active_pairs": metrics.get_gauge_value("active_trading_pairs")
            }
        }
    
    # 添加端点列表
    @app.get("/api/endpoints",
             summary="获取所有API端点",
             description="获取系统所有可用的API端点列表",
             tags=["系统管理"],
             response_model=List[Dict[str, Any]])
    async def get_api_endpoints():
        """获取所有API端点"""
        endpoints = []
        
        for route in app.routes:
            if hasattr(route, 'methods') and hasattr(route, 'path'):
                endpoints.append({
                    "path": route.path,
                    "methods": list(route.methods),
                    "name": route.name,
                    "tags": getattr(route, 'tags', []),
                    "summary": getattr(route, 'summary', None),
                    "description": getattr(route, 'description', None)
                })
        
        return sorted(endpoints, key=lambda x: x['path'])


# 响应模型增强
class APIResponse:
    """API响应模型基类"""
    
    @staticmethod
    def success(data: Any = None, message: str = "操作成功") -> Dict[str, Any]:
        """成功响应"""
        return {
            "success": True,
            "message": message,
            "data": data,
            "timestamp": time.time()
        }
    
    @staticmethod
    def error(message: str, error_code: str = None, details: Any = None) -> Dict[str, Any]:
        """错误响应"""
        return {
            "success": False,
            "message": message,
            "error_code": error_code,
            "details": details,
            "timestamp": time.time()
        }
    
    @staticmethod
    def paginated(data: List[Any], page: int, size: int, total: int) -> Dict[str, Any]:
        """分页响应"""
        return {
            "success": True,
            "data": data,
            "pagination": {
                "page": page,
                "size": size,
                "total": total,
                "pages": (total + size - 1) // size
            },
            "timestamp": time.time()
        }


# 常用的OpenAPI标签
OPENAPI_TAGS = [
    {
        "name": "系统管理",
        "description": "系统状态、健康检查、主机管理等基础功能"
    },
    {
        "name": "账户管理",
        "description": "MT5账户注册、配置、状态管理"
    },
    {
        "name": "交易配对",
        "description": "主从账户配对、跟单策略管理"
    },
    {
        "name": "分布式协调",
        "description": "跨主机协调、负载均衡、故障转移"
    },
    {
        "name": "性能监控",
        "description": "实时性能指标、统计分析、系统监控"
    },
    {
        "name": "配置管理",
        "description": "系统配置、参数调整、设置管理"
    }
]


# 常用的HTTP状态码和错误信息
HTTP_STATUS_CODES = {
    200: {"description": "成功"},
    201: {"description": "创建成功"},
    400: {"description": "请求参数错误"},
    401: {"description": "认证失败"},
    403: {"description": "权限不足"},
    404: {"description": "资源未找到"},
    409: {"description": "资源冲突"},
    422: {"description": "数据验证错误"},
    500: {"description": "服务器内部错误"},
    503: {"description": "服务不可用"}
}