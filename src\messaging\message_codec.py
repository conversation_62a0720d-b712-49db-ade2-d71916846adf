#!/usr/bin/env python3
"""
Unified Message Codec
Handles all JSON message serialization and deserialization
Uses Pydantic models for type safety and validation
"""
import json
import logging
from typing import Any, Dict, Optional, Union
from pydantic import BaseModel

logger = logging.getLogger(__name__)


class MessageCodec:
    """Unified Message Codec - supports Pydantic models"""

    @staticmethod
    def encode(data: Any) -> bytes:
        """
        Encode data to bytes
        Supports Pydantic models, dicts, and other JSON-serializable types
        """
        try:
            # Handle Pydantic models
            if isinstance(data, BaseModel):
                data = data.model_dump()

            # Serialize to JSON string
            json_str = json.dumps(data, ensure_ascii=False, default=str)

            # Encode to bytes
            return json_str.encode('utf-8')

        except Exception as e:
            logger.error(f"Message encoding failed: {e}, data type: {type(data)}")
            # Return error message
            error_data = {
                "error": "encoding_failed",
                "error_message": str(e),
                "data_type": str(type(data))
            }
            return json.dumps(error_data).encode('utf-8')
    
    @staticmethod
    def decode(data: Union[bytes, str, Any]) -> Optional[Dict[str, Any]]:
        """
        将数据解码为字典 
        """
        try:
            json_str = MessageCodec._extract_json_string(data)
            if json_str is None:
                logger.warning("无法提取JSON字符串")
                return None

            json_str = json_str.strip()
            if not json_str:
                logger.warning("JSON字符串为空")
                return None

            result = json.loads(json_str)

            if isinstance(result, dict):
                logger.debug(f"消息解码成功: {list(result.keys())}")
                return result
            elif isinstance(result, (list, str, int, float, bool)):
                logger.debug(f"非字典数据包装: {type(result)}")
                return {"data": result, "type": type(result).__name__}
            else:
                logger.error(f"解码结果类型不支持: {type(result)}")
                return None

        except json.JSONDecodeError as e:
            logger.error(f"JSON解析失败: {e}")
            logger.error(f"原始数据前200字符: {str(data)[:200]}...")
            return MessageCodec._try_fix_json(data)
        except Exception as e:
            logger.error(f"消息解码失败: {e}")
            logger.error(f"数据类型: {type(data)}")
            return None

    @staticmethod
    def _try_fix_json(data: Any) -> Optional[Dict[str, Any]]:
        """尝试修复损坏的JSON数据"""
        try:
            json_str = MessageCodec._extract_json_string(data)
            if not json_str:
                return None

            fixes = [
                lambda s: s.rstrip(','),
                lambda s: s.replace("'", '"'),
                lambda s: ''.join(char for char in s if ord(char) >= 32 or char in '\n\r\t'),
            ]

            for fix in fixes:
                try:
                    fixed_str = fix(json_str)
                    result = json.loads(fixed_str)
                    if isinstance(result, dict):
                        logger.info("JSON修复成功")
                        return result
                except:
                    continue

            logger.error("JSON修复失败")
            return None

        except Exception as e:
            logger.error(f"JSON修复异常: {e}")
            return None
    
    @staticmethod
    def _extract_json_string(data: Any) -> Optional[str]:
        """
        从各种数据类型中提取JSON字符串 
        """
        try:
            if isinstance(data, dict):
                return json.dumps(data)

            elif isinstance(data, bytes):
                raw_str = data.decode('utf-8')
                return MessageCodec._handle_string_data(raw_str)

            elif hasattr(data, 'data'):
                if isinstance(data.data, bytes):
                    raw_str = data.data.decode('utf-8')
                    return MessageCodec._handle_string_data(raw_str)
                elif isinstance(data.data, str):
                    return MessageCodec._handle_string_data(data.data)
                else:
                    logger.warning(f"未知的消息数据类型: {type(data.data)}")
                    return str(data.data)

            elif isinstance(data, str):
                return MessageCodec._handle_string_data(data)

            else:
                logger.warning(f"未知的数据类型: {type(data)}")
                return str(data)

        except Exception as e:
            logger.error(f"提取JSON字符串失败: {e}")
            return None
    
    @staticmethod
    def _handle_string_data(data: str) -> str:
        """
        处理字符串数据，特别是多层编码的复杂情况
        """
        logger.debug(f"处理字符串数据: {data[:100]}...")

        current = data.strip()

        for i in range(10):
            original = current

            if current.startswith('"') and current.endswith('"') and len(current) > 2:
                try:
                    decoded = json.loads(current)
                    if isinstance(decoded, str):
                        current = decoded
                        continue
                    else:
                        return json.dumps(decoded)
                except json.JSONDecodeError as e:
                    logger.debug(f"JSON解析失败 (第{i+1}层): {e}")
                    inner = current[1:-1]
                    inner = inner.replace('\\"', '"')
                    inner = inner.replace('\\\\', '\\')
                    inner = inner.replace("\\'", "'")
                    if inner != current:
                        current = inner
                        continue

            if current.startswith("b'") and current.endswith("'"):
                inner = current[2:-1]
                inner = inner.replace('\\"', '"').replace('\\\\', '\\').replace("\\'", "'")
                current = inner
                continue
            elif current.startswith('b"') and current.endswith('"'):
                inner = current[2:-1]
                inner = inner.replace('\\"', '"').replace('\\\\', '\\')
                current = inner
                continue

            if '\\' in current:
                new_current = current
                new_current = new_current.replace('\\\\', '\\')
                new_current = new_current.replace('\\"', '"')
                new_current = new_current.replace("\\'", "'")

                if new_current != current:
                    current = new_current
                    continue

            if current == original:
                break

        logger.debug(f"最终结果: {current[:100]}...")
        return current

 