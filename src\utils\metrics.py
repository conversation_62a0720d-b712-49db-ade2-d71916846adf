"""
性能指标收集模块
支持Prometheus指标导出和业务指标收集
"""
import time
import threading
import asyncio
import logging
from typing import Dict, List, Optional, Any, Callable
from collections import defaultdict, deque
from dataclasses import dataclass, field
from datetime import datetime, timedelta
import statistics

# Prometheus支持
try:
    from prometheus_client import Counter, Histogram, Gauge, Info, start_http_server
    PROMETHEUS_AVAILABLE = True
except ImportError:
    PROMETHEUS_AVAILABLE = False
    Counter = Histogram = Gauge = Info = None

logger = logging.getLogger(__name__)


@dataclass
class MetricValue:
    """指标值"""
    value: float
    timestamp: float
    labels: Dict[str, str] = field(default_factory=dict)


@dataclass
class PerformanceSnapshot:
    """性能快照"""
    timestamp: float
    cpu_usage: float
    memory_usage: float
    latency_p50: float
    latency_p95: float
    latency_p99: float
    signals_per_second: float
    error_rate: float
    active_connections: int


class MetricsCollector:
    """性能指标收集器"""

    def __init__(self, history_size: int = 1000, enable_prometheus: bool = False):
        self.history_size = history_size
        self.enable_prometheus = enable_prometheus and PROMETHEUS_AVAILABLE
        self._metrics: Dict[str, deque] = defaultdict(lambda: deque(maxlen=history_size))
        self._counters: Dict[str, int] = defaultdict(int)
        self._gauges: Dict[str, float] = defaultdict(float)
        self._histograms: Dict[str, List[float]] = defaultdict(list)
        self._lock = threading.Lock()

        # Prometheus指标（可选）
        if self.enable_prometheus:
            self._init_prometheus_metrics()

    def _init_prometheus_metrics(self):
        """初始化Prometheus指标"""
        if not PROMETHEUS_AVAILABLE:
            logger.warning("Prometheus客户端不可用，跳过Prometheus指标初始化")
            return

        try:
            logger.info("开始初始化Prometheus指标...")

            # 系统指标
            self.prom_signal_processing_duration = Histogram(
            'mt5_signal_processing_duration_seconds',
            'Signal processing duration in seconds',
            ['account_id', 'signal_type', 'status']
        )

            self.prom_trade_matching_accuracy = Gauge(
                'mt5_trade_matching_accuracy_ratio',
                'Trade matching accuracy ratio',
                ['algorithm']
            )

            self.prom_copy_execution_success_rate = Gauge(
                'mt5_copy_execution_success_rate',
                'Copy execution success rate',
                ['master_account', 'slave_account']
            )

            self.prom_signal_queue_size = Gauge(
                'mt5_signal_queue_size',
                'Current signal queue size',
                ['instance_id']
            )

            self.prom_nats_connection_status = Gauge(
                'mt5_nats_connection_status',
                'NATS connection status (1=connected, 0=disconnected)'
            )

            # 业务指标
            self.prom_signals_published = Counter(
                'mt5_signals_published_total',
                'Total number of signals published',
                ['account_id', 'signal_type']
            )

            self.prom_signals_processed = Counter(
                'mt5_signals_processed_total',
                'Total number of signals processed',
                ['account_id', 'status']
            )

            self.prom_trades_executed = Counter(
                'mt5_trades_executed_total',
                'Total number of trades executed',
                ['account_id', 'symbol', 'action', 'status']
            )

            self.prom_errors_total = Counter(
                'mt5_errors_total',
                'Total number of errors',
                ['component', 'error_type']
            )

            logger.info("✅ Prometheus指标初始化完成")

        except Exception as e:
            logger.error(f"❌ Prometheus指标初始化失败: {e}")
            self.enable_prometheus = False
        


    def record(self, metric_name: str, value: float, labels: Dict[str, str] = None):
        """记录指标值"""
        with self._lock:
            metric = MetricValue(
                value=value,
                timestamp=time.time(),
                labels=labels or {}
            )
            self._metrics[metric_name].append(metric)
    
    def increment(self, counter_name: str, value: int = 1, labels: Dict[str, str] = None):
        """增加计数器"""
        with self._lock:
            self._counters[counter_name] += value
            # 直接记录而不调用record方法避免死锁
            metric = MetricValue(
                value=self._counters[counter_name],
                timestamp=time.time(),
                labels=labels or {}
            )
            self._metrics[f"{counter_name}_total"].append(metric)
    
    def set_gauge(self, gauge_name: str, value: float, labels: Dict[str, str] = None):
        """设置仪表值"""
        with self._lock:
            self._gauges[gauge_name] = value
            # 直接记录而不调用record方法避免死锁
            metric = MetricValue(
                value=value,
                timestamp=time.time(),
                labels=labels or {}
            )
            self._metrics[gauge_name].append(metric)
    
    def observe(self, histogram_name: str, value: float, labels: Dict[str, str] = None):
        """观察直方图值"""
        with self._lock:
            self._histograms[histogram_name].append(value)
            # 保持最近的历史记录
            if len(self._histograms[histogram_name]) > self.history_size:
                self._histograms[histogram_name].pop(0)
            # 直接记录而不调用record方法避免死锁
            metric = MetricValue(
                value=value,
                timestamp=time.time(),
                labels=labels or {}
            )
            self._metrics[histogram_name].append(metric)
    
    def get_counter(self, counter_name: str) -> int:
        """获取计数器值"""
        with self._lock:
            return self._counters.get(counter_name, 0)
    
    def get_gauge(self, gauge_name: str) -> float:
        """获取仪表值"""
        with self._lock:
            return self._gauges.get(gauge_name, 0.0)
    
    def get_histogram_stats(self, histogram_name: str) -> Dict[str, float]:
        """获取直方图统计信息"""
        with self._lock:
            values = self._histograms.get(histogram_name, [])
            if not values:
                return {}
            
            return {
                'count': len(values),
                'sum': sum(values),
                'mean': statistics.mean(values),
                'min': min(values),
                'max': max(values),
                'p50': statistics.quantiles(values, n=100)[49] if len(values) >= 50 else statistics.median(values),
                'p95': statistics.quantiles(values, n=100)[94] if len(values) >= 20 else max(values),
                'p99': statistics.quantiles(values, n=100)[98] if len(values) >= 100 else max(values)
            }
    
    def get_rate(self, metric_name: str, duration: float = 60.0) -> float:
        """获取指标的变化率（每秒）"""
        with self._lock:
            metrics = self._metrics.get(metric_name, deque())
            if len(metrics) < 2:
                return 0.0
            
            current_time = time.time()
            cutoff_time = current_time - duration
            
            # 过滤时间窗口内的数据
            recent_metrics = [m for m in metrics if m.timestamp >= cutoff_time]
            
            if len(recent_metrics) < 2:
                return 0.0
            
            # 计算变化率
            time_diff = recent_metrics[-1].timestamp - recent_metrics[0].timestamp
            if time_diff <= 0:
                return 0.0
            
            value_diff = recent_metrics[-1].value - recent_metrics[0].value
            return value_diff / time_diff
    
    def get_latest_value(self, metric_name: str) -> Optional[float]:
        """获取最新的指标值"""
        with self._lock:
            metrics = self._metrics.get(metric_name, deque())
            if not metrics:
                return None
            return metrics[-1].value
    
    def get_metrics_summary(self) -> Dict[str, Any]:
        """获取所有指标的摘要"""
        with self._lock:
            summary = {
                'timestamp': time.time(),
                'counters': dict(self._counters),
                'gauges': dict(self._gauges),
                'histograms': {}
            }

            # 直接计算直方图统计，避免调用需要锁的方法
            for name, values in self._histograms.items():
                if not values:
                    summary['histograms'][name] = {}
                else:
                    try:
                        summary['histograms'][name] = {
                            'count': len(values),
                            'sum': sum(values),
                            'mean': statistics.mean(values),
                            'min': min(values),
                            'max': max(values),
                            'p50': statistics.quantiles(values, n=100)[49] if len(values) >= 50 else statistics.median(values),
                            'p95': statistics.quantiles(values, n=100)[94] if len(values) >= 20 else max(values),
                            'p99': statistics.quantiles(values, n=100)[98] if len(values) >= 100 else max(values)
                        }
                    except Exception:
                        # 如果统计计算失败，返回基本信息
                        summary['histograms'][name] = {
                            'count': len(values),
                            'sum': sum(values) if values else 0,
                            'mean': sum(values) / len(values) if values else 0,
                            'min': min(values) if values else 0,
                            'max': max(values) if values else 0
                        }

            return summary
    
    def clear(self):
        """清除所有指标"""
        with self._lock:
            self._metrics.clear()
            self._counters.clear()
            self._gauges.clear()
            self._histograms.clear()


class PerformanceMonitor:
    """性能监控器"""
    
    def __init__(self, metrics_collector: MetricsCollector):
        self.metrics = metrics_collector
        self.snapshots: deque = deque(maxlen=100)
        self.alerts: List[Dict[str, Any]] = []
        self._running = False
        self._thread = None
        
        # 阈值配置
        self.thresholds = {
            'cpu_usage': 80.0,
            'memory_usage': 1024.0,  # MB
            'latency_p95': 50.0,     # ms
            'error_rate': 0.05       # 5%
        }
    
    def start(self):
        """启动性能监控"""
        if self._running:
            return
        
        self._running = True
        self._thread = threading.Thread(target=self._monitoring_loop)
        self._thread.daemon = True
        self._thread.start()
    
    def stop(self):
        """停止性能监控"""
        self._running = False
        if self._thread:
            self._thread.join()
    
    def _monitoring_loop(self):
        """监控循环"""
        while self._running:
            try:
                snapshot = self._collect_snapshot()
                self.snapshots.append(snapshot)
                self._check_alerts(snapshot)
                time.sleep(10)  # 每10秒收集一次
            except Exception as e:
                print(f"性能监控错误: {e}")
    
    def _collect_snapshot(self) -> PerformanceSnapshot:
        """收集性能快照"""
        import psutil
        import os
        
        # 获取当前进程
        process = psutil.Process(os.getpid())
        
        # CPU和内存使用率
        cpu_usage = process.cpu_percent(interval=0.1)
        memory_usage = process.memory_info().rss / 1024 / 1024  # MB
        
        # 延迟统计
        latency_stats = self.metrics.get_histogram_stats('latency_ms')
        latency_p50 = latency_stats.get('p50', 0)
        latency_p95 = latency_stats.get('p95', 0)
        latency_p99 = latency_stats.get('p99', 0)
        
        # 信号处理速率
        signals_per_second = self.metrics.get_rate('signals_processed_total')
        
        # 错误率
        total_signals = self.metrics.get_counter('signals_processed_total')
        error_signals = self.metrics.get_counter('signals_error_total')
        error_rate = error_signals / max(1, total_signals)
        
        # 活跃连接数
        active_connections = self.metrics.get_gauge('active_connections')
        
        return PerformanceSnapshot(
            timestamp=time.time(),
            cpu_usage=cpu_usage,
            memory_usage=memory_usage,
            latency_p50=latency_p50,
            latency_p95=latency_p95,
            latency_p99=latency_p99,
            signals_per_second=signals_per_second,
            error_rate=error_rate,
            active_connections=int(active_connections)
        )
    
    def _check_alerts(self, snapshot: PerformanceSnapshot):
        """检查告警"""
        alerts = []
        
        if snapshot.cpu_usage > self.thresholds['cpu_usage']:
            alerts.append({
                'type': 'cpu_high',
                'message': f"CPU使用率过高: {snapshot.cpu_usage:.1f}%",
                'timestamp': snapshot.timestamp,
                'value': snapshot.cpu_usage,
                'threshold': self.thresholds['cpu_usage']
            })
        
        if snapshot.memory_usage > self.thresholds['memory_usage']:
            alerts.append({
                'type': 'memory_high',
                'message': f"内存使用过高: {snapshot.memory_usage:.1f}MB",
                'timestamp': snapshot.timestamp,
                'value': snapshot.memory_usage,
                'threshold': self.thresholds['memory_usage']
            })
        
        if snapshot.latency_p95 > self.thresholds['latency_p95']:
            alerts.append({
                'type': 'latency_high',
                'message': f"延迟过高: {snapshot.latency_p95:.1f}ms",
                'timestamp': snapshot.timestamp,
                'value': snapshot.latency_p95,
                'threshold': self.thresholds['latency_p95']
            })
        
        if snapshot.error_rate > self.thresholds['error_rate']:
            alerts.append({
                'type': 'error_rate_high',
                'message': f"错误率过高: {snapshot.error_rate:.1%}",
                'timestamp': snapshot.timestamp,
                'value': snapshot.error_rate,
                'threshold': self.thresholds['error_rate']
            })
        
        self.alerts.extend(alerts)
        
        # 保持最近的告警
        if len(self.alerts) > 100:
            self.alerts = self.alerts[-100:]
    
    def get_latest_snapshot(self) -> Optional[PerformanceSnapshot]:
        """获取最新的性能快照"""
        if not self.snapshots:
            return None
        return self.snapshots[-1]
    
    def get_recent_alerts(self, minutes: int = 30) -> List[Dict[str, Any]]:
        """获取最近的告警"""
        cutoff_time = time.time() - (minutes * 60)
        return [alert for alert in self.alerts if alert['timestamp'] >= cutoff_time]


# 全局指标收集器
_global_metrics = MetricsCollector()
_global_monitor = PerformanceMonitor(_global_metrics)


def get_metrics_collector() -> MetricsCollector:
    """获取全局指标收集器"""
    return _global_metrics


def get_performance_monitor() -> PerformanceMonitor:
    """获取性能监控器"""
    return _global_monitor


def start_performance_monitoring():
    """启动性能监控"""
    _global_monitor.start()


def stop_performance_monitoring():
    """停止性能监控"""
    _global_monitor.stop()


class BusinessMetricsCollector:
    """业务指标收集器"""

    def __init__(self, metrics_collector: MetricsCollector):
        self.metrics = metrics_collector

    def record_signal_processing(self, account_id: str, signal_type: str,
                                duration: float, success: bool):
        """记录信号处理指标"""
        labels = {
            'account_id': account_id,
            'signal_type': signal_type,
            'status': 'success' if success else 'error'
        }

        self.metrics.observe('signal_processing_duration', duration, labels)
        self.metrics.increment('signals_processed', 1, labels)

    def record_trade_matching(self, algorithm: str, accuracy: float,
                            matched_count: int, total_count: int):
        """记录交易匹配指标"""
        labels = {'algorithm': algorithm}

        self.metrics.set_gauge('trade_matching_accuracy', accuracy, labels)
        self.metrics.set_gauge('trade_matching_matched_count', matched_count, labels)
        self.metrics.set_gauge('trade_matching_total_count', total_count, labels)

    def record_copy_execution(self, master_account: str, slave_account: str,
                            success: bool, execution_time: float):
        """记录跟单执行指标"""
        labels = {
            'master_account': master_account,
            'slave_account': slave_account,
            'status': 'success' if success else 'error'
        }

        self.metrics.observe('copy_execution_duration', execution_time, labels)
        self.metrics.increment('copy_executions_total', 1, labels)

        # 计算成功率
        success_rate = self._calculate_success_rate(master_account, slave_account)
        success_labels = {'master_account': master_account, 'slave_account': slave_account}
        self.metrics.set_gauge('copy_execution_success_rate', success_rate, success_labels)

    def record_system_health(self, component: str, status: str,
                           response_time: float = None):
        """记录系统健康状态"""
        labels = {'component': component, 'status': status}

        health_value = 1.0 if status == 'healthy' else 0.0
        self.metrics.set_gauge('system_component_health', health_value, labels)

        if response_time is not None:
            self.metrics.observe('system_component_response_time',
                               response_time, labels)

    def record_error(self, component: str, error_type: str, error_message: str = None):
        """记录错误"""
        labels = {
            'component': component,
            'error_type': error_type
        }

        self.metrics.increment('errors_total', 1, labels)

        # 记录错误详情（如果需要）
        if error_message:
            logger.error(f"组件错误 - {component}: {error_type} - {error_message}")

    def _calculate_success_rate(self, master_account: str, slave_account: str) -> float:
        """计算成功率（简化实现）"""
        # 这里应该从历史数据中计算真实的成功率
        # 为了演示，返回一个模拟值
        pass # TODO


class AlertManager:
    """告警管理器"""

    def __init__(self, metrics_collector: MetricsCollector):
        self.metrics = metrics_collector
        self.alert_rules = []
        self.alert_history = deque(maxlen=1000)
        self.notification_channels = []

    def add_alert_rule(self, name: str, condition: Callable,
                      severity: str = "warning", duration: int = 300):
        """添加告警规则"""
        rule = {
            'name': name,
            'condition': condition,
            'severity': severity,
            'duration': duration,
            'triggered_at': None,
            'active': False
        }
        self.alert_rules.append(rule)
        logger.info(f"添加告警规则: {name}")

    def add_notification_channel(self, channel_type: str, config: Dict[str, Any]):
        """添加通知渠道"""
        channel = {
            'type': channel_type,
            'config': config,
            'enabled': True
        }
        self.notification_channels.append(channel)
        logger.info(f"添加通知渠道: {channel_type}")

    async def check_alerts(self):
        """检查告警条件"""
        current_time = time.time()
        metrics_summary = self.metrics.get_metrics_summary()

        for rule in self.alert_rules:
            try:
                # 检查告警条件
                triggered = rule['condition'](metrics_summary)

                if triggered and not rule['active']:
                    # 新触发的告警
                    rule['triggered_at'] = current_time
                    rule['active'] = True

                    alert = {
                        'name': rule['name'],
                        'severity': rule['severity'],
                        'triggered_at': current_time,
                        'message': f"告警触发: {rule['name']}"
                    }

                    await self._send_alert(alert)
                    self.alert_history.append(alert)

                elif not triggered and rule['active']:
                    # 告警恢复
                    rule['active'] = False
                    rule['triggered_at'] = None

                    recovery_alert = {
                        'name': rule['name'],
                        'severity': 'info',
                        'triggered_at': current_time,
                        'message': f"告警恢复: {rule['name']}"
                    }

                    await self._send_alert(recovery_alert)
                    self.alert_history.append(recovery_alert)

            except Exception as e:
                logger.error(f"检查告警规则失败 {rule['name']}: {e}")

    async def _send_alert(self, alert: Dict[str, Any]):
        """发送告警"""
        for channel in self.notification_channels:
            if not channel['enabled']:
                continue

            try:
                if channel['type'] == 'webhook':
                    await self._send_webhook_alert(alert, channel['config'])
                elif channel['type'] == 'email':
                    await self._send_email_alert(alert, channel['config'])
                elif channel['type'] == 'slack':
                    await self._send_slack_alert(alert, channel['config'])

            except Exception as e:
                logger.error(f"发送告警失败 {channel['type']}: {e}")

    async def _send_webhook_alert(self, alert: Dict[str, Any], config: Dict[str, Any]):
        """发送Webhook告警"""
        try:
            import aiohttp

            async with aiohttp.ClientSession() as session:
                async with session.post(config['url'], json=alert) as response:
                    if response.status == 200:
                        logger.info(f"Webhook告警发送成功: {alert['name']}")
                    else:
                        logger.error(f"Webhook告警发送失败: {response.status}")
        except ImportError:
            logger.warning("aiohttp不可用，无法发送Webhook告警")

    async def _send_email_alert(self, alert: Dict[str, Any], config: Dict[str, Any]):
        """发送邮件告警"""
        # 这里可以实现邮件发送逻辑
        logger.info(f"邮件告警: {alert['name']} -> {config.get('recipients', [])}")

    async def _send_slack_alert(self, alert: Dict[str, Any], config: Dict[str, Any]):
        """发送Slack告警"""
        # 这里可以实现Slack通知逻辑
        logger.info(f"Slack告警: {alert['name']} -> {config.get('channel', '#alerts')}")


# 预定义的告警规则
def create_default_alert_rules() -> List[Dict[str, Any]]:
    """创建默认告警规则"""
    return [
        {
            'name': 'high_signal_processing_latency',
            'condition': lambda metrics: any(
                h > 1.0 for h in metrics.get('histograms', {}).get('signal_processing_duration', {}).get('values', [])
            ),
            'severity': 'warning',
            'duration': 300
        },
        {
            'name': 'low_copy_execution_success_rate',
            'condition': lambda metrics: metrics.get('gauges', {}).get('copy_execution_success_rate', 1.0) < 0.95,
            'severity': 'critical',
            'duration': 120
        },
        {
            'name': 'nats_connection_lost',
            'condition': lambda metrics: metrics.get('gauges', {}).get('nats_connection_status', 1.0) == 0.0,
            'severity': 'critical',
            'duration': 30
        },
        {
            'name': 'high_error_rate',
            'condition': lambda metrics: metrics.get('counters', {}).get('errors_total', 0) > 100,
            'severity': 'warning',
            'duration': 300
        }
    ]


# 全局业务指标收集器和告警管理器
_business_metrics: Optional[BusinessMetricsCollector] = None
_alert_manager: Optional[AlertManager] = None


def get_business_metrics() -> BusinessMetricsCollector:
    """获取业务指标收集器"""
    global _business_metrics
    if _business_metrics is None:
        _business_metrics = BusinessMetricsCollector(get_metrics_collector())
    return _business_metrics


def get_alert_manager() -> AlertManager:
    """获取告警管理器"""
    global _alert_manager
    if _alert_manager is None:
        _alert_manager = AlertManager(get_metrics_collector())

        # 添加默认告警规则
        for rule in create_default_alert_rules():
            _alert_manager.add_alert_rule(**rule)

    return _alert_manager


# 装饰器便捷函数
def timing(metric_name: str, labels: Dict[str, str] = None):
    """计时装饰器"""
    def decorator(func):
        if asyncio.iscoroutinefunction(func):
            async def async_wrapper(*args, **kwargs):
                start_time = time.time()
                try:
                    result = await func(*args, **kwargs)
                    status = "success"
                    return result
                except Exception as e:
                    status = "error"
                    raise
                finally:
                    duration = time.time() - start_time
                    final_labels = (labels or {}).copy()
                    final_labels['status'] = status
                    get_metrics_collector().record_histogram(metric_name, duration, final_labels)
            return async_wrapper
        else:
            def sync_wrapper(*args, **kwargs):
                start_time = time.time()
                try:
                    result = func(*args, **kwargs)
                    status = "success"
                    return result
                except Exception as e:
                    status = "error"
                    raise
                finally:
                    duration = time.time() - start_time
                    final_labels = (labels or {}).copy()
                    final_labels['status'] = status
                    get_metrics_collector().record_histogram(metric_name, duration, final_labels)
            return sync_wrapper
    return decorator


def counter(metric_name: str, labels: Dict[str, str] = None):
    """计数装饰器"""
    def decorator(func):
        if asyncio.iscoroutinefunction(func):
            async def async_wrapper(*args, **kwargs):
                try:
                    result = await func(*args, **kwargs)
                    get_metrics_collector().increment_counter(metric_name, 1.0, labels)
                    return result
                except Exception as e:
                    error_labels = (labels or {}).copy()
                    error_labels['status'] = 'error'
                    get_metrics_collector().increment_counter(metric_name, 1.0, error_labels)
                    raise
            return async_wrapper
        else:
            def sync_wrapper(*args, **kwargs):
                try:
                    result = func(*args, **kwargs)
                    get_metrics_collector().increment_counter(metric_name, 1.0, labels)
                    return result
                except Exception as e:
                    error_labels = (labels or {}).copy()
                    error_labels['status'] = 'error'
                    get_metrics_collector().increment_counter(metric_name, 1.0, error_labels)
                    raise
            return sync_wrapper
    return decorator