#!/usr/bin/env python3
"""
测试配置加载 - 验证所有配置是否正确读取
"""

import sys
import os
from pathlib import Path
from dotenv import load_dotenv

# 添加项目路径
sys.path.insert(0, str(Path(__file__).parent.parent))

from src.utils.logger import get_logger, setup_logging
from src.core.config_manager import ConfigManager

logger = get_logger(__name__)


def test_env_loading():
    """测试环境变量加载"""
    logger.info("🔍 测试环境变量加载...")
    
    # 加载 .env 文件
    load_dotenv()
    
    # 检查关键环境变量
    env_vars = ['MT5_ACC001_PASSWORD', 'MT5_ACC002_PASSWORD']
    
    for var in env_vars:
        value = os.getenv(var)
        if value:
            logger.info(f"✅ {var}: {'*' * len(value)}")
        else:
            logger.error(f"❌ {var}: 未设置")
    
    return all(os.getenv(var) for var in env_vars)


def test_config_manager():
    """测试配置管理器"""
    logger.info("🔍 测试配置管理器...")
    
    try:
        # 创建配置管理器
        config_manager = ConfigManager('config/optimized_system.yaml')
        
        # 测试基本配置读取
        logger.info("测试基本配置读取...")
        config_data = config_manager.config_data
        logger.info(f"配置文件加载成功，包含 {len(config_data)} 个顶级配置项")
        
        # 测试跟单关系读取
        logger.info("测试跟单关系读取...")
        copy_relationships = config_manager.get_copy_relationships()
        logger.info(f"找到 {len(copy_relationships)} 个跟单关系:")
        
        for rel in copy_relationships:
            logger.info(f"  {rel['master_account']} -> {rel['slave_account']}")
            logger.info(f"    模式: {rel['copy_mode']}, 比例: {rel['copy_ratio']}")
        
        # 测试账户配置读取
        logger.info("测试账户配置读取...")
        accounts = ['ACC001', 'ACC002']
        
        for account_id in accounts:
            try:
                account_file = Path(f"config/accounts/{account_id}.yaml")
                if account_file.exists():
                    account_config = config_manager.load_account_config(account_file)
                    
                    account_info = account_config.get('account', {})
                    mt5_info = account_config.get('mt5', {}).get('connection', {})
                    
                    logger.info(f"  {account_id}:")
                    logger.info(f"    ID: {account_info.get('id')}")
                    logger.info(f"    启用: {account_info.get('enabled')}")
                    logger.info(f"    类型: {account_info.get('type')}")
                    logger.info(f"    登录号: {mt5_info.get('login')}")
                    logger.info(f"    服务器: {mt5_info.get('server')}")
                    
                    # 检查密码环境变量
                    password_env = f"MT5_{account_id}_PASSWORD"
                    password = os.getenv(password_env)
                    logger.info(f"    密码环境变量: {'✅ 已设置' if password else '❌ 未设置'}")
                else:
                    logger.warning(f"  {account_id}: 配置文件不存在")
            except Exception as e:
                logger.error(f"  {account_id}: 读取失败 - {e}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 配置管理器测试失败: {e}")
        return False


def test_account_validation():
    """测试账户配置验证"""
    logger.info("🔍 测试账户配置验证...")
    
    try:
        config_manager = ConfigManager('config/optimized_system.yaml')
        
        accounts = ['ACC001', 'ACC002']
        validation_results = {}
        
        for account_id in accounts:
            try:
                account_file = Path(f"config/accounts/{account_id}.yaml")
                if not account_file.exists():
                    logger.warning(f"{account_id}: 配置文件不存在")
                    validation_results[account_id] = False
                    continue
                
                account_config = config_manager.load_account_config(account_file)
                
                # 手动验证逻辑
                account = account_config.get('account', {})
                mt5_config = account_config.get('mt5', {}).get('connection', {})
                
                # 检查必需字段
                required_fields = ['id', 'enabled']
                missing_fields = [f for f in required_fields if f not in account]
                
                if missing_fields:
                    logger.error(f"{account_id}: 缺少必需字段: {missing_fields}")
                    validation_results[account_id] = False
                    continue
                
                # 检查MT5配置
                if account.get('enabled', False):
                    mt5_required = ['login', 'server']
                    missing_mt5 = [f for f in mt5_required if f not in mt5_config]
                    
                    if missing_mt5:
                        logger.error(f"{account_id}: MT5配置缺少字段: {missing_mt5}")
                        validation_results[account_id] = False
                        continue
                    
                    # 检查密码环境变量
                    password_env = f"MT5_{account_id}_PASSWORD"
                    if not os.environ.get(password_env):
                        logger.error(f"{account_id}: 缺少密码环境变量: {password_env}")
                        validation_results[account_id] = False
                        continue
                
                logger.info(f"✅ {account_id}: 验证通过")
                validation_results[account_id] = True
                
            except Exception as e:
                logger.error(f"{account_id}: 验证异常 - {e}")
                validation_results[account_id] = False
        
        # 总结验证结果
        passed = sum(1 for result in validation_results.values() if result)
        total = len(validation_results)
        
        logger.info(f"账户验证结果: {passed}/{total} 通过")
        
        return passed > 0
        
    except Exception as e:
        logger.error(f"❌ 账户验证测试失败: {e}")
        return False


def main():
    """主函数"""
    # 设置日志
    setup_logging({
        'level': 'INFO',
        'format': 'standard'
    })
    
    logger.info("=" * 60)
    logger.info("🔧 配置加载测试")
    logger.info("=" * 60)
    
    tests = [
        ("环境变量加载", test_env_loading),
        ("配置管理器", test_config_manager),
        ("账户配置验证", test_account_validation)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        logger.info(f"运行测试: {test_name}")
        try:
            result = test_func()
            results[test_name] = result
            if result:
                logger.info(f"✅ {test_name}: 通过")
            else:
                logger.error(f"❌ {test_name}: 失败")
        except Exception as e:
            logger.error(f"💥 {test_name}: 异常 - {e}")
            results[test_name] = False
        
        logger.info("-" * 40)
    
    # 总结
    logger.info("=" * 60)
    logger.info("📊 测试结果总结")
    logger.info("=" * 60)
    
    passed = sum(1 for result in results.values() if result)
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅ 通过" if result else "❌ 失败"
        logger.info(f"  {test_name}: {status}")
    
    logger.info(f"\n总计: {passed}/{total} 个测试通过")
    
    if passed == total:
        logger.info("🎉 所有配置测试通过！")
    else:
        logger.error("💥 部分配置测试失败，需要修复")


if __name__ == "__main__":
    main()
