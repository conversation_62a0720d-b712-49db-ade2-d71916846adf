#!/usr/bin/env python3
# scripts/launchers/distributed_launcher.py
"""
MT5跟单系统分布式启动器
"""
import asyncio
import argparse
import os
import sys
from pathlib import Path

# 添加项目根目录到路径
sys.path.insert(0, str(Path(__file__).parent.parent.parent))

from src.distributed.coordinator import SystemCoordinator
from src.distributed.host_lifecycle_manager import HostLifecycleManager
from src.core.mt5_coordinator import DistributedMT5Coordinator
from src.messaging.jetstream_client import JetStreamClient
from src.utils.config_manager import ConfigManager


async def start_distributed_system(host_id: str):
    """启动分布式系统"""
    print(f"🚀 启动分布式MT5系统 - 主机: {host_id}")
    
    # 设置环境变量
    os.environ['MT5_HOST_ID'] = host_id
    os.environ['MT5_DISTRIBUTED_MODE'] = 'true'
    
    try:
        # 1. 初始化配置管理器
        config_manager = ConfigManager("config/core/system.yaml")
        print(f"✅ 配置已加载: {host_id}")
        
        # 2. 初始化JetStream客户端
        jetstream = JetStreamClient()
        await jetstream.connect()
        print(f"✅ JetStream已连接: {host_id}")
        
        # 3. 启动分布式协调器
        coordinator = SystemCoordinator(jetstream_client=jetstream, config_manager=config_manager)
        await coordinator.start()
        print(f"✅ 分布式协调器已启动: {host_id}")
        
        # 4. 启动MT5协调器
        mt5_coordinator = DistributedMT5Coordinator(
            host_id=host_id,
            config_path="config/core/system.yaml"
        )
        await mt5_coordinator.start()
        print(f"✅ MT5协调器已启动: {host_id}")
        
        print(f"🎯 分布式MT5系统运行中 - 主机: {host_id}")
        
        # 保持运行
        while coordinator.running and mt5_coordinator.running:
            await asyncio.sleep(10)
            
    except KeyboardInterrupt:
        print(f"\n🛑 正在停止分布式MT5系统 - 主机: {host_id}")
        
        # 优雅关闭
        if 'mt5_coordinator' in locals():
            await mt5_coordinator.stop()
        if 'coordinator' in locals():
            await coordinator.stop()
        if 'jetstream' in locals():
            await jetstream.disconnect()
            
        print(f"✅ 分布式MT5系统已停止 - 主机: {host_id}")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='MT5跟单系统分布式启动器')
    parser.add_argument('--host-id', required=True, help='主机ID')
    
    args = parser.parse_args()
    
    # 运行分布式系统
    asyncio.run(start_distributed_system(args.host_id))


if __name__ == "__main__":
    main()
