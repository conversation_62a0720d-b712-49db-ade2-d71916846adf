#!/usr/bin/env python3
"""
快速开始示例
"""
import asyncio
import sys
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent.parent))

from src.sdk.mt5_copier_sdk import MT5CopierSDK, HostInfo, AccountInfo, PairingConfig


async def quick_setup():
    """快速设置示例"""
    
    async with MT5CopierSDK("http://localhost:8000") as copier:
        
        print("🚀 开始设置MT5跟单系统...")
        
        # 1. 注册服务器
        print("📡 注册交易服务器...")
        host_info = HostInfo(
            host_id="server1",
            hostname="main-trading-server",
            ip_address="*************",
            capabilities=["master", "slave"]
        )
        
        success = await copier.register_host(host_info)
        if success:
            print("✅ 服务器注册成功")
        else:
            print("❌ 服务器注册失败")
            return
        
        # 2. 注册主账户
        print("👤 注册主账户...")
        master_account = AccountInfo(
            account_id="my_master",
            login=5000001,
            password="my_password",
            server="ICMarkets-Live01",
            host_id="server1",
            account_type="master"
        )
        
        success = await copier.register_account(master_account)
        if success:
            print("✅ 主账户注册成功")
        else:
            print("❌ 主账户注册失败")
            return
        
        # 3. 注册跟单账户
        print("👥 注册跟单账户...")
        slave_account = AccountInfo(
            account_id="my_slave",
            login=6000001,
            password="my_password",
            server="ICMarkets-Live01",
            host_id="server1",
            account_type="slave"
        )
        
        success = await copier.register_account(slave_account)
        if success:
            print("✅ 跟单账户注册成功")
        else:
            print("❌ 跟单账户注册失败")
            return
        
        # 4. 创建跟单配对
        print("🔗 创建跟单配对...")
        pairing_config = PairingConfig(
            master_account="my_master",
            slave_accounts=["my_slave"],
            strategy="proportional",
            lot_multiplier=0.5,  # 50%手数
            copy_sl_tp=True,     # 复制止损止盈
            symbols_filter=["EURUSD", "GBPUSD"],  # 只复制这些品种
            max_positions=5,     # 最大5个持仓
            daily_loss_limit=500.0  # 日损失限制500
        )
        
        try:
            pairing_id = await copier.create_pairing(pairing_config)
            print(f"✅ 跟单配对创建成功! ID: {pairing_id}")
        except Exception as e:
            print(f"❌ 跟单配对创建失败: {e}")
            return
        
        # 5. 监控状态
        print("📊 检查配对状态...")
        status = await copier.get_pairing_status(pairing_id)
        
        if status:
            print("📈 配对状态:")
            print(f"  • 主账户服务: {status.get('master_service', 'unknown')}")
            print(f"  • 从账户服务: {status.get('slave_services', {})}")
            print(f"  • 配对启用: {status.get('pairing', {}).get('enabled', False)}")
            print(f"  • 成功率: {status.get('performance', {}).get('success_rate', 0):.2%}")
        else:
            print("❌ 无法获取配对状态")
            return
        
        # 6. 等待系统健康
        print("🏥 等待系统健康...")
        healthy = await copier.wait_for_healthy(timeout=30)
        
        if healthy:
            print("✅ 系统健康检查通过")
        else:
            print("⚠️  系统健康检查超时，但配对可能仍在启动中")
        
        print("\n🎉 快速设置完成!")
        print("💡 提示:")
        print("  • 访问 http://localhost:8000/docs 查看完整API文档")
        print("  • 使用 python scripts/health_check.py 进行系统健康检查")
        print("  • 查看日志文件了解详细运行状态")


async def advanced_setup():
    """高级设置示例"""
    
    async with MT5CopierSDK("http://localhost:8000") as copier:
        
        print("🔧 开始高级设置...")
        
        # 多主机设置
        hosts = [
            HostInfo(
                host_id="server1",
                hostname="trading-server-1",
                ip_address="*************",
                capabilities=["master"]
            ),
            HostInfo(
                host_id="server2",
                hostname="trading-server-2",
                ip_address="*************",
                capabilities=["slave"]
            )
        ]
        
        for host in hosts:
            success = await copier.register_host(host)
            print(f"{'✅' if success else '❌'} 主机 {host.host_id} 注册{'成功' if success else '失败'}")
        
        # 多账户设置
        accounts = [
            AccountInfo(
                account_id="master1",
                login=5000001,
                password="password1",
                server="ICMarkets-Live01",
                host_id="server1",
                account_type="master"
            ),
            AccountInfo(
                account_id="slave1",
                login=6000001,
                password="password1",
                server="ICMarkets-Live01",
                host_id="server2",
                account_type="slave"
            ),
            AccountInfo(
                account_id="slave2",
                login=6000002,
                password="password2",
                server="ICMarkets-Live02",
                host_id="server2",
                account_type="slave"
            )
        ]
        
        for account in accounts:
            success = await copier.register_account(account)
            print(f"{'✅' if success else '❌'} 账户 {account.account_id} 注册{'成功' if success else '失败'}")
        
        # 多配对设置
        pairings = [
            PairingConfig(
                master_account="master1",
                slave_accounts=["slave1", "slave2"],
                strategy="proportional",
                lot_multiplier=0.3,
                symbols_filter=["EURUSD", "GBPUSD", "USDJPY"],
                max_positions=8,
                daily_loss_limit=1000.0
            )
        ]
        
        pairing_ids = []
        for i, config in enumerate(pairings):
            try:
                pairing_id = await copier.create_pairing(config)
                pairing_ids.append(pairing_id)
                print(f"✅ 配对 {i+1} 创建成功: {pairing_id}")
            except Exception as e:
                print(f"❌ 配对 {i+1} 创建失败: {e}")
        
        # 检查系统状态
        print("\n📊 系统状态:")
        system_status = await copier.get_system_status()
        
        if system_status:
            print(f"  • 系统状态: {system_status.get('status', 'unknown')}")
            print(f"  • 运行时间: {system_status.get('uptime', 0):.1f}秒")
            print(f"  • 配对数量: {system_status.get('pairings', 0)}")
            print(f"  • 活跃配对: {system_status.get('active_pairings', 0)}")
            print(f"  • 服务数量: {system_status.get('services', 0)}")
        
        print("\n🎉 高级设置完成!")


async def monitoring_example():
    """监控示例"""
    
    async with MT5CopierSDK("http://localhost:8000") as copier:
        
        print("📊 开始监控示例...")
        
        # 获取所有配对
        pairings = await copier.list_pairings()
        print(f"📋 找到 {len(pairings)} 个配对:")
        
        for pairing in pairings:
            print(f"  • {pairing.get('id', 'unknown')}: {pairing.get('master_account', 'unknown')} -> {pairing.get('slave_accounts', [])}")
        
        # 监控每个配对的状态
        for pairing in pairings:
            pairing_id = pairing.get('id')
            if pairing_id:
                status = await copier.get_pairing_status(pairing_id)
                
                if status:
                    performance = status.get('performance', {})
                    print(f"\n🔍 配对 {pairing_id} 状态:")
                    print(f"  • 总信号数: {performance.get('total_signals', 0)}")
                    print(f"  • 成功复制: {performance.get('successful_copies', 0)}")
                    print(f"  • 失败复制: {performance.get('failed_copies', 0)}")
                    print(f"  • 成功率: {performance.get('success_rate', 0):.2%}")
        
        # 获取系统健康状态
        health = await copier.get_system_health()
        print(f"\n🏥 系统健康状态: {health.get('status', 'unknown')}")
        
        issues = health.get('issues', [])
        if issues:
            print("⚠️  发现的问题:")
            for issue in issues:
                print(f"  • {issue}")
        else:
            print("✅ 未发现问题")
        
        # 获取性能指标
        metrics = await copier.get_performance_metrics()
        print(f"\n📈 性能指标:")
        print(f"  • 时间戳: {metrics.get('timestamp', 0)}")
        
        # 获取交易统计
        trading_stats = await copier.get_trading_stats()
        if trading_stats:
            print(f"📊 交易统计:")
            print(f"  • 总信号数: {trading_stats.get('total_signals', 0)}")
            print(f"  • 成功复制: {trading_stats.get('successful_copies', 0)}")
            print(f"  • 失败复制: {trading_stats.get('failed_copies', 0)}")
            print(f"  • 成功率: {trading_stats.get('success_rate', 0):.2%}")


async def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='MT5跟单系统快速开始')
    parser.add_argument('--mode', choices=['quick', 'advanced', 'monitor'], 
                       default='quick', help='运行模式')
    parser.add_argument('--api-url', default='http://localhost:8000', 
                       help='API服务器地址')
    
    args = parser.parse_args()
    
    # 设置SDK URL
    MT5CopierSDK.__init__.__defaults__ = (args.api_url, None, 30)
    
    try:
        if args.mode == 'quick':
            await quick_setup()
        elif args.mode == 'advanced':
            await advanced_setup()
        elif args.mode == 'monitor':
            await monitoring_example()
        else:
            print("❌ 未知模式")
            return
            
    except Exception as e:
        print(f"❌ 运行失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())