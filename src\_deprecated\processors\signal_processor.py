# 🚨 AUTO-MIGRATED: Old messaging components replaced with HybridQueueManager
# See MIGRATION_GUIDE.md for details

"""
信号处理器

负责信号的接收、验证、转换、过滤和分发。
"""

import asyncio
from dataclasses import dataclass
from datetime import datetime, timed<PERSON>ta
from decimal import Decimal
from typing import Dict, List, Optional, Set, Callable, Any
from collections import defaultdict
import logging
import hashlib

from ..models import Signal, SignalType, SignalPriority, Account, Pairing
from ..engines import CopyTradingEngine, RiskEngine
from ...storage.cache import CacheManager
from ...communication.messaging import NatsClient
from ...utils.decorators import async_timed, with_circuit_breaker


logger = logging.getLogger(__name__)


@dataclass
class SignalMetrics:
    """信号处理指标"""
    received_count: int = 0
    processed_count: int = 0
    filtered_count: int = 0
    distributed_count: int = 0
    error_count: int = 0
    avg_processing_time_ms: float = 0.0
    last_signal_time: Optional[datetime] = None


class SignalFilter:
    """信号过滤器"""
    
    def __init__(self):
        self.filters: List[Callable] = []
        self.filter_stats = defaultdict(lambda: {'passed': 0, 'filtered': 0})
    
    def add_filter(self, name: str, filter_func: Callable) -> None:
        """添加过滤器"""
        self.filters.append((name, filter_func))
    
    async def apply(self, signal: Signal) -> Tuple[bool, List[str]]:
        """应用所有过滤器"""
        filtered_reasons = []
        
        for name, filter_func in self.filters:
            try:
                passed = await filter_func(signal) if asyncio.iscoroutinefunction(filter_func) else filter_func(signal)
                
                if passed:
                    self.filter_stats[name]['passed'] += 1
                else:
                    self.filter_stats[name]['filtered'] += 1
                    filtered_reasons.append(name)
                    
            except Exception as e:
                logger.error(f"Filter {name} error: {e}")
                filtered_reasons.append(f"{name}_error")
        
        return len(filtered_reasons) == 0, filtered_reasons


class SignalRouter:
    """信号路由器"""
    
    def __init__(self, queue_manager: NatsClient):
        self.nats = queue_manager
        self.routes: Dict[SignalType, List[str]] = defaultdict(list)
        self.routing_stats = defaultdict(int)
    
    def add_route(self, signal_type: SignalType, subject_pattern: str):
        """添加路由规则"""
        self.routes[signal_type].append(subject_pattern)
    
    async def route(self, signal: Signal, target_accounts: List[Account]) -> Dict[str, bool]:
        """路由信号到目标账户"""
        results = {}
        
        # 获取信号类型的路由
        subjects = self.routes.get(signal.type, [])
        
        if not subjects:
            # 使用默认路由
            subjects = [f"signals.{signal.type.value}.{{account_id}}"]
        
        # 为每个目标账户发送信号
        for account in target_accounts:
            for subject_pattern in subjects:
                subject = subject_pattern.format(
                    account_id=account.id,
                    node_id=account.node_id,
                    terminal_id=account.terminal_id
                )
                
                try:
                    await self.nats.publish(subject, signal.to_nats_message())
                    results[account.id] = True
                    self.routing_stats[subject] += 1
                    
                except Exception as e:
                    logger.error(f"Failed to route signal to {account.id}: {e}")
                    results[account.id] = False
        
        return results


class SignalProcessor:
    """信号处理器 - 系统的核心信号处理引擎"""
    
    def __init__(self,
                 cache_manager: CacheManager,
                 queue_manager: NatsClient,
                 copy_engine: CopyTradingEngine,
                 risk_engine: RiskEngine):
        
        self.cache = cache_manager
        self.nats = queue_manager
        self.copy_engine = copy_engine
        self.risk_engine = risk_engine
        
        # 信号过滤器
        self.filter = SignalFilter()
        self._setup_default_filters()
        
        # 信号路由器
        self.router = SignalRouter(queue_manager)
        self._setup_default_routes()
        
        # 信号队列
        self.signal_queue: asyncio.Queue = asyncio.Queue(maxsize=10000)
        self.priority_queue: asyncio.Queue = asyncio.Queue(maxsize=1000)
        
        # 信号去重
        self.signal_cache: Dict[str, datetime] = {}
        self.duplicate_window = timedelta(seconds=5)
        
        # 信号聚合
        self.aggregation_buffer: Dict[str, List[Signal]] = defaultdict(list)
        self.aggregation_window = timedelta(milliseconds=100)
        
        # 处理器池
        self.worker_count = 5
        self.workers: List[asyncio.Task] = []
        
        # 指标
        self.metrics = SignalMetrics()
        
        # 信号处理器注册
        self.handlers: Dict[SignalType, Callable] = {}
        self._register_default_handlers()
    
    async def start(self):
        """启动信号处理器"""
        # 启动工作器
        for i in range(self.worker_count):
            worker = asyncio.create_task(self._signal_worker(i))
            self.workers.append(worker)
        
        # 启动优先级工作器
        priority_worker = asyncio.create_task(self._priority_worker())
        self.workers.append(priority_worker)
        
        # 启动聚合器
        aggregator = asyncio.create_task(self._aggregation_worker())
        self.workers.append(aggregator)
        
        # 订阅信号源
        await self._subscribe_signal_sources()
        
        logger.info(f"Signal processor started with {self.worker_count} workers")
    
    async def stop(self):
        """停止信号处理器"""
        # 取消所有工作器
        for worker in self.workers:
            worker.cancel()
        
        # 等待工作器结束
        await asyncio.gather(*self.workers, return_exceptions=True)
        
        logger.info("Signal processor stopped")
    
    @async_timed
    async def process_signal(self, signal: Signal) -> Dict[str, Any]:
        """处理单个信号"""
        start_time = datetime.utcnow()
        result = {
            'signal_id': signal.id,
            'processed': False,
            'filtered': False,
            'routed_count': 0,
            'errors': []
        }
        
        try:
            # 更新指标
            self.metrics.received_count += 1
            self.metrics.last_signal_time = datetime.utcnow()
            
            # 1. 验证信号
            validation_errors = signal.validate()
            if validation_errors:
                result['errors'].extend(validation_errors)
                return result
            
            # 2. 去重检查
            if self._is_duplicate(signal):
                result['filtered'] = True
                result['errors'].append("Duplicate signal")
                self.metrics.filtered_count += 1
                return result
            
            # 3. 应用过滤器
            passed, filter_reasons = await self.filter.apply(signal)
            if not passed:
                result['filtered'] = True
                result['errors'].extend(filter_reasons)
                self.metrics.filtered_count += 1
                return result
            
            # 4. 根据信号类型处理
            handler = self.handlers.get(signal.type)
            if handler:
                handler_result = await handler(signal)
                result.update(handler_result)
            else:
                # 默认处理
                await self._default_handler(signal)
            
            # 5. 如果是交易信号，进行跟单处理
            if signal.is_trade_signal:
                await self._process_trade_signal(signal)
            
            # 6. 路由信号
            target_accounts = await self._get_target_accounts(signal)
            routing_results = await self.router.route(signal, target_accounts)
            result['routed_count'] = sum(1 for success in routing_results.values() if success)
            
            # 7. 记录处理成功
            result['processed'] = True
            self.metrics.processed_count += 1
            self.metrics.distributed_count += result['routed_count']
            
        except Exception as e:
            logger.error(f"Error processing signal {signal.id}: {e}")
            result['errors'].append(str(e))
            self.metrics.error_count += 1
        
        finally:
            # 更新处理时间
            processing_time = (datetime.utcnow() - start_time).total_seconds() * 1000
            self._update_avg_processing_time(processing_time)
        
        return result
    
    async def submit_signal(self, signal: Signal) -> bool:
        """提交信号到处理队列"""
        try:
            if signal.priority == SignalPriority.CRITICAL:
                await self.priority_queue.put(signal)
            else:
                await self.signal_queue.put(signal)
            return True
            
        except asyncio.QueueFull:
            logger.error(f"Signal queue full, dropping signal {signal.id}")
            return False
    
    def _setup_default_filters(self):
        """设置默认过滤器"""
        
        # 过期信号过滤
        self.filter.add_filter("expired", lambda s: not s.is_expired)
        
        # 最小交易量过滤
        def min_volume_filter(signal: Signal) -> bool:
            if signal.volume:
                return signal.volume >= Decimal('0.01')
            return True
        
        self.filter.add_filter("min_volume", min_volume_filter)
        
        # 交易时间过滤
        def trading_hours_filter(signal: Signal) -> bool:
            # 简化示例：检查是否在交易时间
            now = datetime.utcnow()
            if now.weekday() >= 5:  # 周末
                return False
            return True
        
        self.filter.add_filter("trading_hours", trading_hours_filter)
    
    def _setup_default_routes(self):
        """设置默认路由规则"""
        # 交易信号路由
        self.router.add_route(SignalType.OPEN_POSITION, "signals.trade.open.{account_id}")
        self.router.add_route(SignalType.CLOSE_POSITION, "signals.trade.close.{account_id}")
        self.router.add_route(SignalType.MODIFY_POSITION, "signals.trade.modify.{account_id}")
        
        # 订单信号路由
        self.router.add_route(SignalType.PLACE_ORDER, "signals.order.place.{account_id}")
        self.router.add_route(SignalType.CANCEL_ORDER, "signals.order.cancel.{account_id}")
        
        # 状态信号路由
        self.router.add_route(SignalType.POSITION_OPENED, "signals.status.opened.{account_id}")
        self.router.add_route(SignalType.POSITION_CLOSED, "signals.status.closed.{account_id}")
        
        # 系统信号路由
        self.router.add_route(SignalType.HEARTBEAT, "signals.system.heartbeat.{node_id}")
    
    def _register_default_handlers(self):
        """注册默认处理器"""
        self.handlers[SignalType.OPEN_POSITION] = self._handle_open_position
        self.handlers[SignalType.CLOSE_POSITION] = self._handle_close_position
        self.handlers[SignalType.MODIFY_POSITION] = self._handle_modify_position
        self.handlers[SignalType.BALANCE_UPDATE] = self._handle_balance_update
        self.handlers[SignalType.MARGIN_CALL] = self._handle_margin_call
    
    async def _signal_worker(self, worker_id: int):
        """信号处理工作器"""
        logger.info(f"Signal worker {worker_id} started")
        
        while True:
            try:
                # 从队列获取信号
                signal = await self.signal_queue.get()
                
                # 处理信号
                await self.process_signal(signal)
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Worker {worker_id} error: {e}")
                await asyncio.sleep(1)
    
    async def _priority_worker(self):
        """优先级信号处理工作器"""
        logger.info("Priority signal worker started")
        
        while True:
            try:
                # 优先处理高优先级信号
                signal = await self.priority_queue.get()
                
                # 立即处理
                await self.process_signal(signal)
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Priority worker error: {e}")
                await asyncio.sleep(0.1)
    
    async def _aggregation_worker(self):
        """信号聚合工作器"""
        while True:
            try:
                await asyncio.sleep(self.aggregation_window.total_seconds())
                
                # 处理聚合缓冲区
                for key, signals in list(self.aggregation_buffer.items()):
                    if signals:
                        aggregated = self._aggregate_signals(signals)
                        await self.process_signal(aggregated)
                        self.aggregation_buffer[key].clear()
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Aggregation worker error: {e}")
    
    def _is_duplicate(self, signal: Signal) -> bool:
        """检查信号是否重复"""
        # 生成信号指纹
        fingerprint = self._generate_signal_fingerprint(signal)
        
        # 检查缓存
        if fingerprint in self.signal_cache:
            last_time = self.signal_cache[fingerprint]
            if datetime.utcnow() - last_time < self.duplicate_window:
                return True
        
        # 更新缓存
        self.signal_cache[fingerprint] = datetime.utcnow()
        
        # 清理过期缓存
        if len(self.signal_cache) > 10000:
            self._cleanup_signal_cache()
        
        return False
    
    def _generate_signal_fingerprint(self, signal: Signal) -> str:
        """生成信号指纹"""
        # 基于关键字段生成指纹
        key_parts = [
            signal.source_account_id,
            signal.type.value,
            str(signal.symbol),
            str(signal.trade_type),
            str(signal.volume),
            str(signal.reference_ticket)
        ]
        
        key_string = "|".join(filter(None, key_parts))
        return hashlib.md5(key_string.payload =)).hexdigest()
    
    def _cleanup_signal_cache(self):
        """清理过期的信号缓存"""
        cutoff_time = datetime.utcnow() - self.duplicate_window
        
        expired_keys = [
            key for key, timestamp in self.signal_cache.items()
            if timestamp < cutoff_time
        ]
        
        for key in expired_keys:
            del self.signal_cache[key]
    
    def _aggregate_signals(self, signals: List[Signal]) -> Signal:
        """聚合多个信号"""
        if len(signals) == 1:
            return signals[0]
        
        # 创建聚合信号
        base_signal = signals[0]
        aggregated = Signal(
            type=base_signal.type,
            source_account_id=base_signal.source_account_id,
            symbol=base_signal.symbol,
            trade_type=base_signal.trade_type,
            priority=max(s.priority for s in signals),
            metadata={
                'aggregated': True,
                'signal_count': len(signals),
                'original_signals': [s.id for s in signals]
            }
        )
        
        # 聚合交易量
        if all(s.volume for s in signals):
            aggregated.volume = sum(s.volume for s in signals)
        
        # 使用平均价格
        prices = [s.price for s in signals if s.price]
        if prices:
            aggregated.price = sum(prices) / len(prices)
        
        return aggregated
    
    async def _get_target_accounts(self, signal: Signal) -> List[Account]:
        """获取信号的目标账户"""
        target_accounts = []
        
        # 如果指定了目标账户
        if signal.target_account_ids:
            for account_id in signal.target_account_ids:
                account = await self._get_account(account_id)
                if account:
                    target_accounts.append(account)
        
        # 如果指定了配对
        elif signal.target_pairing_ids:
            for pairing_id in signal.target_pairing_ids:
                pairing = await self._get_pairing(pairing_id)
                if pairing:
                    slave_account = await self._get_account(pairing.slave_account_id)
                    if slave_account:
                        target_accounts.append(slave_account)
        
        # 如果是广播信号
        elif signal.is_broadcast:
            # 获取所有相关账户
            # 这里应该根据业务逻辑决定广播范围
            pass
        
        return target_accounts
    
    async def _process_trade_signal(self, signal: Signal):
        """处理交易信号（触发跟单）"""
        # 获取源账户的所有活跃配对
        pairings = await self._get_account_pairings(signal.source_account_id)
        
        if pairings:
            # 提交给跟单引擎处理
            await self.copy_engine.process_signal(signal, pairings)
    
    # 信号处理器方法
    async def _handle_open_position(self, signal: Signal) -> Dict[str, Any]:
        """处理开仓信号"""
        return {
            'handler': 'open_position',
            'processed': True
        }
    
    async def _handle_close_position(self, signal: Signal) -> Dict[str, Any]:
        """处理平仓信号"""
        return {
            'handler': 'close_position',
            'processed': True
        }
    
    async def _handle_modify_position(self, signal: Signal) -> Dict[str, Any]:
        """处理修改仓位信号"""
        return {
            'handler': 'modify_position',
            'processed': True
        }
    
    async def _handle_balance_update(self, signal: Signal) -> Dict[str, Any]:
        """处理余额更新信号"""
        # 更新账户余额缓存
        if 'balance' in signal.data:
            await self.cache.set(
                f"account_balance:{signal.source_account_id}",
                signal.data['balance'],
                ttl=300
            )
        
        return {
            'handler': 'balance_update',
            'processed': True
        }
    
    async def _handle_margin_call(self, signal: Signal) -> Dict[str, Any]:
        """处理追加保证金信号"""
        # 触发风险警报
        logger.warning(f"Margin call for account {signal.source_account_id}")
        
        # 可能需要暂停该账户的交易
        await self._pause_account_trading(signal.source_account_id)
        
        return {
            'handler': 'margin_call',
            'processed': True,
            'action': 'account_paused'
        }
    
    async def _default_handler(self, signal: Signal):
        """默认信号处理器"""
        logger.debug(f"Using default handler for signal type {signal.type}")
    
    def _update_avg_processing_time(self, processing_time_ms: float):
        """更新平均处理时间"""
        current_avg = self.metrics.avg_processing_time_ms
        total = self.metrics.processed_count
        
        if total > 0:
            self.metrics.avg_processing_time_ms = (
                (current_avg * (total - 1) + processing_time_ms) / total
            )
    
    # 辅助方法
    async def _get_account(self, account_id: str) -> Optional[Account]:
        """获取账户信息"""
        cache_key = f"account:{account_id}"
        cached = await self.cache.get(cache_key)
        
        if cached:
            return Account.from_dict(cached)
        
        return None
    
    async def _get_pairing(self, pairing_id: str) -> Optional[Pairing]:
        """获取配对信息"""
        cache_key = f"pairing:{pairing_id}"
        cached = await self.cache.get(cache_key)
        
        if cached:
            return Pairing(**cached)
        
        return None
    
    async def _get_account_pairings(self, account_id: str) -> List[Pairing]:
        """获取账户的所有活跃配对"""
        cache_key = f"account_pairings:{account_id}"
        cached = await self.cache.get(cache_key)
        
        if cached:
            return [Pairing(**p) for p in cached]
        
        return []
    
    async def _pause_account_trading(self, account_id: str):
        """暂停账户交易"""
        # 设置暂停标志
        await self.cache.set(f"account_paused:{account_id}", True, ttl=3600)
        
        # 发送暂停信号
        pause_signal = Signal(
            type=SignalType.SYSTEM,
            source_account_id="system",
            target_account_ids=[account_id],
            priority=SignalPriority.CRITICAL,
            data={'action': 'pause_trading'}
        )
        
        await self.submit_signal(pause_signal)
    
    async def _subscribe_signal_sources(self):
        """订阅信号源"""
        # 订阅所有交易信号
        await self.nats.subscribe("signals.>", self._on_signal_received)
        
        # 订阅特定事件
        await self.nats.subscribe("events.trade.>", self._on_trade_event)
        await self.nats.subscribe("events.account.>", self._on_account_event)
    
    async def _on_signal_received(self, msg):
        """处理接收到的信号"""
        try:
            signal = Signal.from_nats_message(msg.data)
            await self.submit_signal(signal)
        except Exception as e:
            logger.error(f"Error handling received signal: {e}")
    
    async def _on_trade_event(self, msg):
        """处理交易事件"""
        # 转换为信号
        # ...
        pass
    
    async def _on_account_event(self, msg):
        """处理账户事件"""
        # 转换为信号
        # ...
        pass
    
    def get_metrics(self) -> Dict[str, Any]:
        """获取处理指标"""
        return {
            'received': self.metrics.received_count,
            'processed': self.metrics.processed_count,
            'filtered': self.metrics.filtered_count,
            'distributed': self.metrics.distributed_count,
            'errors': self.metrics.error_count,
            'avg_processing_ms': self.metrics.avg_processing_time_ms,
            'last_signal': self.metrics.last_signal_time.isoformat() if self.metrics.last_signal_time else None,
            'queue_size': self.signal_queue.qsize(),
            'priority_queue_size': self.priority_queue.qsize(),
            'filter_stats': dict(self.filter.filter_stats),
            'routing_stats': dict(self.router.routing_stats)
        }