#!/usr/bin/env python3
"""
Enhanced RPC Architecture V2 - Adapted for Multi-Process Isolation
Properly handles MT5 API limitations with process isolation
"""

import asyncio
import multiprocessing as mp
import queue
import time
from typing import Dict, Any, Optional, List
from dataclasses import dataclass
from concurrent.futures import ThreadPoolExecutor
import logging

logger = logging.getLogger(__name__)


class MT5ProcessWorker(mp.Process):
    """
    Isolated MT5 process worker - one per terminal
    Handles synchronous MT5 API calls in isolation
    """
    
    def __init__(self, account_id: str, command_queue: mp.Queue, result_queue: mp.Queue, terminal_path: str):
        super().__init__()
        self.account_id = account_id
        self.command_queue = command_queue
        self.result_queue = result_queue
        self.terminal_path = terminal_path
        self.mt5 = None
        self.running = mp.Event()
        
    def run(self):
        """Run in isolated process - handles MT5 API calls"""
        try:
            # Import MT5 in the isolated process
            import MetaTrader5 as mt5
            self.mt5 = mt5
            
            # Initialize MT5 connection
            if not self.mt5.initialize(path=self.terminal_path):
                self.result_queue.put({
                    "error": "Failed to initialize MT5",
                    "account_id": self.account_id,
                    "status": "failed"
                })
                return
                
            self.running.set()
            logger.info(f"MT5 Process Worker started for {self.account_id}")
            
            # Main processing loop
            while self.running.is_set():
                try:
                    # Get command with timeout
                    command = self.command_queue.get(timeout=0.1)
                    
                    if command.get("type") == "shutdown":
                        break
                        
                    # Process RPC command
                    result = self._process_rpc_command(command)
                    
                    # Send result back
                    self.result_queue.put(result)
                    
                except queue.Empty:
                    continue
                except Exception as e:
                    logger.error(f"Error processing command: {e}")
                    self.result_queue.put({
                        "error": str(e),
                        "status": "failed"
                    })
                    
        except Exception as e:
            logger.error(f"Fatal error in MT5 worker: {e}")
        finally:
            if self.mt5:
                self.mt5.shutdown()
                
    def _process_rpc_command(self, command: Dict) -> Dict:
        """Process RPC command - runs synchronously in isolated process"""
        method = command.get("method")
        params = command.get("params", {})
        request_id = command.get("request_id")
        
        try:
            if method == "get_positions":
                result = self._get_positions(params)
            elif method == "get_account_info":
                result = self._get_account_info(params)
            elif method == "send_order":
                result = self._send_order(params)
            elif method == "get_symbol_info":
                result = self._get_symbol_info(params)
            elif method == "health_check":
                result = self._health_check(params)
            else:
                result = {"error": f"Unknown method: {method}"}
                
            result["request_id"] = request_id
            result["account_id"] = self.account_id
            return result
            
        except Exception as e:
            return {
                "error": str(e),
                "request_id": request_id,
                "account_id": self.account_id,
                "status": "failed"
            }
            
    def _get_positions(self, params: Dict) -> Dict:
        """Get positions - synchronous MT5 API call"""
        positions = self.mt5.positions_get()
        
        if positions is None:
            return {"positions": [], "count": 0, "status": "success"}
            
        positions_data = []
        for pos in positions:
            positions_data.append({
                "ticket": pos.ticket,
                "symbol": pos.symbol,
                "type": pos.type,
                "volume": pos.volume,
                "price_open": pos.price_open,
                "price_current": pos.price_current,
                "profit": pos.profit,
                "swap": pos.swap,
                "comment": pos.comment
            })
            
        return {
            "positions": positions_data,
            "count": len(positions_data),
            "status": "success"
        }
        
    def _get_account_info(self, params: Dict) -> Dict:
        """Get account info - synchronous MT5 API call"""
        account_info = self.mt5.account_info()
        
        if account_info is None:
            return {"error": "Failed to get account info", "status": "failed"}
            
        return {
            "login": account_info.login,
            "balance": account_info.balance,
            "equity": account_info.equity,
            "margin": account_info.margin,
            "margin_free": account_info.margin_free,
            "margin_level": account_info.margin_level,
            "currency": account_info.currency,
            "server": account_info.server,
            "status": "success"
        }
        
    def _send_order(self, params: Dict) -> Dict:
        """Send order - synchronous MT5 API call"""
        # Prepare order request
        request = {
            "action": self.mt5.TRADE_ACTION_DEAL,
            "symbol": params.get("symbol"),
            "volume": params.get("volume"),
            "type": params.get("type"),
            "price": params.get("price", 0.0),
            "sl": params.get("sl", 0.0),
            "tp": params.get("tp", 0.0),
            "comment": params.get("comment", ""),
            "type_time": self.mt5.ORDER_TIME_GTC,
            "type_filling": self.mt5.ORDER_FILLING_IOC,
        }
        
        # Send order
        result = self.mt5.order_send(request)
        
        if result is None:
            return {"error": "Order send failed", "status": "failed"}
            
        return {
            "retcode": result.retcode,
            "deal": result.deal,
            "order": result.order,
            "volume": result.volume,
            "price": result.price,
            "comment": result.comment,
            "status": "success" if result.retcode == self.mt5.TRADE_RETCODE_DONE else "failed"
        }
        
    def _get_symbol_info(self, params: Dict) -> Dict:
        """Get symbol info - synchronous MT5 API call"""
        symbol = params.get("symbol")
        
        if not symbol:
            return {"error": "Symbol parameter required", "status": "failed"}
            
        symbol_info = self.mt5.symbol_info(symbol)
        
        if symbol_info is None:
            return {"error": f"Symbol {symbol} not found", "status": "failed"}
            
        return {
            "symbol": symbol_info.name,
            "bid": symbol_info.bid,
            "ask": symbol_info.ask,
            "spread": symbol_info.spread,
            "digits": symbol_info.digits,
            "point": symbol_info.point,
            "trade_mode": symbol_info.trade_mode,
            "volume_min": symbol_info.volume_min,
            "volume_max": symbol_info.volume_max,
            "volume_step": symbol_info.volume_step,
            "status": "success"
        }
        
    def _health_check(self, params: Dict) -> Dict:
        """Health check - synchronous MT5 API call"""
        account_info = self.mt5.account_info()
        
        if account_info is None:
            return {"status": "failed", "error": "Account not connected"}
            
        return {
            "status": "healthy",
            "account_id": self.account_id,
            "login": account_info.login,
            "server": account_info.server,
            "connected": True,
            "timestamp": time.time()
        }


class MT5RPCServiceV2:
    """
    Enhanced MT5 RPC Service V2 - Properly handles process isolation
    Bridges async RPC requests to synchronous MT5 API calls in isolated processes
    """
    
    def __init__(self, account_id: str, terminal_path: str, jetstream_client):
        self.account_id = account_id
        self.terminal_path = terminal_path
        self.jetstream = jetstream_client
        self.rpc_subject = f"mt5.{account_id}.rpc"
        
        # Process communication queues
        self.command_queue = mp.Queue()
        self.result_queue = mp.Queue()
        
        # MT5 worker process
        self.worker_process = None
        
        # Thread pool for blocking queue operations
        self.executor = ThreadPoolExecutor(max_workers=2)
        
        # Pending requests tracking
        self.pending_requests = {}
        self.request_counter = 0
        
    async def start(self):
        """Start RPC service and MT5 worker process"""
        # Start MT5 worker process
        self.worker_process = MT5ProcessWorker(
            self.account_id,
            self.command_queue,
            self.result_queue,
            self.terminal_path
        )
        self.worker_process.start()
        
        # Start RPC server
        await self.jetstream.nc.subscribe(
            subject=self.rpc_subject,
            cb=self.handle_rpc_request
        )
        
        # Start result processor
        asyncio.create_task(self._process_results())
        
        logger.info(f"✅ RPC Service V2 started for {self.account_id}")
        
    async def stop(self):
        """Stop RPC service and worker process"""
        if self.worker_process:
            # Send shutdown command
            self.command_queue.put({"type": "shutdown"})
            self.worker_process.join(timeout=5)
            if self.worker_process.is_alive():
                self.worker_process.terminate()
                
        self.executor.shutdown(wait=True)
        
    async def handle_rpc_request(self, msg):
        """Handle incoming RPC request - async handler"""
        try:
            from src.messaging.message_codec import MessageCodec
            request = MessageCodec.decode(msg.data)
            
            # Generate request ID
            self.request_counter += 1
            request_id = f"{self.account_id}_{self.request_counter}"
            
            # Store pending request info
            self.pending_requests[request_id] = {
                "reply_to": msg.reply,
                "timestamp": time.time()
            }
            
            # Send command to worker process
            command = {
                "method": request.get("method"),
                "params": request.get("params", {}),
                "request_id": request_id
            }
            
            # Use thread pool to avoid blocking on queue.put
            await asyncio.get_event_loop().run_in_executor(
                self.executor,
                self.command_queue.put,
                command
            )
            
        except Exception as e:
            # Send error response directly
            if msg.reply:
                error_response = {
                    "error": str(e),
                    "account_id": self.account_id,
                    "status": "failed"
                }
                payload = MessageCodec.encode(error_response)
                await self.jetstream.nc.publish(msg.reply, payload)
                
    async def _process_results(self):
        """Process results from worker process - runs continuously"""
        while True:
            try:
                # Get result from queue (non-blocking)
                result = await asyncio.get_event_loop().run_in_executor(
                    self.executor,
                    self._get_result_with_timeout
                )
                
                if result:
                    request_id = result.get("request_id")
                    
                    # Find pending request
                    if request_id in self.pending_requests:
                        pending = self.pending_requests.pop(request_id)
                        reply_to = pending["reply_to"]
                        
                        # Send response
                        if reply_to:
                            from src.messaging.message_codec import MessageCodec
                            payload = MessageCodec.encode(result)
                            await self.jetstream.nc.publish(reply_to, payload)
                            
                # Clean up old pending requests (timeout after 30 seconds)
                current_time = time.time()
                expired = [
                    req_id for req_id, info in self.pending_requests.items()
                    if current_time - info["timestamp"] > 30
                ]
                for req_id in expired:
                    self.pending_requests.pop(req_id, None)
                    
            except Exception as e:
                logger.error(f"Error processing results: {e}")
                await asyncio.sleep(0.1)
                
    def _get_result_with_timeout(self, timeout: float = 0.1):
        """Get result from queue with timeout - synchronous"""
        try:
            return self.result_queue.get(timeout=timeout)
        except queue.Empty:
            return None


class MT5RPCClientV2:
    """
    RPC Client V2 - Compatible with process-isolated RPC service
    """
    
    def __init__(self, jetstream_client):
        self.jetstream = jetstream_client
        
    async def call_rpc(self, account_id: str, method: str, params: Dict = None, timeout: float = 10.0) -> Dict:
        """Call RPC method on remote MT5 service"""
        try:
            request = {
                "method": method,
                "params": params or {},
                "timestamp": time.time()
            }
            
            subject = f"mt5.{account_id}.rpc"
            response = await self.jetstream.request(subject, request, timeout=timeout)
            
            if response is None:
                return {"error": "No response received", "status": "timeout"}
                
            return response
            
        except Exception as e:
            return {"error": str(e), "status": "failed"}
            
    # Convenience methods remain the same
    async def get_positions(self, account_id: str) -> Dict:
        return await self.call_rpc(account_id, "get_positions")
        
    async def get_account_info(self, account_id: str) -> Dict:
        return await self.call_rpc(account_id, "get_account_info")
        
    async def send_order(self, account_id: str, symbol: str, order_type: int,
                        volume: float, price: float = 0.0, sl: float = 0.0,
                        tp: float = 0.0, comment: str = "") -> Dict:
        params = {
            "symbol": symbol,
            "type": order_type,
            "volume": volume,
            "price": price,
            "sl": sl,
            "tp": tp,
            "comment": comment
        }
        return await self.call_rpc(account_id, "send_order", params)
        
    async def health_check(self, account_id: str) -> Dict:
        return await self.call_rpc(account_id, "health_check")


# Example usage with proper process isolation
async def example_usage():
    """
    Example showing proper process isolation for MT5 RPC
    """
    from src.messaging.jetstream_client import JetStreamClient
    
    # Initialize JetStream client
    jetstream = JetStreamClient(["nats://localhost:4222"])
    await jetstream.connect()
    
    # Create RPC services for different accounts (each in separate process)
    services = {}
    
    # Account configurations
    accounts = [
        {"id": "ACC001", "path": "C:/MT5/terminal1/terminal64.exe"},
        {"id": "ACC002", "path": "C:/MT5/terminal2/terminal64.exe"},
        {"id": "ACC003", "path": "C:/MT5/terminal3/terminal64.exe"}
    ]
    
    # Start RPC services
    for account in accounts:
        service = MT5RPCServiceV2(
            account["id"],
            account["path"],
            jetstream
        )
        await service.start()
        services[account["id"]] = service
        
    # Create RPC client
    rpc_client = MT5RPCClientV2(jetstream)
    
    # Now we can make RPC calls to any account
    # Each call is handled by an isolated process
    
    # Get positions from all accounts concurrently
    tasks = []
    for account in accounts:
        tasks.append(rpc_client.get_positions(account["id"]))
        
    results = await asyncio.gather(*tasks)
    
    for i, result in enumerate(results):
        print(f"{accounts[i]['id']} positions: {result}")
        
    # Cleanup
    for service in services.values():
        await service.stop()


if __name__ == "__main__":
    asyncio.run(example_usage())