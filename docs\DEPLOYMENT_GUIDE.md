# MT5 高频交易系统部署指南

## 📋 目录

- [部署架构](#部署架构)
- [环境要求](#环境要求)
- [部署方式](#部署方式)
- [生产环境最佳实践](#生产环境最佳实践)
- [高可用配置](#高可用配置)
- [性能调优](#性能调优)
- [安全加固](#安全加固)
- [监控配置](#监控配置)
- [故障恢复](#故障恢复)

## 🏗️ 部署架构

### 单机部署架构
```
┌─────────────────────────────────────────┐
│           负载均衡器 (Nginx)              │
├─────────────────────────────────────────┤
│     API服务      │    监控服务           │
├──────────────────┼─────────────────────┤
│   主账户监控器    │   从账户执行器        │
├──────────────────┴─────────────────────┤
│         消息队列 (NATS) + Redis          │
├─────────────────────────────────────────┤
│            数据库 (PostgreSQL)           │
└─────────────────────────────────────────┘
```

### 分布式部署架构
```
┌────────────────────────────────────────────────────┐
│                  负载均衡器集群                      │
│              (HAProxy / Nginx Cluster)              │
├────────────────────────────────────────────────────┤
│   Region A          │  Region B         │ Region C  │
├────────────────────┼──────────────────┼──────────┤
│ • API Cluster      │ • API Cluster    │ • API     │
│ • Master Monitor   │ • Slave Executor │ • Slave   │
│ • NATS Node        │ • NATS Node      │ • NATS    │
│ • Redis Master     │ • Redis Slave    │ • Redis   │
└────────────────────┴──────────────────┴──────────┘
```

## 💻 环境要求

### 最低配置（开发环境）
- **CPU**: 2核心
- **内存**: 4GB RAM
- **存储**: 20GB SSD
- **网络**: 100Mbps
- **系统**: Ubuntu 20.04+ / CentOS 8+ / Windows Server 2019+

### 推荐配置（生产环境）
- **CPU**: 8核心以上
- **内存**: 16GB RAM以上
- **存储**: 100GB NVMe SSD
- **网络**: 1Gbps低延迟网络
- **系统**: Ubuntu 22.04 LTS

### 软件依赖
```bash
# 系统依赖
sudo apt-get update
sudo apt-get install -y \
    python3.9 python3.9-dev python3.9-venv \
    build-essential libssl-dev libffi-dev \
    git wget curl nano htop \
    redis-server postgresql postgresql-contrib

# Docker安装
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh

# Docker Compose安装
sudo curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose
```

## 🚀 部署方式

### 方式一：Docker Compose部署（推荐）

#### 1. 准备配置文件
```bash
# 克隆项目
git clone https://github.com/your-org/mt5-python.git
cd mt5-python

# 创建环境变量文件
cp .env.example .env
nano .env

# 配置必要的环境变量
MT5_MASTER_PASSWORD=your_secure_password
NATS_URL=nats://nats:4222
REDIS_URL=redis://redis:6379
DATABASE_URL=************************************/mt5db
API_SECRET_KEY=your_api_secret_key
```

#### 2. 启动服务
```bash
# 生产环境部署
docker-compose -f docker-compose.prod.yml up -d

# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f
```

#### 3. 验证部署
```bash
# 健康检查
curl http://localhost:8000/health

# 查看Grafana
open http://localhost:3000

# 查看API文档
open http://localhost:8000/docs
```

### 方式二：Kubernetes部署

#### 1. 准备Kubernetes集群
```bash
# 使用kubeadm初始化集群（主节点）
sudo kubeadm init --pod-network-cidr=**********/16

# 配置kubectl
mkdir -p $HOME/.kube
sudo cp -i /etc/kubernetes/admin.conf $HOME/.kube/config
sudo chown $(id -u):$(id -g) $HOME/.kube/config

# 安装网络插件
kubectl apply -f https://raw.githubusercontent.com/coreos/flannel/master/Documentation/kube-flannel.yml
```

#### 2. 部署MT5系统
```bash
# 创建命名空间
kubectl create namespace mt5-system

# 创建密钥
kubectl create secret generic mt5-secrets \
  --from-literal=mt5-password='your_password' \
  --from-literal=api-key='your_api_key' \
  -n mt5-system

# 部署配置
kubectl apply -f k8s/configmap.yaml -n mt5-system
kubectl apply -f k8s/persistent-volume.yaml -n mt5-system

# 部署服务
kubectl apply -f k8s/nats-deployment.yaml -n mt5-system
kubectl apply -f k8s/redis-deployment.yaml -n mt5-system
kubectl apply -f k8s/postgres-deployment.yaml -n mt5-system
kubectl apply -f k8s/api-deployment.yaml -n mt5-system
kubectl apply -f k8s/master-monitor-deployment.yaml -n mt5-system
kubectl apply -f k8s/slave-executor-deployment.yaml -n mt5-system

# 部署Ingress
kubectl apply -f k8s/ingress.yaml -n mt5-system
```

#### 3. 使用Helm部署（可选）
```bash
# 添加Helm仓库
helm repo add mt5-system https://your-org.github.io/mt5-helm-charts
helm repo update

# 使用Helm安装
helm install mt5 mt5-system/mt5-trading-system \
  --namespace mt5-system \
  --create-namespace \
  --values values.production.yaml
```

### 方式三：裸机部署

#### 1. 安装Python环境
```bash
# 创建虚拟环境
python3.9 -m venv venv
source venv/bin/activate

# 安装依赖
pip install -r requirements.txt
pip install -e .
```

#### 2. 配置系统服务
```bash
# 创建systemd服务文件
sudo nano /etc/systemd/system/mt5-master-monitor.service
```

```ini
[Unit]
Description=MT5 Master Monitor Service
After=network.target

[Service]
Type=simple
User=mt5user
Group=mt5user
WorkingDirectory=/opt/mt5-python
Environment="PATH=/opt/mt5-python/venv/bin"
ExecStart=/opt/mt5-python/venv/bin/python scripts/start_master.py --config config/production.yaml
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
```

#### 3. 启动服务
```bash
# 重载systemd配置
sudo systemctl daemon-reload

# 启动服务
sudo systemctl start mt5-master-monitor
sudo systemctl start mt5-slave-executor
sudo systemctl start mt5-api

# 设置开机自启
sudo systemctl enable mt5-master-monitor
sudo systemctl enable mt5-slave-executor
sudo systemctl enable mt5-api
```

## 🏆 生产环境最佳实践

### 1. 资源限制
```yaml
# docker-compose.prod.yml
services:
  master-monitor:
    deploy:
      resources:
        limits:
          cpus: '2.0'
          memory: 2G
        reservations:
          cpus: '1.0'
          memory: 1G
```

### 2. 日志管理
```yaml
# 配置日志轮转
logging:
  driver: "json-file"
  options:
    max-size: "100m"
    max-file: "10"
    compress: "true"
```

### 3. 健康检查
```yaml
healthcheck:
  test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
  interval: 30s
  timeout: 10s
  retries: 3
  start_period: 40s
```

### 4. 数据持久化
```yaml
volumes:
  - ./data/postgres:/var/lib/postgresql/data
  - ./data/redis:/data
  - ./logs:/app/logs
```

## 🔄 高可用配置

### NATS集群配置
```bash
# nats-server-1.conf
port: 4222
cluster {
  port: 6222
  routes: [
    nats://nats-2:6222
    nats://nats-3:6222
  ]
}
```

### Redis主从配置
```bash
# redis-slave.conf
replicaof redis-master 6379
replica-read-only yes
```

### PostgreSQL主从复制
```bash
# 主库配置
wal_level = replica
max_wal_senders = 3
wal_keep_segments = 64

# 从库配置
hot_standby = on
```

## ⚡ 性能调优

### 系统内核优化
```bash
# /etc/sysctl.conf
net.core.somaxconn = 65535
net.ipv4.tcp_max_syn_backlog = 65535
net.ipv4.tcp_tw_reuse = 1
net.ipv4.tcp_fin_timeout = 30
net.ipv4.ip_local_port_range = 1024 65535
vm.swappiness = 10
```

### Python优化
```bash
# 使用uvloop提升性能
pip install uvloop

# 在代码中启用
import uvloop
uvloop.install()
```

### NATS性能优化
```yaml
# 增大缓冲区
max_payload: 8MB
max_pending: 256MB
write_deadline: "10s"
```

## 🔐 安全加固

### 1. 网络安全
```bash
# 配置防火墙
sudo ufw allow 22/tcp
sudo ufw allow 80/tcp
sudo ufw allow 443/tcp
sudo ufw allow 8000/tcp
sudo ufw allow 4222/tcp
sudo ufw enable
```

### 2. SSL/TLS配置
```nginx
server {
    listen 443 ssl http2;
    server_name mt5.yourdomain.com;
    
    ssl_certificate /etc/letsencrypt/live/mt5.yourdomain.com/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/mt5.yourdomain.com/privkey.pem;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers HIGH:!aNULL:!MD5;
}
```

### 3. 访问控制
```yaml
# API认证配置
security:
  api_key_header: "X-API-Key"
  jwt_secret: "${JWT_SECRET}"
  jwt_algorithm: "HS256"
  access_token_expire_minutes: 30
```

## 📊 监控配置

### Prometheus配置
```yaml
# prometheus.yml
global:
  scrape_interval: 15s
  evaluation_interval: 15s

scrape_configs:
  - job_name: 'mt5-api'
    static_configs:
      - targets: ['api:8000']
    metrics_path: '/metrics'
    
  - job_name: 'mt5-monitors'
    static_configs:
      - targets: ['master-monitor:9090']
```

### Grafana仪表板
```bash
# 导入预配置的仪表板
curl -X POST *********************************/api/dashboards/import \
  -H "Content-Type: application/json" \
  -d @grafana/dashboards/mt5-overview.json
```

### 告警规则
```yaml
# alerting-rules.yml
groups:
  - name: mt5_alerts
    rules:
      - alert: HighLatency
        expr: mt5_signal_latency_ms > 10
        for: 5m
        annotations:
          summary: "High trading signal latency"
          
      - alert: ServiceDown
        expr: up == 0
        for: 1m
        annotations:
          summary: "Service {{ $labels.job }} is down"
```

## 🔧 故障恢复

### 备份策略
```bash
#!/bin/bash
# backup.sh

# 备份数据库
pg_dump -h postgres -U mt5user mt5db > backup_$(date +%Y%m%d_%H%M%S).sql

# 备份Redis
redis-cli --rdb /backup/redis_$(date +%Y%m%d_%H%M%S).rdb

# 备份配置文件
tar -czf config_backup_$(date +%Y%m%d_%H%M%S).tar.gz config/

# 上传到S3
aws s3 cp backup_*.sql s3://mt5-backups/
aws s3 cp redis_*.rdb s3://mt5-backups/
aws s3 cp config_*.tar.gz s3://mt5-backups/
```

### 灾难恢复流程
```bash
# 1. 停止所有服务
docker-compose down

# 2. 恢复数据库
psql -h postgres -U mt5user mt5db < backup_20240101_120000.sql

# 3. 恢复Redis数据
redis-cli --rdb /backup/redis_20240101_120000.rdb

# 4. 恢复配置
tar -xzf config_backup_20240101_120000.tar.gz

# 5. 重启服务
docker-compose up -d

# 6. 验证恢复
python scripts/health_check.py --comprehensive
```

### 回滚流程
```bash
# 使用Docker镜像标签回滚
docker-compose down
export MT5_VERSION=v1.9.0
docker-compose up -d

# 使用Kubernetes回滚
kubectl rollout undo deployment/mt5-api -n mt5-system
kubectl rollout undo deployment/mt5-master-monitor -n mt5-system
```

## 📝 部署检查清单

- [ ] 所有环境变量已正确配置
- [ ] 防火墙规则已设置
- [ ] SSL证书已安装
- [ ] 数据库已初始化
- [ ] 备份策略已配置
- [ ] 监控告警已设置
- [ ] 日志轮转已配置
- [ ] 资源限制已设置
- [ ] 健康检查已启用
- [ ] 故障恢复流程已测试

---

*更多详细信息请参考[官方文档](https://github.com/your-org/mt5-python/wiki)*