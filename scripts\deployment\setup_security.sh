#!/bin/bash

# MT5 Trading System Security Setup Script
# 自动化安全配置脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_header() {
    echo -e "${PURPLE}========================================${NC}"
    echo -e "${PURPLE} $1${NC}"
    echo -e "${PURPLE}========================================${NC}"
}

# 检查Python环境
check_python() {
    log_header "1. Checking Python Environment"
    
    if ! command -v python3 >/dev/null 2>&1; then
        log_error "Python3 is not installed"
        exit 1
    fi
    
    python_version=$(python3 --version 2>&1 | cut -d' ' -f2)
    log_success "Python3 found: $python_version"
    
    # 检查必要的Python包
    log_info "Checking required Python packages..."
    
    packages=(
        "cryptography"
        "PyJWT"
        "pyyaml"
        "requests"
    )
    
    missing_packages=()
    
    for package in "${packages[@]}"; do
        if ! python3 -c "import $package" >/dev/null 2>&1; then
            missing_packages+=("$package")
        fi
    done
    
    if [ ${#missing_packages[@]} -gt 0 ]; then
        log_warning "Missing packages: ${missing_packages[*]}"
        log_info "Installing missing packages..."
        
        for package in "${missing_packages[@]}"; do
            pip3 install "$package" || {
                log_error "Failed to install $package"
                exit 1
            }
        done
        
        log_success "All required packages installed"
    else
        log_success "All required Python packages are available"
    fi
}

# 生成安全密钥
generate_keys() {
    log_header "2. Generating Security Keys"
    
    if [ -f ".env" ]; then
        log_warning ".env file already exists"
        read -p "Do you want to regenerate security keys? (y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            log_info "Skipping key generation"
            return
        fi
    fi
    
    log_info "Running security key generator..."
    
    if python3 scripts/generate_security_keys.py; then
        log_success "Security keys generated successfully"
    else
        log_error "Failed to generate security keys"
        exit 1
    fi
}

# 修复文件权限
fix_permissions() {
    log_header "3. Fixing File Permissions"
    
    # 安全配置文件权限
    security_files=(
        ".env"
        "config/security/encryption_keys.yaml"
        "config/security/api_permissions.yaml"
    )
    
    for file in "${security_files[@]}"; do
        if [ -f "$file" ]; then
            if chmod 600 "$file" 2>/dev/null; then
                log_success "Set permissions 600 for $file"
            else
                log_warning "Could not set permissions for $file (may be on Windows/WSL)"
            fi
        else
            log_warning "$file not found"
        fi
    done
    
    # 脚本执行权限
    script_files=(
        "run.sh"
        "scripts/security_check.sh"
        "scripts/setup_security.sh"
        "scripts/generate_security_keys.py"
        "scripts/validate_security.py"
        "scripts/test_encryption.py"
        "scripts/test_authentication.py"
    )
    
    for file in "${script_files[@]}"; do
        if [ -f "$file" ]; then
            if chmod +x "$file" 2>/dev/null; then
                log_success "Set executable permission for $file"
            else
                log_warning "Could not set executable permission for $file"
            fi
        else
            log_warning "$file not found"
        fi
    done
}

# 创建必要的目录
create_directories() {
    log_header "4. Creating Security Directories"
    
    directories=(
        "logs"
        "config/security/tls_certificates"
        "data/encrypted"
        "backups/keys"
    )
    
    for dir in "${directories[@]}"; do
        if [ ! -d "$dir" ]; then
            if mkdir -p "$dir"; then
                log_success "Created directory: $dir"
                
                # 设置日志目录权限
                if [[ "$dir" == "logs" ]]; then
                    chmod 750 "$dir" 2>/dev/null || log_warning "Could not set permissions for $dir"
                fi
                
                # 设置敏感目录权限
                if [[ "$dir" == *"keys"* ]] || [[ "$dir" == *"certificates"* ]]; then
                    chmod 700 "$dir" 2>/dev/null || log_warning "Could not set permissions for $dir"
                fi
            else
                log_error "Failed to create directory: $dir"
            fi
        else
            log_info "Directory already exists: $dir"
        fi
    done
}

# 生成TLS证书（测试用）
generate_test_certificates() {
    log_header "5. Generating Test TLS Certificates"
    
    cert_dir="config/security/tls_certificates"
    cert_file="$cert_dir/server.crt"
    key_file="$cert_dir/server.key"
    
    if [ -f "$cert_file" ] && [ -f "$key_file" ]; then
        log_info "TLS certificates already exist"
        return
    fi
    
    if command -v openssl >/dev/null 2>&1; then
        log_info "Generating self-signed TLS certificate..."
        
        openssl req -x509 -newkey rsa:4096 \
            -keyout "$key_file" \
            -out "$cert_file" \
            -days 365 -nodes \
            -subj "/C=CN/ST=Beijing/L=Beijing/O=MT5 Trading System/OU=Security/CN=localhost" \
            2>/dev/null
        
        if [ $? -eq 0 ]; then
            log_success "TLS certificates generated"
            chmod 600 "$key_file" 2>/dev/null || log_warning "Could not set key file permissions"
            chmod 644 "$cert_file" 2>/dev/null || log_warning "Could not set cert file permissions"
        else
            log_error "Failed to generate TLS certificates"
        fi
    else
        log_warning "OpenSSL not found, skipping TLS certificate generation"
        log_info "Install OpenSSL to generate TLS certificates"
    fi
}

# 验证安全配置
validate_security() {
    log_header "6. Validating Security Configuration"
    
    log_info "Running security validation..."
    
    if python3 scripts/validate_security.py; then
        log_success "Security validation passed"
    else
        log_warning "Security validation found issues (see details above)"
        log_info "This is normal for initial setup - some warnings are expected"
    fi
}

# 创建安全备份
create_security_backup() {
    log_header "7. Creating Security Backup"
    
    backup_dir="backups/keys/$(date +%Y%m%d_%H%M%S)"
    
    if mkdir -p "$backup_dir"; then
        log_info "Creating security backup in $backup_dir"
        
        # 备份关键文件
        files_to_backup=(
            ".env"
            "config/security/encryption_keys.yaml"
            "config/security/api_permissions.yaml"
        )
        
        for file in "${files_to_backup[@]}"; do
            if [ -f "$file" ]; then
                cp "$file" "$backup_dir/" && log_success "Backed up $file"
            fi
        done
        
        # 创建备份信息
        {
            echo "# MT5 Trading System Security Backup"
            echo "Created: $(date)"
            echo "Hostname: $(hostname)"
            echo "User: $(whoami)"
            echo ""
            echo "Files backed up:"
            for file in "${files_to_backup[@]}"; do
                if [ -f "$backup_dir/$(basename "$file")" ]; then
                    echo "- $file"
                fi
            done
        } > "$backup_dir/backup_info.txt"
        
        log_success "Security backup created: $backup_dir"
    else
        log_error "Failed to create backup directory"
    fi
}

# 显示安全状态总结
show_security_summary() {
    log_header "Security Setup Summary"
    
    echo ""
    log_info "===================== SETUP COMPLETED ====================="
    
    # 检查关键文件
    if [ -f ".env" ]; then
        log_success "✓ Environment variables configured (.env)"
    else
        log_error "✗ Environment variables missing"
    fi
    
    if [ -f "config/security/encryption_keys.yaml" ]; then
        log_success "✓ Encryption keys configured"
    else
        log_error "✗ Encryption keys missing"
    fi
    
    if [ -f "config/security/tls_certificates/server.crt" ]; then
        log_success "✓ TLS certificates available"
    else
        log_warning "⚠ TLS certificates not configured"
    fi
    
    echo ""
    log_info "Next steps:"
    log_info "1. Review and customize .env file for your environment"
    log_info "2. Run 'bash scripts/security_check.sh' for full security audit"
    log_info "3. Run 'python3 scripts/test_encryption.py' to test encryption"
    log_info "4. Configure production TLS certificates if needed"
    log_info "5. Review config/security/SECURITY_GUIDE.md for details"
    
    echo ""
    log_success "Security setup completed successfully! 🔒"
}

# 主函数
main() {
    log_header "MT5 Trading System Security Setup"
    log_info "Automated security configuration script"
    
    echo ""
    log_info "This script will:"
    log_info "- Check Python environment and install required packages"
    log_info "- Generate secure encryption keys and tokens"
    log_info "- Set appropriate file permissions"
    log_info "- Create necessary directories"
    log_info "- Generate test TLS certificates"
    log_info "- Validate security configuration"
    log_info "- Create security backup"
    
    echo ""
    read -p "Continue with security setup? (Y/n): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Nn]$ ]]; then
        log_info "Security setup cancelled"
        exit 0
    fi
    
    # 执行所有设置步骤
    check_python
    generate_keys
    fix_permissions
    create_directories
    generate_test_certificates
    validate_security
    create_security_backup
    show_security_summary
}

# 运行主函数
main "$@"