"""
分布式协调器 - 自动管理所有账户
根据配置自动启动和管理主从账户
"""

import asyncio
from typing import Dict, List, Optional
from datetime import datetime

from src.monitors.master_monitor import OptimizedMasterMonitor
from src.executors.slave_executor import OptimizedSlaveExecutor
from src.messaging.jetstream_client import JetStreamClient
from src.core.state_manager import StateManager
from src.utils.logger import get_logger

logger = get_logger(__name__)


class DistributedTradingCoordinator:
    """
    分布式交易协调器
    自动根据配置管理账户，无需硬编码
    """
    
    def __init__(self, 
                 config_manager: DistributedConfigManager,
                 jetstream_client: JetStreamClient,
                 state_manager: StateManager):
        self.config_manager = config_manager
        self.jetstream = jetstream_client
        self.state_manager = state_manager
        
        # 运行中的监控器和执行器
        self.master_monitors: Dict[str, OptimizedMasterMonitor] = {}
        self.slave_executors: Dict[str, OptimizedSlaveExecutor] = {}
        
        # 共享的路由器（避免重复创建）
        self.shared_router = None
        
        self.running = False
        
    async def start(self):
        """启动协调器"""
        logger.info("🚀 启动分布式交易协调器")
        
        self.running = True
        
        # 1. 加载配置
        await self.config_manager.load_all_configs()
        
        # 2. 启动配置监控
        await self.config_manager.start_config_watch()
        
        # 3. 创建共享路由器
        await self._create_shared_router()
        
        # 4. 自动启动本主机的所有账户
        await self._start_local_accounts()
        
        # 5. 注册账户位置信息
        await self._register_account_locations()
        
        # 6. 启动健康检查
        asyncio.create_task(self._health_check_loop())
        
        # 7. 监听配置重载事件
        asyncio.create_task(self._config_reload_listener())
        
        logger.info("✅ 分布式交易协调器启动完成")
    
    async def _create_shared_router(self):
        """创建共享路由器"""
        from src.distributed.message_router import EnhancedDistributedRouter
        
        self.shared_router = EnhancedDistributedRouter(
            self.jetstream,
            self.config_manager,
            self.config_manager.current_host_id,
            self.state_manager
        )
        
        # 标记为共享路由器
        self.shared_router._is_shared = True
        
        await self.shared_router.start()
        logger.info("共享路由器已创建")
    
    async def _start_local_accounts(self):
        """启动本主机的所有账户"""
        local_accounts = self.config_manager.local_accounts
        
        # 按类型分组
        masters = []
        slaves = []
        
        for account_id in local_accounts:
            account = self.config_manager.get_account_config(account_id)
            if account.account_type == 'master':
                masters.append(account_id)
            else:
                slaves.append(account_id)
        
        # 启动主账户
        for master_id in masters:
            await self._start_master_account(master_id)
        
        # 启动从账户
        for slave_id in slaves:
            await self._start_slave_account(slave_id)
        
        logger.info(f"启动了 {len(masters)} 个主账户, {len(slaves)} 个从账户")
    
    async def _start_master_account(self, account_id: str):
        """启动主账户监控"""
        try:
            account_config = self.config_manager.get_account_config(account_id)
            if not account_config:
                logger.error(f"找不到账户配置: {account_id}")
                return
            
            # 创建MT5客户端（这里需要你的MT5连接逻辑）
            mt5_client = await self._create_mt5_client(account_config)
            
            # 创建主账户监控器
            monitor = OptimizedMasterMonitor(
                account_id=account_id,
                jetstream_client=self.jetstream,
                config_manager=self.config_manager,
                state_manager=self.state_manager,
                mt5_client=mt5_client,
                host_id=self.config_manager.current_host_id,
                shared_router=self.shared_router  # 使用共享路由器
            )
            
            # 设置监控参数
            if account_config.monitoring:
                monitor.batch_size = account_config.monitoring.get('batch_size', 10)
                monitor.batch_timeout_ms = account_config.monitoring.get('batch_timeout_ms', 100)
            
            # 启动监控
            await monitor.start()
            
            self.master_monitors[account_id] = monitor
            logger.info(f"✅ 主账户启动成功: {account_id}")
            
        except Exception as e:
            logger.error(f"启动主账户失败 {account_id}: {e}")
    
    async def _start_slave_account(self, account_id: str):
        """启动从账户执行器"""
        try:
            account_config = self.config_manager.get_account_config(account_id)
            if not account_config:
                logger.error(f"找不到账户配置: {account_id}")
                return
            
            # 创建MT5客户端
            mt5_client = await self._create_mt5_client(account_config)
            
            # 创建从账户执行器
            executor = OptimizedSlaveExecutor(
                account_id=account_id,
                jetstream_client=self.jetstream,
                config_manager=self.config_manager,
                state_manager=self.state_manager,
                mt5_client=mt5_client,
                host_id=self.config_manager.current_host_id,
                shared_router=self.shared_router  # 使用共享路由器
            )
            
            # 设置执行参数
            if account_config.execution:
                algo = account_config.execution.get('algorithm', 'market')
                executor.config.set('execution.algorithm', algo)
                
                slippage = account_config.execution.get('max_slippage', 0.001)
                executor.config.set('execution.max_slippage', slippage)
            
            # 动态设置配对关系
            await self._setup_slave_pairings(executor, account_id)
            
            # 启动执行器
            await executor.start()
            
            self.slave_executors[account_id] = executor
            logger.info(f"✅ 从账户启动成功: {account_id}")
            
        except Exception as e:
            logger.error(f"启动从账户失败 {account_id}: {e}")
    
    async def _setup_slave_pairings(self, executor: OptimizedSlaveExecutor, slave_id: str):
        """设置从账户的配对关系"""
        # 获取配对配置
        pairings = self.config_manager.get_pairings_for_slave(slave_id)
        
        # 清空现有配对
        await executor.state_manager.set("active_pairings", {}, scope=StateScope.HOST)
        
        # 添加新配对
        for pairing in pairings:
            pairing_config = {
                'master_account_id': pairing.master_id,
                'slave_account_id': pairing.slave_id,
                'copy_ratio': pairing.copy_ratio,
                'enabled': pairing.enabled,
                'filters': pairing.filters or {}
            }
            
            await executor._set_active_pairing(pairing.master_id, pairing_config)
            
            logger.info(f"配对设置: {slave_id} -> {pairing.master_id} "
                       f"(比例: {pairing.copy_ratio})")
    
    async def _register_account_locations(self):
        """注册所有账户的位置信息"""
        for account_id in self.config_manager.local_accounts:
            account = self.config_manager.get_account_config(account_id)
            
            location_info = {
                'account_id': account_id,
                'account_type': account.account_type,
                'host_id': self.config_manager.current_host_id,
                'datacenter': self.config_manager.hosts[self.config_manager.current_host_id].datacenter,
                'broker': account.broker,
                'registered_at': datetime.now().isoformat()
            }
            
            # 保存到分布式状态
            await self.state_manager.set(
                f"account_location:{account_id}",
                location_info,
                scope=StateScope.GLOBAL
            )
            
            # 发布位置更新事件
            await self.jetstream.publish(
                "MT5.ROUTING.ACCOUNT_LOCATION",
                location_info
            )
        
        logger.info("账户位置信息已注册")
    
    async def _health_check_loop(self):
        """健康检查循环"""
        while self.running:
            try:
                # 检查所有监控器和执行器的状态
                health_data = {
                    'host_id': self.config_manager.current_host_id,
                    'timestamp': datetime.now().isoformat(),
                    'masters': {},
                    'slaves': {}
                }
                
                # 检查主账户
                for account_id, monitor in self.master_monitors.items():
                    stats = await monitor.get_stats()
                    health_data['masters'][account_id] = {
                        'running': monitor.running,
                        'signals_published': stats.get('signals_published', 0),
                        'last_signal_time': stats.get('last_signal_time', 0)
                    }
                
                # 检查从账户
                for account_id, executor in self.slave_executors.items():
                    stats = await executor.get_stats()
                    health_data['slaves'][account_id] = {
                        'running': executor.running,
                        'signals_received': stats.get('signals_received', 0),
                        'signals_executed': stats.get('signals_executed', 0),
                        'active_subscriptions': stats.get('active_subscriptions', 0)
                    }
                
                # 发布健康状态
                await self.jetstream.publish(
                    f"MT5.HEALTH.{self.config_manager.current_host_id}",
                    health_data
                )
                
                await asyncio.sleep(30)  # 30秒一次
                
            except Exception as e:
                logger.error(f"健康检查失败: {e}")
                await asyncio.sleep(5)
    
    async def _config_reload_listener(self):
        """监听配置重载事件"""
        async def handle_reload(msg):
            try:
                logger.info("收到配置重载通知")
                
                # 重新加载本地账户
                old_accounts = set(self.config_manager.local_accounts)
                await self.config_manager.load_all_configs()
                new_accounts = set(self.config_manager.local_accounts)
                
                # 找出变化
                added_accounts = new_accounts - old_accounts
                removed_accounts = old_accounts - new_accounts
                
                # 停止已删除的账户
                for account_id in removed_accounts:
                    await self._stop_account(account_id)
                
                # 启动新增的账户
                for account_id in added_accounts:
                    account = self.config_manager.get_account_config(account_id)
                    if account.account_type == 'master':
                        await self._start_master_account(account_id)
                    else:
                        await self._start_slave_account(account_id)
                
                # 更新现有账户的配对关系
                for account_id in new_accounts.intersection(old_accounts):
                    if account_id in self.slave_executors:
                        await self._update_slave_pairings(account_id)
                
                logger.info("配置重载完成")
                
            except Exception as e:
                logger.error(f"配置重载失败: {e}")
        
        await self.jetstream.subscribe("config:reload", handle_reload)
    
    async def _stop_account(self, account_id: str):
        """停止账户"""
        if account_id in self.master_monitors:
            await self.master_monitors[account_id].stop()
            del self.master_monitors[account_id]
            logger.info(f"主账户已停止: {account_id}")
            
        if account_id in self.slave_executors:
            await self.slave_executors[account_id].stop()
            del self.slave_executors[account_id]
            logger.info(f"从账户已停止: {account_id}")
    
    async def _update_slave_pairings(self, slave_id: str):
        """更新从账户的配对关系"""
        if slave_id in self.slave_executors:
            executor = self.slave_executors[slave_id]
            await executor.reload_pairings()
            logger.info(f"更新配对关系: {slave_id}")
    
    async def _create_mt5_client(self, account_config: AccountConfig):
        """创建MT5客户端（需要实现）"""
        # 这里应该根据账户配置创建MT5连接
        # 示例代码，实际需要你的MT5连接逻辑
        mt5_config = {
            'login': account_config.login,
            'password': '从安全存储获取',  # 不要硬编码密码
            'server': account_config.server
        }
        
        # return await create_mt5_connection(mt5_config)
        return None  # 占位符
    
    async def stop(self):
        """停止协调器"""
        self.running = False
        
        # 停止所有账户
        for account_id in list(self.master_monitors.keys()):
            await self._stop_account(account_id)
            
        for account_id in list(self.slave_executors.keys()):
            await self._stop_account(account_id)
        
        # 停止配置监控
        await self.config_manager.stop_config_watch()
        
        # 停止共享路由器
        if self.shared_router:
            await self.shared_router.stop()
        
        logger.info("分布式交易协调器已停止")
