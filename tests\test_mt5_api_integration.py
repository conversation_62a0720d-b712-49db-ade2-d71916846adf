#!/usr/bin/env python3
"""
MT5 API集成单元测试
测试MT5 API调用的错误处理和数据格式转换的正确性
"""

import asyncio
import unittest
from unittest.mock import Mock, AsyncMock, patch
import sys
import os
import time
import platform

# 添加项目根目录到路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

# 导入待测试的模块
from src.core.separated_process_runners import (
    MT5ErrorHandler, MT5APIException, validate_trade_params,
    handle_mt5_response, retry_with_backoff, APICallThrottler, DataCache,
    optimized_api_call
)


class TestMT5ErrorHandler(unittest.TestCase):
    """测试MT5错误处理器"""
    
    def test_is_retryable_error(self):
        """测试可重试错误判断"""
        # 可重试的错误码
        self.assertTrue(MT5ErrorHandler.is_retryable_error(2))
        self.assertTrue(MT5ErrorHandler.is_retryable_error(8))
        self.assertTrue(MT5ErrorHandler.is_retryable_error(128))
        
        # 不可重试的错误码
        self.assertFalse(MT5ErrorHandler.is_retryable_error(0))  # 成功
        self.assertFalse(MT5ErrorHandler.is_retryable_error(3))  # 参数错误
        self.assertFalse(MT5ErrorHandler.is_retryable_error(6))  # 没有权限
    
    def test_is_network_error(self):
        """测试网络错误判断"""
        # 网络错误码
        self.assertTrue(MT5ErrorHandler.is_network_error(64))
        self.assertTrue(MT5ErrorHandler.is_network_error(128))
        self.assertTrue(MT5ErrorHandler.is_network_error(134))
        
        # 非网络错误码
        self.assertFalse(MT5ErrorHandler.is_network_error(0))
        self.assertFalse(MT5ErrorHandler.is_network_error(3))
    
    def test_get_error_message(self):
        """测试错误消息获取"""
        self.assertEqual(MT5ErrorHandler.get_error_message(0), "成功")
        self.assertEqual(MT5ErrorHandler.get_error_message(3), "参数错误")
        self.assertIn("未知错误码", MT5ErrorHandler.get_error_message(9999))
    
    def test_should_increase_delay(self):
        """测试是否应该增加延迟"""
        # 网络错误应该增加延迟
        self.assertTrue(MT5ErrorHandler.should_increase_delay(128))
        # 非网络错误不应该增加延迟
        self.assertFalse(MT5ErrorHandler.should_increase_delay(3))


class TestMT5APIException(unittest.TestCase):
    """测试MT5 API异常"""
    
    def test_exception_creation(self):
        """测试异常创建"""
        exc = MT5APIException("测试错误", retcode=123, account_id="ACC001")
        self.assertEqual(str(exc), "测试错误")
        self.assertEqual(exc.retcode, 123)
        self.assertEqual(exc.account_id, "ACC001")
        self.assertIsNotNone(exc.timestamp)


class TestTradeParamsValidation(unittest.TestCase):
    """测试交易参数验证"""
    
    def test_valid_params(self):
        """测试有效参数"""
        # 应该不抛出异常
        validate_trade_params("EURUSD", 0.1, "BUY")
        validate_trade_params("GBPUSD", 1.0, 0)
        validate_trade_params("USDJPY", 10.0, "SELL")
    
    def test_invalid_symbol(self):
        """测试无效交易品种"""
        with self.assertRaises(MT5APIException):
            validate_trade_params("", 0.1, "BUY")
        
        with self.assertRaises(MT5APIException):
            validate_trade_params(None, 0.1, "BUY")
    
    def test_invalid_volume(self):
        """测试无效交易手数"""
        with self.assertRaises(MT5APIException):
            validate_trade_params("EURUSD", 0, "BUY")  # 手数为0
        
        with self.assertRaises(MT5APIException):
            validate_trade_params("EURUSD", -0.1, "BUY")  # 负手数
        
        with self.assertRaises(MT5APIException):
            validate_trade_params("EURUSD", 101, "BUY")  # 超过安全限制
    
    def test_invalid_order_type(self):
        """测试无效订单类型"""
        with self.assertRaises(MT5APIException):
            validate_trade_params("EURUSD", 0.1, "INVALID")
        
        with self.assertRaises(MT5APIException):
            validate_trade_params("EURUSD", 0.1, 99)


class TestMT5ResponseHandler(unittest.TestCase):
    """测试MT5响应处理"""
    
    def setUp(self):
        """设置测试环境"""
        self.mock_response = Mock()
    
    def test_successful_response(self):
        """测试成功响应"""
        self.mock_response.status = 'success'
        self.mock_response.data = {'test': 'data'}
        self.mock_response.error_message = None
        
        result = handle_mt5_response(self.mock_response, "测试操作", "ACC001")
        self.assertEqual(result, {'test': 'data'})
    
    def test_failed_response(self):
        """测试失败响应"""
        self.mock_response.status = 'error'
        self.mock_response.error_message = "测试错误"
        
        with self.assertRaises(MT5APIException) as cm:
            handle_mt5_response(self.mock_response, "测试操作", "ACC001")
        
        self.assertIn("测试错误", str(cm.exception))
    
    def test_no_data_response(self):
        """测试无数据响应"""
        self.mock_response.status = 'success'
        self.mock_response.data = None
        
        with self.assertRaises(MT5APIException) as cm:
            handle_mt5_response(self.mock_response, "测试操作", "ACC001")
        
        self.assertIn("无数据返回", str(cm.exception))
    
    def test_no_response(self):
        """测试空响应"""
        with self.assertRaises(MT5APIException) as cm:
            handle_mt5_response(None, "测试操作", "ACC001")
        
        self.assertIn("无响应", str(cm.exception))


class TestRetryWithBackoff(unittest.IsolatedAsyncioTestCase):
    """测试重试机制 - 使用IsolatedAsyncioTestCase确保异步执行"""
    
    def setUp(self):
        """设置测试环境"""
        self.call_count = 0
    
    async def test_successful_first_call(self):
        """测试第一次调用成功"""
        start_time = time.perf_counter()
        
        async def success_func():
            self.call_count += 1
            await asyncio.sleep(0.001)  # 确保有可测量的异步操作
            return "success"
        
        result = await retry_with_backoff(success_func, max_retries=3, account_id="TEST")
        
        end_time = time.perf_counter()
        duration = end_time - start_time
        
        self.assertEqual(result, "success")
        self.assertEqual(self.call_count, 1)
        # 验证实际执行了异步逻辑 - 放宽时间要求
        self.assertGreater(duration, 0.0005, "异步调用应该有可测量的执行时间")
    
    async def test_retry_on_retryable_error(self):
        """测试可重试错误的重试"""
        start_time = time.perf_counter()
        
        async def retry_func():
            self.call_count += 1
            if self.call_count < 3:
                exc = MT5APIException("可重试错误", retcode=2)
                raise exc
            return "success"
        
        result = await retry_with_backoff(
            retry_func, 
            max_retries=3, 
            initial_delay=0.005,  # 进一步缩短延迟用于测试
            account_id="TEST"
        )
        
        end_time = time.perf_counter()
        duration = end_time - start_time
        
        self.assertEqual(result, "success")
        self.assertEqual(self.call_count, 3)
        # 验证重试机制实际产生了延迟 - 放宽要求
        self.assertGreater(duration, 0.005, "重试应该产生可测量的延迟")
    
    async def test_max_retries_exceeded(self):
        """测试超过最大重试次数"""
        start_time = time.perf_counter()
        
        async def fail_func():
            self.call_count += 1
            raise MT5APIException("持续失败", retcode=2)
        
        with self.assertRaises(MT5APIException):
            await retry_with_backoff(
                fail_func, 
                max_retries=2, 
                initial_delay=0.005,  # 缩短延迟
                account_id="TEST"
            )
        
        end_time = time.perf_counter()
        duration = end_time - start_time
        
        self.assertEqual(self.call_count, 3)  # 原始调用 + 2次重试
        # 验证重试延迟实际生效 - 放宽要求
        self.assertGreater(duration, 0.005, "失败重试应该产生延迟")


class TestAPICallThrottler(unittest.IsolatedAsyncioTestCase):
    """测试API调用限流器 - 使用IsolatedAsyncioTestCase确保异步执行"""
    
    def setUp(self):
        """设置测试环境"""
        self.throttler = APICallThrottler(max_calls_per_second=20.0)  # 提高频率用于测试
    
    async def test_throttling_behavior(self):
        """测试限流行为"""
        start_time = time.perf_counter()
        
        # 连续进行5次调用
        for i in range(5):
            await self.throttler.throttle("ACC001", "test_op")
        
        end_time = time.perf_counter()
        duration = end_time - start_time
        
        # 计算期望的最小延迟 (5次调用，间隔50ms)
        expected_min_duration = 4 * (1.0 / 20.0)  # 4个间隔
        
        print(f"限流测试: 实际耗时 {duration:.3f}s, 期望最小 {expected_min_duration:.3f}s")
        
        # 验证限流实际生效 - 放宽要求适应Windows系统
        self.assertGreaterEqual(duration, expected_min_duration * 0.6, 
                               f"限流应该产生至少 {expected_min_duration * 0.6:.3f}s 的延迟")
    
    def test_get_stats(self):
        """测试统计信息获取"""
        # 模拟一些调用
        self.throttler.call_counts["ACC001:test"] = 5
        self.throttler.call_counts["ACC002:test"] = 3
        
        stats = self.throttler.get_stats("ACC001")
        self.assertEqual(stats["test"], 5)
        
        stats = self.throttler.get_stats("ACC002")
        self.assertEqual(stats["test"], 3)


class TestDataCache(unittest.IsolatedAsyncioTestCase):
    """测试数据缓存器 - 使用IsolatedAsyncioTestCase确保异步执行"""
    
    def setUp(self):
        """设置测试环境"""
        self.cache = DataCache(default_ttl=0.1)  # 100ms TTL for more stable testing
    
    async def test_cache_set_and_get(self):
        """测试缓存设置和获取"""
        start_time = time.perf_counter()
        
        await self.cache.set("test_key", "test_value")
        await asyncio.sleep(0.001)  # 确保有异步操作
        result = await self.cache.get("test_key")
        
        end_time = time.perf_counter()
        duration = end_time - start_time
        
        self.assertEqual(result, "test_value")
        # 验证异步操作实际执行 - 放宽要求
        self.assertGreater(duration, 0.0005, "缓存操作应该有可测量的执行时间")
    
    async def test_cache_expiration(self):
        """测试缓存过期"""
        start_time = time.perf_counter()
        
        await self.cache.set("test_key", "test_value")
        
        # 等待缓存过期
        await asyncio.sleep(0.15)  # 超过TTL (100ms)
        
        result = await self.cache.get("test_key")
        
        end_time = time.perf_counter()
        duration = end_time - start_time
        
        self.assertIsNone(result)
        # 验证实际等待了过期时间 - 放宽要求
        self.assertGreater(duration, 0.12, "应该实际等待了过期时间")
    
    async def test_cache_custom_ttl(self):
        """测试自定义TTL"""
        await self.cache.set("test_key", "test_value")
        
        # 使用短TTL
        result = await self.cache.get("test_key", ttl=0.02)
        self.assertEqual(result, "test_value")
        
        # 等待自定义TTL过期
        await asyncio.sleep(0.03)
        
        result = await self.cache.get("test_key", ttl=0.02)
        self.assertIsNone(result)
    
    async def test_clear_expired(self):
        """测试清理过期缓存"""
        # 记录开始时间
        start_time = time.perf_counter()
        
        await self.cache.set("test_key1", "value1")
        await self.cache.set("test_key2", "value2")
        
        # 验证缓存已设置
        self.assertEqual(len(self.cache.cache), 2)
        self.assertEqual(len(self.cache.timestamps), 2)
        
        # 等待缓存过期 - 使用更保守的等待时间
        await asyncio.sleep(0.2)  # 确保超过TTL (100ms) 的2倍
        
        elapsed = time.perf_counter() - start_time
        print(f"缓存过期等待时间: {elapsed*1000:.2f}ms (TTL: 100ms)")
        
        await self.cache.clear_expired()
        
        # 检查缓存是否已清理
        cache_remaining = len(self.cache.cache)
        timestamps_remaining = len(self.cache.timestamps)
        
        print(f"清理后剩余缓存项: {cache_remaining}, 时间戳项: {timestamps_remaining}")
        
        self.assertEqual(cache_remaining, 0, f"缓存应为空，但还有{cache_remaining}项")
        self.assertEqual(timestamps_remaining, 0, f"时间戳应为空，但还有{timestamps_remaining}项")


class TestOptimizedAPICall(unittest.IsolatedAsyncioTestCase):
    """测试优化的API调用 - 使用IsolatedAsyncioTestCase确保异步执行"""
    
    def setUp(self):
        """设置测试环境"""
        # 重置全局缓存
        from src.core.separated_process_runners import _data_cache
        _data_cache.cache.clear()
        _data_cache.timestamps.clear()
    
    async def test_cache_hit(self):
        """测试缓存命中"""
        call_count = 0
        start_time = time.time()
        
        async def test_func():
            nonlocal call_count
            call_count += 1
            await asyncio.sleep(0.01)  # 模拟异步操作
            return "test_result"
        
        # 第一次调用
        result1 = await optimized_api_call(
            "ACC001", "test", test_func, 
            cache_key="test_cache", cache_ttl=0.5
        )
        
        # 第二次调用应该命中缓存
        result2 = await optimized_api_call(
            "ACC001", "test", test_func,
            cache_key="test_cache", cache_ttl=0.5
        )
        
        end_time = time.time()
        duration = end_time - start_time
        
        self.assertEqual(result1, "test_result")
        self.assertEqual(result2, "test_result")
        self.assertEqual(call_count, 1)  # 只调用了一次
        
        # 验证缓存实际生效（第二次调用更快）
        self.assertGreater(duration, 0.01, "应该执行了至少一次异步操作")
        self.assertLess(duration, 0.05, "缓存应该避免了第二次异步操作")
    
    async def test_throttling_integration(self):
        """测试限流集成"""
        start_time = time.time()
        
        async def fast_func():
            return "fast_result"
        
        # 连续调用，不使用缓存
        await optimized_api_call("ACC001", "fast_test", fast_func, cache_key=None)
        await optimized_api_call("ACC001", "fast_test", fast_func, cache_key=None)
        await optimized_api_call("ACC001", "fast_test", fast_func, cache_key=None)
        
        end_time = time.time()
        duration = end_time - start_time
        
        print(f"限流集成测试: 3次调用耗时 {duration:.3f}s")
        
        # 应该有限流延迟 (基于8 calls/second的全局限流)
        expected_min_duration = 2 * (1.0 / 8.0)  # 2个间隔
        self.assertGreaterEqual(duration, expected_min_duration * 0.5, 
                               f"限流集成应该产生至少 {expected_min_duration * 0.5:.3f}s 的延迟")


def run_unit_tests():
    """运行所有单元测试 - 修复异步测试执行问题"""
    print("🧪 开始运行MT5 API集成单元测试...")
    print(f"🖥️  运行环境: {platform.system()} {platform.release()}")
    
    # 创建测试套件 - 区分同步和异步测试
    sync_test_classes = [
        TestMT5ErrorHandler,
        TestMT5APIException, 
        TestTradeParamsValidation,
        TestMT5ResponseHandler,
    ]
    
    async_test_classes = [
        TestRetryWithBackoff,
        TestAPICallThrottler,
        TestDataCache,
        TestOptimizedAPICall
    ]
    
    total_tests = 0
    failed_tests = 0
    
    # 运行同步测试
    print("\n📋 运行同步测试...")
    for test_class in sync_test_classes:
        suite = unittest.TestLoader().loadTestsFromTestCase(test_class)
        runner = unittest.TextTestRunner(verbosity=1, stream=open(os.devnull, 'w'))
        result = runner.run(suite)
        
        total_tests += result.testsRun
        failed_tests += len(result.failures) + len(result.errors)
        
        status = "✅" if result.wasSuccessful() else "❌"
        print(f"{status} {test_class.__name__}: {result.testsRun} 测试")
    
    # 运行异步测试 - 使用正确的异步执行
    print("\n🔄 运行异步测试...")
    
    # Windows兼容性：使用ProactorEventLoop
    if platform.system() == 'Windows':
        asyncio.set_event_loop_policy(asyncio.WindowsProactorEventLoopPolicy())
    
    for test_class in async_test_classes:
        suite = unittest.TestLoader().loadTestsFromTestCase(test_class)
        runner = unittest.TextTestRunner(verbosity=1, stream=open(os.devnull, 'w'))
        result = runner.run(suite)
        
        total_tests += result.testsRun
        failed_tests += len(result.failures) + len(result.errors)
        
        status = "✅" if result.wasSuccessful() else "❌"
        print(f"{status} {test_class.__name__}: {result.testsRun} 测试")
        
        # 输出异常详情（如果有）
        if result.failures:
            for test, traceback in result.failures:
                print(f"   ⚠️  失败: {test}")
        if result.errors:
            for test, traceback in result.errors:
                print(f"   ❌ 错误: {test}")
    
    success_rate = (total_tests - failed_tests) / total_tests if total_tests > 0 else 0
    
    print(f"\n📊 单元测试总结:")
    print(f"总测试数: {total_tests}")
    print(f"通过: {total_tests - failed_tests}")
    print(f"失败: {failed_tests}")
    print(f"成功率: {success_rate:.1%}")
    
    # 只有完全成功才返回True
    return failed_tests == 0


if __name__ == '__main__':
    success = run_unit_tests()
    if success:
        print("✅ 所有单元测试通过！")
    else:
        print("❌ 部分单元测试失败")
    
    sys.exit(0 if success else 1)