"""
动态角色管理API
允许在运行时调整账户的主从关系
"""
from fastapi import APIRouter, HTTPException
from pydantic import BaseModel
from typing import List, Dict, Any, Optional
from ..config.redis_client import RedisClient
from ..messaging.nats_client import NATSClient
from ..utils.logger import get_logger
import json


router = APIRouter(prefix="/api/roles", tags=["role_management"])
logger = get_logger("RoleManagementAPI")


class RoleUpdateRequest(BaseModel):
    """角色更新请求"""
    account_id: str
    role: str  # "master", "slave", "standalone"
    master_settings: Optional[Dict[str, Any]] = None
    slave_settings: Optional[Dict[str, Any]] = None


class SubscriptionRequest(BaseModel):
    """订阅管理请求"""
    slave_account_id: str
    master_account_ids: List[str]
    action: str  # "add", "remove", "set"


class RoleManager:
    """角色管理器"""
    
    def __init__(self):
        self.redis_client = RedisClient()
        self.nats_client = NATSClient()
        
    async def get_account_role(self, account_id: str) -> Dict[str, Any]:
        """获取账户当前角色"""
        role_key = f"account:{account_id}:role"
        role_data = await self.redis_client.get(role_key)
        
        if not role_data:
            return {"role": "standalone", "settings": {}}
            
        return json.loads(role_data)
        
    async def update_account_role(self, account_id: str, role: str, settings: Dict[str, Any] = None) -> bool:
        """更新账户角色"""
        role_key = f"account:{account_id}:role"
        role_data = {
            "role": role,
            "settings": settings or {},
            "updated_at": datetime.now().isoformat()
        }
        
        # 保存到Redis
        await self.redis_client.set(role_key, json.dumps(role_data))
        
        # 发布角色变更事件
        await self.nats_client.publish(
            f"role.changed.{account_id}",
            {
                "account_id": account_id,
                "new_role": role,
                "settings": settings
            }
        )
        
        logger.info(f"账户 {account_id} 角色更新为: {role}")
        return True
        
    async def update_subscriptions(self, slave_id: str, master_ids: List[str], action: str) -> bool:
        """更新从账户的订阅关系"""
        sub_key = f"subscriptions:{slave_id}"
        
        current_subs = await self.redis_client.get(sub_key)
        current_masters = json.loads(current_subs) if current_subs else []
        
        if action == "add":
            # 添加新的主账户
            new_masters = list(set(current_masters + master_ids))
        elif action == "remove":
            # 移除指定的主账户
            new_masters = [m for m in current_masters if m not in master_ids]
        elif action == "set":
            # 设置为新的主账户列表
            new_masters = master_ids
        else:
            raise ValueError(f"未知的操作: {action}")
            
        # 保存更新后的订阅列表
        await self.redis_client.set(sub_key, json.dumps(new_masters))
        
        # 发布订阅变更事件
        await self.nats_client.publish(
            f"subscription.changed.{slave_id}",
            {
                "slave_id": slave_id,
                "masters": new_masters,
                "action": action
            }
        )
        
        logger.info(f"从账户 {slave_id} 订阅更新: {new_masters}")
        return True
        
    async def get_all_masters(self) -> List[Dict[str, Any]]:
        """获取所有主账户"""
        # 扫描所有账户角色
        pattern = "account:*:role"
        keys = await self.redis_client.scan_keys(pattern)
        
        masters = []
        for key in keys:
            role_data = await self.redis_client.get(key)
            if role_data:
                data = json.loads(role_data)
                if data.get("role") == "master":
                    account_id = key.split(":")[1]
                    masters.append({
                        "account_id": account_id,
                        "settings": data.get("settings", {})
                    })
                    
        return masters
        
    async def get_slave_subscriptions(self, slave_id: str) -> List[str]:
        """获取从账户的订阅列表"""
        sub_key = f"subscriptions:{slave_id}"
        subs = await self.redis_client.get(sub_key)
        return json.loads(subs) if subs else []


# 延迟实例化管理器
role_manager = None

def get_role_manager():
    """获取角色管理器实例"""
    global role_manager
    if role_manager is None:
        role_manager = RoleManager()
    return role_manager


@router.get("/account/{account_id}")
async def get_account_role(account_id: str):
    """获取账户当前角色配置"""
    try:
        manager = get_role_manager()
        role_info = await manager.get_account_role(account_id)
        return {"status": "success", "data": role_info}
    except Exception as e:
        logger.error(f"获取角色失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.put("/account/{account_id}")
async def update_account_role(account_id: str, request: RoleUpdateRequest):
    """更新账户角色"""
    try:
        settings = {}
        if request.role == "master" and request.master_settings:
            settings = request.master_settings
        elif request.role == "slave" and request.slave_settings:
            settings = request.slave_settings
            
        manager = get_role_manager()
        success = await manager.update_account_role(
            account_id, 
            request.role,
            settings
        )
        
        if success:
            return {"status": "success", "message": f"账户 {account_id} 角色已更新为 {request.role}"}
        else:
            raise HTTPException(status_code=500, detail="角色更新失败")
            
    except Exception as e:
        logger.error(f"更新角色失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/subscriptions")
async def manage_subscriptions(request: SubscriptionRequest):
    """管理从账户的订阅关系"""
    try:
        manager = get_role_manager()
        success = await manager.update_subscriptions(
            request.slave_account_id,
            request.master_account_ids,
            request.action
        )
        
        if success:
            return {
                "status": "success", 
                "message": f"订阅关系已更新",
                "slave_id": request.slave_account_id,
                "masters": request.master_account_ids
            }
        else:
            raise HTTPException(status_code=500, detail="订阅更新失败")
            
    except Exception as e:
        logger.error(f"管理订阅失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/masters")
async def list_all_masters():
    """列出所有主账户"""
    try:
        manager = get_role_manager()
        masters = await manager.get_all_masters()
        return {"status": "success", "data": masters}
    except Exception as e:
        logger.error(f"获取主账户列表失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/subscriptions/{slave_id}")
async def get_subscriptions(slave_id: str):
    """获取从账户的订阅列表"""
    try:
        manager = get_role_manager()
        subscriptions = await manager.get_slave_subscriptions(slave_id)
        return {
            "status": "success",
            "slave_id": slave_id,
            "subscribed_masters": subscriptions
        }
    except Exception as e:
        logger.error(f"获取订阅列表失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


from datetime import datetime