# MT5 Trading System Frontend

<div align="center">

English | [简体中文](README.md)

</div>

Frontend interface for the MT5 dynamic pairing trading system.

## Technology Stack

- React 18
- TypeScript
- Vite
- Ant Design
- Chart.js
- Socket.io

## Features

- Real-time account monitoring
- Pairing management interface
- Trading data visualization
- System status monitoring
- Risk management controls

## Development Guide

### Install Dependencies

```bash
npm install
```

### Development Mode

```bash
npm run dev
```

### Build Production Version

```bash
npm run build
```

### Run Tests

```bash
npm test
```

### Code Formatting

```bash
npm run format
```

## Project Structure

```
src/
├── components/     # Reusable components
├── pages/         # Page components
├── hooks/         # Custom hooks
├── services/      # API services
├── utils/         # Utility functions
├── styles/        # Style files
└── types/         # TypeScript type definitions
```

## Environment Variables

Create `.env` file:

```
VITE_API_BASE_URL=http://localhost:8000
VITE_WS_URL=ws://localhost:8765
```

## Deployment

```bash
npm run build
```

Build files will be output to the `dist/` directory.