# MT5分布式交易系统 - 核心系统配置
# 这是主配置文件，定义系统的基础设置和默认值

# ============================================================================
# 系统基础设置
# ============================================================================
system:
  name: "MT5 Distributed Trading System"
  version: "2.0.0"
  environment: ${MT5_ENVIRONMENT:-development}  # development/testing/production
  
# ============================================================================
# 配置导入声明（按加载顺序）
# ============================================================================
imports:
  - "infrastructure.yaml"           # 基础设施配置
  - "monitoring.yaml"              # 监控配置
  - "../hosts/deployment.yaml"     # 主机部署配置
  - "../accounts/*.yaml"           # 账户配置（自动发现）
  - "../relationships/copy_trading.yaml"  # 跟单关系

# ============================================================================
# 账户管理
# ============================================================================
accounts:
  auto_discovery: true
  config_directory: "config/accounts"
  validation:
    enabled: true
    schema_validation: true
    cross_reference_check: true

# ============================================================================
# 应用程序配置
# ============================================================================
application:
  # 进程管理
  process:
    isolation_mode: "full"          # full/partial/none
    restart_policy: "unless-stopped"
    graceful_shutdown_timeout: 30
    
  # 性能优化
  performance:
    max_workers: ${MT5_MAX_WORKERS:-20}
    event_loop_policy: "uvloop"
    memory_limit_mb: ${MT5_MEMORY_LIMIT:-4096}
    
  # 安全设置
  security:
    authentication_enabled: false  # 开发环境默认关闭
    api_rate_limiting: false
    data_encryption: false

# ============================================================================
# 网络和通信
# ============================================================================
network:
  # 内部通信超时
  timeouts:
    connect: 10
    read: 30
    write: 10
    keepalive: 60
    
  # 重试策略
  retry:
    max_attempts: 3
    backoff_factor: 2
    jitter: true

# ============================================================================
# 数据管理
# ============================================================================
data:
  # 数据持久化
  persistence:
    enabled: true
    backup_enabled: true
    backup_interval_hours: 24
    retention_days: 30
    
  # 缓存策略
  cache:
    enabled: true
    default_ttl: 3600
    max_memory_mb: 512

# ============================================================================
# 日志配置基础设置
# ============================================================================
logging:
  level: ${MT5_LOG_LEVEL:-INFO}
  format: "json"
  output:
    console: true
    file: true
    
# ============================================================================
# 健康检查和监控基础
# ============================================================================
health:
  checks:
    enabled: true
    interval: 30
    timeout: 10
    endpoint: "/health"
    
  metrics:
    enabled: true
    collection_interval: 60

# ============================================================================
# 开发和调试
# ============================================================================
debug:
  enabled: ${MT5_DEBUG:-false}
  profiling: false
  trace_enabled: false
  
# ============================================================================
# 功能开关
# ============================================================================
features:
  copy_trading: true
  distributed_deployment: true
  advanced_monitoring: true
  api_gateway: true
  web_interface: false  # 默认关闭，按需启用