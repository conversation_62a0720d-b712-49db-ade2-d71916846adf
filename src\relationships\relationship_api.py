#!/usr/bin/env python3
"""
关系管理命令行工具
提供便捷的CLI接口来管理交易关系
"""

import click
import json
import yaml
from datetime import datetime
from typing import List, Dict

from .relationship_manager import (
    get_relationship_manager,
    RelationshipType,
    RelationshipStatus
)


@click.group()
@click.option('--storage-path', default=None, help='关系存储路径')
@click.pass_context
def cli(ctx, storage_path):
    """MT5跟单关系管理工具"""
    ctx.ensure_object(dict)
    ctx.obj['manager'] = get_relationship_manager(storage_path)


@cli.command()
@click.argument('source_account')
@click.argument('target_account')
@click.option('--type', 'relationship_type', 
              type=click.Choice(['forward', 'reverse', 'bidirectional', 'hedge']),
              default='forward', help='关系类型')
@click.option('--ratio', type=float, default=1.0, help='手数比例')
@click.option('--name', help='关系名称')
@click.option('--description', help='关系描述')
@click.pass_context
def create(ctx, source_account, target_account, relationship_type, ratio, name, description):
    """创建新的跟单关系"""
    manager = ctx.obj['manager']
    
    try:
        relationship = manager.create_relationship(
            source_account=source_account,
            target_account=target_account,
            relationship_type=RelationshipType(relationship_type),
            name=name or f"{source_account} -> {target_account}",
            description=description or f"{relationship_type}跟单关系"
        )
        
        # 设置手数比例
        if ratio != 1.0:
            relationship.volume_mapping.ratio = ratio
            manager.update_relationship(relationship.relationship_id, 
                                      volume_mapping=relationship.volume_mapping)
        
        click.echo(f"✅ 创建跟单关系成功:")
        click.echo(f"   关系ID: {relationship.relationship_id}")
        click.echo(f"   类型: {relationship_type}")
        click.echo(f"   比例: {ratio}")
        
    except Exception as e:
        click.echo(f"❌ 创建失败: {e}", err=True)


@cli.command()
@click.argument('relationship_id')
@click.option('--ratio', type=float, help='更新手数比例')
@click.option('--status', type=click.Choice(['active', 'paused', 'disabled']), help='更新状态')
@click.option('--name', help='更新名称')
@click.pass_context
def update(ctx, relationship_id, ratio, status, name):
    """更新跟单关系"""
    manager = ctx.obj['manager']
    
    try:
        updates = {}
        if ratio is not None:
            relationship = manager.get_relationship(relationship_id)
            if relationship:
                relationship.volume_mapping.ratio = ratio
                updates['volume_mapping'] = relationship.volume_mapping
        
        if status:
            updates['status'] = RelationshipStatus(status)
        
        if name:
            updates['name'] = name
        
        if updates:
            manager.update_relationship(relationship_id, **updates)
            click.echo(f"✅ 更新关系成功: {relationship_id}")
        else:
            click.echo("⚠️ 没有需要更新的内容")
            
    except Exception as e:
        click.echo(f"❌ 更新失败: {e}", err=True)


@cli.command()
@click.argument('relationship_id')
@click.confirmation_option(prompt='确认删除此关系？')
@click.pass_context
def delete(ctx, relationship_id):
    """删除跟单关系"""
    manager = ctx.obj['manager']
    
    try:
        if manager.delete_relationship(relationship_id):
            click.echo(f"✅ 删除关系成功: {relationship_id}")
        else:
            click.echo(f"❌ 关系不存在: {relationship_id}", err=True)
    except Exception as e:
        click.echo(f"❌ 删除失败: {e}", err=True)


@cli.command()
@click.option('--account', help='按账户过滤')
@click.option('--status', type=click.Choice(['active', 'paused', 'disabled', 'all']), 
              default='all', help='按状态过滤')
@click.option('--format', 'output_format', type=click.Choice(['table', 'json', 'yaml']), 
              default='table', help='输出格式')
@click.pass_context
def list(ctx, account, status, output_format):
    """列出跟单关系"""
    manager = ctx.obj['manager']
    
    try:
        relationships = list(manager.relationships.values())
        
        # 过滤
        if account:
            relationships = [r for r in relationships 
                           if r.source_account == account or r.target_account == account]
        
        if status != 'all':
            relationships = [r for r in relationships if r.status.value == status]
        
        if output_format == 'table':
            _print_relationships_table(relationships)
        elif output_format == 'json':
            data = [_relationship_to_dict(r) for r in relationships]
            click.echo(json.dumps(data, indent=2, ensure_ascii=False))
        elif output_format == 'yaml':
            data = [_relationship_to_dict(r) for r in relationships]
            click.echo(yaml.dump(data, default_flow_style=False, ensure_ascii=False))
            
    except Exception as e:
        click.echo(f"❌ 列表获取失败: {e}", err=True)


@cli.command()
@click.argument('relationship_id')
@click.option('--format', 'output_format', type=click.Choice(['json', 'yaml']), 
              default='yaml', help='输出格式')
@click.pass_context
def show(ctx, relationship_id, output_format):
    """显示关系详情"""
    manager = ctx.obj['manager']
    
    try:
        relationship = manager.get_relationship(relationship_id)
        if not relationship:
            click.echo(f"❌ 关系不存在: {relationship_id}", err=True)
            return
        
        data = _relationship_to_dict(relationship)
        
        if output_format == 'json':
            click.echo(json.dumps(data, indent=2, ensure_ascii=False))
        elif output_format == 'yaml':
            click.echo(yaml.dump(data, default_flow_style=False, ensure_ascii=False))
            
    except Exception as e:
        click.echo(f"❌ 获取详情失败: {e}", err=True)


@cli.command()
@click.argument('relationship_id')
@click.pass_context
def pause(ctx, relationship_id):
    """暂停跟单关系"""
    manager = ctx.obj['manager']
    
    try:
        if manager.pause_relationship(relationship_id):
            click.echo(f"✅ 暂停关系成功: {relationship_id}")
        else:
            click.echo(f"❌ 关系不存在: {relationship_id}", err=True)
    except Exception as e:
        click.echo(f"❌ 暂停失败: {e}", err=True)


@cli.command()
@click.argument('relationship_id')
@click.pass_context
def resume(ctx, relationship_id):
    """恢复跟单关系"""
    manager = ctx.obj['manager']
    
    try:
        if manager.resume_relationship(relationship_id):
            click.echo(f"✅ 恢复关系成功: {relationship_id}")
        else:
            click.echo(f"❌ 关系不存在: {relationship_id}", err=True)
    except Exception as e:
        click.echo(f"❌ 恢复失败: {e}", err=True)


@cli.command()
@click.pass_context
def stats(ctx):
    """显示统计信息"""
    manager = ctx.obj['manager']
    
    try:
        stats = manager.get_stats()
        active_relationships = manager.get_active_relationships()
        
        click.echo("📊 跟单关系统计:")
        click.echo(f"   总关系数量: {stats['total_relationships']}")
        click.echo(f"   激活关系数量: {len(active_relationships)}")
        click.echo(f"   最后更新: {stats['last_updated']}")
        
        # 按账户统计
        source_counts = {}
        target_counts = {}
        for rel in manager.relationships.values():
            source_counts[rel.source_account] = source_counts.get(rel.source_account, 0) + 1
            target_counts[rel.target_account] = target_counts.get(rel.target_account, 0) + 1
        
        if source_counts:
            click.echo("\n📈 信号源统计:")
            for account, count in sorted(source_counts.items()):
                click.echo(f"   {account}: {count} 个关系")
        
        if target_counts:
            click.echo("\n📊 跟随者统计:")
            for account, count in sorted(target_counts.items()):
                click.echo(f"   {account}: {count} 个关系")
                
    except Exception as e:
        click.echo(f"❌ 获取统计失败: {e}", err=True)


@cli.command()
@click.option('--format', 'output_format', type=click.Choice(['yaml', 'json']), 
              default='yaml', help='导出格式')
@click.option('--output', help='输出文件路径')
@click.pass_context
def export(ctx, output_format, output):
    """导出关系配置"""
    manager = ctx.obj['manager']
    
    try:
        export_data = manager.export_relationships(output_format)
        
        if output:
            with open(output, 'w', encoding='utf-8') as f:
                f.write(export_data)
            click.echo(f"✅ 导出成功: {output}")
        else:
            click.echo(export_data)
            
    except Exception as e:
        click.echo(f"❌ 导出失败: {e}", err=True)


@cli.command()
@click.argument('file_path')
@click.option('--format', 'input_format', type=click.Choice(['yaml', 'json']), 
              help='导入格式（自动检测）')
@click.option('--overwrite', is_flag=True, help='覆盖现有关系')
@click.pass_context
def import_data(ctx, file_path, input_format, overwrite):
    """导入关系配置"""
    manager = ctx.obj['manager']
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            data = f.read()
        
        # 自动检测格式
        if not input_format:
            if file_path.endswith('.yaml') or file_path.endswith('.yml'):
                input_format = 'yaml'
            elif file_path.endswith('.json'):
                input_format = 'json'
            else:
                raise ValueError("无法自动检测文件格式，请指定 --format")
        
        imported_count = manager.import_relationships(data, input_format, overwrite)
        click.echo(f"✅ 导入成功: {imported_count} 个关系")
        
    except Exception as e:
        click.echo(f"❌ 导入失败: {e}", err=True)


@cli.command()
@click.pass_context
def cleanup(ctx):
    """清理过期关系"""
    manager = ctx.obj['manager']
    
    try:
        expired_count = manager.cleanup_expired_relationships()
        click.echo(f"✅ 清理完成: {expired_count} 个过期关系")
    except Exception as e:
        click.echo(f"❌ 清理失败: {e}", err=True)


# ============ 批量操作命令 ============

@cli.group()
def batch():
    """批量操作"""
    pass


@batch.command()
@click.argument('config_file')
@click.pass_context
def create_from_config(ctx, config_file):
    """从配置文件批量创建关系"""
    manager = ctx.obj['manager']
    
    try:
        with open(config_file, 'r', encoding='utf-8') as f:
            if config_file.endswith('.yaml') or config_file.endswith('.yml'):
                config = yaml.safe_load(f)
            else:
                config = json.load(f)
        
        relationships_config = config.get('relationships', [])
        created = manager.bulk_create_relationships(relationships_config)
        
        click.echo(f"✅ 批量创建完成: {len(created)}/{len(relationships_config)} 个关系")
        
    except Exception as e:
        click.echo(f"❌ 批量创建失败: {e}", err=True)


@batch.command()
@click.argument('accounts', nargs=-1, required=True)
@click.option('--type', 'relationship_type',
              type=click.Choice(['forward', 'reverse']),
              default='forward', help='关系类型')
@click.option('--ratio', type=float, default=1.0, help='手数比例')
@click.pass_context
def create_star(ctx, accounts, relationship_type, ratio):
    """创建星型关系（一个信号源对多个跟随者）"""
    if len(accounts) < 2:
        click.echo("❌ 至少需要2个账户", err=True)
        return
    
    manager = ctx.obj['manager']
    source_account = accounts[0]
    target_accounts = accounts[1:]
    
    try:
        created_count = 0
        for target_account in target_accounts:
            relationship = manager.create_relationship(
                source_account=source_account,
                target_account=target_account,
                relationship_type=RelationshipType(relationship_type),
                name=f"Star: {source_account} -> {target_account}"
            )
            
            if ratio != 1.0:
                relationship.volume_mapping.ratio = ratio
                manager.update_relationship(relationship.relationship_id, 
                                          volume_mapping=relationship.volume_mapping)
            
            created_count += 1
        
        click.echo(f"✅ 创建星型关系成功: {source_account} -> {created_count} 个账户")
        
    except Exception as e:
        click.echo(f"❌ 批量创建失败: {e}", err=True)


@batch.command()
@click.argument('accounts', nargs=-1, required=True)
@click.option('--ratio', type=float, default=1.0, help='手数比例')
@click.pass_context
def create_mesh(ctx, accounts, ratio):
    """创建网状关系（账户之间两两互相跟单）"""
    if len(accounts) < 2:
        click.echo("❌ 至少需要2个账户", err=True)
        return
    
    manager = ctx.obj['manager']
    
    try:
        created_count = 0
        for i, source_account in enumerate(accounts):
            for j, target_account in enumerate(accounts):
                if i != j:  # 不能自己跟自己
                    relationship = manager.create_relationship(
                        source_account=source_account,
                        target_account=target_account,
                        relationship_type=RelationshipType.FORWARD,
                        name=f"Mesh: {source_account} -> {target_account}"
                    )
                    
                    if ratio != 1.0:
                        relationship.volume_mapping.ratio = ratio
                        manager.update_relationship(relationship.relationship_id, 
                                                  volume_mapping=relationship.volume_mapping)
                    
                    created_count += 1
        
        click.echo(f"✅ 创建网状关系成功: {len(accounts)} 个账户，{created_count} 个关系")
        
    except Exception as e:
        click.echo(f"❌ 批量创建失败: {e}", err=True)


# ============ 辅助函数 ============

def _print_relationships_table(relationships: List):
    """打印关系表格"""
    if not relationships:
        click.echo("没有找到关系")
        return
    
    # 表头
    headers = ["ID", "名称", "源账户", "目标账户", "类型", "状态", "比例"]
    col_widths = [8, 20, 12, 12, 10, 8, 6]
    
    # 打印表头
    header_line = " | ".join(h.ljust(w) for h, w in zip(headers, col_widths))
    click.echo(header_line)
    click.echo("-" * len(header_line))
    
    # 打印数据行
    for rel in relationships:
        row = [
            rel.relationship_id[:8],
            (rel.name or "")[:20],
            rel.source_account[:12],
            rel.target_account[:12],
            rel.relationship_type.value[:10],
            rel.status.value[:8],
            f"{rel.volume_mapping.ratio:.2f}"[:6]
        ]
        row_line = " | ".join(cell.ljust(w) for cell, w in zip(row, col_widths))
        click.echo(row_line)


def _relationship_to_dict(relationship) -> Dict:
    """转换关系对象为字典"""
    from dataclasses import asdict
    data = asdict(relationship)
    
    # 处理日期时间
    for key, value in data.items():
        if isinstance(value, datetime):
            data[key] = value.isoformat()
        elif hasattr(value, 'value'):  # 枚举类型
            data[key] = value.value
    
    return data


if __name__ == '__main__':
    cli()