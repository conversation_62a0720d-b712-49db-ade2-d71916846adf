# 资源限制配置
# 定义系统资源的使用限制，防止资源耗尽

# 内存限制
memory:
  # 系统级内存限制
  system:
    max_usage_percent: 80     # 最大内存使用百分比
    warning_threshold: 70     # 内存警告阈值
    critical_threshold: 90    # 内存危险阈值
    
  # 进程内存限制
  process:
    max_heap_size: "2G"       # 最大堆内存
    max_non_heap_size: "512M" # 最大非堆内存
    gc_threshold: "1.5G"      # GC触发阈值
    
  # 组件内存限制
  components:
    master_monitor: "512M"    # 主监控器内存限制
    slave_executor: "256M"    # 从执行器内存限制
    api_server: "1G"          # API服务器内存限制
    cache_service: "2G"       # 缓存服务内存限制
    
  # 缓存内存限制
  cache:
    max_cache_size: "1G"      # 最大缓存大小
    cache_entry_limit: 100000 # 缓存条目限制
    memory_mapped_size: "512M" # 内存映射大小

# CPU限制
cpu:
  # 系统CPU限制
  system:
    max_usage_percent: 85     # 最大CPU使用率
    warning_threshold: 70     # CPU警告阈值
    critical_threshold: 95    # CPU危险阈值
    
  # 进程CPU限制
  process:
    max_cpu_percent: 50       # 单进程最大CPU使用率
    nice_level: -10           # 进程优先级
    
  # 组件CPU限制
  components:
    master_monitor:
      max_cpu_percent: 30
      thread_count: 4
    slave_executor:
      max_cpu_percent: 20
      thread_count: 2
    api_server:
      max_cpu_percent: 40
      thread_count: 8
      
  # 线程池限制
  thread_pools:
    max_threads: 32           # 最大线程数
    core_threads: 8           # 核心线程数
    queue_size: 1000          # 队列大小

# 网络限制
network:
  # 连接数限制
  connections:
    max_total_connections: 1000      # 总连接数限制
    max_connections_per_host: 100    # 单主机连接数限制
    connection_timeout: 30.0         # 连接超时时间
    read_timeout: 10.0               # 读取超时时间
    
  # 带宽限制
  bandwidth:
    max_upload_mbps: 100      # 最大上传带宽(Mbps)
    max_download_mbps: 100    # 最大下载带宽(Mbps)
    burst_limit: 150          # 突发流量限制
    
  # 请求限制
  requests:
    max_requests_per_second: 1000    # 每秒最大请求数
    max_concurrent_requests: 200     # 最大并发请求数
    request_timeout: 30.0            # 请求超时时间

# 存储限制
storage:
  # 磁盘空间限制
  disk:
    max_usage_percent: 85     # 最大磁盘使用率
    warning_threshold: 75     # 磁盘警告阈值
    min_free_space: "10G"     # 最小可用空间
    
  # 日志文件限制
  logs:
    max_file_size: "100M"     # 单个日志文件最大大小
    max_total_size: "10G"     # 日志总大小限制
    retention_days: 30        # 日志保留天数
    
  # 数据文件限制
  data:
    max_database_size: "50G"  # 数据库最大大小
    max_cache_files: "5G"     # 缓存文件最大大小
    backup_retention: 7       # 备份保留天数

# 文件描述符限制
file_descriptors:
  soft_limit: 65536           # 软限制
  hard_limit: 131072          # 硬限制
  warning_threshold: 50000    # 警告阈值

# 数据库连接限制
database:
  # 连接池限制
  connection_pool:
    max_connections: 50       # 最大连接数
    min_connections: 5        # 最小连接数
    max_idle_time: 300        # 最大空闲时间(秒)
    
  # 查询限制
  queries:
    max_execution_time: 30.0  # 最大执行时间(秒)
    max_result_size: 10000    # 最大结果集大小
    max_memory_per_query: "100M" # 单查询最大内存

# 消息队列限制
message_queue:
  # NATS限制
  nats:
    max_payload: "8M"         # 最大消息大小
    max_pending: "256M"       # 最大待处理消息
    max_connections: 100      # 最大连接数
    
  # Redis限制
  redis:
    max_memory: "2G"          # 最大内存使用
    max_clients: 100          # 最大客户端连接数
    timeout: 5.0              # 操作超时时间

# 监控限制
monitoring:
  # 指标收集限制
  metrics:
    max_metrics_per_second: 10000    # 每秒最大指标数
    max_series: 100000               # 最大时间序列数
    retention_period: "30d"          # 指标保留期
    
  # 告警限制
  alerts:
    max_alerts_per_minute: 100       # 每分钟最大告警数
    alert_throttling: true           # 告警限流
    max_notification_rate: 10        # 最大通知频率

# 安全限制
security:
  # 认证限制
  authentication:
    max_login_attempts: 5     # 最大登录尝试次数
    lockout_duration: 900     # 锁定时间(秒)
    session_timeout: 3600     # 会话超时时间
    
  # API限制
  api:
    rate_limit: 1000          # API调用频率限制
    burst_limit: 200          # 突发请求限制
    ip_whitelist_size: 1000   # IP白名单大小

# 环境特定限制
environments:
  development:
    memory:
      system:
        max_usage_percent: 60
    cpu:
      system:
        max_usage_percent: 50
    network:
      requests:
        max_requests_per_second: 100
        
  testing:
    memory:
      system:
        max_usage_percent: 70
    cpu:
      system:
        max_usage_percent: 60
    storage:
      logs:
        retention_days: 7
        
  production:
    memory:
      system:
        max_usage_percent: 80
    cpu:
      system:
        max_usage_percent: 85
    storage:
      logs:
        retention_days: 30

# 动态调整配置
dynamic_adjustment:
  enabled: true
  
  # 自动调整策略
  strategies:
    memory_pressure:
      enabled: true
      threshold: 0.8
      action: "reduce_cache"
      
    cpu_pressure:
      enabled: true
      threshold: 0.85
      action: "throttle_requests"
      
    disk_pressure:
      enabled: true
      threshold: 0.85
      action: "cleanup_logs"
      
  # 调整参数
  adjustment:
    check_interval: 60        # 检查间隔(秒)
    adjustment_factor: 0.1    # 调整幅度
    min_adjustment: 0.05      # 最小调整幅度
    max_adjustment: 0.5       # 最大调整幅度

# 告警配置
alerts:
  # 资源告警
  resource_alerts:
    memory_high:
      threshold: 80
      severity: "warning"
      
    memory_critical:
      threshold: 90
      severity: "critical"
      
    cpu_high:
      threshold: 85
      severity: "warning"
      
    disk_full:
      threshold: 90
      severity: "critical"
      
  # 通知配置
  notifications:
    email: true
    slack: false
    webhook: true
    
# 恢复策略
recovery:
  # 自动恢复
  auto_recovery:
    enabled: true
    
    # 恢复策略
    strategies:
      restart_service:
        enabled: true
        memory_threshold: 95
        cpu_threshold: 98
        
      scale_down:
        enabled: true
        load_threshold: 0.9
        
      cleanup_resources:
        enabled: true
        disk_threshold: 95
        
  # 恢复参数
  recovery_params:
    cooldown_period: 300      # 冷却期(秒)
    max_recovery_attempts: 3  # 最大恢复尝试次数
    recovery_timeout: 600     # 恢复超时时间(秒)