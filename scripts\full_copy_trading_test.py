#!/usr/bin/env python3
"""
完整的端到端跟单测试 - 包含实际交易执行
"""

import asyncio
import sys
import os
import time
from pathlib import Path
from dotenv import load_dotenv

# 添加项目路径
sys.path.insert(0, str(Path(__file__).parent.parent))

from src.utils.logger import get_logger, setup_logging
from src.core.mt5_client import MT5Client
from src.core.signal_types import UnifiedTradeSignal, OrderType, SignalAction
from src.messaging.jetstream_client import JetStreamClient
from src.core.copy_strategy_processor import CopyStrategyProcessor
from src.utils.config_manager import ConfigManager

logger = get_logger(__name__)


class FullCopyTradingTest:
    """完整的跟单测试系统"""
    
    def __init__(self):
        # 加载环境变量
        load_dotenv()
        
        self.config_manager = ConfigManager('config/optimized_system.yaml')
        self.mt5_clients = {}
        self.jetstream_client = None
        self.strategy_processor = CopyStrategyProcessor(self.config_manager)
        self.monitoring = False
        
    async def setup(self):
        """初始化"""
        logger.info("🔧 初始化完整测试环境...")
        
        # 初始化 JetStream
        self.jetstream_client = JetStreamClient(['nats://localhost:4222'])
        await self.jetstream_client.connect()
        
        logger.info("✅ 测试环境初始化完成")
    
    async def connect_mt5_accounts(self):
        """连接MT5账户"""
        logger.info("🧪 连接MT5账户...")
        
        accounts = [
            {
                'id': 'ACC001',
                'login': ********,
                'password': os.getenv('MT5_ACC001_PASSWORD'),
                'server': 'TradeMaxGlobal-Demo',
                'terminal_path': 'D:/MetaTrader5/v1-mt5/terminal64.exe'
            },
            {
                'id': 'ACC002', 
                'login': ********,
                'password': os.getenv('MT5_ACC002_PASSWORD'),
                'server': 'TradeMaxGlobal-Demo',
                'terminal_path': 'D:/MetaTrader5/v2-mt5/terminal64.exe'
            }
        ]
        
        for account in accounts:
            try:
                logger.info(f"连接账户: {account['id']}")
                
                client = MT5Client(
                    account_id=account['id'],
                    login=account['login'],
                    password=account['password'],
                    server=account['server'],
                    terminal_path=account['terminal_path']
                )
                
                success = await client.connect()
                
                if success:
                    logger.info(f"✅ {account['id']} 连接成功")
                    if client.account_info:
                        logger.info(f"  余额: {client.account_info.balance}")
                        logger.info(f"  权益: {client.account_info.equity}")

                    positions = await client.get_positions()
                    logger.info(f"  当前持仓: {len(positions) if positions else 0}")

                    self.mt5_clients[account['id']] = client
                else:
                    logger.error(f"❌ {account['id']} 连接失败")
                    
            except Exception as e:
                logger.error(f"❌ {account['id']} 连接异常: {e}")
        
        return len(self.mt5_clients) >= 2
    
    async def test_copy_strategy(self):
        """测试跟单策略"""
        logger.info("🧪 测试跟单策略...")
        
        try:
            # 创建测试信号
            test_signal = UnifiedTradeSignal.create_open_signal(
                master_account="ACC001",
                symbol="EURUSD",
                order_type=OrderType.BUY,
                volume=0.01,
                price=1.1000,
                position_id=99999,
                sl=1.0950,
                tp=1.1050,
                comment="Full copy trading test"
            )
            
            logger.info(f"原始信号: {test_signal.symbol} {test_signal.order_type.value} {test_signal.volume}")
            
            # 应用跟单策略
            copy_signal = self.strategy_processor.apply_copy_strategy(
                signal=test_signal,
                master_account="ACC001",
                slave_account="ACC002"
            )
            
            if copy_signal:
                logger.info(f"✅ 跟单信号: {copy_signal.symbol} {copy_signal.order_type.value} {copy_signal.volume}")
                
                # 验证策略应用
                expected_volume = 0.01 * 0.5  # 0.005
                if abs(copy_signal.volume - expected_volume) < 0.001:
                    logger.info("✅ 手数调整正确")
                else:
                    logger.warning(f"⚠️ 手数调整异常: 期望={expected_volume}, 实际={copy_signal.volume}")
                
                return copy_signal
            else:
                logger.error("❌ 跟单信号生成失败")
                return None
                
        except Exception as e:
            logger.error(f"❌ 跟单策略测试异常: {e}")
            return None
    
    async def simulate_master_trading(self):
        """模拟主账户交易"""
        logger.info("🎯 模拟主账户交易...")
        
        if 'ACC001' not in self.mt5_clients:
            logger.error("❌ ACC001未连接")
            return False
        
        master_client = self.mt5_clients['ACC001']
        
        try:
            # 获取当前市场价格
            symbol_info = await master_client.get_symbol_info('EURUSD')
            if not symbol_info:
                logger.error("❌ 无法获取EURUSD市场信息")
                return False
            
            current_price = symbol_info.get('ask', 1.1000)
            logger.info(f"当前EURUSD价格: {current_price}")
            
            # 创建真实交易信号
            trade_signal = UnifiedTradeSignal.create_open_signal(
                master_account="ACC001",
                symbol="EURUSD",
                order_type=OrderType.BUY,
                volume=0.01,
                price=current_price,
                position_id=int(time.time()),
                sl=current_price - 0.0050,  # 50点止损
                tp=current_price + 0.0050,  # 50点止盈
                comment="Master account trade"
            )
            
            logger.info(f"创建主账户交易信号: {trade_signal.symbol} {trade_signal.order_type.value} "
                      f"{trade_signal.volume} @ {trade_signal.price}")
            
            # 发布主账户信号
            success = await self.jetstream_client.publish(
                subject="MT5.TRADES.ACC001",
                data=trade_signal.to_dict()
            )
            
            if success:
                logger.info("✅ 主账户信号发布成功")
                
                # 应用跟单策略
                copy_signal = self.strategy_processor.apply_copy_strategy(
                    signal=trade_signal,
                    master_account="ACC001",
                    slave_account="ACC002"
                )
                
                if copy_signal:
                    logger.info(f"✅ 生成跟单信号: {copy_signal.symbol} {copy_signal.order_type.value} "
                              f"{copy_signal.volume} @ {copy_signal.price}")
                    
                    # 发布跟单信号
                    copy_success = await self.jetstream_client.publish(
                        subject="MT5.TRADES.ACC002",
                        data=copy_signal.to_dict()
                    )
                    
                    if copy_success:
                        logger.info("✅ 跟单信号发布成功")
                        return True
                    else:
                        logger.error("❌ 跟单信号发布失败")
                else:
                    logger.error("❌ 跟单信号生成失败")
            else:
                logger.error("❌ 主账户信号发布失败")
            
            return False
            
        except Exception as e:
            logger.error(f"❌ 模拟交易异常: {e}")
            return False
    
    async def monitor_accounts_detailed(self, duration=60):
        """详细监控账户变化"""
        logger.info(f"🔍 开始详细监控 ({duration}秒)...")
        logger.info("💡 监控内容:")
        logger.info("  - 账户余额和权益变化")
        logger.info("  - 持仓数量和详情变化")
        logger.info("  - 交易信号传递情况")
        
        # 记录初始状态
        initial_states = {}
        for account_id, client in self.mt5_clients.items():
            try:
                positions = await client.get_positions()

                initial_states[account_id] = {
                    'balance': client.account_info.balance if client.account_info else 0,
                    'equity': client.account_info.equity if client.account_info else 0,
                    'positions': len(positions) if positions else 0,
                    'position_details': positions if positions else []
                }
                
                logger.info(f"{account_id} 初始状态:")
                logger.info(f"  余额: {initial_states[account_id]['balance']:.2f}")
                logger.info(f"  权益: {initial_states[account_id]['equity']:.2f}")
                logger.info(f"  持仓: {initial_states[account_id]['positions']}")
                
            except Exception as e:
                logger.error(f"获取 {account_id} 初始状态失败: {e}")
        
        # 监控循环
        self.monitoring = True
        start_time = time.time()
        check_count = 0
        
        while self.monitoring and (time.time() - start_time < duration):
            await asyncio.sleep(5)  # 每5秒检查一次
            check_count += 1
            
            logger.info(f"📊 详细检查 #{check_count} ({int(time.time() - start_time)}s)")
            
            for account_id, client in self.mt5_clients.items():
                try:
                    # 刷新账户信息
                    await client.verify_connection()

                    positions = await client.get_positions()

                    current_balance = client.account_info.balance if client.account_info else 0
                    current_equity = client.account_info.equity if client.account_info else 0
                    current_positions = len(positions) if positions else 0
                    
                    # 检查变化
                    if account_id in initial_states:
                        initial = initial_states[account_id]
                        
                        balance_change = current_balance - initial['balance']
                        equity_change = current_equity - initial['equity']
                        position_change = current_positions - initial['positions']
                        
                        # 只在有变化时记录
                        if (abs(balance_change) > 0.01 or 
                            abs(equity_change) > 0.01 or 
                            position_change != 0):
                            
                            logger.info(f"🔄 {account_id} 检测到变化:")
                            logger.info(f"  余额: {initial['balance']:.2f} -> {current_balance:.2f} ({balance_change:+.2f})")
                            logger.info(f"  权益: {initial['equity']:.2f} -> {current_equity:.2f} ({equity_change:+.2f})")
                            logger.info(f"  持仓: {initial['positions']} -> {current_positions} ({position_change:+d})")
                            
                            # 显示持仓详情
                            if positions:
                                logger.info(f"  持仓详情:")
                                for i, pos in enumerate(positions):
                                    pos_type = "BUY" if pos.type == 0 else "SELL"
                                    logger.info(f"    #{i+1}: {pos.symbol} "
                                              f"{pos_type} "
                                              f"{pos.volume} "
                                              f"@ {pos.price_open} "
                                              f"P&L: {pos.profit}")
                            
                            # 更新初始状态
                            initial_states[account_id] = {
                                'balance': current_balance,
                                'equity': current_equity,
                                'positions': current_positions,
                                'position_details': positions
                            }
                    
                except Exception as e:
                    logger.error(f"监控 {account_id} 时出错: {e}")
        
        logger.info("✅ 详细监控完成")
    
    def stop_monitoring(self):
        """停止监控"""
        self.monitoring = False
        logger.info("🛑 监控已停止")
    
    async def cleanup(self):
        """清理"""
        logger.info("🧹 清理资源...")
        
        self.monitoring = False
        
        for account_id, client in self.mt5_clients.items():
            try:
                await client.disconnect()
                logger.info(f"✅ {account_id} 连接已断开")
            except Exception as e:
                logger.warning(f"⚠️ {account_id} 断开时出错: {e}")
        
        if self.jetstream_client:
            await self.jetstream_client.disconnect()
            logger.info("✅ JetStream 连接已断开")


async def main():
    """主函数"""
    # 设置日志
    setup_logging({
        'level': 'INFO',
        'format': 'standard'
    })
    
    logger.info("=" * 60)
    logger.info("🚀 完整端到端跟单测试")
    logger.info("=" * 60)
    
    tester = FullCopyTradingTest()
    
    try:
        # 初始化
        await tester.setup()
        
        # 连接MT5账户
        logger.info("第一步: 连接MT5账户")
        mt5_success = await tester.connect_mt5_accounts()
        
        if not mt5_success:
            logger.error("❌ MT5连接失败，无法继续")
            return 1
        
        # 测试跟单策略
        logger.info("第二步: 测试跟单策略")
        strategy_result = await tester.test_copy_strategy()
        
        if not strategy_result:
            logger.error("❌ 跟单策略测试失败")
            return 1
        
        # 模拟主账户交易
        logger.info("第三步: 模拟主账户交易")
        trade_success = await tester.simulate_master_trading()
        
        if trade_success:
            logger.info("✅ 交易信号传递成功")
        else:
            logger.warning("⚠️ 交易信号传递失败")
        
        # 开始详细监控
        logger.info("第四步: 开始详细监控")
        logger.info("🎯 现在请您在MT5终端进行以下操作:")
        logger.info("  1. 在ACC001开仓EURUSD买单（0.01手）")
        logger.info("  2. 观察ACC002是否自动跟单（应该是0.005手）")
        logger.info("  3. 修改ACC001的止损止盈")
        logger.info("  4. 平仓ACC001的订单")
        logger.info("  5. 按 Ctrl+C 停止监控")
        
        # 开始监控
        await tester.monitor_accounts_detailed(300)  # 5分钟监控
        
        logger.info("🎉 完整测试完成！")
        return 0
        
    except KeyboardInterrupt:
        logger.info("🛑 收到中断信号")
        tester.stop_monitoring()
        return 0
    except Exception as e:
        logger.error(f"❌ 测试异常: {e}")
        return 1
    finally:
        await tester.cleanup()


if __name__ == "__main__":
    try:
        exit_code = asyncio.run(main())
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n🛑 测试已停止")
        sys.exit(0)
