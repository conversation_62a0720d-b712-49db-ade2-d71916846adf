# MT5 Trading System - Web界面

<div align="center">

[English](README_EN.md) | 简体中文

</div>

这个目录包含MT5动态配对交易系统的Web界面组件，包括后端API和前端应用。

## 目录结构

```
web/
├── backend/                      # 后端API扩展
│   ├── api/
│   │   ├── __init__.py
│   │   ├── app.py              # FastAPI应用
│   │   ├── routers/            # API路由
│   │   │   ├── __init__.py
│   │   │   ├── accounts.py    # 账户管理API
│   │   │   ├── pairings.py    # 配对管理API
│   │   │   ├── monitoring.py  # 监控API
│   │   │   ├── dashboard.py   # 仪表板API
│   │   │   └── websocket.py   # WebSocket端点
│   │   ├── models/             # 数据模型
│   │   │   ├── __init__.py
│   │   │   ├── requests.py    # 请求模型
│   │   │   └── responses.py   # 响应模型
│   │   └── services/           # 业务服务
│   │       ├── __init__.py
│   │       ├── pairing_service.py
│   │       └── dashboard_service.py
│   └── requirements.txt
│
├── frontend/                     # React前端应用
│   ├── public/
│   │   └── index.html
│   ├── src/
│   │   ├── components/          # React组件
│   │   │   ├── Dashboard/      # 仪表板
│   │   │   │   └── index.js
│   │   │   ├── Accounts/       # 账户管理
│   │   │   │   └── index.js
│   │   │   ├── Pairings/       # 配对管理
│   │   │   │   └── index.js
│   │   │   ├── Monitoring/     # 实时监控
│   │   │   │   └── index.js
│   │   │   └── Common/         # 公共组件
│   │   │       └── index.js
│   │   ├── services/           # API服务
│   │   │   └── index.js
│   │   ├── hooks/              # 自定义Hooks
│   │   │   └── index.js
│   │   ├── utils/              # 工具函数
│   │   │   └── index.js
│   │   ├── store/              # Redux状态管理
│   │   │   └── index.js
│   │   ├── App.js
│   │   └── index.js
│   ├── package.json
│   └── README.md
│
└── static/                       # 静态资源
    ├── css/
    │   └── style.css
    ├── js/
    │   └── app.js
    └── images/
        └── .gitkeep
```

## 功能说明

### Backend API
- **专门的Web API服务**：为前端界面提供定制化的API接口
- **路由管理**：按功能模块组织的API路由
- **数据模型**：请求和响应的数据验证模型
- **业务服务**：处理复杂业务逻辑的服务层

### Frontend Application
- **组件化架构**：按功能区域组织的React组件
- **状态管理**：使用Redux进行全局状态管理
- **API服务**：与后端API的通信接口
- **自定义Hooks**：复用的React逻辑
- **工具函数**：通用的工具和辅助函数

### Static Resources
- **CSS样式**：全局样式和主题
- **JavaScript**：原生JS功能
- **图片资源**：图标、logo等静态图片

## 开发指南

### 启动后端API
```bash
cd web/backend
pip install -r requirements.txt
uvicorn api.app:app --reload --port 8001
```

### 启动前端应用
```bash
cd web/frontend
npm install
npm start
```

### 开发注意事项
1. 后端API运行在端口8001
2. 前端开发服务器运行在端口3000
3. 所有API路由都以 `/api/v1/` 为前缀
4. WebSocket连接用于实时数据推送

## 与主系统的关系

这个Web界面是主系统的一个扩展模块：
- 通过API调用主系统的功能
- 提供用户友好的操作界面
- 实时展示系统状态和数据
- 可以独立部署和维护 