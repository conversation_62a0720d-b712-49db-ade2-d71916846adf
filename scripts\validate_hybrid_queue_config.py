#!/usr/bin/env python3
"""
混合队列配置验证脚本
验证混合消息队列管理器配置的正确性和后端连接状态
"""

import asyncio
import sys
import os
import yaml
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.messaging.hybrid_queue_manager import HybridQueueManager, HybridQueueConfig, QueueBackendType
from src.utils.logger import get_logger

logger = get_logger(__name__)


async def validate_config_file(config_path: str) -> bool:
    """验证配置文件"""
    try:
        with open(config_path, 'r', encoding='utf-8') as f:
            config_data = yaml.safe_load(f)
        
        logger.info(f"配置文件加载成功: {config_path}")
        
        # 验证必需的配置项
        queue_config = config_data.get('queue_manager', {})
        
        required_fields = ['primary_backend', 'backup_backends']
        missing_fields = [field for field in required_fields if field not in queue_config]
        
        if missing_fields:
            logger.error(f"配置文件缺少必需字段: {missing_fields}")
            return False
        
        # 验证后端类型
        try:
            primary_backend = QueueBackendType(queue_config['primary_backend'])
            backup_backends = [QueueBackendType(backend) for backend in queue_config['backup_backends']]
            logger.info(f"后端配置验证成功 - 主: {primary_backend}, 备: {backup_backends}")
        except ValueError as e:
            logger.error(f"无效的后端类型: {e}")
            return False
        
        # 验证路由规则
        routing_config = config_data.get('messaging', {}).get('routing', {})
        rules = routing_config.get('rules', [])
        
        logger.info(f"找到 {len(rules)} 条路由规则")
        for i, rule in enumerate(rules):
            required_rule_fields = ['source', 'target', 'priority', 'route_type']
            missing_rule_fields = [field for field in required_rule_fields if field not in rule]
            
            if missing_rule_fields:
                logger.warning(f"路由规则 {i+1} 缺少字段: {missing_rule_fields}")
        
        logger.info("配置文件验证通过")
        return True
        
    except FileNotFoundError:
        logger.error(f"配置文件未找到: {config_path}")
        return False
    except yaml.YAMLError as e:
        logger.error(f"配置文件YAML格式错误: {e}")
        return False
    except Exception as e:
        logger.error(f"配置文件验证失败: {e}")
        return False


async def test_backend_connections(config_path: str) -> bool:
    """测试后端连接"""
    try:
        # 加载配置
        with open(config_path, 'r', encoding='utf-8') as f:
            config_data = yaml.safe_load(f)
        
        queue_config_data = config_data.get('queue_manager', {})
        
        # 创建混合队列配置
        hybrid_config = HybridQueueConfig(
            primary_backend=QueueBackendType(queue_config_data.get('primary_backend', 'local_memory')),
            backup_backends=[
                QueueBackendType(backend) 
                for backend in queue_config_data.get('backup_backends', [])
            ],
            local_fallback=queue_config_data.get('local_fallback', True),
            backend_configs={
                QueueBackendType.NATS: {
                    'nats': {
                        'host_id': 'test-host',
                        'servers': queue_config_data.get('nats_servers', ['nats://localhost:4222']),
                        'user': queue_config_data.get('nats_user'),
                        'password': queue_config_data.get('nats_password')
                    }
                },
                QueueBackendType.REDIS_STREAMS: {
                    'redis': {
                        'host': queue_config_data.get('redis_host', 'localhost'),
                        'port': queue_config_data.get('redis_port', 6379),
                        'db': queue_config_data.get('redis_db', 0),
                        'password': queue_config_data.get('redis_password'),
                        'consumer_group': 'test-workers'
                    }
                }
            }
        )
        
        # 创建混合队列管理器
        queue_manager = HybridQueueManager(hybrid_config)
        
        logger.info("开始测试后端连接...")
        
        # 初始化队列管理器
        if not await queue_manager.initialize():
            logger.error("队列管理器初始化失败")
            return False
        
        # 检查后端健康状态
        status = queue_manager.get_status()
        
        logger.info("后端连接状态:")
        for backend, health in status['backend_health'].items():
            status_msg = "健康" if health['healthy'] else "不健康"
            logger.info(f"  {backend}: {status_msg} (延迟: {health['latency_ms']:.1f}ms, 错误率: {health['error_rate']:.3f})")
        
        logger.info(f"当前活跃后端: 主={status['primary_backend']}, 备={status['backup_backends']}")
        logger.info(f"路由模式: {status['routing_mode']}")
        
        # 测试消息发布
        from src.messaging.message_types import MessageEnvelope, MessagePriority
        import time
        
        test_message = MessageEnvelope(
            id=f"test_{int(time.time() * 1000)}",
            subject="test.connection",
            payload={"test": True, "timestamp": time.time()},
            timestamp=time.time()
        )
        
        logger.info("测试消息发布...")
        success = await queue_manager.publish("test.connection", test_message, MessagePriority.REALTIME_QUERY)
        
        if success:
            logger.info("消息发布测试成功")
        else:
            logger.warning("消息发布测试失败")
        
        # 关闭队列管理器
        await queue_manager.shutdown()
        
        logger.info("后端连接测试完成")
        return True
        
    except Exception as e:
        logger.error(f"后端连接测试失败: {e}")
        return False


async def main():
    """主函数"""
    logger.info("开始混合队列配置验证")
    
    # 配置文件路径
    config_path = project_root / "config" / "messaging" / "hybrid_queue_config.yaml"
    
    if not config_path.exists():
        logger.error(f"配置文件不存在: {config_path}")
        sys.exit(1)
    
    # 1. 验证配置文件
    logger.info("=== 步骤1: 验证配置文件 ===")
    if not await validate_config_file(str(config_path)):
        logger.error("配置文件验证失败")
        sys.exit(1)
    
    # 2. 测试后端连接（可选，需要服务运行）
    if len(sys.argv) > 1 and sys.argv[1] == "--test-connections":
        logger.info("=== 步骤2: 测试后端连接 ===")
        if not await test_backend_connections(str(config_path)):
            logger.error("后端连接测试失败")
            sys.exit(1)
    else:
        logger.info("跳过后端连接测试（使用 --test-connections 参数启用）")
    
    logger.info("✅ 混合队列配置验证完成")


if __name__ == "__main__":
    asyncio.run(main())