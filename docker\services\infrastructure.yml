# MT5分布式交易系统 - 基础设施服务
# 包含Redis、NATS、监控等基础组件

version: '3.8'

services:
  # ============================================================================
  # Redis - 数据存储和缓存
  # ============================================================================
  redis:
    image: redis:7-alpine
    container_name: mt5-redis
    ports:
      - "${REDIS_EXTERNAL_PORT:-6379}:6379"
    volumes:
      - redis_data:/data
      - ../configs/redis/redis.conf:/etc/redis/redis.conf:ro
    command: redis-server /etc/redis/redis.conf
    environment:
      - REDIS_REPLICATION_MODE=master
      - REDIS_OPTIMIZATION_ENABLED=true
    sysctls:
      - net.core.somaxconn=65535
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 3
      start_period: 15s
    restart: unless-stopped
    networks:
      - mt5-network
    labels:
      - "mt5.service=redis"
      - "mt5.component=infrastructure"

  # ============================================================================
  # NATS - 消息队列服务
  # ============================================================================
  nats:
    image: nats:2.10-alpine
    container_name: mt5-nats
    ports:
      - "${NATS_CLIENT_PORT:-4222}:4222"   # 客户端端口
      - "${NATS_HTTP_PORT:-8222}:8222"     # HTTP监控端口
      - "${NATS_CLUSTER_PORT:-6222}:6222"  # 集群端口
    volumes:
      - nats_data:/data
      - ../configs/nats/nats.conf:/etc/nats/nats.conf:ro
    command:
      - "--config=/etc/nats/nats.conf"
    healthcheck:
      test: ["CMD", "sh", "-c", "echo 'PING' | nc localhost 4222 | grep -q PONG || exit 1"]
      interval: 15s
      timeout: 10s
      retries: 5
      start_period: 30s
    restart: unless-stopped
    networks:
      - mt5-network
    labels:
      - "mt5.service=nats"
      - "mt5.component=infrastructure"

  # ============================================================================
  # Prometheus - 指标收集
  # ============================================================================
  prometheus:
    image: prom/prometheus:latest
    container_name: mt5-prometheus
    ports:
      - "${PROMETHEUS_PORT:-9090}:9090"
    volumes:
      - ../configs/prometheus/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - ../configs/prometheus/rules:/etc/prometheus/rules:ro
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=${PROMETHEUS_RETENTION:-7d}'
      - '--web.enable-lifecycle'
      - '--web.enable-admin-api'
      - '--storage.tsdb.wal-compression'
    healthcheck:
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost:9090/-/healthy"]
      interval: 15s
      timeout: 10s
      retries: 3
      start_period: 20s
    restart: unless-stopped
    networks:
      - mt5-network
    labels:
      - "mt5.service=prometheus"
      - "mt5.component=monitoring"

  # ============================================================================
  # Pushgateway - 指标推送网关
  # ============================================================================
  pushgateway:
    image: prom/pushgateway:latest
    container_name: mt5-pushgateway
    ports:
      - "${PUSHGATEWAY_PORT:-9091}:9091"
    environment:
      - PUSHGATEWAY_OPTIMIZATION=enabled
    healthcheck:
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost:9091/-/healthy"]
      interval: 15s
      timeout: 10s
      retries: 3
      start_period: 15s
    restart: unless-stopped
    networks:
      - mt5-network
    labels:
      - "mt5.service=pushgateway"
      - "mt5.component=monitoring"

  # ============================================================================
  # Grafana - 数据可视化
  # ============================================================================
  grafana:
    image: grafana/grafana:latest
    container_name: mt5-grafana
    ports:
      - "${GRAFANA_PORT:-3000}:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_ADMIN_PASSWORD:-admin}
      - GF_USERS_ALLOW_SIGN_UP=false
      - GF_AUTH_ANONYMOUS_ENABLED=${GRAFANA_ANONYMOUS:-false}
      - GF_AUTH_ANONYMOUS_ORG_ROLE=Viewer
      - GF_FEATURE_TOGGLES_ENABLE=publicDashboards
      - GF_ANALYTICS_REPORTING_ENABLED=false
    volumes:
      - grafana_data:/var/lib/grafana
      - ../configs/grafana/dashboards:/var/lib/grafana/dashboards:ro
      - ../configs/grafana/datasources:/etc/grafana/provisioning/datasources:ro
    depends_on:
      - prometheus
      - redis
    healthcheck:
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost:3000/api/health"]
      interval: 15s
      timeout: 10s
      retries: 3
      start_period: 30s
    restart: unless-stopped
    networks:
      - mt5-network
    labels:
      - "mt5.service=grafana"
      - "mt5.component=monitoring"

  # ============================================================================
  # Redis Commander - Redis管理界面（可选）
  # ============================================================================
  redis-commander:
    image: rediscommander/redis-commander:latest
    container_name: mt5-redis-commander
    ports:
      - "${REDIS_COMMANDER_PORT:-8081}:8081"
    environment:
      - REDIS_HOSTS=mt5-redis:redis:6379
      - REDIS_COMMANDER_OPTIMIZATION=hash-support
    depends_on:
      - redis
    restart: unless-stopped
    networks:
      - mt5-network
    labels:
      - "mt5.service=redis-commander"
      - "mt5.component=tools"
    profiles:
      - "tools"  # 可选组件，使用 --profile tools 启用

# ============================================================================
# 网络配置
# ============================================================================
networks:
  mt5-network:
    driver: bridge
    name: mt5-network
    labels:
      - "mt5.network=infrastructure"

# ============================================================================
# 数据卷配置
# ============================================================================
volumes:
  redis_data:
    driver: local
    labels:
      - "mt5.data=redis"
      
  nats_data:
    driver: local
    labels:
      - "mt5.data=nats"
      
  prometheus_data:
    driver: local
    labels:
      - "mt5.data=prometheus"
      
  grafana_data:
    driver: local
    labels:
      - "mt5.data=grafana"