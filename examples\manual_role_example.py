#!/usr/bin/env python3
"""
手动角色管理示例
演示如何独立设置每个终端的角色，不受配置组限制
"""
import asyncio
import sys
import time
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent.parent))

from src.multi_terminal.terminal_manager import MultiTerminalManager
from src.multi_terminal.independent_role_manager import IndependentRoleManager, TerminalRole
from src.config.settings import ConfigManager
from src.config.redis_client import RedisClient
from src.messaging.nats_client import NATSClient, NATSConfig
from src.utils.logger import get_logger, setup_logging


logger = get_logger(__name__)


class ManualRoleDemo:
    """手动角色管理演示"""
    
    def __init__(self, config_path: str):
        self.config_path = config_path
        self.config_manager = ConfigManager(config_path)
        self.terminal_manager: MultiTerminalManager = None
        self.role_manager: IndependentRoleManager = None
        self.redis_client: RedisClient = None
        self.nats_client: NATSClient = None
    
    async def initialize(self):
        """初始化系统"""
        logger.info("初始化手动角色管理系统...")
        
        try:
            # 加载配置
            await self.config_manager.load_config()
            
            # 初始化Redis客户端
            redis_settings = self.config_manager.get_redis_settings()
            self.redis_client = RedisClient(
                url=redis_settings.url,
                db=redis_settings.db,
                password=redis_settings.password
            )
            
            if not await self.redis_client.connect():
                raise Exception("Redis连接失败")
            
            # 初始化NATS客户端
            nats_settings = self.config_manager.get_nats_settings()
            nats_config = NATSConfig(
                servers=nats_settings.servers,
                name="manual-role-demo"
            )
            
            self.nats_client = NATSClient(nats_config)
            if not await self.nats_client.connect():
                raise Exception("NATS连接失败")
            
            # 初始化终端管理器
            self.terminal_manager = MultiTerminalManager("demo_host")
            await self.terminal_manager.start()
            
            # 初始化独立角色管理器
            self.role_manager = IndependentRoleManager(
                self.terminal_manager, self.nats_client, self.redis_client
            )
            await self.role_manager.start()
            
            logger.info("手动角色管理系统初始化完成")
            
        except Exception as e:
            logger.error(f"初始化失败: {e}")
            raise
    
    async def cleanup(self):
        """清理资源"""
        if self.role_manager:
            await self.role_manager.stop()
        if self.terminal_manager:
            await self.terminal_manager.stop()
        if self.nats_client:
            await self.nats_client.disconnect()
        if self.redis_client:
            await self.redis_client.disconnect()
    
    async def register_demo_terminals(self):
        """注册演示终端"""
        logger.info("注册演示终端...")
        
        # 获取账户配置
        master_accounts = self.config_manager.get_master_accounts()
        slave_accounts = self.config_manager.get_slave_accounts()
        
        # 注册6个终端（3个主账户配置 + 3个从账户配置）
        all_accounts = master_accounts[:3] + slave_accounts[:3]
        
        for account in all_accounts:
            try:
                terminal_id = await self.terminal_manager.register_terminal(account)
                logger.info(f"终端已注册: {terminal_id}")
            except Exception as e:
                logger.error(f"注册终端失败 {account.id}: {e}")
    
    async def show_current_status(self):
        """显示当前状态"""
        print("\n" + "="*80)
        print("📊 当前终端状态")
        print("="*80)
        
        terminals = await self.terminal_manager.get_all_terminals_status()
        summary = await self.role_manager.get_system_summary()
        
        print(f"终端总数: {summary['total_terminals']}")
        print(f"👑 主账户: {summary['master_terminals']}")
        print(f"👥 从账户: {summary['slave_terminals']}")
        print(f"⚪ 空闲: {summary['idle_terminals']}")
        print()
        
        print("终端详情:")
        for i, terminal in enumerate(terminals, 1):
            role_icon = {
                'master': '👑',
                'slave': '👥',
                'idle': '⚪'
            }.get(terminal['role'], '❓')
            
            status_icon = "🟢" if terminal['status'] == 'running' else "🔴"
            
            print(f"  {i}. {role_icon} {terminal['terminal_id']} - {terminal['role']} ({status_icon} {terminal['status']})")
        
        print("="*80 + "\n")
    
    async def demonstrate_manual_role_setting(self):
        """演示手动角色设置"""
        logger.info("开始演示手动角色设置...")
        
        terminals = await self.terminal_manager.get_all_terminals_status()
        if len(terminals) < 4:
            logger.error("需要至少4个终端来演示")
            return
        
        terminal_ids = [t['terminal_id'] for t in terminals]
        
        # 演示场景1: 今天的配置
        print("🌅 场景1: 今天的交易配置")
        print("设置: 终端1为主账户，终端2、3为从账户，其余空闲")
        
        await self.role_manager.set_terminal_role(terminal_ids[0], TerminalRole.MASTER, "demo_user")
        await self.role_manager.set_terminal_role(terminal_ids[1], TerminalRole.SLAVE, "demo_user")
        await self.role_manager.set_terminal_role(terminal_ids[2], TerminalRole.SLAVE, "demo_user")
        
        # 其余设为空闲
        for i in range(3, len(terminal_ids)):
            await self.role_manager.set_terminal_role(terminal_ids[i], TerminalRole.IDLE, "demo_user")
        
        await self.show_current_status()
        await asyncio.sleep(10)
        
        # 演示场景2: 明天的配置
        print("🌄 场景2: 明天的交易配置")
        print("设置: 终端2、4为主账户，终端1、3为从账户，其余空闲")
        
        await self.role_manager.set_terminal_role(terminal_ids[1], TerminalRole.MASTER, "demo_user")
        if len(terminal_ids) > 3:
            await self.role_manager.set_terminal_role(terminal_ids[3], TerminalRole.MASTER, "demo_user")
        
        await self.role_manager.set_terminal_role(terminal_ids[0], TerminalRole.SLAVE, "demo_user")
        await self.role_manager.set_terminal_role(terminal_ids[2], TerminalRole.SLAVE, "demo_user")
        
        # 其余设为空闲
        for i in range(4, len(terminal_ids)):
            await self.role_manager.set_terminal_role(terminal_ids[i], TerminalRole.IDLE, "demo_user")
        
        await self.show_current_status()
        await asyncio.sleep(10)
        
        # 演示场景3: 测试配置
        print("🧪 场景3: 测试配置")
        print("设置: 只有终端1为主账户，终端2为从账户，其余空闲")
        
        await self.role_manager.set_terminal_role(terminal_ids[0], TerminalRole.MASTER, "demo_user")
        await self.role_manager.set_terminal_role(terminal_ids[1], TerminalRole.SLAVE, "demo_user")
        
        # 其余全部设为空闲
        for i in range(2, len(terminal_ids)):
            await self.role_manager.set_terminal_role(terminal_ids[i], TerminalRole.IDLE, "demo_user")
        
        await self.show_current_status()
        await asyncio.sleep(10)
    
    async def demonstrate_batch_setting(self):
        """演示批量设置"""
        logger.info("演示批量角色设置...")
        
        terminals = await self.terminal_manager.get_all_terminals_status()
        terminal_ids = [t['terminal_id'] for t in terminals]
        
        print("🔄 批量设置演示")
        print("设置: 前一半为主账户，后一半为从账户")
        
        mid_point = len(terminal_ids) // 2
        
        assignments = {}
        
        # 前一半设为主账户
        for i in range(mid_point):
            assignments[terminal_ids[i]] = TerminalRole.MASTER
        
        # 后一半设为从账户
        for i in range(mid_point, len(terminal_ids)):
            assignments[terminal_ids[i]] = TerminalRole.SLAVE
        
        results = await self.role_manager.batch_set_roles(assignments, "demo_batch")
        
        success_count = sum(1 for success in results.values() if success)
        print(f"批量设置结果: 成功 {success_count}/{len(assignments)} 个终端")
        
        for terminal_id, success in results.items():
            status = "✅" if success else "❌"
            print(f"  {status} {terminal_id}")
        
        await self.show_current_status()
        await asyncio.sleep(10)
    
    async def show_role_history(self):
        """显示角色变更历史"""
        print("📊 角色变更历史")
        print("="*80)
        
        history = await self.role_manager.get_role_change_history(20)
        
        if not history:
            print("没有角色变更历史")
            return
        
        for event in reversed(history[-10:]):  # 显示最近10条
            import datetime
            time_str = datetime.datetime.fromtimestamp(event.timestamp).strftime("%H:%M:%S")
            status_icon = "✅" if event.success else "❌"
            
            print(f"{status_icon} {time_str} - {event.terminal_id}: {event.old_role} → {event.new_role} (by {event.user})")
        
        print("="*80 + "\n")
    
    async def run_demo(self):
        """运行完整演示"""
        try:
            logger.info("🎯 开始手动角色管理演示")
            
            # 1. 初始化系统
            await self.initialize()
            
            # 2. 注册终端
            await self.register_demo_terminals()
            
            # 3. 显示初始状态
            await self.show_current_status()
            
            # 4. 演示手动角色设置
            await self.demonstrate_manual_role_setting()
            
            # 5. 演示批量设置
            await self.demonstrate_batch_setting()
            
            # 6. 显示变更历史
            await self.show_role_history()
            
            # 7. 重置所有角色
            print("🔄 重置所有角色为空闲状态")
            await self.role_manager.reset_all_roles("demo_reset")
            await self.show_current_status()
            
            logger.info("✅ 手动角色管理演示完成")
            
        except Exception as e:
            logger.error(f"演示失败: {e}")
            raise
        finally:
            await self.cleanup()


async def main():
    """主函数"""
    # 设置日志
    setup_logging({"level": "INFO"})
    
    # 配置文件路径
    config_path = "config/multi_terminal_config.yaml"
    
    # 创建演示实例
    demo = ManualRoleDemo(config_path)
    
    try:
        await demo.run_demo()
    except KeyboardInterrupt:
        logger.info("演示被用户中断")
    except Exception as e:
        logger.error(f"演示失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    print("""
🎯 MT5手动角色管理演示

这个演示将展示如何：
1. 独立设置每个终端的角色
2. 不受配置组限制的灵活管理
3. 批量角色设置
4. 角色变更历史跟踪

特点：
✅ 完全手动控制
✅ 任意角色组合
✅ 实时角色切换
✅ 历史记录追踪

按 Ctrl+C 可以随时停止演示
""")
    
    input("按回车键开始演示...")
    asyncio.run(main())
