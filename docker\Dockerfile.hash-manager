# Redis Hash Manager Service Dockerfile
# 专用于Redis Hash操作的优化API服务

FROM python:3.11-alpine

# 设置工作目录
WORKDIR /app

# 安装系统依赖
RUN apk add --no-cache \
    gcc \
    musl-dev \
    linux-headers \
    redis

# 复制Docker专用requirements文件
COPY requirements-docker.txt ./requirements.txt

# 安装Python依赖
RUN pip install --no-cache-dir -r requirements.txt

# 复制应用代码
COPY src/ ./src/
COPY config/ ./config/

# 创建专用的Hash Manager API服务
COPY docker/hash_manager_api.py ./

# 设置环境变量
ENV PYTHONPATH=/app
ENV REDIS_HOST=redis
ENV REDIS_PORT=6379
ENV HASH_MANAGER_PORT=8082

# 暴露端口
EXPOSE 8082

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=30s --retries=3 \
    CMD python -c "import requests; requests.get('http://localhost:8082/health')"

# 启动命令
CMD ["python", "hash_manager_api.py"]
