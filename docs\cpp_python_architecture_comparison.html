<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>C++与Python混合架构方案对比</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f7fa;
            color: #333;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
        }
        
        h1, h2 {
            color: #2c3e50;
        }
        
        .architecture-overview {
            background: white;
            border-radius: 10px;
            padding: 30px;
            margin: 20px 0;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .connection-comparison {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        
        .connection-card {
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            border-top: 4px solid #3498db;
        }
        
        .connection-card.recommended {
            border-top-color: #27ae60;
        }
        
        .connection-card.fast {
            border-top-color: #e74c3c;
        }
        
        .connection-card h3 {
            margin-top: 0;
            color: #2c3e50;
        }
        
        .metric {
            display: flex;
            justify-content: space-between;
            padding: 8px 0;
            border-bottom: 1px solid #ecf0f1;
        }
        
        .metric:last-child {
            border-bottom: none;
        }
        
        .metric-label {
            font-weight: 500;
            color: #7f8c8d;
        }
        
        .metric-value {
            font-weight: bold;
            color: #2c3e50;
        }
        
        .pros-cons {
            margin-top: 15px;
        }
        
        .pros, .cons {
            margin: 10px 0;
        }
        
        .pros ul, .cons ul {
            margin: 5px 0;
            padding-left: 20px;
        }
        
        .pros li {
            color: #27ae60;
        }
        
        .cons li {
            color: #e74c3c;
        }
        
        .architecture-diagram {
            background: white;
            border-radius: 10px;
            padding: 30px;
            margin: 20px 0;
            text-align: center;
        }
        
        .flow-diagram {
            display: flex;
            align-items: center;
            justify-content: center;
            flex-wrap: wrap;
            gap: 20px;
            margin: 20px 0;
        }
        
        .component {
            background: #3498db;
            color: white;
            padding: 15px 25px;
            border-radius: 5px;
            font-weight: bold;
            position: relative;
        }
        
        .component.cpp {
            background: #e74c3c;
        }
        
        .component.python {
            background: #f39c12;
        }
        
        .arrow {
            font-size: 24px;
            color: #34495e;
        }
        
        .deployment-scenarios {
            background: white;
            border-radius: 10px;
            padding: 30px;
            margin: 20px 0;
        }
        
        .scenario-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }
        
        .scenario-table th, .scenario-table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #ecf0f1;
        }
        
        .scenario-table th {
            background: #f8f9fa;
            font-weight: bold;
            color: #2c3e50;
        }
        
        .scenario-table tr:hover {
            background: #f8f9fa;
        }
        
        .recommendation-box {
            background: #e8f5e9;
            border-left: 4px solid #27ae60;
            padding: 20px;
            margin: 20px 0;
            border-radius: 5px;
        }
        
        .warning-box {
            background: #fff3cd;
            border-left: 4px solid #f39c12;
            padding: 20px;
            margin: 20px 0;
            border-radius: 5px;
        }
        
        .performance-chart {
            margin: 20px 0;
            height: 300px;
            position: relative;
        }
        
        .bar {
            position: absolute;
            bottom: 0;
            width: 60px;
            background: #3498db;
            color: white;
            text-align: center;
            padding-top: 10px;
            border-radius: 5px 5px 0 0;
            transition: all 0.3s ease;
        }
        
        .bar:hover {
            transform: translateY(-5px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        
        .bar-label {
            position: absolute;
            bottom: -25px;
            width: 100%;
            text-align: center;
            font-size: 12px;
            color: #7f8c8d;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>C++统计套利与Python MT5跟单系统集成方案</h1>
        
        <div class="architecture-overview">
            <h2>系统架构概览</h2>
            <div class="flow-diagram">
                <div class="component">市场数据</div>
                <span class="arrow">→</span>
                <div class="component python">Python MT5</div>
                <span class="arrow">→</span>
                <div class="component cpp">C++ 统计套利引擎</div>
                <span class="arrow">→</span>
                <div class="component">交易信号</div>
                <span class="arrow">→</span>
                <div class="component python">Python 执行系统</div>
            </div>
            
            <p style="text-align: center; color: #7f8c8d; margin-top: 20px;">
                C++负责高性能计算，Python负责MT5接口和执行
            </p>
        </div>
        
        <h2>连接方案对比</h2>
        
        <div class="connection-comparison">
            <!-- gRPC方案 -->
            <div class="connection-card recommended">
                <h3>🚀 gRPC (推荐方案)</h3>
                <div class="metric">
                    <span class="metric-label">延迟</span>
                    <span class="metric-value">~1ms</span>
                </div>
                <div class="metric">
                    <span class="metric-label">吞吐量</span>
                    <span class="metric-value">100K+ msg/s</span>
                </div>
                <div class="metric">
                    <span class="metric-label">复杂度</span>
                    <span class="metric-value">中等</span>
                </div>
                <div class="metric">
                    <span class="metric-label">跨机器</span>
                    <span class="metric-value">✅ 支持</span>
                </div>
                
                <div class="pros-cons">
                    <div class="pros">
                        <strong>优点：</strong>
                        <ul>
                            <li>跨语言支持完善</li>
                            <li>支持流式传输</li>
                            <li>自动序列化/反序列化</li>
                            <li>负载均衡支持</li>
                        </ul>
                    </div>
                    <div class="cons">
                        <strong>缺点：</strong>
                        <ul>
                            <li>需要定义Proto文件</li>
                            <li>序列化有开销</li>
                        </ul>
                    </div>
                </div>
            </div>
            
            <!-- 共享内存方案 -->
            <div class="connection-card fast">
                <h3>⚡ 共享内存 (最快)</h3>
                <div class="metric">
                    <span class="metric-label">延迟</span>
                    <span class="metric-value">~10μs</span>
                </div>
                <div class="metric">
                    <span class="metric-label">吞吐量</span>
                    <span class="metric-value">1M+ msg/s</span>
                </div>
                <div class="metric">
                    <span class="metric-label">复杂度</span>
                    <span class="metric-value">高</span>
                </div>
                <div class="metric">
                    <span class="metric-label">跨机器</span>
                    <span class="metric-value">❌ 不支持</span>
                </div>
                
                <div class="pros-cons">
                    <div class="pros">
                        <strong>优点：</strong>
                        <ul>
                            <li>延迟极低</li>
                            <li>零拷贝传输</li>
                            <li>CPU开销最小</li>
                        </ul>
                    </div>
                    <div class="cons">
                        <strong>缺点：</strong>
                        <ul>
                            <li>仅限同机部署</li>
                            <li>需要处理同步问题</li>
                            <li>调试困难</li>
                        </ul>
                    </div>
                </div>
            </div>
            
            <!-- ZeroMQ方案 -->
            <div class="connection-card">
                <h3>📡 ZeroMQ</h3>
                <div class="metric">
                    <span class="metric-label">延迟</span>
                    <span class="metric-value">~100μs</span>
                </div>
                <div class="metric">
                    <span class="metric-label">吞吐量</span>
                    <span class="metric-value">500K+ msg/s</span>
                </div>
                <div class="metric">
                    <span class="metric-label">复杂度</span>
                    <span class="metric-value">低</span>
                </div>
                <div class="metric">
                    <span class="metric-label">跨机器</span>
                    <span class="metric-value">✅ 支持</span>
                </div>
                
                <div class="pros-cons">
                    <div class="pros">
                        <strong>优点：</strong>
                        <ul>
                            <li>简单易用</li>
                            <li>高性能</li>
                            <li>多种消息模式</li>
                            <li>无需中间件</li>
                        </ul>
                    </div>
                    <div class="cons">
                        <strong>缺点：</strong>
                        <ul>
                            <li>需要自行处理序列化</li>
                            <li>缺少服务发现</li>
                        </ul>
                    </div>
                </div>
            </div>
            
            <!-- Redis Streams方案 -->
            <div class="connection-card">
                <h3>💾 Redis Streams</h3>
                <div class="metric">
                    <span class="metric-label">延迟</span>
                    <span class="metric-value">~5ms</span>
                </div>
                <div class="metric">
                    <span class="metric-label">吞吐量</span>
                    <span class="metric-value">50K+ msg/s</span>
                </div>
                <div class="metric">
                    <span class="metric-label">复杂度</span>
                    <span class="metric-value">低</span>
                </div>
                <div class="metric">
                    <span class="metric-label">跨机器</span>
                    <span class="metric-value">✅ 支持</span>
                </div>
                
                <div class="pros-cons">
                    <div class="pros">
                        <strong>优点：</strong>
                        <ul>
                            <li>消息持久化</li>
                            <li>可重放历史</li>
                            <li>分布式友好</li>
                            <li>消费者组支持</li>
                        </ul>
                    </div>
                    <div class="cons">
                        <strong>缺点：</strong>
                        <ul>
                            <li>延迟相对较高</li>
                            <li>需要Redis服务</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="deployment-scenarios">
            <h2>部署场景建议</h2>
            <table class="scenario-table">
                <tr>
                    <th>场景</th>
                    <th>推荐方案</th>
                    <th>备选方案</th>
                    <th>原因</th>
                </tr>
                <tr>
                    <td>开发测试</td>
                    <td>gRPC</td>
                    <td>ZeroMQ</td>
                    <td>易于调试，功能完整</td>
                </tr>
                <tr>
                    <td>单机高频交易</td>
                    <td>共享内存</td>
                    <td>ZeroMQ (IPC)</td>
                    <td>延迟要求极高</td>
                </tr>
                <tr>
                    <td>分布式部署</td>
                    <td>gRPC</td>
                    <td>Redis Streams</td>
                    <td>跨机器通信，需要可靠性</td>
                </tr>
                <tr>
                    <td>混合部署</td>
                    <td>gRPC + 共享内存</td>
                    <td>-</td>
                    <td>主用gRPC，关键路径用共享内存</td>
                </tr>
                <tr>
                    <td>需要消息重放</td>
                    <td>Redis Streams</td>
                    <td>Kafka</td>
                    <td>历史数据回放需求</td>
                </tr>
            </table>
        </div>
        
        <div class="architecture-diagram">
            <h2>混合架构最佳实践</h2>
            <div style="background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0;">
                <h3>三层通信架构</h3>
                <div style="display: grid; grid-template-columns: repeat(3, 1fr); gap: 20px; margin-top: 20px;">
                    <div style="text-align: center;">
                        <div style="background: #e74c3c; color: white; padding: 15px; border-radius: 5px; margin-bottom: 10px;">
                            <strong>关键路径</strong>
                        </div>
                        <p>共享内存<br>延迟: 10μs<br>用于: 高频信号</p>
                    </div>
                    <div style="text-align: center;">
                        <div style="background: #3498db; color: white; padding: 15px; border-radius: 5px; margin-bottom: 10px;">
                            <strong>主通信</strong>
                        </div>
                        <p>gRPC<br>延迟: 1ms<br>用于: 常规通信</p>
                    </div>
                    <div style="text-align: center;">
                        <div style="background: #27ae60; color: white; padding: 15px; border-radius: 5px; margin-bottom: 10px;">
                            <strong>持久化</strong>
                        </div>
                        <p>Redis Streams<br>延迟: 5ms<br>用于: 审计日志</p>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="recommendation-box">
            <h3>🎯 推荐实施方案</h3>
            <ol>
                <li><strong>第一阶段：</strong>使用gRPC实现基础集成，验证系统逻辑</li>
                <li><strong>第二阶段：</strong>在同机部署场景下，为关键路径添加共享内存通道</li>
                <li><strong>第三阶段：</strong>添加Redis Streams作为持久化和灾备通道</li>
                <li><strong>第四阶段：</strong>根据实际性能瓶颈，优化特定通信路径</li>
            </ol>
        </div>
        
        <div class="warning-box">
            <h3>⚠️ 注意事项</h3>
            <ul>
                <li>C++和Python的数据结构对齐要特别注意，尤其是使用共享内存时</li>
                <li>跨语言的时间戳格式要统一（建议使用Unix微秒时间戳）</li>
                <li>错误处理和重连机制要完善，防止一端崩溃影响另一端</li>
                <li>性能监控要覆盖两端，便于定位瓶颈</li>
            </ul>
        </div>
        
        <div class="architecture-diagram">
            <h2>性能对比图表</h2>
            <div class="performance-chart">
                <div class="bar" style="height: 250px; left: 50px; background: #e74c3c;">
                    10μs
                    <div class="bar-label">共享内存</div>
                </div>
                <div class="bar" style="height: 200px; left: 150px; background: #f39c12;">
                    100μs
                    <div class="bar-label">ZeroMQ</div>
                </div>
                <div class="bar" style="height: 150px; left: 250px; background: #3498db;">
                    1ms
                    <div class="bar-label">gRPC</div>
                </div>
                <div class="bar" style="height: 100px; left: 350px; background: #27ae60;">
                    5ms
                    <div class="bar-label">Redis</div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>