# 🚨 AUTO-MIGRATED: Old messaging components replaced with HybridQueueManager
# See MIGRATION_GUIDE.md for details

"""
生产环境优化器
整合所有生产环境的优化和配置
"""
import asyncio
import logging
from typing import Dict, Any, Optional
from dataclasses import dataclass

import os
from .environment_manager import get_environment_manager
from ..utils.metrics import get_metrics_collector
from ..utils.recovery_strategies import get_recovery_manager
from ..messaging.hybrid_queue_manager import HybridQueueManager, QueueConfig
# 遗留组件已移除，使用新的relationships模块
from ..relationships.relationship_manager import RelationshipManager

logger = logging.getLogger(__name__)


@dataclass
class ProductionConfig:
    """生产环境配置"""
    max_concurrent_connections: int = 100
    connection_timeout: float = 30.0
    health_check_interval: float = 60.0
    metrics_collection_interval: float = 10.0
    auto_scaling_enabled: bool = True
    backup_enabled: bool = True
    security_enabled: bool = True


class ProductionOptimizer:
    """生产环境优化器"""
    
    def __init__(self, config: ProductionConfig = None):
        self.config = config or ProductionConfig()
        self.env_manager = get_environment_manager()
        self.metrics_collector = get_metrics_collector()
        self.recovery_manager = get_recovery_manager()
        
        # 组件实例
        self.queue_manager: Optional[HybridQueueManager] = None
        self.relationship_manager: Optional[RelationshipManager] = None
        
        # 监控任务
        self.health_check_task: Optional[asyncio.Task] = None
        self.metrics_task: Optional[asyncio.Task] = None
        
        logger.info("生产环境优化器初始化完成")
    
    async def initialize(self):
        """初始化生产环境"""
        logger.info("🚀 初始化生产环境")
        
        # 1. 验证环境配置
        await self._validate_environment()
        
        # 2. 初始化监控
        await self._initialize_monitoring()
        
        # 3. 初始化NATS客户端
        await self._initialize_nats()
        
        # 4. 初始化跟单系统
        await self._initialize_copy_components()
        
        # 5. 启动健康检查
        await self._start_health_checks()
        
        logger.info("✅ 生产环境初始化完成")
    
    async def _validate_environment(self):
        """验证环境配置"""
        logger.info("验证环境配置...")
        
        # 检查必需的环境变量
        required_vars = [
            'NATS_PASSWORD', 'DB_PASSWORD', 'REDIS_PASSWORD', 'JWT_SECRET'
        ]
        
        missing_vars = []
        for var in required_vars:
            if not os.getenv(var):
                missing_vars.append(var)
        
        if missing_vars:
            raise ValueError(f"缺少必需的环境变量: {missing_vars}")
        
        # 验证配置文件
        if not self.env_manager.config:
            raise ValueError("环境配置为空")
        
        logger.info("✅ 环境配置验证通过")
    
    async def _initialize_monitoring(self):
        """初始化监控"""
        logger.info("初始化监控系统...")
        
        # 启动Prometheus服务器
        monitor_config = self.env_manager.get_monitoring_config()
        if monitor_config.get('prometheus', {}).get('enabled', False):
            port = monitor_config.get('prometheus', {}).get('port', 8080)
            success = self.metrics_collector.start_prometheus_server(port)
            if success:
                logger.info(f"✅ Prometheus服务器启动成功: {port}")
            else:
                logger.error("❌ Prometheus服务器启动失败")
        
        # 启动指标收集任务
        self.metrics_task = asyncio.create_task(self._metrics_collection_loop())
        
        logger.info("✅ 监控系统初始化完成")
    
    async def _initialize_nats(self):
        """初始化NATS客户端"""
        logger.info("初始化NATS客户端...")
        
        nats_config_dict = self.env_manager.config.get('nats', {})
        
        # 创建队列配置
        queue_config = QueueConfig(
            nats_servers=nats_config_dict.get('servers', ['nats://localhost:4222']),
            nats_user=nats_config_dict.get('auth', {}).get('user'),
            nats_password=os.getenv('NATS_PASSWORD'),
            redis_url=nats_config_dict.get('redis', {}).get('url', 'redis://localhost:6379'),
            **nats_config_dict.get('connection', {})
        )
        
        # 创建客户端
        self.queue_manager = HybridQueueManager(queue_config)
        
        # 连接
        success = await self.queue_manager.connect()
        if success:
            logger.info("✅ NATS客户端连接成功")
        else:
            raise Exception("NATS客户端连接失败")
    
    async def _initialize_copy_components(self):
        """初始化跟单组件"""
        logger.info("初始化跟单组件...")
        
        # 1. 初始化跟单关系管理器
        config_file = self.env_manager.config.get('copy_trading', {}).get('config_file', 'config/copy_relationships.yaml')
        self.relationship_manager = RelationshipManager(config_file)
        
        # 2. 使用HybridQueueManager作为消息队列
        queue_manager = self.queue_manager
        
        # 3. 初始化跟单交易引擎（待实现）
        # TODO: 使用新的统一架构实现跟单引擎
        # self.copy_trading_engine = CopyTradingEngine(queue_manager)
        # await self.copy_trading_engine.start()
        
        logger.info("✅ 跟单组件初始化完成")
    
    async def _start_health_checks(self):
        """启动健康检查"""
        logger.info("启动健康检查...")
        
        self.health_check_task = asyncio.create_task(self._health_check_loop())
        
        logger.info("✅ 健康检查启动完成")
    
    async def _health_check_loop(self):
        """健康检查循环"""
        while True:
            try:
                await asyncio.sleep(self.config.health_check_interval)
                
                # 检查NATS连接
                if self.queue_manager:
                    nats_healthy = await self.queue_manager.health_check()
                    self.metrics_collector.set_gauge('nats_connection_status', 1.0 if nats_healthy else 0.0)
                
                # 检查跟单系统
                if self.relationship_manager:
                    stats = self.relationship_manager.get_stats()
                    self.metrics_collector.set_gauge('active_copy_relationships', stats.get('active_relationships', 0))
                    self.metrics_collector.set_gauge('total_copy_relationships', stats.get('total_relationships', 0))
                    
                # TODO: 重新实现跟单引擎统计
                # if self.copy_trading_engine:
                #     engine_stats = self.copy_trading_engine.get_statistics()
                #     self.metrics_collector.set_gauge('connected_accounts', engine_stats['connected_accounts'])
                
                # 记录健康检查完成
                self.metrics_collector.increment_counter('health_checks_completed')
                
            except Exception as e:
                logger.error(f"健康检查异常: {e}")
                self.metrics_collector.increment_counter('health_check_errors')
                await asyncio.sleep(5)
    
    async def _metrics_collection_loop(self):
        """指标收集循环"""
        while True:
            try:
                await asyncio.sleep(self.config.metrics_collection_interval)
                
                # 收集系统指标
                await self._collect_system_metrics()
                
                # 收集业务指标
                await self._collect_business_metrics()
                
            except Exception as e:
                logger.error(f"指标收集异常: {e}")
                await asyncio.sleep(5)
    
    async def _collect_system_metrics(self):
        """收集系统指标"""
        import psutil
        
        # CPU使用率
        cpu_percent = psutil.cpu_percent()
        self.metrics_collector.set_gauge('system_cpu_usage', cpu_percent)
        
        # 内存使用率
        memory = psutil.virtual_memory()
        self.metrics_collector.set_gauge('system_memory_usage', memory.percent)
        
        # 磁盘使用率
        disk = psutil.disk_usage('/')
        self.metrics_collector.set_gauge('system_disk_usage', disk.percent)
    
    async def _collect_business_metrics(self):
        """收集业务指标"""
        # TODO: 重新实现业务指标收集
        # 等待跟单引擎迁移完成后，使用新的统计接口
        if self.relationship_manager:
            stats = self.relationship_manager.get_stats()
            self.metrics_collector.set_gauge('active_relationships', stats.get('active_relationships', 0))
            self.metrics_collector.set_gauge('total_relationships', stats.get('total_relationships', 0))
        
        # TODO: 从HybridQueueManager获取消息队列统计
        if self.queue_manager:
            queue_stats = await self.queue_manager.get_metrics()
            for metric_name, metric_value in queue_stats.items():
                self.metrics_collector.set_gauge(f'queue_{metric_name}', metric_value)
    
    async def shutdown(self):
        """关闭生产环境"""
        logger.info("关闭生产环境...")
        
        # 停止健康检查
        if self.health_check_task:
            self.health_check_task.cancel()
        
        # 停止指标收集
        if self.metrics_task:
            self.metrics_task.cancel()
        
        # TODO: 停止跟单系统
        # if self.copy_trading_engine:
        #     await self.copy_trading_engine.stop()
        
        # 断开NATS连接
        if self.queue_manager:
            await self.queue_manager.disconnect()
        
        logger.info("✅ 生产环境关闭完成")
    
    async def get_status(self) -> Dict[str, Any]:
        """获取系统状态"""
        status = {
            'environment': self.env_manager.environment,
            'is_production': self.env_manager.is_production(),
            'queue_connected': await self.queue_manager.is_connected() if self.queue_manager else False,
            'metrics_enabled': self.metrics_collector.enable_prometheus,
            'health_check_running': self.health_check_task and not self.health_check_task.done(),
        }
        
        # 添加统计信息
        if self.relationship_manager:
            status['relationship_stats'] = self.relationship_manager.get_stats()
        
        # TODO: 添加跟单引擎状态
        # if self.copy_trading_engine:
        #     status['copy_engine_stats'] = self.copy_trading_engine.get_statistics()
        
        return status


# 全局优化器实例
_production_optimizer: Optional[ProductionOptimizer] = None


def get_production_optimizer() -> ProductionOptimizer:
    """获取生产环境优化器"""
    global _production_optimizer
    if _production_optimizer is None:
        _production_optimizer = ProductionOptimizer()
    return _production_optimizer


async def initialize_production_environment():
    """初始化生产环境"""
    optimizer = get_production_optimizer()
    await optimizer.initialize()
    return optimizer


async def shutdown_production_environment():
    """关闭生产环境"""
    global _production_optimizer
    if _production_optimizer:
        await _production_optimizer.shutdown()
        _production_optimizer = None