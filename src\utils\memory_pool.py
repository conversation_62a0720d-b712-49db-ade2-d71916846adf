"""
内存池管理器
用于优化高频信号处理中的内存分配和回收
"""
import asyncio
import time
import weakref
from typing import Dict, List, Optional, Any, TypeVar, Generic, Callable
from dataclasses import dataclass, field
from collections import deque
from threading import Lock
import gc
import sys

from .logger import get_logger
from .metrics import get_metrics_collector

logger = get_logger(__name__)
metrics = get_metrics_collector()

T = TypeVar('T')


@dataclass
class PoolStats:
    """池统计信息"""
    total_allocated: int = 0
    total_freed: int = 0
    active_objects: int = 0
    pool_size: int = 0
    hit_rate: float = 0.0
    miss_rate: float = 0.0
    memory_usage_mb: float = 0.0
    gc_collections: int = 0
    last_gc_time: float = 0.0


@dataclass
class PoolConfig:
    """池配置"""
    initial_size: int = 100
    max_size: int = 1000
    growth_factor: float = 1.5
    cleanup_interval: float = 60.0  # 60秒清理一次
    max_idle_time: float = 300.0    # 5分钟未使用则释放
    enable_gc_tuning: bool = True
    enable_metrics: bool = True


class MemoryPool(Generic[T]):
    """通用内存池"""
    
    def __init__(self, factory: Callable[[], T], config: Optional[PoolConfig] = None):
        self.factory = factory
        self.config = config or PoolConfig()
        
        # 可用对象池
        self.available: deque[T] = deque()
        
        # 已分配对象追踪
        self.allocated: Dict[int, T] = {}
        
        # 统计信息
        self.stats = PoolStats()
        
        # 线程安全锁
        self.lock = Lock()
        
        # 清理任务
        self.cleanup_task: Optional[asyncio.Task] = None
        
        # 初始化池
        self._initialize_pool()
    
    def _initialize_pool(self):
        """初始化池"""
        try:
            with self.lock:
                for _ in range(self.config.initial_size):
                    obj = self.factory()
                    self.available.append(obj)
                    self.stats.pool_size += 1
                
                logger.info(f"内存池初始化完成 - 初始大小: {self.config.initial_size}")
                
        except Exception as e:
            logger.error(f"内存池初始化失败: {e}")
    
    def acquire(self) -> T:
        """获取对象"""
        with self.lock:
            if self.available:
                # 从池中获取
                obj = self.available.popleft()
                self.stats.total_allocated += 1
                self.stats.active_objects += 1
                
                # 记录分配
                obj_id = id(obj)
                self.allocated[obj_id] = obj
                
                # 更新命中率
                self._update_hit_rate(True)
                
                logger.debug(f"从池中获取对象 {obj_id}")
                return obj
            
            else:
                # 池为空，创建新对象
                if self.stats.pool_size < self.config.max_size:
                    obj = self.factory()
                    self.stats.total_allocated += 1
                    self.stats.active_objects += 1
                    self.stats.pool_size += 1
                    
                    # 记录分配
                    obj_id = id(obj)
                    self.allocated[obj_id] = obj
                    
                    # 更新命中率
                    self._update_hit_rate(False)
                    
                    logger.debug(f"创建新对象 {obj_id} (池大小: {self.stats.pool_size})")
                    return obj
                
                else:
                    # 池已满，直接创建临时对象
                    obj = self.factory()
                    logger.warning(f"池已满，创建临时对象 {id(obj)}")
                    return obj
    
    def release(self, obj: T) -> bool:
        """释放对象"""
        try:
            with self.lock:
                obj_id = id(obj)
                
                if obj_id in self.allocated:
                    del self.allocated[obj_id]
                    self.stats.active_objects -= 1
                    self.stats.total_freed += 1
                    
                    # 重置对象状态
                    if hasattr(obj, 'reset'):
                        obj.reset()
                    
                    # 放回池中
                    if len(self.available) < self.config.max_size:
                        self.available.append(obj)
                        logger.debug(f"对象 {obj_id} 已放回池中")
                        return True
                    else:
                        logger.debug(f"池已满，对象 {obj_id} 被丢弃")
                        return False
                
                else:
                    logger.warning(f"尝试释放未跟踪的对象 {obj_id}")
                    return False
                    
        except Exception as e:
            logger.error(f"释放对象失败: {e}")
            return False
    
    def _update_hit_rate(self, hit: bool):
        """更新命中率"""
        if hit:
            self.stats.hit_rate = (
                (self.stats.hit_rate * (self.stats.total_allocated - 1) + 1) / 
                self.stats.total_allocated
            )
        else:
            self.stats.miss_rate = (
                (self.stats.miss_rate * (self.stats.total_allocated - 1) + 1) / 
                self.stats.total_allocated
            )
    
    def get_stats(self) -> PoolStats:
        """获取池统计信息"""
        with self.lock:
            self.stats.memory_usage_mb = self._calculate_memory_usage()
            return self.stats
    
    def _calculate_memory_usage(self) -> float:
        """计算内存使用量"""
        try:
            # 估算内存使用
            total_size = 0
            
            # 计算可用对象大小
            for obj in self.available:
                total_size += sys.getsizeof(obj)
            
            # 计算已分配对象大小
            for obj in self.allocated.values():
                total_size += sys.getsizeof(obj)
            
            return total_size / (1024 * 1024)  # MB
            
        except Exception as e:
            logger.error(f"计算内存使用失败: {e}")
            return 0.0
    
    async def start_cleanup_task(self):
        """启动清理任务"""
        if self.cleanup_task is None:
            self.cleanup_task = asyncio.create_task(self._cleanup_loop())
            logger.info("内存池清理任务已启动")
    
    async def stop_cleanup_task(self):
        """停止清理任务"""
        if self.cleanup_task:
            self.cleanup_task.cancel()
            try:
                await self.cleanup_task
            except asyncio.CancelledError:
                pass
            self.cleanup_task = None
            logger.info("内存池清理任务已停止")
    
    async def _cleanup_loop(self):
        """清理循环"""
        while True:
            try:
                await asyncio.sleep(self.config.cleanup_interval)
                await self._cleanup_idle_objects()
                
                if self.config.enable_gc_tuning:
                    await self._tune_gc()
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"清理循环错误: {e}")
    
    async def _cleanup_idle_objects(self):
        """清理空闲对象"""
        with self.lock:
            # 保持最小池大小
            min_size = max(self.config.initial_size // 2, 10)
            
            if len(self.available) > min_size:
                # 移除超过最小大小的对象
                excess = len(self.available) - min_size
                
                for _ in range(excess):
                    if self.available:
                        obj = self.available.popleft()
                        self.stats.pool_size -= 1
                
                logger.debug(f"清理了 {excess} 个空闲对象")
    
    async def _tune_gc(self):
        """调优垃圾收集"""
        try:
            # 强制进行垃圾收集
            before_count = len(gc.get_objects())
            collected = gc.collect()
            after_count = len(gc.get_objects())
            
            if collected > 0:
                self.stats.gc_collections += 1
                self.stats.last_gc_time = time.time()
                
                logger.debug(f"GC清理: {collected} 个对象被回收, "
                           f"对象数量: {before_count} -> {after_count}")
            
            # 记录指标
            if self.config.enable_metrics:
                metrics.gauge('memory_pool_objects_before_gc', before_count)
                metrics.gauge('memory_pool_objects_after_gc', after_count)
                metrics.gauge('memory_pool_gc_collected', collected)
                
        except Exception as e:
            logger.error(f"GC调优失败: {e}")
    
    def clear(self):
        """清空池"""
        with self.lock:
            self.available.clear()
            self.allocated.clear()
            self.stats = PoolStats()
            logger.info("内存池已清空")


class TradeSignalPool:
    """交易信号专用内存池"""
    
    def __init__(self, config: Optional[PoolConfig] = None):
        self.config = config or PoolConfig(initial_size=200, max_size=2000)
        
        # 不同类型信号的池
        self.pools: Dict[str, MemoryPool] = {}
        
        # 信号工厂
        self.signal_factories = {
            'position_open': lambda: self._create_position_signal(),
            'position_close': lambda: self._create_position_signal(),
            'position_modify': lambda: self._create_position_signal(),
            'order_pending': lambda: self._create_order_signal(),
            'order_cancel': lambda: self._create_order_signal(),
        }
        
        # 初始化各类型池
        self._initialize_pools()
    
    def _initialize_pools(self):
        """初始化各类型池"""
        for signal_type, factory in self.signal_factories.items():
            pool = MemoryPool(factory, self.config)
            self.pools[signal_type] = pool
            logger.info(f"已初始化 {signal_type} 信号池")
    
    def _create_position_signal(self) -> Dict[str, Any]:
        """创建持仓信号"""
        return {
            'type': '',
            'master_id': '',
            'slave_ids': [],
            'ticket': 0,
            'data': {},
            'timestamp': 0.0,
            'capture_latency_ms': 0.0,
            'processing_latency_ms': 0.0,
            'in_use': False
        }
    
    def _create_order_signal(self) -> Dict[str, Any]:
        """创建订单信号"""
        return {
            'type': '',
            'master_id': '',
            'slave_ids': [],
            'order_id': 0,
            'data': {},
            'timestamp': 0.0,
            'capture_latency_ms': 0.0,
            'processing_latency_ms': 0.0,
            'in_use': False
        }
    
    def acquire_signal(self, signal_type: str) -> Optional[Dict[str, Any]]:
        """获取信号对象"""
        pool = self.pools.get(signal_type)
        if pool:
            signal = pool.acquire()
            signal['in_use'] = True
            signal['type'] = signal_type
            return signal
        else:
            logger.warning(f"未找到 {signal_type} 类型的信号池")
            return None
    
    def release_signal(self, signal: Dict[str, Any]) -> bool:
        """释放信号对象"""
        signal_type = signal.get('type')
        pool = self.pools.get(signal_type)
        
        if pool:
            # 重置信号状态
            signal['in_use'] = False
            signal['type'] = ''
            signal['master_id'] = ''
            signal['slave_ids'] = []
            signal['data'] = {}
            signal['timestamp'] = 0.0
            signal['capture_latency_ms'] = 0.0
            signal['processing_latency_ms'] = 0.0
            
            return pool.release(signal)
        else:
            logger.warning(f"未找到 {signal_type} 类型的信号池")
            return False
    
    async def start_all_cleanup_tasks(self):
        """启动所有清理任务"""
        for signal_type, pool in self.pools.items():
            await pool.start_cleanup_task()
            logger.info(f"{signal_type} 信号池清理任务已启动")
    
    async def stop_all_cleanup_tasks(self):
        """停止所有清理任务"""
        for signal_type, pool in self.pools.items():
            await pool.stop_cleanup_task()
            logger.info(f"{signal_type} 信号池清理任务已停止")
    
    def get_all_stats(self) -> Dict[str, PoolStats]:
        """获取所有池的统计信息"""
        stats = {}
        for signal_type, pool in self.pools.items():
            stats[signal_type] = pool.get_stats()
        return stats
    
    def get_summary_stats(self) -> Dict[str, Any]:
        """获取汇总统计信息"""
        all_stats = self.get_all_stats()
        
        total_allocated = sum(stats.total_allocated for stats in all_stats.values())
        total_freed = sum(stats.total_freed for stats in all_stats.values())
        total_active = sum(stats.active_objects for stats in all_stats.values())
        total_pool_size = sum(stats.pool_size for stats in all_stats.values())
        total_memory = sum(stats.memory_usage_mb for stats in all_stats.values())
        
        # 计算平均命中率
        hit_rates = [stats.hit_rate for stats in all_stats.values() if stats.hit_rate > 0]
        avg_hit_rate = sum(hit_rates) / len(hit_rates) if hit_rates else 0
        
        return {
            'total_allocated': total_allocated,
            'total_freed': total_freed,
            'total_active': total_active,
            'total_pool_size': total_pool_size,
            'total_memory_mb': total_memory,
            'avg_hit_rate': avg_hit_rate,
            'pool_types': len(self.pools),
            'pools': all_stats
        }


class MemoryPoolManager:
    """内存池管理器"""
    
    def __init__(self):
        self.pools: Dict[str, Any] = {}
        self.running = False
        self.monitoring_task: Optional[asyncio.Task] = None
    
    def register_pool(self, name: str, pool: Any):
        """注册内存池"""
        self.pools[name] = pool
        logger.info(f"注册内存池: {name}")
    
    def get_pool(self, name: str) -> Optional[Any]:
        """获取内存池"""
        return self.pools.get(name)
    
    async def start_monitoring(self):
        """启动监控"""
        if not self.running:
            self.running = True
            self.monitoring_task = asyncio.create_task(self._monitoring_loop())
            logger.info("内存池监控已启动")
    
    async def stop_monitoring(self):
        """停止监控"""
        if self.running:
            self.running = False
            if self.monitoring_task:
                self.monitoring_task.cancel()
                try:
                    await self.monitoring_task
                except asyncio.CancelledError:
                    pass
            logger.info("内存池监控已停止")
    
    async def _monitoring_loop(self):
        """监控循环"""
        while self.running:
            try:
                await asyncio.sleep(30)  # 每30秒监控一次
                
                for pool_name, pool in self.pools.items():
                    if hasattr(pool, 'get_summary_stats'):
                        stats = pool.get_summary_stats()
                        
                        # 记录指标
                        metrics.gauge(f'memory_pool_{pool_name}_allocated', stats['total_allocated'])
                        metrics.gauge(f'memory_pool_{pool_name}_active', stats['total_active'])
                        metrics.gauge(f'memory_pool_{pool_name}_size', stats['total_pool_size'])
                        metrics.gauge(f'memory_pool_{pool_name}_memory_mb', stats['total_memory_mb'])
                        metrics.gauge(f'memory_pool_{pool_name}_hit_rate', stats['avg_hit_rate'])
                        
                        logger.info(f"内存池 {pool_name} 统计: "
                                   f"已分配={stats['total_allocated']}, "
                                   f"活跃={stats['total_active']}, "
                                   f"池大小={stats['total_pool_size']}, "
                                   f"内存={stats['total_memory_mb']:.2f}MB, "
                                   f"命中率={stats['avg_hit_rate']:.2%}")
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"监控循环错误: {e}")


# 全局内存池管理器实例
memory_pool_manager = MemoryPoolManager()


def get_memory_pool_manager() -> MemoryPoolManager:
    """获取全局内存池管理器"""
    return memory_pool_manager


# 便捷函数
def create_trade_signal_pool(config: Optional[PoolConfig] = None) -> TradeSignalPool:
    """创建交易信号内存池"""
    return TradeSignalPool(config)