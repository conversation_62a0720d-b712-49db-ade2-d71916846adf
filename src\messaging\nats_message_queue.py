#!/usr/bin/env python3
# 🚨 AUTO-MIGRATED: Old messaging components replaced with HybridQueueManager
# See MIGRATION_GUIDE.md for details

"""
NATS消息队列实现
基于现有的JetStreamClient，提供统一的MessageQueue接口
"""

import asyncio
import json
import time
from typing import Dict, Any, Optional, List, Callable

from .message_queue_interface import MessageQueueInterface, QueueConfig, QueueStatus
from .message_types import MessageEnvelope
from .priority_queue import MessagePriority
# Avoid circular import - TYPE_CHECKING only
from typing import TYPE_CHECKING
if TYPE_CHECKING:
    from .hybrid_queue_manager import HybridQueueManager
from ..utils.logger import get_logger

logger = get_logger(__name__)


class NATSMessageQueue(MessageQueueInterface):
    """
    NATS消息队列实现
    
    基于HybridQueueManager统一接口，实现NATS后端
    支持优先级、批量操作、故障恢复等功能
    """
    
    def __init__(self, config: QueueConfig, queue_manager: Optional['HybridQueueManager'] = None):
        super().__init__(config)
        self.queue_manager = queue_manager
        self._connected = False
        self._subscriptions: Dict[str, Any] = {}
        
        # 从配置中提取NATS连接参数
        self.nats_config = config.connection_params.get('nats', {})
        self.host_id = self.nats_config.get('host_id', 'default')
        
        # 优先级主题映射
        self._priority_subjects = {
            MessagePriority.SYSTEM_CRITICAL: "MT5.SIGNALS.SYSTEM_CRITICAL",
            MessagePriority.RISK_COMMAND: "MT5.SIGNALS.RISK_COMMAND", 
            MessagePriority.SIGNAL_EXECUTION: "MT5.SIGNALS.SIGNAL_EXECUTION",
            MessagePriority.REALTIME_QUERY: "MT5.SIGNALS.REALTIME_QUERY",
            MessagePriority.BACKGROUND_TASK: "MT5.SIGNALS.BACKGROUND_TASK"
        }
    
    async def connect(self) -> bool:
        """连接到NATS JetStream"""
        try:
            logger.info("正在连接NATS JetStream...")
            
            # 如果没有提供HybridQueueManager，创建一个新的
            if not self.queue_manager:
                # Import at runtime to avoid circular import
                from .hybrid_queue_manager import HybridQueueManager
                self.queue_manager = HybridQueueManager(self.config)
            
            # 连接队列管理器
            if not await self.queue_manager.is_connected():
                if not await self.queue_manager.connect():
                    logger.error("HybridQueueManager连接失败")
                    self.status = QueueStatus.FAILED
                    return False
            
            self._connected = True
            self.status = QueueStatus.CONNECTED
            logger.info("NATS MessageQueue连接成功")
            return True
            
        except Exception as e:
            logger.error(f"NATS连接失败: {e}")
            self.status = QueueStatus.FAILED
            return False
    
    async def disconnect(self) -> None:
        """断开NATS连接"""
        try:
            if self.queue_manager and await self.queue_manager.is_connected():
                await self.queue_manager.disconnect()
            
            self._connected = False
            self.status = QueueStatus.DISCONNECTED
            self._subscriptions.clear()
            
            logger.info("NATS MessageQueue已断开连接")
            
        except Exception as e:
            logger.error(f"断开NATS连接失败: {e}")
    
    async def publish(
        self, 
        subject: str, 
        message: MessageEnvelope,
        priority: MessagePriority = MessagePriority.REALTIME_QUERY
    ) -> bool:
        """发布消息到NATS"""
        if not self._connected or not self.queue_manager:
            logger.error("NATS未连接")
            return False
        
        try:
            start_time = time.perf_counter()
            
            # 根据优先级调整主题
            priority_subject = self._get_priority_subject(subject, priority)
            
            # 准备消息数据
            message_data = {
                "id": message.id,
                "subject": subject,
                "original_subject": subject,  # 保留原始主题
                "payload": message.payload,
                "headers": message.headers,
                "timestamp": message.timestamp,
                "priority": priority.name,
                "ttl": message.ttl
            }
            
            # 使用HybridQueueManager发布消息
            success = await self.queue_manager.publish(priority_subject, message, priority)
            
            # 更新指标
            latency_ms = (time.perf_counter() - start_time) * 1000
            self._update_metrics("publish", success, latency_ms)
            
            if success:
                logger.debug(f"NATS消息发布成功: {priority_subject} (优先级: {priority.name})")
            else:
                logger.warning(f"NATS消息发布失败: {priority_subject}")
            
            return success
            
        except Exception as e:
            logger.error(f"NATS发布消息失败: {e}")
            self._update_metrics("publish", False)
            return False
    
    async def publish_batch(
        self, 
        messages: List[tuple[str, MessageEnvelope, MessagePriority]]
    ) -> int:
        """批量发布消息"""
        if not self._connected or not self.queue_manager:
            logger.error("NATS未连接")
            return 0
        
        success_count = 0
        
        try:
            # 创建批量发布任务
            tasks = []
            for subject, message, priority in messages:
                task = self.publish(subject, message, priority)
                tasks.append(task)
            
            # 并行执行
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # 统计成功数量
            for result in results:
                if isinstance(result, bool) and result:
                    success_count += 1
                elif isinstance(result, Exception):
                    logger.error(f"批量发布消息失败: {result}")
            
            logger.debug(f"NATS批量发布完成: {success_count}/{len(messages)}")
            
        except Exception as e:
            logger.error(f"NATS批量发布失败: {e}")
        
        return success_count
    
    async def subscribe(
        self, 
        subject: str, 
        callback: Callable[[MessageEnvelope], None],
        queue_group: Optional[str] = None
    ) -> bool:
        """订阅NATS消息"""
        if not self._connected or not self.queue_manager:
            logger.error("NATS未连接")
            return False
        
        try:
            # 包装回调函数以适配NATS格式
            async def nats_callback(msg):
                try:
                    # 解析NATS消息
                    data = json.loads(msg.data.decode('utf-8'))
                    
                    # 创建MessageEnvelope
                    envelope = MessageEnvelope(
                        id=data.get('id', ''),
                        subject=data.get('original_subject', subject),
                        payload=data.get('payload', {}),
                        headers=data.get('headers', {}),
                        timestamp=data.get('timestamp', time.time()),
                        ttl=data.get('ttl')
                    )
                    
                    # 调用用户回调
                    if asyncio.iscoroutinefunction(callback):
                        await callback(envelope)
                    else:
                        callback(envelope)
                    
                    # 确认消息
                    await msg.ack()
                    self._update_metrics("receive", True)
                    
                except Exception as e:
                    logger.error(f"NATS消息处理失败: {e}")
                    await msg.nak()
                    self._update_metrics("receive", False)
            
            # 订阅所有优先级主题
            subscription_keys = []
            for priority in MessagePriority:
                priority_subject = self._get_priority_subject(subject, priority)
                
                # 使用HybridQueueManager订阅
                success = await self.queue_manager.subscribe(priority_subject, nats_callback, queue_group)
                
                if success:
                    consumer_key = f"{priority_subject}_{queue_group or 'broadcast'}"
                    subscription_keys.append(consumer_key)
                    logger.debug(f"NATS订阅成功: {priority_subject}")
            
            if subscription_keys:
                self._subscriptions[subject] = subscription_keys
                return True
            else:
                logger.error(f"NATS订阅失败: {subject}")
                return False
                
        except Exception as e:
            logger.error(f"NATS订阅失败: {e}")
            return False
    
    async def unsubscribe(self, subject: str) -> bool:
        """取消NATS订阅"""
        if subject not in self._subscriptions:
            return True
        
        try:
            subscription_keys = self._subscriptions[subject]
            
            for consumer_key in subscription_keys:
                # 从consumer_key中提取subject和queue_group
                parts = consumer_key.rsplit('_', 1)
                if len(parts) == 2:
                    subject_part, queue_group = parts
                    await self.queue_manager.unsubscribe(subject_part, queue_group if queue_group != 'broadcast' else None) 
            
            del self._subscriptions[subject]
            logger.info(f"NATS取消订阅成功: {subject}")
            return True
            
        except Exception as e:
            logger.error(f"NATS取消订阅失败: {e}")
            return False
    
    async def request(
        self, 
        subject: str, 
        message: MessageEnvelope,
        timeout_ms: int = 5000
    ) -> Optional[MessageEnvelope]:
        """NATS请求-响应模式"""
        if not self._connected or not self.queue_manager:
            logger.error("NATS未连接")
            return None
        
        try:
            # NATS JetStream主要用于发布-订阅，不直接支持请求-响应
            # 这里可以通过临时订阅实现
            response_subject = f"{subject}.response.{int(time.time() * 1000)}"
            response_received = None
            response_event = asyncio.Event()
            
            # 临时订阅响应
            async def response_callback(response_msg):
                nonlocal response_received
                response_received = response_msg
                response_event.set()
            
            await self.subscribe(response_subject, response_callback)
            
            # 发送请求（包含响应主题）
            message.headers['reply_to'] = response_subject
            await self.publish(subject, message)
            
            # 等待响应
            try:
                await asyncio.wait_for(response_event.wait(), timeout=timeout_ms / 1000)
                return response_received
            except asyncio.TimeoutError:
                logger.warning(f"NATS请求超时: {subject}")
                return None
            finally:
                # 清理临时订阅
                await self.unsubscribe(response_subject)
                
        except Exception as e:
            logger.error(f"NATS请求失败: {e}")
            return None
    
    async def get_queue_depth(self, subject: str) -> int:
        """获取队列深度"""
        if not self._connected or not self.queue_manager:
            return 0
        
        try:
            # 通过JetStream API获取流信息
            total_messages = 0
            for priority in MessagePriority:
                priority_subject = self._get_priority_subject(subject, priority)
                stream_name = self._get_stream_for_subject(priority_subject)
                if stream_name:
                    stream_info = await self.queue_manager.get_stream_info(stream_name)
                    if stream_info:
                        total_messages += stream_info.get('messages', 0)
            
            self.metrics.queue_depth = total_messages
            return total_messages
            
        except Exception as e:
            logger.error(f"获取NATS队列深度失败: {e}")
            return 0
    
    async def purge_queue(self, subject: str) -> bool:
        """清空队列"""
        if not self._connected or not self.queue_manager:
            return False
        
        try:
            success = True
            for priority in MessagePriority:
                priority_subject = self._get_priority_subject(subject, priority)
                stream_name = self._get_stream_for_subject(priority_subject)
                if stream_name:
                    if not await self.queue_manager.purge_stream(stream_name):
                        success = False
            
            if success:
                logger.info(f"NATS队列清空成功: {subject}")
            else:
                logger.warning(f"NATS队列清空部分失败: {subject}")
            
            return success
            
        except Exception as e:
            logger.error(f"清空NATS队列失败: {e}")
            return False
    
    def _get_priority_subject(self, base_subject: str, priority: MessagePriority) -> str:
        """根据优先级获取主题名称"""
        if priority in self._priority_subjects:
            return f"{self._priority_subjects[priority]}.{base_subject}"
        else:
            return f"MT5.SIGNALS.{priority.name}.{base_subject}"
    
    def _get_stream_for_subject(self, subject: str) -> Optional[str]:
        """根据主题获取对应的流名称"""
        if "MT5.LOCAL." in subject:
            return f"MT5_LOCAL_{self.host_id}"
        elif "MT5.SIGNALS." in subject or "MT5.COPY." in subject:
            return "MT5_SIGNALS"
        elif "MT5.RPC." in subject:
            return "MT5_RPC"
        elif "MT5.CONTROL." in subject or "MT5.MONITOR." in subject:
            return "MT5_CONTROL"
        else:
            return "MT5_SIGNALS"  # 默认流
    
    async def health_check(self) -> bool:
        """NATS健康检查"""
        if not self._connected or not self.queue_manager:
            return False
        
        try:
            # 使用HybridQueueManager的健康检查
            return await self.queue_manager.health_check()
        except Exception:
            return False