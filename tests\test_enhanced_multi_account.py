#!/usr/bin/env python3
"""
增强的多账户并发测试
测试5-10个并发账户，验证资源使用情况，测试负载均衡
"""

import asyncio
import json
import os
import sys
import tempfile
import time
import psutil
import threading
from pathlib import Path
from typing import Dict, Any, List
from dataclasses import dataclass

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# 导入真实组件
try:
    from src.core.mt5_account_monitor import MT5AccountMonitor
    from src.core.mt5_account_executor import MT5AccountExecutor
    from src.core.mt5_rpc_client import MT5RPCClient
    from src.messaging.jetstream_client import JetStreamClient
    from src.messaging.message_types import MessageEnvelope, TradeSignal, SignalType, PositionSignalData
    
    REAL_COMPONENTS_AVAILABLE = True
    print("✅ 增强多账户测试组件导入成功")
    
except ImportError as e:
    print(f"❌ 导入组件失败: {e}")
    sys.exit(1)


@dataclass
class ResourceMetrics:
    """资源使用指标"""
    timestamp: float
    cpu_percent: float
    memory_mb: float
    memory_percent: float
    thread_count: int
    connection_count: int


@dataclass
class PerformanceMetrics:
    """性能指标"""
    account_id: str
    messages_sent: int
    messages_received: int
    avg_latency_ms: float
    max_latency_ms: float
    error_count: int
    throughput_msg_per_sec: float


class ResourceMonitor:
    """资源监控器"""
    
    def __init__(self):
        self.metrics = []
        self.monitoring = False
        self.monitor_thread = None
    
    def start_monitoring(self, interval: float = 1.0):
        """开始监控"""
        self.monitoring = True
        self.monitor_thread = threading.Thread(
            target=self._monitor_loop,
            args=(interval,),
            daemon=True
        )
        self.monitor_thread.start()
        print("  📊 资源监控已启动")
    
    def stop_monitoring(self):
        """停止监控"""
        self.monitoring = False
        if self.monitor_thread:
            self.monitor_thread.join(timeout=2)
        print("  📊 资源监控已停止")
    
    def _monitor_loop(self, interval: float):
        """监控循环"""
        process = psutil.Process()
        
        while self.monitoring:
            try:
                # 获取CPU和内存使用率
                cpu_percent = process.cpu_percent()
                memory_info = process.memory_info()
                memory_mb = memory_info.rss / 1024 / 1024
                memory_percent = process.memory_percent()
                
                # 获取线程数
                thread_count = process.num_threads()
                
                # 获取连接数（近似）
                try:
                    connection_count = len(process.connections())
                except (psutil.AccessDenied, psutil.NoSuchProcess):
                    connection_count = 0
                
                metric = ResourceMetrics(
                    timestamp=time.time(),
                    cpu_percent=cpu_percent,
                    memory_mb=memory_mb,
                    memory_percent=memory_percent,
                    thread_count=thread_count,
                    connection_count=connection_count
                )
                
                self.metrics.append(metric)
                
                # 保持最近1000个指标
                if len(self.metrics) > 1000:
                    self.metrics = self.metrics[-1000:]
                
                time.sleep(interval)
                
            except Exception as e:
                print(f"    ⚠️ 资源监控错误: {e}")
                time.sleep(interval)
    
    def get_summary(self) -> Dict[str, Any]:
        """获取资源使用总结"""
        if not self.metrics:
            return {}
        
        cpu_values = [m.cpu_percent for m in self.metrics]
        memory_values = [m.memory_mb for m in self.metrics]
        thread_values = [m.thread_count for m in self.metrics]
        connection_values = [m.connection_count for m in self.metrics]
        
        return {
            'duration_seconds': self.metrics[-1].timestamp - self.metrics[0].timestamp,
            'cpu_usage': {
                'avg': sum(cpu_values) / len(cpu_values),
                'max': max(cpu_values),
                'min': min(cpu_values)
            },
            'memory_usage': {
                'avg_mb': sum(memory_values) / len(memory_values),
                'max_mb': max(memory_values),
                'min_mb': min(memory_values)
            },
            'thread_count': {
                'avg': sum(thread_values) / len(thread_values),
                'max': max(thread_values),
                'min': min(thread_values)
            },
            'connection_count': {
                'avg': sum(connection_values) / len(connection_values),
                'max': max(connection_values),
                'min': min(connection_values)
            },
            'sample_count': len(self.metrics)
        }


class LoadBalancer:
    """负载均衡器"""
    
    def __init__(self):
        self.account_loads = {}
        self.round_robin_index = 0
    
    def update_load(self, account_id: str, load_metric: float):
        """更新账户负载"""
        self.account_loads[account_id] = {
            'load': load_metric,
            'timestamp': time.time()
        }
    
    def get_least_loaded_account(self, account_ids: List[str]) -> str:
        """获取负载最小的账户"""
        if not account_ids:
            return None
        
        # 如果没有负载信息，使用轮询
        if not self.account_loads:
            return self._round_robin_select(account_ids)
        
        # 找到负载最小的账户
        min_load = float('inf')
        selected_account = None
        
        for account_id in account_ids:
            if account_id in self.account_loads:
                load = self.account_loads[account_id]['load']
                if load < min_load:
                    min_load = load
                    selected_account = account_id
        
        return selected_account or self._round_robin_select(account_ids)
    
    def _round_robin_select(self, account_ids: List[str]) -> str:
        """轮询选择"""
        if not account_ids:
            return None
        
        selected = account_ids[self.round_robin_index % len(account_ids)]
        self.round_robin_index += 1
        return selected
    
    def get_load_distribution(self) -> Dict[str, Any]:
        """获取负载分布"""
        if not self.account_loads:
            return {}
        
        loads = [info['load'] for info in self.account_loads.values()]
        
        return {
            'account_count': len(self.account_loads),
            'avg_load': sum(loads) / len(loads),
            'max_load': max(loads),
            'min_load': min(loads),
            'load_variance': sum((load - sum(loads) / len(loads)) ** 2 for load in loads) / len(loads),
            'accounts': dict(self.account_loads)
        }


class EnhancedMultiAccountTest:
    """增强的多账户并发测试类"""
    
    def __init__(self):
        self.test_data_dir = Path(tempfile.mkdtemp(prefix="enhanced_multi_account_test_"))
        self.jetstream_clients = []
        self.monitors = []
        self.executors = []
        self.resource_monitor = ResourceMonitor()
        self.load_balancer = LoadBalancer()
        self.performance_metrics = []
        print(f"测试数据目录: {self.test_data_dir}")
    
    def cleanup(self):
        """清理测试资源"""
        import shutil
        try:
            # 停止资源监控
            self.resource_monitor.stop_monitoring()
            
            # 停止所有监控器
            for monitor in self.monitors:
                if hasattr(monitor, 'running'):
                    monitor.running = False
            
            # 停止所有执行器
            for executor in self.executors:
                if hasattr(executor, 'running'):
                    executor.running = False
            
            # 断开所有JetStream连接
            for client in self.jetstream_clients:
                if hasattr(client, 'disconnect'):
                    asyncio.create_task(client.disconnect())
            
            shutil.rmtree(self.test_data_dir)
            print("🧹 增强多账户测试环境清理完成")
        except Exception as e:
            print(f"⚠️ 清理警告: {e}")
    
    async def setup_multi_account_infrastructure(self, account_count: int = 8):
        """设置多账户基础设施"""
        print(f"\n🏗️ 设置 {account_count} 个账户的增强基础设施...")
        
        try:
            # 为每个账户创建专用的JetStream客户端
            for i in range(account_count):
                account_id = f"ENHANCED_ACC_{i+1:03d}"
                
                # 监控器客户端
                monitor_config = {
                    'servers': ['nats://localhost:4222'],
                    'stream_name': f'ENHANCED_MONITOR_{account_id}',
                    'subjects': [f'ENHANCED.MONITOR.{account_id}.>']
                }
                
                monitor_client = JetStreamClient(monitor_config)
                connected = await monitor_client.connect()
                
                if not connected:
                    print(f"  ❌ 账户 {account_id} 监控器连接失败")
                    return False
                
                self.jetstream_clients.append(monitor_client)
                
                # 执行器客户端
                executor_config = {
                    'servers': ['nats://localhost:4222'],
                    'stream_name': f'ENHANCED_EXECUTOR_{account_id}',
                    'subjects': [f'ENHANCED.EXECUTOR.{account_id}.>']
                }
                
                executor_client = JetStreamClient(executor_config)
                await executor_client.connect()
                self.jetstream_clients.append(executor_client)
                
                print(f"  ✅ 账户 {account_id} 基础设施设置完成")
            
            print(f"  ✅ {account_count} 个账户基础设施全部设置完成")
            return True
            
        except Exception as e:
            print(f"  ❌ 多账户基础设施设置失败: {e}")
            return False
    
    async def test_concurrent_accounts_with_monitoring(self, account_count: int = 8):
        """测试并发账户并监控资源使用"""
        print(f"\n👥 测试 {account_count} 个并发账户并监控资源使用...")
        
        # 设置基础设施
        if not await self.setup_multi_account_infrastructure(account_count):
            return False
        
        try:
            # 启动资源监控
            self.resource_monitor.start_monitoring(interval=0.5)
            
            # 创建RPC客户端
            rpc_config = {
                'servers': ['nats://localhost:4222'],
                'stream_name': 'ENHANCED_MULTI_RPC_CLIENT'
            }
            rpc_jetstream = JetStreamClient(rpc_config)
            await rpc_jetstream.connect()
            self.jetstream_clients.append(rpc_jetstream)
            
            rpc_client = MT5RPCClient(rpc_jetstream)
            
            # 创建多个监控器和执行器
            for i in range(account_count):
                account_id = f"ENHANCED_MONITOR_{i+1:03d}"
                
                account_config = {
                    'login': f'{80000 + i}',
                    'server': 'EnhancedMulti-Server',
                    'password': f'enhanced_password_{i}'
                }
                
                monitor_client = self.jetstream_clients[i * 2]
                
                monitor = MT5AccountMonitor(
                    account_id=account_id,
                    account_config=account_config,
                    event_publisher=monitor_client,
                    rpc_client=rpc_client,
                    host_id=f'enhanced_multi_host_{i}'
                )
                
                self.monitors.append(monitor)
                
                # 创建对应的执行器
                executor_account_id = f"ENHANCED_EXECUTOR_{i+1:03d}"
                executor_config = {
                    'login': f'{90000 + i}',
                    'server': 'EnhancedMulti-Server',
                    'password': f'enhanced_exec_password_{i}'
                }
                
                executor_client = self.jetstream_clients[i * 2 + 1]
                
                executor = MT5AccountExecutor(
                    account_id=executor_account_id,
                    account_config=executor_config,
                    command_subscriber=executor_client,
                    result_publisher=executor_client,
                    rpc_client=rpc_client
                )
                
                self.executors.append(executor)
                
                print(f"  ✅ 账户对 {i+1} 创建成功: {account_id} + {executor_account_id}")
            
            # 并发账户任务
            async def account_workload_task(account_index: int, duration: int = 10):
                """单个账户的工作负载任务"""
                monitor = self.monitors[account_index]
                executor = self.executors[account_index]
                
                operations = 0
                errors = 0
                latencies = []
                start_time = time.time()
                
                try:
                    while time.time() - start_time < duration:
                        operation_start = time.time()
                        
                        try:
                            # 监控器操作
                            monitor_data = {
                                'account_id': monitor.account_id,
                                'balance': 10000.0 + operations * 0.1,
                                'equity': 10000.0 + operations * 0.1,
                                'timestamp': time.time(),
                                'operation_count': operations
                            }
                            
                            # 模拟发布监控数据
                            await monitor.event_publisher.publish(
                                subject=f'ENHANCED.MONITOR.{monitor.account_id}.DATA',
                                data=monitor_data
                            )
                            
                            # 执行器操作
                            execution_result = {
                                'account_id': executor.account_id,
                                'operation': f'exec_op_{operations}',
                                'timestamp': time.time(),
                                'status': 'completed'
                            }
                            
                            # 模拟发布执行结果
                            await executor.result_publisher.publish(
                                subject=f'ENHANCED.EXECUTOR.{executor.account_id}.RESULT',
                                data=execution_result
                            )
                            
                            operations += 1
                            
                            # 记录延迟
                            latency = (time.time() - operation_start) * 1000
                            latencies.append(latency)
                            
                            # 更新负载均衡器
                            load_metric = latency  # 使用延迟作为负载指标
                            self.load_balancer.update_load(monitor.account_id, load_metric)
                            
                            await asyncio.sleep(0.1)  # 100ms间隔
                            
                        except Exception as e:
                            errors += 1
                            print(f"    ⚠️ 账户 {account_index} 操作错误: {e}")
                    
                    # 计算性能指标
                    duration_actual = time.time() - start_time
                    avg_latency = sum(latencies) / len(latencies) if latencies else 0
                    max_latency = max(latencies) if latencies else 0
                    throughput = operations / duration_actual
                    
                    performance = PerformanceMetrics(
                        account_id=monitor.account_id,
                        messages_sent=operations * 2,  # 监控器 + 执行器
                        messages_received=operations * 2,
                        avg_latency_ms=avg_latency,
                        max_latency_ms=max_latency,
                        error_count=errors,
                        throughput_msg_per_sec=throughput * 2
                    )
                    
                    self.performance_metrics.append(performance)
                    
                    return {
                        'account_index': account_index,
                        'operations': operations,
                        'errors': errors,
                        'avg_latency_ms': avg_latency,
                        'throughput': throughput
                    }
                    
                except Exception as e:
                    return {
                        'account_index': account_index,
                        'error': str(e),
                        'operations': operations,
                        'errors': errors + 1
                    }
            
            # 启动所有账户任务
            print(f"  🚀 启动 {len(self.monitors)} 个并发账户任务...")
            
            account_tasks = [
                account_workload_task(i, duration=8)
                for i in range(len(self.monitors))
            ]
            
            start_time = time.time()
            results = await asyncio.gather(*account_tasks)
            end_time = time.time()
            
            # 停止资源监控
            self.resource_monitor.stop_monitoring()
            
            # 分析结果
            successful_accounts = [r for r in results if 'error' not in r]
            total_operations = sum(r['operations'] for r in successful_accounts)
            total_errors = sum(r['errors'] for r in results)
            
            # 获取资源使用总结
            resource_summary = self.resource_monitor.get_summary()
            
            # 获取负载分布
            load_distribution = self.load_balancer.get_load_distribution()
            
            print(f"  📊 并发账户测试结果:")
            print(f"    成功账户: {len(successful_accounts)}/{len(self.monitors)}")
            print(f"    总操作数: {total_operations}")
            print(f"    总错误数: {total_errors}")
            print(f"    总耗时: {end_time - start_time:.2f}秒")
            
            if resource_summary:
                print(f"  📊 资源使用情况:")
                print(f"    平均CPU: {resource_summary['cpu_usage']['avg']:.1f}%")
                print(f"    峰值CPU: {resource_summary['cpu_usage']['max']:.1f}%")
                print(f"    平均内存: {resource_summary['memory_usage']['avg_mb']:.1f}MB")
                print(f"    峰值内存: {resource_summary['memory_usage']['max_mb']:.1f}MB")
                print(f"    平均线程数: {resource_summary['thread_count']['avg']:.0f}")
                print(f"    峰值线程数: {resource_summary['thread_count']['max']}")
            
            if load_distribution:
                print(f"  📊 负载均衡情况:")
                print(f"    平均负载: {load_distribution['avg_load']:.2f}ms")
                print(f"    负载方差: {load_distribution['load_variance']:.2f}")
                print(f"    负载范围: {load_distribution['min_load']:.2f}-{load_distribution['max_load']:.2f}ms")
            
            # 成功条件：80%账户成功，资源使用合理
            success_rate = len(successful_accounts) / len(self.monitors)
            resource_reasonable = (
                resource_summary.get('cpu_usage', {}).get('avg', 100) < 80 and
                resource_summary.get('memory_usage', {}).get('avg_mb', 1000) < 500
            )
            
            return success_rate >= 0.8 and resource_reasonable
            
        except Exception as e:
            print(f"  ❌ 并发账户监控测试失败: {e}")
            return False
    
    async def test_load_balancing(self):
        """测试负载均衡"""
        print(f"\n⚖️ 测试负载均衡...")
        
        try:
            # 模拟不同负载的账户
            account_ids = [f"BALANCE_TEST_{i:03d}" for i in range(5)]
            
            # 设置不同的负载
            loads = [10.5, 25.3, 15.8, 30.2, 8.7]  # 模拟延迟(ms)
            
            for account_id, load in zip(account_ids, loads):
                self.load_balancer.update_load(account_id, load)
            
            print(f"  ✅ 设置了 {len(account_ids)} 个账户的负载信息")
            
            # 测试负载均衡选择
            selections = []
            for _ in range(100):
                selected = self.load_balancer.get_least_loaded_account(account_ids)
                selections.append(selected)
            
            # 统计选择分布
            selection_counts = {}
            for account_id in account_ids:
                selection_counts[account_id] = selections.count(account_id)
            
            print(f"  📊 负载均衡选择分布:")
            for account_id, count in selection_counts.items():
                load = next(load for aid, load in zip(account_ids, loads) if aid == account_id)
                print(f"    {account_id}: {count}次 (负载: {load}ms)")
            
            # 验证负载最小的账户被选择最多
            min_load_account = min(zip(account_ids, loads), key=lambda x: x[1])[0]
            max_selections = max(selection_counts.values())
            most_selected = [aid for aid, count in selection_counts.items() if count == max_selections][0]
            
            load_balancing_effective = (min_load_account == most_selected)
            
            print(f"  📊 负载均衡效果: {'✅ 有效' if load_balancing_effective else '❌ 无效'}")
            print(f"    最低负载账户: {min_load_account}")
            print(f"    最多选择账户: {most_selected}")
            
            return load_balancing_effective
            
        except Exception as e:
            print(f"  ❌ 负载均衡测试失败: {e}")
            return False
    
    async def run_all_tests(self):
        """运行所有增强多账户测试"""
        print("🚀 开始增强多账户并发测试...")
        
        test_results = {}
        
        # 运行各项测试
        test_results['concurrent_accounts_monitoring'] = await self.test_concurrent_accounts_with_monitoring(8)
        test_results['load_balancing'] = await self.test_load_balancing()
        
        # 统计结果
        passed = sum(1 for result in test_results.values() if result)
        total = len(test_results)
        success_rate = (passed / total) * 100
        
        print(f"\n📊 增强多账户并发测试结果:")
        print(f"总测试数: {total}")
        print(f"通过: {passed}")
        print(f"失败: {total - passed}")
        print(f"成功率: {success_rate:.1f}%")
        
        print(f"\n📋 详细结果:")
        for test_name, result in test_results.items():
            status = "✅ 通过" if result else "❌ 失败"
            print(f"  {test_name}: {status}")
        
        # 输出性能总结
        if self.performance_metrics:
            print(f"\n📈 性能总结:")
            total_throughput = sum(m.throughput_msg_per_sec for m in self.performance_metrics)
            avg_latency = sum(m.avg_latency_ms for m in self.performance_metrics) / len(self.performance_metrics)
            total_errors = sum(m.error_count for m in self.performance_metrics)
            
            print(f"  总吞吐量: {total_throughput:.0f} 消息/秒")
            print(f"  平均延迟: {avg_latency:.2f}ms")
            print(f"  总错误数: {total_errors}")
        
        return success_rate >= 50  # 50%以上算成功


async def main():
    """主函数"""
    if not REAL_COMPONENTS_AVAILABLE:
        print("❌ 增强多账户测试组件不可用，无法运行测试")
        return False
    
    test_suite = EnhancedMultiAccountTest()
    
    try:
        success = await test_suite.run_all_tests()
        
        if success:
            print("\n🎉 增强多账户并发测试成功!")
        else:
            print("\n⚠️ 增强多账户并发测试部分失败")
        
        return success
        
    finally:
        test_suite.cleanup()


if __name__ == '__main__':
    result = asyncio.run(main())
    sys.exit(0 if result else 1)
