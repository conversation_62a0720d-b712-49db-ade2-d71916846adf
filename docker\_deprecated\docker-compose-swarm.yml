version: '3.8'

services:
  nats:
    image: nats:2.10-alpine
    ports:
      - "4222:4222"
      - "8222:8222"
    volumes:
      - ./nats.conf:/etc/nats/nats.conf:ro
      - nats-data:/data
      - nats-logs:/logs
    command: ["-c", "/etc/nats/nats.conf"]
    deploy:
      replicas: 3
      placement:
        constraints:
          - node.role == manager
    networks:
      - mt5-network

  redis-master:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis-data:/data
      - ./redis.conf:/usr/local/etc/redis/redis.conf
    command: redis-server /usr/local/etc/redis/redis.conf
    deploy:
      replicas: 1
      placement:
        constraints:
          - node.role == manager
    networks:
      - mt5-network

  redis-sentinel:
    image: redis:7-alpine
    ports:
      - "26379:26379"
    volumes:
      - ./redis-sentinel.conf:/usr/local/etc/redis/sentinel.conf
    command: redis-sentinel /usr/local/etc/redis/sentinel.conf
    deploy:
      replicas: 3
    networks:
      - mt5-network
    depends_on:
      - redis-master

  mt5-api:
    image: mt5-copier:latest
    environment:
      - NATS_URL=nats://nats:4222
      - REDIS_URL=redis://redis-master:6379
      - MT5_ACC001_PASSWORD=${MT5_ACC001_PASSWORD}
      - MT5_ACC002_PASSWORD=${MT5_ACC002_PASSWORD}
      - MT5_ACC003_PASSWORD=${MT5_ACC003_PASSWORD}
    deploy:
      replicas: 2
      placement:
        max_replicas_per_node: 1
    networks:
      - mt5-network
    depends_on:
      - nats
      - redis-master

volumes:
  nats-data:
  nats-logs:
  redis-data:

networks:
  mt5-network:
    driver: overlay
    attachable: true