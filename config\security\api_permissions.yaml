# API权限配置
# 定义API端点的访问权限和认证策略

# 权限角色定义
roles:
  # 超级管理员
  super_admin:
    name: "Super Administrator"
    description: "Full system access with all permissions"
    permissions: ["*"]  # 通配符表示所有权限
    
  # 系统管理员
  admin:
    name: "Administrator"
    description: "System administration access"
    permissions:
      - "system:read"
      - "system:write"
      - "accounts:read"
      - "accounts:write"
      - "trading:read"
      - "trading:write"
      - "monitoring:read"
      - "monitoring:write"
      - "logs:read"
      
  # 交易员
  trader:
    name: "Trader"
    description: "Trading operations access"
    permissions:
      - "accounts:read"
      - "trading:read"
      - "trading:write"
      - "positions:read"
      - "orders:read"
      - "orders:write"
      - "signals:read"
      
  # 分析师
  analyst:
    name: "Analyst"
    description: "Read-only access for analysis"
    permissions:
      - "accounts:read"
      - "trading:read"
      - "positions:read"
      - "orders:read"
      - "signals:read"
      - "monitoring:read"
      - "performance:read"
      
  # 观察员
  viewer:
    name: "Viewer"
    description: "Read-only access to basic information"
    permissions:
      - "accounts:read"
      - "positions:read"
      - "monitoring:read"
      
  # API服务
  api_service:
    name: "API Service"
    description: "Service-to-service communication"
    permissions:
      - "internal:read"
      - "internal:write"
      - "health:read"
      - "metrics:read"

# API端点权限映射
endpoints:
  # 健康检查端点（公开访问）
  "/health":
    methods:
      GET:
        authentication: false
        permissions: []
        rate_limit: 100  # 每分钟请求数
        
  "/metrics":
    methods:
      GET:
        authentication: true
        permissions: ["metrics:read"]
        rate_limit: 60
        
  # 账户管理端点
  "/api/accounts":
    methods:
      GET:
        authentication: true
        permissions: ["accounts:read"]
        rate_limit: 30
      POST:
        authentication: true
        permissions: ["accounts:write"]
        rate_limit: 10
        
  "/api/accounts/{account_id}":
    methods:
      GET:
        authentication: true
        permissions: ["accounts:read"]
        rate_limit: 60
      PUT:
        authentication: true
        permissions: ["accounts:write"]
        rate_limit: 20
      DELETE:
        authentication: true
        permissions: ["accounts:write"]
        rate_limit: 5
        
  "/api/accounts/{account_id}/role":
    methods:
      POST:
        authentication: true
        permissions: ["accounts:write"]
        rate_limit: 10
        
  # 交易端点
  "/api/positions":
    methods:
      GET:
        authentication: true
        permissions: ["positions:read"]
        rate_limit: 100
        
  "/api/positions/{position_id}":
    methods:
      GET:
        authentication: true
        permissions: ["positions:read"]
        rate_limit: 100
      DELETE:
        authentication: true
        permissions: ["trading:write"]
        rate_limit: 50
        
  "/api/orders":
    methods:
      GET:
        authentication: true
        permissions: ["orders:read"]
        rate_limit: 100
      POST:
        authentication: true
        permissions: ["orders:write"]
        rate_limit: 60
        
  "/api/orders/{order_id}":
    methods:
      GET:
        authentication: true
        permissions: ["orders:read"]
        rate_limit: 100
      PUT:
        authentication: true
        permissions: ["orders:write"]
        rate_limit: 30
      DELETE:
        authentication: true
        permissions: ["orders:write"]
        rate_limit: 30
        
  # 信号执行端点
  "/api/execute/signal":
    methods:
      POST:
        authentication: true
        permissions: ["trading:write"]
        rate_limit: 120
        
  "/api/signals":
    methods:
      GET:
        authentication: true
        permissions: ["signals:read"]
        rate_limit: 200
        
  # 系统管理端点
  "/api/system/config":
    methods:
      GET:
        authentication: true
        permissions: ["system:read"]
        rate_limit: 20
      PUT:
        authentication: true
        permissions: ["system:write"]
        rate_limit: 5
        
  "/api/system/restart":
    methods:
      POST:
        authentication: true
        permissions: ["system:write"]
        rate_limit: 2
        
  # 性能监控端点
  "/api/performance":
    methods:
      GET:
        authentication: true
        permissions: ["performance:read"]
        rate_limit: 60
        
  "/api/logs":
    methods:
      GET:
        authentication: true
        permissions: ["logs:read"]
        rate_limit: 30

# 用户权限配置
users:
  # 系统用户（示例）
  system_admin:
    username: "admin"
    email: "<EMAIL>"
    roles: ["admin"]
    enabled: true
    api_key_enabled: true
    
  trader_001:
    username: "trader001"
    email: "<EMAIL>"
    roles: ["trader"]
    enabled: true
    api_key_enabled: true
    
  analyst_001:
    username: "analyst001"
    email: "<EMAIL>"
    roles: ["analyst"]
    enabled: true
    api_key_enabled: false

# API密钥配置
api_keys:
  # API密钥设置
  settings:
    key_length: 32              # 密钥长度
    expiry_days: 90             # 有效期（天）
    rotation_warning_days: 7    # 轮换警告天数
    max_keys_per_user: 3        # 每用户最大密钥数
    
  # 预定义的API密钥（生产环境应使用环境变量）
  keys:
    - key_id: "api_001"
      key_hash: "${API_KEY_001_HASH}"  # 密钥哈希值
      user: "system_admin"
      roles: ["admin"]
      enabled: true
      created_at: "2024-01-01T00:00:00Z"
      expires_at: "2024-04-01T00:00:00Z"
      
    - key_id: "api_002"
      key_hash: "${API_KEY_002_HASH}"
      user: "trader_001"
      roles: ["trader"]
      enabled: true
      created_at: "2024-01-01T00:00:00Z"
      expires_at: "2024-04-01T00:00:00Z"

# JWT配置
jwt:
  # JWT设置
  algorithm: "HS256"
  secret: "${JWT_SECRET}"
  access_token_expire_minutes: 30
  refresh_token_expire_days: 7
  
  # JWT声明
  claims:
    issuer: "mt5-trading-system"
    audience: "mt5-api"
    
  # JWT验证设置
  verify_signature: true
  verify_expiration: true
  verify_audience: true
  verify_issuer: true

# 访问控制设置
access_control:
  # IP白名单
  ip_whitelist:
    enabled: true
    addresses:
      - "127.0.0.1"          # 本地访问
      - "***********/24"     # 内网访问
      - "10.0.0.0/8"         # 私有网络
    allow_all_for_health: true  # 健康检查允许所有IP
    
  # 地理位置限制
  geo_restrictions:
    enabled: false
    allowed_countries: ["US", "GB", "SG", "HK", "JP"]
    blocked_countries: []
    
  # 时间访问限制
  time_restrictions:
    enabled: false
    allowed_hours:
      start: "06:00"
      end: "22:00"
    timezone: "UTC"
    
# 速率限制
rate_limiting:
  # 全局速率限制
  global:
    enabled: true
    requests_per_minute: 1000
    burst_size: 200
    
  # 用户级速率限制
  per_user:
    enabled: true
    requests_per_minute: 100
    burst_size: 20
    
  # IP级速率限制
  per_ip:
    enabled: true
    requests_per_minute: 200
    burst_size: 50
    
  # 端点级速率限制（在endpoints部分定义）
  
# 审计日志
audit:
  # 审计设置
  enabled: true
  log_level: "INFO"
  
  # 审计事件
  events:
    authentication: true      # 认证事件
    authorization: true       # 授权事件
    api_access: true         # API访问
    permission_changes: true  # 权限变更
    sensitive_operations: true # 敏感操作
    
  # 审计日志格式
  format:
    timestamp: true
    user_id: true
    ip_address: true
    user_agent: true
    endpoint: true
    method: true
    parameters: false        # 不记录参数（可能包含敏感信息）
    response_code: true
    
  # 审计日志存储
  storage:
    type: "file"            # file, database, syslog
    file_path: "logs/audit.log"
    rotation: true
    max_size: "100MB"
    retention_days: 365

# 安全头配置
security_headers:
  # CORS配置
  cors:
    enabled: true
    allowed_origins:
      - "http://localhost:3000"
      - "https://trading.company.com"
    allowed_methods: ["GET", "POST", "PUT", "DELETE", "OPTIONS"]
    allowed_headers: ["Authorization", "Content-Type", "X-API-Key"]
    allow_credentials: true
    max_age: 86400
    
  # 安全头
  headers:
    X-Content-Type-Options: "nosniff"
    X-Frame-Options: "DENY"
    X-XSS-Protection: "1; mode=block"
    Strict-Transport-Security: "max-age=31536000; includeSubDomains"
    Content-Security-Policy: "default-src 'self'"

# 环境特定配置
environments:
  development:
    access_control:
      ip_whitelist:
        enabled: false
    rate_limiting:
      global:
        requests_per_minute: 10000
        
  testing:
    audit:
      events:
        api_access: false
    rate_limiting:
      global:
        requests_per_minute: 5000
        
  production:
    access_control:
      ip_whitelist:
        enabled: true
    audit:
      enabled: true
    rate_limiting:
      global:
        requests_per_minute: 1000