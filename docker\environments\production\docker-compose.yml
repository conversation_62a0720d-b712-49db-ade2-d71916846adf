# MT5分布式交易系统 - 生产环境
# 高可用、高性能配置，适合生产部署

version: '3.8'

# ============================================================================
# 生产环境服务配置
# ============================================================================
services:
  # 基础设施服务（生产配置）
  redis:
    image: redis:7-alpine
    container_name: mt5-redis-prod
    ports:
      - "6379:6379"
    volumes:
      - redis_prod_data:/data
      - ../../configs/redis/redis-prod.conf:/etc/redis/redis.conf:ro
    command: redis-server /etc/redis/redis.conf
    environment:
      - REDIS_REPLICATION_MODE=master
      - REDIS_OPTIMIZATION_ENABLED=true
    sysctls:
      - net.core.somaxconn=65535
    deploy:
      resources:
        limits:
          memory: 2G
          cpus: '1.0'
        reservations:
          memory: 1G
          cpus: '0.5'
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 3
      start_period: 15s
    restart: unless-stopped
    networks:
      - mt5-prod-network

  nats:
    image: nats:2.10-alpine
    container_name: mt5-nats-prod
    ports:
      - "4222:4222"
      - "8222:8222"
      - "6222:6222"
    volumes:
      - nats_prod_data:/data
      - ../../configs/nats/nats-prod.conf:/etc/nats/nats.conf:ro
    command: ["--config=/etc/nats/nats.conf"]
    deploy:
      resources:
        limits:
          memory: 1G
          cpus: '0.5'
        reservations:
          memory: 512M
          cpus: '0.25'
    healthcheck:
      test: ["CMD", "sh", "-c", "echo 'PING' | nc localhost 4222 | grep -q PONG || exit 1"]
      interval: 15s
      timeout: 10s
      retries: 5
      start_period: 30s
    restart: unless-stopped
    networks:
      - mt5-prod-network

  # 监控服务（生产配置）
  prometheus:
    image: prom/prometheus:latest
    container_name: mt5-prometheus-prod
    ports:
      - "9090:9090"
    volumes:
      - ../../configs/prometheus/prometheus-prod.yml:/etc/prometheus/prometheus.yml:ro
      - ../../configs/prometheus/rules:/etc/prometheus/rules:ro
      - prometheus_prod_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--storage.tsdb.retention.time=7d'
      - '--web.enable-lifecycle'
      - '--web.enable-admin-api'
      - '--storage.tsdb.wal-compression'
    deploy:
      resources:
        limits:
          memory: 4G
          cpus: '2.0'
        reservations:
          memory: 2G
          cpus: '1.0'
    healthcheck:
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost:9090/-/healthy"]
      interval: 15s
      timeout: 10s
      retries: 3
      start_period: 20s
    restart: unless-stopped
    networks:
      - mt5-prod-network

  pushgateway:
    image: prom/pushgateway:latest
    container_name: mt5-pushgateway-prod
    ports:
      - "9091:9091"
    deploy:
      resources:
        limits:
          memory: 512M
          cpus: '0.5'
    healthcheck:
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost:9091/-/healthy"]
      interval: 15s
      timeout: 10s
      retries: 3
      start_period: 15s
    restart: unless-stopped
    networks:
      - mt5-prod-network

  grafana:
    image: grafana/grafana:latest
    container_name: mt5-grafana-prod
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_ADMIN_PASSWORD}
      - GF_USERS_ALLOW_SIGN_UP=false
      - GF_AUTH_ANONYMOUS_ENABLED=false
      - GF_ANALYTICS_REPORTING_ENABLED=false
      - GF_SECURITY_DISABLE_GRAVATAR=true
    volumes:
      - grafana_prod_data:/var/lib/grafana
      - ../../configs/grafana/dashboards:/var/lib/grafana/dashboards:ro
      - ../../configs/grafana/datasources:/etc/grafana/provisioning/datasources:ro
    depends_on:
      - prometheus
      - redis
    deploy:
      resources:
        limits:
          memory: 1G
          cpus: '0.5'
    healthcheck:
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost:3000/api/health"]
      interval: 15s
      timeout: 10s
      retries: 3
      start_period: 30s
    restart: unless-stopped
    networks:
      - mt5-prod-network

  # MT5应用服务（生产配置）
  mt5-coordinator:
    build:
      context: ../../..
      dockerfile: docker/Dockerfile
    image: mt5-trading-system:prod
    container_name: mt5-coordinator-prod
    ports:
      - "8000:8000"
      - "8001:8001"
    volumes:
      - ../../../config:/app/config:ro
      - ../../../logs:/app/logs
      - ../../../.env.production:/app/.env:ro
      - /etc/ssl/certs:/etc/ssl/certs:ro  # 生产环境SSL证书
    environment:
      - PYTHONPATH=/app
      - MT5_ENVIRONMENT=production
      - MT5_CONFIG_PATH=/app/config/core/system.yaml
      - HOST_ID=${MT5_HOST_ID:-production-coordinator}
      - MT5_NODE_TYPE=coordinator
      - MT5_DEPLOYMENT_MODE=production
      - MT5_DEBUG=false
      - MT5_LOG_LEVEL=INFO
      
      # 基础设施连接
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_DB=0
      - NATS_HOST=nats
      - NATS_PORT=4222
      - PROMETHEUS_PUSHGATEWAY_URL=http://pushgateway:9091
      
      # 安全配置
      - AUTH_TOKEN_SECRET=${AUTH_TOKEN_SECRET}
      - MT5_ENABLE_TLS=true
      - MT5_ENABLE_AUTH=true
      
      # 账户密码（从环境变量读取）
      - MT5_ACC001_PASSWORD=${MT5_ACC001_PASSWORD}
      - MT5_ACC002_PASSWORD=${MT5_ACC002_PASSWORD}
      - MT5_ACC003_PASSWORD=${MT5_ACC003_PASSWORD}
      
    depends_on:
      - redis
      - nats
      - pushgateway
    deploy:
      resources:
        limits:
          memory: 8G
          cpus: '4.0'
        reservations:
          memory: 4G
          cpus: '2.0'
      restart_policy:
        condition: unless-stopped
        delay: 30s
        max_attempts: 5
    networks:
      - mt5-prod-network
    labels:
      - "mt5.environment=production"
      - "mt5.service=coordinator"
      - "mt5.tier=application"
    command: ["python", "main.py", "--host-id", "${MT5_HOST_ID:-production-coordinator}", "--config", "/app/config/core/system.yaml"]
    
    # 生产环境健康检查
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8001/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 120s

  # Hash Manager（生产优化）
  redis-hash-manager:
    build:
      context: ../../..
      dockerfile: docker/Dockerfile.hash-manager
    image: mt5-hash-manager:prod
    container_name: mt5-hash-manager-prod
    ports:
      - "8082:8082"
    environment:
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_DB=0
      - HASH_MANAGER_PORT=8082
    depends_on:
      - redis
    deploy:
      resources:
        limits:
          memory: 1G
          cpus: '1.0'
    restart: unless-stopped
    networks:
      - mt5-prod-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8082/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s

# ============================================================================
# 生产环境网络
# ============================================================================
networks:
  mt5-prod-network:
    driver: bridge
    name: mt5-prod-network
    labels:
      - "mt5.environment=production"

# ============================================================================
# 生产环境数据卷
# ============================================================================
volumes:
  redis_prod_data:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: /opt/mt5-trading/data/redis
    labels:
      - "mt5.environment=production"
      - "mt5.data=redis"
      
  nats_prod_data:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: /opt/mt5-trading/data/nats
    labels:
      - "mt5.environment=production"
      - "mt5.data=nats"
      
  prometheus_prod_data:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: /opt/mt5-trading/data/prometheus
    labels:
      - "mt5.environment=production"
      - "mt5.data=prometheus"
      
  grafana_prod_data:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: /opt/mt5-trading/data/grafana
    labels:
      - "mt5.environment=production"
      - "mt5.data=grafana"