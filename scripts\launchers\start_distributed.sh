#!/bin/bash
# 分布式MT5交易系统启动脚本 (Linux版本)
# 使用方法: ./start_distributed.sh [host_id] [config_path]

set -e

# 设置默认值
DEFAULT_HOST_ID="host-001"
DEFAULT_CONFIG_PATH="config/"

# 获取参数
HOST_ID=${1:-$DEFAULT_HOST_ID}
CONFIG_PATH=${2:-$DEFAULT_CONFIG_PATH}

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_header() {
    echo -e "${BLUE}========================================${NC}"
    echo -e "${BLUE}$1${NC}"
    echo -e "${BLUE}========================================${NC}"
}

# 检查函数
check_python() {
    if ! command -v python3 &> /dev/null; then
        log_error "未找到Python3环境"
        log_error "请安装Python 3.10+: sudo apt install python3 python3-pip python3-venv"
        exit 1
    fi
    
    local python_version=$(python3 --version | cut -d' ' -f2)
    log_info "Python版本: $python_version"
}

check_venv() {
    if [ ! -d "venv" ]; then
        log_warn "未找到虚拟环境，正在创建..."
        python3 -m venv venv
        if [ $? -ne 0 ]; then
            log_error "创建虚拟环境失败"
            exit 1
        fi
    fi
}

check_dependencies() {
    log_info "检查依赖包..."
    source venv/bin/activate
    
    if ! pip show nats-py &> /dev/null; then
        log_info "安装依赖包..."
        pip install -r requirements.txt
        if [ $? -ne 0 ]; then
            log_error "依赖包安装失败"
            exit 1
        fi
    fi
}

check_config() {
    if [ ! -f "${CONFIG_PATH}/system.yaml" ]; then
        log_error "配置文件不存在: ${CONFIG_PATH}/system.yaml"
        log_error "请复制并修改 config/distributed_example.yaml"
        exit 1
    fi
}

check_env() {
    if [ ! -f ".env" ]; then
        log_warn "未找到.env文件"
        log_warn "请创建.env文件并配置必要的环境变量"
        log_warn "参考: .env.example"
    fi
}

check_network() {
    log_info "检查网络连接..."
    if ! ping -c 1 ******* &> /dev/null; then
        log_warn "网络连接检查失败"
    fi
}

# 主函数
main() {
    log_header "分布式MT5交易系统启动脚本"
    log_info "主机ID: $HOST_ID"
    log_info "配置路径: $CONFIG_PATH"
    
    # 环境检查
    check_python
    check_venv
    check_dependencies
    check_config
    check_env
    check_network
    
    # 设置环境变量
    export MT5_HOST_ID="$HOST_ID"
    export MT5_CONFIG_PATH="$CONFIG_PATH"
    
    # 创建日志目录
    mkdir -p logs
    
    # 激活虚拟环境
    source venv/bin/activate
    
    log_header "启动分布式交易系统..."
    
    # 启动系统
    python -m src.distributed_main
    
    # 检查退出代码
    exit_code=$?
    if [ $exit_code -ne 0 ]; then
        log_header "系统异常退出，错误代码: $exit_code"
        log_error "请检查日志文件: logs/mt5_distributed.log"
        log_error "常见问题:"
        log_error "1. 网络连接问题"
        log_error "2. MT5账户配置错误"
        log_error "3. NATS/Redis服务不可用"
        log_error "4. 配置文件格式错误"
    else
        log_header "系统正常退出"
    fi
    
    exit $exit_code
}

# 信号处理
trap 'log_warn "收到中断信号，正在停止..."; exit 130' INT TERM

# 运行主函数
main "$@"
