@echo off
REM 分布式MT5交易系统启动脚本
REM 使用方法: start_distributed.bat [host_id] [config_path]

setlocal enabledelayedexpansion

REM 设置默认值
set DEFAULT_HOST_ID=host-001
set DEFAULT_CONFIG_PATH=config/

REM 获取参数
set HOST_ID=%1
set CONFIG_PATH=%2

REM 使用默认值如果参数为空
if "%HOST_ID%"=="" set HOST_ID=%DEFAULT_HOST_ID%
if "%CONFIG_PATH%"=="" set CONFIG_PATH=%DEFAULT_CONFIG_PATH%

echo ========================================
echo 分布式MT5交易系统启动脚本
echo ========================================
echo 主机ID: %HOST_ID%
echo 配置路径: %CONFIG_PATH%
echo ========================================

REM 检查Python环境
python --version >nul 2>&1
if errorlevel 1 (
    echo 错误: 未找到Python环境
    echo 请确保Python 3.10+已安装并添加到PATH
    pause
    exit /b 1
)

REM 检查虚拟环境
if not exist "venv\Scripts\activate.bat" (
    echo 警告: 未找到虚拟环境，正在创建...
    python -m venv venv
    if errorlevel 1 (
        echo 错误: 创建虚拟环境失败
        pause
        exit /b 1
    )
)

REM 激活虚拟环境
echo 激活虚拟环境...
call venv\Scripts\activate.bat

REM 检查依赖
echo 检查依赖包...
pip show nats-py >nul 2>&1
if errorlevel 1 (
    echo 安装依赖包...
    pip install -r requirements.txt
    if errorlevel 1 (
        echo 错误: 依赖包安装失败
        pause
        exit /b 1
    )
)

REM 检查配置文件
if not exist "%CONFIG_PATH%\system.yaml" (
    echo 错误: 配置文件不存在: %CONFIG_PATH%\system.yaml
    echo 请复制并修改 config\distributed_example.yaml
    pause
    exit /b 1
)

REM 检查环境变量文件
if not exist ".env" (
    echo 警告: 未找到.env文件
    echo 请创建.env文件并配置必要的环境变量
    echo 参考: .env.example
)

REM 设置环境变量
set MT5_HOST_ID=%HOST_ID%
set MT5_CONFIG_PATH=%CONFIG_PATH%

REM 创建日志目录
if not exist "logs" mkdir logs

REM 检查MT5连接
echo 检查MT5环境...
python -c "import MetaTrader5 as mt5; print('MT5模块检查通过')" 2>nul
if errorlevel 1 (
    echo 警告: MT5模块检查失败，请确保MetaTrader 5已正确安装
)

REM 检查网络连接
echo 检查网络连接...
ping -n 1 ******* >nul 2>&1
if errorlevel 1 (
    echo 警告: 网络连接检查失败
)

echo ========================================
echo 启动分布式交易系统...
echo ========================================

REM 启动系统
python -m src.distributed_main

REM 检查退出代码
if errorlevel 1 (
    echo ========================================
    echo 系统异常退出，错误代码: %errorlevel%
    echo ========================================
    echo 请检查日志文件: logs\mt5_distributed.log
    echo 常见问题:
    echo 1. 网络连接问题
    echo 2. MT5账户配置错误
    echo 3. NATS/Redis服务不可用
    echo 4. 配置文件格式错误
    echo ========================================
) else (
    echo ========================================
    echo 系统正常退出
    echo ========================================
)

pause
endlocal
