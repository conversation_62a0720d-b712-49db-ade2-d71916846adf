#!/usr/bin/env python3
"""
Scripts目录清理工具
分析scripts目录中的文件，将测试文件移动到tests目录，清理重复和过时的文件
"""
import os
import shutil
from pathlib import Path
from typing import List, Dict, Tuple

class ScriptsDirectoryCleaner:
    """Scripts目录清理器"""
    
    def __init__(self):
        self.project_root = Path(__file__).parent.parent
        self.scripts_dir = self.project_root / "scripts"
        self.tests_dir = self.project_root / "tests"
        
        # 分类规则
        self.test_files = []
        self.backup_files = []
        self.duplicate_files = []
        self.utility_scripts = []
        self.deployment_scripts = []
        self.obsolete_files = []
        
    def analyze_scripts_directory(self) -> Dict[str, List[str]]:
        """分析scripts目录中的文件"""
        print("🔍 分析scripts目录...")
        
        for file_path in self.scripts_dir.glob("*"):
            if file_path.is_file():
                self._categorize_file(file_path)
        
        return {
            "test_files": self.test_files,
            "backup_files": self.backup_files,
            "duplicate_files": self.duplicate_files,
            "utility_scripts": self.utility_scripts,
            "deployment_scripts": self.deployment_scripts,
            "obsolete_files": self.obsolete_files
        }
    
    def _categorize_file(self, file_path: Path):
        """分类文件"""
        filename = file_path.name
        
        # 测试文件
        if (filename.startswith("test_") or 
            "test" in filename.lower() or
            filename.startswith("verify_") or
            filename.startswith("check_")):
            self.test_files.append(filename)
            
        # 备份文件
        elif filename.endswith(".bak") or filename.endswith(".old"):
            self.backup_files.append(filename)
            
        # 重复/过时文件
        elif self._is_duplicate_or_obsolete(filename):
            if self._is_duplicate(filename):
                self.duplicate_files.append(filename)
            else:
                self.obsolete_files.append(filename)
                
        # 部署脚本
        elif (filename.startswith("deploy") or 
              filename.startswith("start_") or
              filename.startswith("setup_") or
              "deployment" in filename.lower()):
            self.deployment_scripts.append(filename)
            
        # 工具脚本
        else:
            self.utility_scripts.append(filename)
    
    def _is_duplicate_or_obsolete(self, filename: str) -> bool:
        """检查是否为重复或过时文件"""
        obsolete_patterns = [
            "start_old.py",
            "test_config.py.bak",
            "diagnose_system.py.bak",
            "mock_api_server.py",  # 已有更好的测试工具
        ]
        
        duplicate_patterns = [
            ("start_enhanced_system.py", "start_distributed.py"),
            ("deploy_system.py", "production_deployment.py"),
        ]
        
        return filename in obsolete_patterns or any(filename in pair for pair in duplicate_patterns)
    
    def _is_duplicate(self, filename: str) -> bool:
        """检查是否为重复文件"""
        duplicate_pairs = [
            ("start_enhanced_system.py", "start_distributed.py"),
            ("deploy_system.py", "production_deployment.py"),
        ]
        
        return any(filename in pair for pair in duplicate_pairs)
    
    def generate_cleanup_plan(self) -> Dict[str, List[Tuple[str, str]]]:
        """生成清理计划"""
        print("📋 生成清理计划...")
        
        cleanup_plan = {
            "move_to_tests": [],
            "delete_files": [],
            "keep_files": [],
            "consolidate_files": []
        }
        
        # 移动测试文件到tests目录
        for test_file in self.test_files:
            target_dir = self._determine_test_subdirectory(test_file)
            cleanup_plan["move_to_tests"].append((test_file, target_dir))
        
        # 删除备份和过时文件
        for backup_file in self.backup_files + self.obsolete_files:
            cleanup_plan["delete_files"].append((backup_file, "备份/过时文件"))
        
        # 处理重复文件
        for duplicate_file in self.duplicate_files:
            cleanup_plan["delete_files"].append((duplicate_file, "重复文件"))
        
        # 保留的工具和部署脚本
        for script in self.utility_scripts + self.deployment_scripts:
            cleanup_plan["keep_files"].append((script, "保留"))
        
        return cleanup_plan
    
    def _determine_test_subdirectory(self, test_file: str) -> str:
        """确定测试文件应该放在tests的哪个子目录"""
        if "verify_" in test_file or "check_" in test_file:
            return "tests/integration/"
        elif "performance" in test_file.lower():
            return "tests/performance/"
        elif "config" in test_file.lower():
            return "tests/config/"
        elif "distributed" in test_file.lower():
            return "tests/distributed/"
        else:
            return "tests/"
    
    def execute_cleanup(self, cleanup_plan: Dict[str, List[Tuple[str, str]]]) -> bool:
        """执行清理计划"""
        print("🧹 执行清理计划...")
        
        try:
            # 移动测试文件
            for test_file, target_dir in cleanup_plan["move_to_tests"]:
                self._move_test_file(test_file, target_dir)
            
            # 删除不需要的文件
            for file_to_delete, reason in cleanup_plan["delete_files"]:
                self._delete_file(file_to_delete, reason)
            
            print("✅ 清理完成！")
            return True
            
        except Exception as e:
            print(f"❌ 清理失败: {e}")
            return False
    
    def _move_test_file(self, test_file: str, target_dir: str):
        """移动测试文件"""
        source_path = self.scripts_dir / test_file
        target_path = self.project_root / target_dir / test_file
        
        # 确保目标目录存在
        target_path.parent.mkdir(parents=True, exist_ok=True)
        
        # 检查目标文件是否已存在
        if target_path.exists():
            print(f"⚠️ 目标文件已存在，跳过: {test_file}")
            return
        
        shutil.move(str(source_path), str(target_path))
        print(f"📁 移动: {test_file} -> {target_dir}")
    
    def _delete_file(self, filename: str, reason: str):
        """删除文件"""
        file_path = self.scripts_dir / filename
        
        if file_path.exists():
            file_path.unlink()
            print(f"🗑️ 删除: {filename} ({reason})")
        else:
            print(f"⚠️ 文件不存在: {filename}")
    
    def generate_report(self, analysis: Dict[str, List[str]], cleanup_plan: Dict[str, List[Tuple[str, str]]]):
        """生成清理报告"""
        print("\n" + "="*60)
        print("📊 Scripts目录清理报告")
        print("="*60)
        
        print(f"\n🔍 文件分析结果:")
        for category, files in analysis.items():
            if files:
                print(f"  {category.replace('_', ' ').title()}: {len(files)} 个文件")
                for file in files[:3]:  # 只显示前3个
                    print(f"    - {file}")
                if len(files) > 3:
                    print(f"    ... 还有 {len(files) - 3} 个文件")
        
        print(f"\n📋 清理计划:")
        for action, items in cleanup_plan.items():
            if items:
                print(f"  {action.replace('_', ' ').title()}: {len(items)} 个操作")
        
        print(f"\n📁 推荐的scripts目录结构:")
        print("scripts/")
        print("├── deployment/")
        print("│   ├── deploy_system.py")
        print("│   ├── production_deployment.py")
        print("│   └── setup_remote_host.py")
        print("├── utilities/")
        print("│   ├── config_manager.py")
        print("│   ├── health_check.py")
        print("│   └── role_manager.py")
        print("└── maintenance/")
        print("    ├── cleanup_config.py")
        print("    ├── collect_logs.py")
        print("    └── migrate_config.py")
        
        print("\n✅ 建议: 考虑将scripts按功能分类到子目录中")


def main():
    """主函数"""
    cleaner = ScriptsDirectoryCleaner()
    
    # 分析目录
    analysis = cleaner.analyze_scripts_directory()
    
    # 生成清理计划
    cleanup_plan = cleaner.generate_cleanup_plan()
    
    # 生成报告
    cleaner.generate_report(analysis, cleanup_plan)
    
    # 询问是否执行清理
    print(f"\n❓ 是否执行清理计划? (y/N): ", end="")
    response = input().strip().lower()
    
    if response in ['y', 'yes']:
        success = cleaner.execute_cleanup(cleanup_plan)
        if success:
            print("\n🎉 Scripts目录清理完成！")
            print("💡 建议: 运行测试确保所有功能正常工作")
        else:
            print("\n❌ 清理过程中出现错误")
    else:
        print("\n📋 清理计划已生成，可稍后手动执行")


if __name__ == "__main__":
    main()
