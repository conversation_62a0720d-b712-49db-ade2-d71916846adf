"""
智能错误恢复策略模块
提供指数退避、断路器模式等高级错误恢复机制
"""
import asyncio
import time
import random
import logging
from typing import Dict, List, Optional, Any, Callable, Union
from dataclasses import dataclass, field
from enum import Enum
from abc import ABC, abstractmethod

logger = logging.getLogger(__name__)


class CircuitBreakerOpenError(Exception):
    """断路器开路异常"""
    pass


class RecoveryStrategy(Enum):
    """恢复策略类型"""
    EXPONENTIAL_BACKOFF = "exponential_backoff"
    FIXED_DELAY = "fixed_delay"
    LINEAR_BACKOFF = "linear_backoff"
    CUSTOM = "custom"


class CircuitState(Enum):
    """断路器状态"""
    CLOSED = "closed"      # 正常状态
    OPEN = "open"          # 断路状态
    HALF_OPEN = "half_open"  # 半开状态


@dataclass
class FailureRecord:
    """失败记录"""
    timestamp: float
    error_type: str
    error_message: str
    context: Dict[str, Any] = field(default_factory=dict)


@dataclass
class RecoveryMetrics:
    """恢复指标"""
    total_attempts: int = 0
    successful_attempts: int = 0
    failed_attempts: int = 0
    last_success_time: Optional[float] = None
    last_failure_time: Optional[float] = None
    failure_rate: float = 0.0
    average_recovery_time: float = 0.0


class ExponentialBackoffStrategy:
    """指数退避策略"""
    
    def __init__(self, 
                 base_delay: float = 1.0,
                 max_delay: float = 300.0,
                 factor: float = 2.0,
                 jitter: bool = True,
                 max_attempts: int = 10):
        self.base_delay = base_delay
        self.max_delay = max_delay
        self.factor = factor
        self.jitter = jitter
        self.max_attempts = max_attempts
        self.current_attempt = 0
        
        logger.info(f"指数退避策略初始化: base_delay={base_delay}, max_delay={max_delay}, "
                   f"factor={factor}, max_attempts={max_attempts}")
    
    def get_delay(self, attempt: int) -> float:
        """获取延迟时间"""
        if attempt >= self.max_attempts:
            return self.max_delay
        
        # 计算指数退避延迟
        delay = self.base_delay * (self.factor ** attempt)
        delay = min(delay, self.max_delay)
        
        # 添加随机抖动
        if self.jitter:
            jitter_factor = random.uniform(0.5, 1.5)
            delay *= jitter_factor
        
        return delay
    
    def should_retry(self, attempt: int) -> bool:
        """判断是否应该重试"""
        return attempt < self.max_attempts
    
    def reset(self):
        """重置策略"""
        self.current_attempt = 0
        logger.debug("指数退避策略已重置")


class CircuitBreaker:
    """断路器模式"""
    
    def __init__(self,
                 failure_threshold: int = 5,
                 recovery_timeout: float = 60.0,
                 success_threshold: int = 3,
                 timeout: float = 10.0):
        self.failure_threshold = failure_threshold
        self.recovery_timeout = recovery_timeout
        self.success_threshold = success_threshold
        self.timeout = timeout
        
        self.failure_count = 0
        self.success_count = 0
        self.last_failure_time: Optional[float] = None
        self.state = CircuitState.CLOSED
        self._lock = asyncio.Lock()
        
        logger.info(f"断路器初始化: failure_threshold={failure_threshold}, "
                   f"recovery_timeout={recovery_timeout}, success_threshold={success_threshold}")
    
    async def call(self, func: Callable, *args, **kwargs) -> Any:
        """通过断路器调用函数"""
        async with self._lock:
            if self.state == CircuitState.OPEN:
                if not self._should_attempt_reset():
                    raise CircuitBreakerOpenError("断路器处于开路状态")
                else:
                    self.state = CircuitState.HALF_OPEN
                    self.success_count = 0
                    logger.info("断路器切换到半开状态")
        
        try:
            # 设置超时
            result = await asyncio.wait_for(func(*args, **kwargs), timeout=self.timeout)
            await self._on_success()
            return result
        
        except asyncio.TimeoutError:
            await self._on_failure("timeout")
            raise
        except Exception as e:
            await self._on_failure(str(e))
            raise
    
    async def _on_success(self):
        """成功回调"""
        async with self._lock:
            self.failure_count = 0
            
            if self.state == CircuitState.HALF_OPEN:
                self.success_count += 1
                if self.success_count >= self.success_threshold:
                    self.state = CircuitState.CLOSED
                    logger.info("断路器切换到关闭状态")
            
            logger.debug(f"断路器成功调用: state={self.state.value}, success_count={self.success_count}")
    
    async def _on_failure(self, error: str):
        """失败回调"""
        async with self._lock:
            self.failure_count += 1
            self.last_failure_time = time.time()
            
            if self.state == CircuitState.HALF_OPEN:
                self.state = CircuitState.OPEN
                logger.warning("断路器从半开状态切换到开路状态")
            elif self.failure_count >= self.failure_threshold:
                self.state = CircuitState.OPEN
                logger.warning(f"断路器达到失败阈值，切换到开路状态: {error}")
            
            logger.debug(f"断路器失败调用: state={self.state.value}, "
                        f"failure_count={self.failure_count}, error={error}")
    
    def _should_attempt_reset(self) -> bool:
        """判断是否应该尝试重置"""
        if not self.last_failure_time:
            return True
        
        return time.time() - self.last_failure_time >= self.recovery_timeout
    
    def get_state(self) -> CircuitState:
        """获取当前状态"""
        return self.state
    
    def record_call(self, is_success: bool):
        """记录调用结果（同步版本）"""
        if is_success:
            self.failure_count = 0
            if self.state == CircuitState.HALF_OPEN:
                self.success_count += 1
                if self.success_count >= self.success_threshold:
                    self.state = CircuitState.CLOSED
        else:
            self.failure_count += 1
            self.last_failure_time = time.time()
            if self.state == CircuitState.HALF_OPEN:
                self.state = CircuitState.OPEN
            elif self.failure_count >= self.failure_threshold:
                self.state = CircuitState.OPEN
    
    def get_metrics(self) -> Dict[str, Any]:
        """获取指标"""
        return {
            'state': self.state.value,
            'failure_count': self.failure_count,
            'success_count': self.success_count,
            'last_failure_time': self.last_failure_time,
            'failure_threshold': self.failure_threshold,
            'recovery_timeout': self.recovery_timeout
        }


class RetryPolicy:
    """重试策略"""
    
    def __init__(self,
                 strategy: RecoveryStrategy = RecoveryStrategy.EXPONENTIAL_BACKOFF,
                 max_attempts: int = 3,
                 base_delay: float = 1.0,
                 max_delay: float = 60.0,
                 factor: float = 2.0,
                 jitter: bool = True,
                 retryable_exceptions: Optional[List[type]] = None):
        self.strategy = strategy
        self.max_attempts = max_attempts
        self.base_delay = base_delay
        self.max_delay = max_delay
        self.factor = factor
        self.jitter = jitter
        self.retryable_exceptions = retryable_exceptions or [Exception]
        
        # 初始化具体策略
        if strategy == RecoveryStrategy.EXPONENTIAL_BACKOFF:
            self.backoff_strategy = ExponentialBackoffStrategy(
                base_delay=base_delay,
                max_delay=max_delay,
                factor=factor,
                jitter=jitter,
                max_attempts=max_attempts
            )
        
        self.metrics = RecoveryMetrics()
        logger.info(f"重试策略初始化: {strategy.value}, max_attempts={max_attempts}")
    
    async def execute(self, func: Callable, *args, **kwargs) -> Any:
        """执行函数，带重试"""
        start_time = time.time()
        last_exception = None
        
        for attempt in range(self.max_attempts):
            try:
                self.metrics.total_attempts += 1
                result = await func(*args, **kwargs)
                
                # 记录成功
                self.metrics.successful_attempts += 1
                self.metrics.last_success_time = time.time()
                
                if attempt > 0:
                    recovery_time = time.time() - start_time
                    self.metrics.average_recovery_time = (
                        (self.metrics.average_recovery_time * (self.metrics.successful_attempts - 1) + recovery_time) /
                        self.metrics.successful_attempts
                    )
                    logger.info(f"重试成功: attempt={attempt + 1}, recovery_time={recovery_time:.2f}s")
                
                return result
                
            except Exception as e:
                last_exception = e
                self.metrics.failed_attempts += 1
                self.metrics.last_failure_time = time.time()
                
                # 检查是否是可重试的异常
                if not self._is_retryable_exception(e):
                    logger.error(f"不可重试的异常: {type(e).__name__}: {e}")
                    raise
                
                # 检查是否还有重试机会
                if attempt + 1 >= self.max_attempts:
                    logger.error(f"重试次数用尽: {attempt + 1}/{self.max_attempts}")
                    break
                
                # 计算延迟时间
                delay = self._calculate_delay(attempt)
                logger.warning(f"重试失败 (attempt {attempt + 1}/{self.max_attempts}): "
                              f"{type(e).__name__}: {e}, 等待 {delay:.2f}s 后重试")
                
                await asyncio.sleep(delay)
        
        # 更新失败率
        self.metrics.failure_rate = (
            self.metrics.failed_attempts / self.metrics.total_attempts
            if self.metrics.total_attempts > 0 else 0.0
        )
        
        # 抛出最后的异常
        if last_exception:
            raise last_exception
    
    def _is_retryable_exception(self, exception: Exception) -> bool:
        """判断异常是否可重试"""
        return any(isinstance(exception, exc_type) for exc_type in self.retryable_exceptions)
    
    def _calculate_delay(self, attempt: int) -> float:
        """计算延迟时间"""
        if self.strategy == RecoveryStrategy.EXPONENTIAL_BACKOFF:
            return self.backoff_strategy.get_delay(attempt)
        elif self.strategy == RecoveryStrategy.FIXED_DELAY:
            return self.base_delay
        elif self.strategy == RecoveryStrategy.LINEAR_BACKOFF:
            delay = self.base_delay * (attempt + 1)
            if self.jitter:
                delay *= random.uniform(0.8, 1.2)
            return min(delay, self.max_delay)
        else:
            return self.base_delay
    
    def get_metrics(self) -> RecoveryMetrics:
        """获取指标"""
        return self.metrics
    
    def reset_metrics(self):
        """重置指标"""
        self.metrics = RecoveryMetrics()
        if hasattr(self, 'backoff_strategy'):
            self.backoff_strategy.reset()
        logger.debug("重试策略指标已重置")


class AdaptiveRetryPolicy:
    """自适应重试策略"""
    
    def __init__(self,
                 initial_policy: RetryPolicy,
                 success_threshold: int = 10,
                 failure_threshold: int = 5,
                 adaptation_window: float = 300.0):
        self.current_policy = initial_policy
        self.success_threshold = success_threshold
        self.failure_threshold = failure_threshold
        self.adaptation_window = adaptation_window
        
        self.recent_results: List[bool] = []
        self.last_adaptation_time = time.time()
        
        logger.info(f"自适应重试策略初始化: success_threshold={success_threshold}, "
                   f"failure_threshold={failure_threshold}")
    
    async def execute(self, func: Callable, *args, **kwargs) -> Any:
        """执行函数，带自适应重试"""
        start_time = time.time()
        
        try:
            result = await self.current_policy.execute(func, *args, **kwargs)
            self._record_result(True)
            return result
        except Exception as e:
            self._record_result(False)
            raise
        finally:
            # 检查是否需要调整策略
            if time.time() - self.last_adaptation_time > self.adaptation_window:
                await self._adapt_policy()
    
    def _record_result(self, success: bool):
        """记录结果"""
        self.recent_results.append(success)
        
        # 保持滑动窗口大小
        max_window_size = max(self.success_threshold, self.failure_threshold) * 2
        if len(self.recent_results) > max_window_size:
            self.recent_results.pop(0)
    
    async def _adapt_policy(self):
        """调整策略"""
        if len(self.recent_results) < 10:  # 样本太少，不调整
            return
        
        success_count = sum(self.recent_results)
        failure_count = len(self.recent_results) - success_count
        success_rate = success_count / len(self.recent_results)
        
        current_max_attempts = self.current_policy.max_attempts
        
        # 根据成功率调整策略
        if success_rate > 0.8 and current_max_attempts > 1:
            # 成功率高，减少重试次数
            new_max_attempts = max(1, current_max_attempts - 1)
            logger.info(f"成功率高({success_rate:.2f})，减少重试次数: {current_max_attempts} → {new_max_attempts}")
        elif success_rate < 0.5 and current_max_attempts < 10:
            # 成功率低，增加重试次数
            new_max_attempts = min(10, current_max_attempts + 1)
            logger.info(f"成功率低({success_rate:.2f})，增加重试次数: {current_max_attempts} → {new_max_attempts}")
        else:
            new_max_attempts = current_max_attempts
        
        # 创建新的策略
        if new_max_attempts != current_max_attempts:
            self.current_policy = RetryPolicy(
                strategy=self.current_policy.strategy,
                max_attempts=new_max_attempts,
                base_delay=self.current_policy.base_delay,
                max_delay=self.current_policy.max_delay,
                factor=self.current_policy.factor,
                jitter=self.current_policy.jitter,
                retryable_exceptions=self.current_policy.retryable_exceptions
            )
        
        self.last_adaptation_time = time.time()
        
        logger.debug(f"策略调整完成: success_rate={success_rate:.2f}, "
                    f"max_attempts={new_max_attempts}")
    
    def get_metrics(self) -> Dict[str, Any]:
        """获取指标"""
        success_count = sum(self.recent_results) if self.recent_results else 0
        total_count = len(self.recent_results)
        
        return {
            'current_policy_metrics': self.current_policy.get_metrics(),
            'recent_success_rate': success_count / total_count if total_count > 0 else 0.0,
            'recent_attempts': total_count,
            'current_max_attempts': self.current_policy.max_attempts,
            'last_adaptation_time': self.last_adaptation_time
        }


class RecoveryManager:
    """恢复管理器"""
    
    def __init__(self):
        self.retry_policies: Dict[str, RetryPolicy] = {}
        self.circuit_breakers: Dict[str, CircuitBreaker] = {}
        self.failure_records: Dict[str, List[FailureRecord]] = {}
        self.global_metrics = RecoveryMetrics()
        
        logger.info("恢复管理器初始化完成")
    
    def create_retry_policy(self, 
                          name: str,
                          strategy: RecoveryStrategy = RecoveryStrategy.EXPONENTIAL_BACKOFF,
                          **kwargs) -> RetryPolicy:
        """创建重试策略"""
        policy = RetryPolicy(strategy=strategy, **kwargs)
        self.retry_policies[name] = policy
        logger.info(f"创建重试策略: {name}")
        return policy
    
    def create_circuit_breaker(self,
                             name: str,
                             failure_threshold: int = 5,
                             recovery_timeout: float = 60.0,
                             **kwargs) -> CircuitBreaker:
        """创建断路器"""
        breaker = CircuitBreaker(
            failure_threshold=failure_threshold,
            recovery_timeout=recovery_timeout,
            **kwargs
        )
        self.circuit_breakers[name] = breaker
        logger.info(f"创建断路器: {name}")
        return breaker
    
    def create_adaptive_policy(self,
                             name: str,
                             initial_policy: RetryPolicy,
                             **kwargs) -> AdaptiveRetryPolicy:
        """创建自适应策略"""
        policy = AdaptiveRetryPolicy(initial_policy=initial_policy, **kwargs)
        logger.info(f"创建自适应策略: {name}")
        return policy
    
    def record_failure(self,
                      component: str,
                      error_type: str,
                      error_message: str,
                      context: Optional[Dict[str, Any]] = None):
        """记录失败"""
        if component not in self.failure_records:
            self.failure_records[component] = []
        
        record = FailureRecord(
            timestamp=time.time(),
            error_type=error_type,
            error_message=error_message,
            context=context or {}
        )
        
        self.failure_records[component].append(record)
        
        # 保持记录数量限制
        if len(self.failure_records[component]) > 100:
            self.failure_records[component].pop(0)
        
        logger.debug(f"记录失败: {component} - {error_type}: {error_message}")
    
    def get_failure_analysis(self, component: str) -> Dict[str, Any]:
        """获取失败分析"""
        if component not in self.failure_records:
            return {}
        
        records = self.failure_records[component]
        current_time = time.time()
        
        # 分析失败类型
        error_types = {}
        recent_failures = []
        
        for record in records:
            error_types[record.error_type] = error_types.get(record.error_type, 0) + 1
            
            # 最近1小时的失败
            if current_time - record.timestamp < 3600:
                recent_failures.append({
                    'timestamp': record.timestamp,
                    'error_type': record.error_type,
                    'error_message': record.error_message,
                    'time_ago': current_time - record.timestamp
                })
        
        return {
            'component': component,
            'total_failures': len(records),
            'error_types': error_types,
            'recent_failures': recent_failures,
            'failure_rate': len(recent_failures) / 3600 if recent_failures else 0.0
        }
    
    def get_all_metrics(self) -> Dict[str, Any]:
        """获取所有指标"""
        return {
            'retry_policies': {
                name: policy.get_metrics() 
                for name, policy in self.retry_policies.items()
            },
            'circuit_breakers': {
                name: breaker.get_metrics() 
                for name, breaker in self.circuit_breakers.items()
            },
            'failure_analysis': {
                component: self.get_failure_analysis(component)
                for component in self.failure_records.keys()
            },
            'global_metrics': self.global_metrics
        }


class CircuitBreakerOpenError(Exception):
    """断路器开路异常"""
    pass


# 全局恢复管理器实例
_recovery_manager: Optional[RecoveryManager] = None


def get_recovery_manager() -> RecoveryManager:
    """获取全局恢复管理器"""
    global _recovery_manager
    if _recovery_manager is None:
        _recovery_manager = RecoveryManager()
    return _recovery_manager


# 装饰器函数
def retry(strategy: RecoveryStrategy = RecoveryStrategy.EXPONENTIAL_BACKOFF,
          max_attempts: int = 3,
          base_delay: float = 1.0,
          max_delay: float = 60.0,
          factor: float = 2.0,
          jitter: bool = True,
          retryable_exceptions: Optional[List[type]] = None):
    """重试装饰器"""
    def decorator(func):
        policy = RetryPolicy(
            strategy=strategy,
            max_attempts=max_attempts,
            base_delay=base_delay,
            max_delay=max_delay,
            factor=factor,
            jitter=jitter,
            retryable_exceptions=retryable_exceptions
        )
        
        async def wrapper(*args, **kwargs):
            return await policy.execute(func, *args, **kwargs)
        
        return wrapper
    return decorator


def circuit_breaker(failure_threshold: int = 5,
                   recovery_timeout: float = 60.0,
                   success_threshold: int = 3,
                   timeout: float = 10.0):
    """断路器装饰器"""
    def decorator(func):
        breaker = CircuitBreaker(
            failure_threshold=failure_threshold,
            recovery_timeout=recovery_timeout,
            success_threshold=success_threshold,
            timeout=timeout
        )
        
        async def wrapper(*args, **kwargs):
            return await breaker.call(func, *args, **kwargs)
        
        return wrapper
    return decorator