{"name": "mt5-trading-frontend", "version": "2.0.0", "description": "MT5 Dynamic Trading System Frontend", "main": "src/index.js", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "test": "jest", "lint": "eslint src --ext .js,.jsx,.ts,.tsx", "format": "prettier --write src/**/*.{js,jsx,ts,tsx,json,css,md}"}, "keywords": ["mt5", "trading", "forex", "frontend"], "author": "MT5 Trading Team", "license": "MIT", "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.8.0", "axios": "^1.3.0", "chart.js": "^4.2.0", "react-chartjs-2": "^5.2.0", "socket.io-client": "^4.6.0", "antd": "^5.2.0", "dayjs": "^1.11.0", "lodash": "^4.17.21"}, "devDependencies": {"@types/react": "^18.0.27", "@types/react-dom": "^18.0.10", "@types/lodash": "^4.14.191", "@vitejs/plugin-react": "^3.1.0", "vite": "^4.1.0", "typescript": "^4.9.4", "eslint": "^8.34.0", "prettier": "^2.8.0", "jest": "^29.4.0"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}