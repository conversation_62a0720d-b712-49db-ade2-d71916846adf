#!/usr/bin/env python3
"""
MT5 RPC Client - For calling MT5 RPC services
Provides clean interface for other components to call MT5 operations
"""

import asyncio
import time
from typing import Dict, Any, Optional
from ..messaging.jetstream_client import JetStreamClient
from ..utils.logger import get_logger

logger = get_logger(__name__)


class MT5RPCClient:
    """
    RPC Client for calling MT5 services
    Clean interface for control operations as suggested in the architecture
    """
    
    def __init__(self, jetstream_client: JetStreamClient):
        self.jetstream = jetstream_client
        
        self.metrics = {
            'total_calls': 0,
            'successful_calls': 0,
            'failed_calls': 0,
            'timeout_calls': 0,
            'retry_calls': 0,
            'latency_p50': 0.0,
            'latency_p95': 0.0,
            'latency_p99': 0.0,
            'call_latencies': [],  # Keep last 1000 latencies for percentile calculation
            'method_stats': {},  # Per-method statistics
            'error_counts': {}   # Error type counting
        }
        
    async def call_rpc(self, account_id: str, method: str, params: Dict = None, timeout: float = 10.0) -> Dict:
        """
        Call RPC method on remote MT5 service with performance monitoring
        """
        start_time = time.time()
        self.metrics['total_calls'] += 1
        
        if method not in self.metrics['method_stats']:
            self.metrics['method_stats'][method] = {
                'calls': 0, 'successes': 0, 'failures': 0, 'avg_latency': 0.0
            }
        
        method_stats = self.metrics['method_stats'][method]
        method_stats['calls'] += 1
        
        try:
            request = {
                "account_id": account_id,
                "command": method,
                "params": params or {},
                "timestamp": time.time()
            }
            
            subject = f"MT5.REQUEST.{account_id}"
            response = await self.jetstream.request(subject, request, timeout=timeout)
            
            latency = (time.time() - start_time) * 1000  # Convert to milliseconds
            self._update_latency_metrics(latency, method_stats)
            
            if response is None:
                self.metrics['timeout_calls'] += 1
                method_stats['failures'] += 1
                self._update_error_count("timeout")
                return {"error": "No response received", "status": "timeout"}
            
            self.metrics['successful_calls'] += 1
            method_stats['successes'] += 1
            return response
            
        except Exception as e:
            latency = (time.time() - start_time) * 1000
            self._update_latency_metrics(latency, method_stats)
            
            self.metrics['failed_calls'] += 1
            method_stats['failures'] += 1
            self._update_error_count(type(e).__name__)
            
            logger.error(f"RPC call failed for {account_id}.{method}: {e}")
            return {"error": str(e), "status": "failed"}
    
    def _update_latency_metrics(self, latency: float, method_stats: Dict):
        """Update latency metrics and percentiles"""
        self.metrics['call_latencies'].append(latency)
        if len(self.metrics['call_latencies']) > 1000:
            self.metrics['call_latencies'].pop(0)
        
        method_calls = method_stats['calls']
        current_avg = method_stats['avg_latency']
        method_stats['avg_latency'] = ((current_avg * (method_calls - 1)) + latency) / method_calls
        
        if len(self.metrics['call_latencies']) >= 10:
            sorted_latencies = sorted(self.metrics['call_latencies'])
            n = len(sorted_latencies)
            
            self.metrics['latency_p50'] = sorted_latencies[int(n * 0.50)]
            self.metrics['latency_p95'] = sorted_latencies[int(n * 0.95)]
            self.metrics['latency_p99'] = sorted_latencies[int(n * 0.99)]
    
    def _update_error_count(self, error_type: str):
        """Update error type counts"""
        if error_type not in self.metrics['error_counts']:
            self.metrics['error_counts'][error_type] = 0
        self.metrics['error_counts'][error_type] += 1

    async def call_rpc_with_retry(self, account_id: str, method: str, params: Dict = None,
                                 max_retries: int = 3, backoff: float = 0.1, timeout: float = 5.0) -> Dict:
        """
        Call RPC with retry mechanism and exponential backoff
        """
        last_error = None
        
        for attempt in range(max_retries + 1):
            try:
                result = await self.call_rpc(account_id, method, params, timeout=timeout)
                
                if result.get("status") != "timeout":
                    if attempt > 0:
                        logger.info(f"RPC retry successful on attempt {attempt + 1} for {account_id}.{method}")
                    return result
                    
                last_error = result.get("error", "timeout")
                if attempt < max_retries:
                    wait_time = backoff * (2 ** attempt)  # Exponential backoff
                    logger.warning(f"RPC timeout, retrying in {wait_time:.2f}s (attempt {attempt + 1}/{max_retries})")
                    await asyncio.sleep(wait_time)
                    
            except Exception as e:
                last_error = str(e)
                if attempt < max_retries:
                    wait_time = backoff * (2 ** attempt)
                    logger.warning(f"RPC error, retrying in {wait_time:.2f}s: {e}")
                    await asyncio.sleep(wait_time)
                else:
                    logger.error(f"RPC failed after {max_retries} retries: {e}")
                    
        return {
            "error": f"Max retries ({max_retries}) exceeded. Last error: {last_error}",
            "status": "failed"
        }
    
    
    async def get_positions(self, account_id: str) -> Dict:
        """Get positions from specific account"""
        return await self.call_rpc(account_id, "get_positions")
    
    async def get_account_info(self, account_id: str) -> Dict:
        """Get account info from specific account"""
        return await self.call_rpc(account_id, "get_account_info")
    
    async def send_order(self, account_id: str, symbol: str, order_type: int, 
                        volume: float, price: float = 0.0, sl: float = 0.0, 
                        tp: float = 0.0, comment: str = "") -> Dict:
        """Send order to specific account"""
        params = {
            "symbol": symbol,
            "type": order_type,
            "volume": volume,
            "price": price,
            "sl": sl,
            "tp": tp,
            "comment": comment
        }
        return await self.call_rpc(account_id, "send_order", params)
    
    async def get_symbol_info(self, account_id: str, symbol: str) -> Dict:
        """Get symbol information from specific account"""
        params = {"symbol": symbol}
        return await self.call_rpc(account_id, "get_symbol_info", params)
    
    async def health_check(self, account_id: str) -> Dict:
        """Check health of specific account"""
        return await self.call_rpc(account_id, "health_check")
    
    
    async def get_positions_batch(self, account_ids: list) -> Dict[str, Dict]:
        """Get positions from multiple accounts concurrently"""
        tasks = []
        for account_id in account_ids:
            tasks.append(self.get_positions(account_id))
        
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        batch_result = {}
        for i, account_id in enumerate(account_ids):
            result = results[i]
            if isinstance(result, Exception):
                batch_result[account_id] = {"error": str(result), "status": "failed"}
            else:
                batch_result[account_id] = result
        
        return batch_result
    
    async def health_check_batch(self, account_ids: list) -> Dict[str, Dict]:
        """Check health of multiple accounts concurrently"""
        tasks = []
        for account_id in account_ids:
            tasks.append(self.health_check(account_id))
        
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        batch_result = {}
        for i, account_id in enumerate(account_ids):
            result = results[i]
            if isinstance(result, Exception):
                batch_result[account_id] = {"error": str(result), "status": "failed"}
            else:
                batch_result[account_id] = result
        
        return batch_result
    
    async def send_order_batch(self, orders: list) -> Dict[str, Dict]:
        """
        Send orders to multiple accounts concurrently
        """
        tasks = []
        account_ids = []
        
        for order in orders:
            account_id = order.pop("account_id")  # Remove account_id from params
            account_ids.append(account_id)
            tasks.append(self.send_order(account_id, **order))
        
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        batch_result = {}
        for i, account_id in enumerate(account_ids):
            result = results[i]
            if isinstance(result, Exception):
                batch_result[account_id] = {"error": str(result), "status": "failed"}
            else:
                batch_result[account_id] = result
        
        return batch_result



    def get_performance_metrics(self) -> Dict[str, Any]:
        """Get comprehensive performance metrics"""
        success_rate = (self.metrics['successful_calls'] / max(self.metrics['total_calls'], 1)) * 100
        
        return {
            'overview': {
                'total_calls': self.metrics['total_calls'],
                'successful_calls': self.metrics['successful_calls'],
                'failed_calls': self.metrics['failed_calls'],
                'timeout_calls': self.metrics['timeout_calls'],
                'retry_calls': self.metrics['retry_calls'],
                'success_rate_percent': round(success_rate, 2)
            },
            'latency_metrics': {
                'p50_ms': round(self.metrics['latency_p50'], 2),
                'p95_ms': round(self.metrics['latency_p95'], 2),
                'p99_ms': round(self.metrics['latency_p99'], 2),
                'samples': len(self.metrics['call_latencies'])
            },
            'method_stats': self.metrics['method_stats'],
            'error_distribution': self.metrics['error_counts']
        }
    
    def reset_metrics(self):
        """Reset all performance metrics"""
        self.metrics = {
            'total_calls': 0,
            'successful_calls': 0,
            'failed_calls': 0,
            'timeout_calls': 0,
            'retry_calls': 0,
            'latency_p50': 0.0,
            'latency_p95': 0.0,
            'latency_p99': 0.0,
            'call_latencies': [],
            'method_stats': {},
            'error_counts': {}
        }


# Convenience functions for easy import
async def create_rpc_client(jetstream_client: JetStreamClient) -> MT5RPCClient:
    """Create and return MT5 RPC client"""
    return MT5RPCClient(jetstream_client)



