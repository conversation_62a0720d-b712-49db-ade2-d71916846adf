# MT5进程隔离架构文档

## 概述

本文档描述了MT5交易系统的进程隔离架构，该架构集成了`process.py`的优秀技术，实现了真正的进程级隔离，消除了账户间的状态冲突。

## 架构对比

### 🔴 旧架构问题（线程模式）
- **状态冲突**: 多账户共享单一MT5实例，导致严重状态混乱
- **账户混乱**: 经常发生账户连接错误，如日志所示的账户混乱问题
- **资源争夺**: 多账户争夺单一MT5连接资源
- **错误传播**: 一个账户的问题影响所有其他账户
- **难以调试**: 状态共享导致问题难以定位和解决

### 🟢 新架构优势（进程隔离）
- **完全隔离**: 每个账户独立MT5进程，无状态冲突
- **账户安全**: 不可能发生账户混乱，每个进程只处理一个账户
- **资源独立**: 每个进程有独立的资源限制和管理
- **错误隔离**: 一个账户崩溃不影响其他账户
- **易于监控**: 进程级监控，状态清晰可见
- **自动恢复**: 智能重启机制，系统自愈能力强

## 核心组件

### 1. MT5ProcessManager
**位置**: `src/core/mt5_process_manager.py`

进程管理器是整个系统的核心，负责：
- 管理所有MT5进程的生命周期
- 进程间通信（IPC）协调
- 健康监控和自动重启
- 资源限制和管理

**主要功能**:
```python
# 启动进程管理器
manager = MT5ProcessManager()
manager.start_manager()

# 添加账户（自动创建独立进程）
manager.add_account('ACC001', {
    'login': 12345,
    'password': 'password',
    'server': 'server',
    'terminal_path': '/path/to/terminal'
})

# 发送命令到特定账户进程
response = manager.send_command('ACC001', 'get_account_info')

# 获取系统健康状态
health = manager.get_system_health()
```

### 2. MT5Process
**位置**: `src/core/mt5_process_manager.py`

独立的MT5连接进程，每个账户一个：
- 独立的MT5连接和状态管理
- 命令处理和响应
- 心跳发送和连接监控
- 自动重连机制

### 3. MT5Client (重构版)
**位置**: `src/core/mt5_client.py`

进程隔离版本的MT5客户端：
- 通过进程管理器与MT5进程通信
- 保持与旧版本API的兼容性
- 自动进程管理和连接处理

**使用示例**:
```python
from src.core.mt5_client import create_mt5_client

# 创建进程隔离的MT5客户端
client = create_mt5_client(
    account_id='ACC001',
    login=12345,
    password='password',
    server='server'
)

# 连接（自动创建独立进程）
await client.connect()

# 获取账户信息（通过进程通信）
account_info = await client.get_account_info()

# 发送交易订单
result = await client.send_order(trade_request)
```

### 4. 统一数据模型
**位置**: `src/messaging/message_types.py`

集成了所有Pydantic数据模型：
- `CommandRequest/CommandResponse`: 进程间命令通信
- `ProcessStatus`: 进程状态信息
- `TradeRequest/TradeResult`: 交易相关数据
- `SystemHealth`: 系统健康状态

## 进程通信机制

### 命令-响应模式
```python
# 命令请求
request = CommandRequest(
    type=CommandType.GET_ACCOUNT_INFO,
    params={},
    request_id="unique_id"
)

# 命令响应
response = CommandResponse(
    type='account_info',
    account='ACC001',
    status='success',
    data={'balance': 10000.0, 'equity': 10000.0},
    request_id="unique_id"
)
```

### 队列通信
- **命令队列**: 主进程 → MT5进程
- **结果队列**: MT5进程 → 主进程  
- **系统队列**: 心跳、状态更新等系统消息

## 监控和自动重启

### 健康监控
- **进程存活检查**: 定期检查进程是否运行
- **心跳监控**: 监控进程心跳，检测僵死进程
- **资源监控**: CPU、内存使用情况监控
- **错误计数**: 跟踪进程错误次数

### 自动重启策略
```python
# 启用自动重启
manager.enable_auto_restart('ACC001', max_restarts=5, time_window=3600)

# 重启策略配置
restart_policy = {
    'enabled': True,
    'max_restarts': 5,      # 最大重启次数
    'time_window': 3600,    # 时间窗口（秒）
    'restart_delay': 30,    # 重启延迟（秒）
}
```

### 故障处理
1. **进程死亡**: 自动检测并重启
2. **心跳超时**: 强制重启僵死进程
3. **错误过多**: 达到阈值时重启进程
4. **资源超限**: 监控并警告资源使用异常

## 部署和配置

### 环境要求
- Python 3.8+
- MetaTrader5 Python包
- psutil (进程监控)
- pydantic (数据验证)

### 配置示例
```yaml
# config/accounts/ACC001.yaml
mt5:
  connection:
    login: 12345
    server: "MetaQuotes-Demo"
    terminal_path: "C:/Program Files/MetaTrader 5/terminal64.exe"
    timeout: 30000
    retry_attempts: 3
    retry_delay: 5

process:
  auto_restart: true
  max_restarts: 5
  time_window: 3600
  resource_limits:
    cpu_limit: 50.0
    memory_limit: 2048
```

### 环境变量
```bash
# 账户密码（安全存储）
export MT5_ACC001_PASSWORD="your_password"
export MT5_ACC002_PASSWORD="your_password"
```

## 测试和验证

### 单元测试
```bash
# 运行进程隔离测试
python -m pytest tests/test_process_isolation.py -v
```

### 集成测试
```bash
# 运行完整集成测试
python tests/integration_test_process_isolation.py
```

### 测试覆盖
- ✅ 进程管理器初始化和关闭
- ✅ 多账户进程隔离验证
- ✅ 命令请求响应机制
- ✅ 进程状态跟踪
- ✅ 健康监控和自动重启
- ✅ 并发操作隔离性
- ✅ 错误处理和恢复

## 迁移指南

### 从旧版本迁移
1. **更新导入**:
   ```python
   # 旧版本
   from src.core.mt5_client import MT5Client
   
   # 新版本
   from src.core.mt5_client import create_mt5_client
   ```

2. **更新客户端创建**:
   ```python
   # 旧版本
   client = MT5Client(account_id, login, password, server)
   
   # 新版本
   client = create_mt5_client(account_id, login, password, server)
   ```

3. **API兼容性**: 大部分API保持兼容，无需修改业务逻辑

### 配置迁移
- 将账户配置移至独立的YAML文件
- 设置环境变量存储敏感信息
- 配置进程资源限制

## 性能优化

### 资源管理
- 进程级资源限制
- 内存使用监控
- CPU使用率控制

### 通信优化
- 异步命令处理
- 批量操作支持
- 超时机制

### 监控优化
- 智能心跳频率调整
- 分级错误处理
- 渐进式重启策略

## 故障排除

### 常见问题
1. **进程启动失败**: 检查终端路径和权限
2. **连接超时**: 调整超时配置和网络设置
3. **内存泄漏**: 监控进程资源使用
4. **频繁重启**: 检查重启策略配置

### 日志分析
```bash
# 查看进程管理器日志
tail -f mt5_terminals/logs/mt5_process_manager.log

# 查看特定账户日志
tail -f mt5_terminals/logs/ACC001_stdout.log
```

### 调试工具
- 进程状态查看
- 系统健康监控
- 实时日志跟踪

## 总结

新的进程隔离架构彻底解决了MT5交易系统中的账户混乱和状态冲突问题，提供了：

- 🔒 **完全隔离**: 每个账户独立进程，无状态共享
- 🚀 **高可靠性**: 自动监控和重启，系统自愈能力
- 📊 **可观测性**: 详细的进程状态和健康监控
- 🔧 **易维护性**: 清晰的架构和完善的测试覆盖
- 🔄 **向后兼容**: 保持API兼容性，平滑迁移

这个架构为MT5交易系统提供了工业级的稳定性和可扩展性，是从线程模式到进程隔离的重大升级。
