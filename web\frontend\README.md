# MT5 Trading System Frontend

<div align="center">

[English](README_EN.md) | 简体中文

</div>

MT5动态配对交易系统的前端界面。


```mermaid 
graph TB
    subgraph "用户界面层"
        Browser[浏览器]
        ReactApp[React应用]
    end
    
    subgraph "前端服务层"
        Nginx[Nginx反向代理<br/>:80]
        Frontend[React前端<br/>:3000]
    end
    
    subgraph "API服务层"
        RelAPI[关系管理API<br/>:8001]
        MainAPI[主API网关<br/>:8000]
        WebSocket[WebSocket服务<br/>实时通信]
    end
    
    subgraph "核心功能"
        RelMgr[关系管理器<br/>CRUD操作]
        RealTime[实时监控<br/>状态更新]
        Graph[关系图<br/>可视化]
        Monitor[账户监控<br/>性能统计]
    end
    
    subgraph "数据层"
        RelDB[(关系数据库)]
        Redis[(Redis缓存)]
        NATS[(NATS消息队列)]
    end
    
    Browser --> Nginx
    Nginx --> Frontend
    Nginx --> RelAPI
    Nginx --> MainAPI
    
    ReactApp --> RelMgr
    ReactApp --> RealTime
    ReactApp --> Graph
    ReactApp --> Monitor
    
    RelAPI --> RelDB
    MainAPI --> Redis
    WebSocket --> NATS
    
    RelMgr -.-> RelAPI
    RealTime -.-> WebSocket
    Graph -.-> RelAPI
    Monitor -.-> MainAPI
```


## 技术栈

- React 18
- TypeScript
- Vite
- Ant Design
- Chart.js
- Socket.io

## 功能特性

- 实时账户监控
- 配对管理界面
- 交易数据可视化
- 系统状态监控
- 风险管理控制

## 开发指南

### 安装依赖

```bash
npm install
```

### 开发模式

```bash
npm run dev
```

### 构建生产版本

```bash
npm run build
```

### 运行测试

```bash
npm test
```

### 代码格式化

```bash
npm run format
```

## 项目结构

```
src/
├── components/     # 可复用组件
├── pages/         # 页面组件
├── hooks/         # 自定义hooks
├── services/      # API服务
├── utils/         # 工具函数
├── styles/        # 样式文件
└── types/         # TypeScript类型定义
```

## 环境变量

创建 `.env` 文件：

```
VITE_API_BASE_URL=http://localhost:8000
VITE_WS_URL=ws://localhost:8765
```

## 部署

```bash
npm run build
```

构建文件将输出到 `dist/` 目录。 