"""
混合并发处理器 - 针对MT5高频交易优化的并发架构
结合多进程和多线程的优势，避免死锁，提供微秒级响应
"""

import asyncio
import threading
import multiprocessing as mp
from multiprocessing import shared_memory
import time
import queue
from typing import Dict, Any, Optional, List, Callable
from dataclasses import dataclass
from enum import Enum
import logging
import signal
import os
import struct
import mmap
from concurrent.futures import ThreadPoolExecutor, ProcessPoolExecutor
import ctypes

logger = logging.getLogger(__name__)

class ProcessorType(Enum):
    """处理器类型"""
    CRITICAL_REALTIME = "critical_realtime"  # 微秒级，使用线程
    HIGH_THROUGHPUT = "high_throughput"      # 高吞吐，使用进程
    BACKGROUND = "background"                # 后台任务，使用进程池

@dataclass
class TaskDefinition:
    """任务定义"""
    id: str
    processor_type: ProcessorType
    priority: int
    timeout_ms: int
    func: Callable
    args: tuple = ()
    kwargs: dict = None

class SharedMemoryManager:
    """共享内存管理器 - 避免死锁的关键"""
    
    def __init__(self, size_mb: int = 10):
        self.size_bytes = size_mb * 1024 * 1024
        self.shm = shared_memory.SharedMemory(create=True, size=self.size_bytes)
        self.mutex = mp.Lock()
        self.offsets = {}
        self.current_offset = 0
        
    def allocate(self, name: str, size: int) -> int:
        """分配共享内存区域"""
        with self.mutex:
            if name in self.offsets:
                return self.offsets[name]
            
            if self.current_offset + size > self.size_bytes:
                raise MemoryError("Shared memory exhausted")
            
            offset = self.current_offset
            self.offsets[name] = offset
            self.current_offset += size
            return offset
    
    def write(self, name: str, data: bytes):
        """写入共享内存"""
        offset = self.offsets.get(name)
        if offset is None:
            offset = self.allocate(name, len(data))
        
        self.shm.buf[offset:offset + len(data)] = data
    
    def read(self, name: str, size: int) -> bytes:
        """读取共享内存"""
        offset = self.offsets.get(name)
        if offset is None:
            return b""
        
        return bytes(self.shm.buf[offset:offset + size])
    
    def cleanup(self):
        """清理共享内存"""
        try:
            self.shm.close()
            self.shm.unlink()
        except:
            pass

class DeadlockFreeQueue:
    """无死锁队列 - 使用无锁算法"""
    
    def __init__(self, maxsize: int = 10000):
        self.maxsize = maxsize
        self.queue = mp.Queue(maxsize)
        self.stats = {
            'enqueued': mp.Value('i', 0),
            'dequeued': mp.Value('i', 0),
            'dropped': mp.Value('i', 0)
        }
    
    def put_nowait(self, item) -> bool:
        """非阻塞入队"""
        try:
            self.queue.put_nowait(item)
            with self.stats['enqueued'].get_lock():
                self.stats['enqueued'].value += 1
            return True
        except queue.Full:
            with self.stats['dropped'].get_lock():
                self.stats['dropped'].value += 1
            return False
    
    def get_nowait(self):
        """非阻塞出队"""
        try:
            item = self.queue.get_nowait()
            with self.stats['dequeued'].get_lock():
                self.stats['dequeued'].value += 1
            return item
        except queue.Empty:
            return None
    
    def get_stats(self) -> Dict[str, int]:
        """获取队列统计"""
        return {
            'enqueued': self.stats['enqueued'].value,
            'dequeued': self.stats['dequeued'].value,
            'dropped': self.stats['dropped'].value,
            'size': self.queue.qsize()
        }

class RealtimeThreadProcessor:
    """实时线程处理器 - 微秒级响应"""
    
    def __init__(self, thread_count: int = 4):
        self.thread_count = thread_count
        self.task_queue = asyncio.Queue(maxsize=1000)
        self.result_callbacks = {}
        self.workers = []
        self.running = False
        
        # 使用RLock避免死锁
        self.callback_lock = threading.RLock()
        
        # 性能统计
        self.stats = {
            'processed': 0,
            'avg_latency_us': 0,
            'errors': 0
        }
    
    async def start(self):
        """启动处理器"""
        self.running = True
        
        for i in range(self.thread_count):
            worker = asyncio.create_task(self._worker(i))
            self.workers.append(worker)
        
        logger.info(f"Realtime thread processor started with {self.thread_count} workers")
    
    async def stop(self):
        """停止处理器"""
        self.running = False
        
        for worker in self.workers:
            worker.cancel()
        
        await asyncio.gather(*self.workers, return_exceptions=True)
        logger.info("Realtime thread processor stopped")
    
    async def submit_task(self, task: TaskDefinition, callback: Optional[Callable] = None) -> bool:
        """提交任务"""
        try:
            if callback:
                with self.callback_lock:
                    self.result_callbacks[task.id] = callback
            
            await self.task_queue.put(task)
            return True
        except asyncio.QueueFull:
            logger.warning(f"Realtime queue full, dropping task {task.id}")
            return False
    
    async def _worker(self, worker_id: int):
        """工作线程"""
        logger.info(f"Realtime worker {worker_id} started")
        
        while self.running:
            try:
                # 设置超时避免死锁
                task = await asyncio.wait_for(self.task_queue.get(), timeout=1.0)
                
                # 执行任务
                start_time = time.time()
                try:
                    if asyncio.iscoroutinefunction(task.func):
                        result = await task.func(*task.args, **(task.kwargs or {}))
                    else:
                        result = task.func(*task.args, **(task.kwargs or {}))
                    
                    # 调用回调
                    with self.callback_lock:
                        callback = self.result_callbacks.pop(task.id, None)
                        if callback:
                            callback(result)
                    
                    # 更新统计
                    latency_us = (time.time() - start_time) * 1000000
                    self._update_stats(latency_us, False)
                    
                except Exception as e:
                    logger.error(f"Task {task.id} failed: {e}")
                    self._update_stats(0, True)
                
            except asyncio.TimeoutError:
                continue
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Worker {worker_id} error: {e}")
                await asyncio.sleep(0.1)
    
    def _update_stats(self, latency_us: float, is_error: bool):
        """更新统计信息"""
        self.stats['processed'] += 1
        
        if is_error:
            self.stats['errors'] += 1
        else:
            # 更新平均延迟
            current_avg = self.stats['avg_latency_us']
            processed = self.stats['processed']
            self.stats['avg_latency_us'] = (current_avg * (processed - 1) + latency_us) / processed

class HighThroughputProcessPool:
    """高吞吐进程池 - CPU密集型任务"""
    
    def __init__(self, process_count: int = None):
        self.process_count = process_count or mp.cpu_count()
        self.task_queue = DeadlockFreeQueue(maxsize=5000)
        self.result_queue = DeadlockFreeQueue(maxsize=5000)
        self.processes = []
        self.running = False
        
        # 共享内存管理
        self.shm_manager = SharedMemoryManager(size_mb=50)
        
        # 结果处理线程
        self.result_thread = None
    
    def start(self):
        """启动进程池"""
        self.running = True
        
        # 启动工作进程
        for i in range(self.process_count):
            p = mp.Process(target=self._worker_process, args=(i,))
            p.start()
            self.processes.append(p)
        
        # 启动结果处理线程
        self.result_thread = threading.Thread(target=self._result_handler, daemon=True)
        self.result_thread.start()
        
        logger.info(f"High throughput process pool started with {self.process_count} processes")
    
    def stop(self):
        """停止进程池"""
        self.running = False
        
        # 等待进程结束
        for p in self.processes:
            p.terminate()
            p.join(timeout=5)
            if p.is_alive():
                p.kill()
        
        # 清理共享内存
        self.shm_manager.cleanup()
        
        logger.info("High throughput process pool stopped")
    
    def submit_task(self, task: TaskDefinition, callback: Optional[Callable] = None) -> bool:
        """提交任务到进程池"""
        task_data = {
            'id': task.id,
            'func_name': task.func.__name__,
            'args': task.args,
            'kwargs': task.kwargs or {},
            'timeout_ms': task.timeout_ms,
            'callback': callback is not None
        }
        
        return self.task_queue.put_nowait(task_data)
    
    def _worker_process(self, worker_id: int):
        """工作进程"""
        logger.info(f"Process worker {worker_id} started (PID: {os.getpid()})")
        
        # 设置信号处理
        signal.signal(signal.SIGTERM, self._signal_handler)
        signal.signal(signal.SIGINT, self._signal_handler)
        
        while self.running:
            try:
                task_data = self.task_queue.get_nowait()
                if task_data is None:
                    time.sleep(0.001)  # 1ms
                    continue
                
                # 执行任务
                start_time = time.time()
                try:
                    # 这里需要动态导入函数
                    # 实际实现时应该有函数注册机制
                    result = self._execute_task(task_data)
                    
                    # 返回结果
                    result_data = {
                        'id': task_data['id'],
                        'result': result,
                        'success': True,
                        'execution_time_ms': (time.time() - start_time) * 1000,
                        'worker_id': worker_id
                    }
                    
                    self.result_queue.put_nowait(result_data)
                    
                except Exception as e:
                    logger.error(f"Process task {task_data['id']} failed: {e}")
                    
                    error_data = {
                        'id': task_data['id'],
                        'error': str(e),
                        'success': False,
                        'worker_id': worker_id
                    }
                    
                    self.result_queue.put_nowait(error_data)
                
            except Exception as e:
                logger.error(f"Process worker {worker_id} error: {e}")
                time.sleep(0.1)
    
    def _execute_task(self, task_data: Dict[str, Any]) -> Any:
        """执行任务（示例）"""
        # 这里应该有实际的任务执行逻辑
        # 根据func_name动态调用相应的函数
        func_name = task_data['func_name']
        args = task_data['args']
        kwargs = task_data['kwargs']
        
        # 模拟CPU密集型任务
        result = sum(i * i for i in range(1000))
        return result
    
    def _result_handler(self):
        """结果处理线程"""
        while self.running:
            try:
                result = self.result_queue.get_nowait()
                if result is None:
                    time.sleep(0.001)
                    continue
                
                # 处理结果
                if result.get('callback'):
                    # 这里应该调用相应的回调函数
                    pass
                
            except Exception as e:
                logger.error(f"Result handler error: {e}")
                time.sleep(0.1)
    
    def _signal_handler(self, signum, frame):
        """信号处理"""
        logger.info(f"Process received signal {signum}")
        self.running = False
    
    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        return {
            'task_queue': self.task_queue.get_stats(),
            'result_queue': self.result_queue.get_stats(),
            'active_processes': len([p for p in self.processes if p.is_alive()]),
            'total_processes': len(self.processes)
        }

class HybridProcessor:
    """混合处理器 - 统一管理多种并发模式"""
    
    def __init__(self):
        self.realtime_processor = RealtimeThreadProcessor(thread_count=4)
        self.process_pool = HighThroughputProcessPool()
        self.background_executor = ThreadPoolExecutor(max_workers=2)
        
        self.running = False
        self.task_router = {}
        
        # 注册默认任务路由
        self._register_default_routes()
    
    def _register_default_routes(self):
        """注册默认任务路由"""
        self.task_router.update({
            # 微秒级任务 - 使用线程
            'price_update': ProcessorType.CRITICAL_REALTIME,
            'signal_processing': ProcessorType.CRITICAL_REALTIME,
            'order_validation': ProcessorType.CRITICAL_REALTIME,
            
            # 高吞吐任务 - 使用进程
            'risk_calculation': ProcessorType.HIGH_THROUGHPUT,
            'portfolio_analysis': ProcessorType.HIGH_THROUGHPUT,
            'backtesting': ProcessorType.HIGH_THROUGHPUT,
            
            # 后台任务 - 使用线程池
            'log_processing': ProcessorType.BACKGROUND,
            'data_cleanup': ProcessorType.BACKGROUND,
            'report_generation': ProcessorType.BACKGROUND,
        })
    
    async def start(self):
        """启动混合处理器"""
        self.running = True
        
        # 启动实时处理器
        await self.realtime_processor.start()
        
        # 启动进程池
        self.process_pool.start()
        
        logger.info("Hybrid processor started successfully")
    
    async def stop(self):
        """停止混合处理器"""
        self.running = False
        
        # 停止实时处理器
        await self.realtime_processor.stop()
        
        # 停止进程池
        self.process_pool.stop()
        
        # 关闭后台执行器
        self.background_executor.shutdown(wait=True)
        
        logger.info("Hybrid processor stopped")
    
    async def submit_task(self, task: TaskDefinition, callback: Optional[Callable] = None) -> bool:
        """提交任务到适当的处理器"""
        if not self.running:
            return False
        
        processor_type = task.processor_type
        
        if processor_type == ProcessorType.CRITICAL_REALTIME:
            return await self.realtime_processor.submit_task(task, callback)
        
        elif processor_type == ProcessorType.HIGH_THROUGHPUT:
            return self.process_pool.submit_task(task, callback)
        
        elif processor_type == ProcessorType.BACKGROUND:
            try:
                future = self.background_executor.submit(task.func, *task.args, **(task.kwargs or {}))
                if callback:
                    future.add_done_callback(lambda f: callback(f.result()))
                return True
            except Exception as e:
                logger.error(f"Background task submission failed: {e}")
                return False
        
        return False
    
    def auto_route_task(self, task_name: str, func: Callable, *args, **kwargs) -> Optional[TaskDefinition]:
        """自动路由任务"""
        processor_type = self.task_router.get(task_name, ProcessorType.BACKGROUND)
        
        return TaskDefinition(
            id=f"{task_name}_{time.time()}",
            processor_type=processor_type,
            priority=1,
            timeout_ms=1000,
            func=func,
            args=args,
            kwargs=kwargs
        )
    
    def get_comprehensive_stats(self) -> Dict[str, Any]:
        """获取综合统计信息"""
        return {
            'realtime_processor': self.realtime_processor.stats,
            'process_pool': self.process_pool.get_stats(),
            'background_executor': {
                'active_threads': self.background_executor._threads,
                'pending_tasks': self.background_executor._work_queue.qsize()
            },
            'hybrid_processor': {
                'running': self.running,
                'registered_routes': len(self.task_router)
            }
        }

# 使用示例
async def example_usage():
    """使用示例"""
    processor = HybridProcessor()
    
    try:
        await processor.start()
        
        # 提交不同类型的任务
        
        # 1. 微秒级实时任务
        realtime_task = TaskDefinition(
            id="price_update_1",
            processor_type=ProcessorType.CRITICAL_REALTIME,
            priority=1,
            timeout_ms=1,
            func=lambda: time.time()  # 模拟价格更新
        )
        
        await processor.submit_task(realtime_task)
        
        # 2. 高吞吐CPU密集型任务
        cpu_task = TaskDefinition(
            id="risk_calc_1",
            processor_type=ProcessorType.HIGH_THROUGHPUT,
            priority=2,
            timeout_ms=100,
            func=lambda: sum(i*i for i in range(10000))  # 模拟风险计算
        )
        
        await processor.submit_task(cpu_task)
        
        # 3. 后台任务
        bg_task = TaskDefinition(
            id="log_cleanup_1",
            processor_type=ProcessorType.BACKGROUND,
            priority=3,
            timeout_ms=5000,
            func=lambda: "Log cleanup completed"
        )
        
        await processor.submit_task(bg_task)
        
        # 运行一段时间
        await asyncio.sleep(2)
        
        # 打印统计信息
        stats = processor.get_comprehensive_stats()
        print(f"Processor stats: {stats}")
        
    finally:
        await processor.stop()

if __name__ == "__main__":
    asyncio.run(example_usage())