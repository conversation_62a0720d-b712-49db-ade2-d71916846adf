"""
分布式流配置实现
具体的流创建和管理代码
"""

from typing import Dict, List, Optional, Set
import hashlib
import asyncio
from datetime import datetime

from src.messaging.jetstream_client import JetStreamClient, JetStreamConfig
from src.utils.logger import get_logger

logger = get_logger(__name__)


class DistributedStreamSetup:
    """分布式流设置和管理"""
    
    def __init__(self, cluster_config: Dict):
        """
        cluster_config = {
            'cluster_name': 'mt5_trading_cluster',
            'nats_servers': ['nats://host1:4222', 'nats://host2:4222'],
            'host_id': 'host001',
            'datacenter': 'dc1'
        }
        """
        self.cluster_config = cluster_config
        self.js_clients: Dict[str, JetStreamClient] = {}
        
    async def setup_all_streams(self):
        """设置所有必需的流"""
        
        # 1. 本地流（每个主机一个）
        await self._setup_local_stream()
        
        # 2. 全局流（集群共享）
        await self._setup_global_stream()
        
        # 3. 路由流（集群共享）
        await self._setup_routing_stream()
        
        # 4. 控制流（集群共享）
        await self._setup_control_stream()
        
        # 5. 分片流（可选）
        await self._setup_sharded_streams()
        
        logger.info("✅ 分布式流设置完成")
    
    async def _setup_local_stream(self):
        """设置本地流"""
        host_id = self.cluster_config['host_id']
        
        config = {
            "name": f"MT5_LOCAL_{host_id}",
            "subjects": [
                f"MT5.LOCAL.{host_id}.TRADES.*",
                f"MT5.LOCAL.{host_id}.SIGNALS.*",
                f"MT5.LOCAL.{host_id}.ORDERS.*",
                f"MT5.LOCAL.{host_id}.STATUS.*"
            ],
            "retention": "limits",
            "max_age": 86400,  # 24小时
            "max_msgs": 1000000,
            "max_bytes": 1 * 1024 * 1024 * 1024,  # 1GB
            "storage": "file",
            "num_replicas": 1,  # 本地流不需要副本
            "placement": {
                "cluster": self.cluster_config['cluster_name'],
                "tags": [host_id]  # 限制在特定主机
            }
        }
        
        await self._create_stream(config)
        logger.info(f"✅ 本地流创建成功: {config['name']}")
    
    async def _setup_global_stream(self):
        """设置全局流"""
        config = {
            "name": "MT5_GLOBAL_STREAM",
            "subjects": [
                "MT5.GLOBAL.TRADES.*",
                "MT5.GLOBAL.SIGNALS.*",
                "MT5.GLOBAL.ORDERS.*",
                "MT5.GLOBAL.BROADCAST.*"
            ],
            "retention": "limits",
            "max_age": 604800,  # 7天
            "max_msgs": ********,
            "max_bytes": 10 * 1024 * 1024 * 1024,  # 10GB
            "storage": "file",
            "num_replicas": 3,  # 3副本保证高可用
            "placement": {
                "cluster": self.cluster_config['cluster_name'],
                "num_replicas": 3,
                "tags": []  # 不限制位置，自动分布
            }
        }
        
        await self._create_stream(config)
        logger.info(f"✅ 全局流创建成功: {config['name']}")
    
    async def _setup_routing_stream(self):
        """设置路由流"""
        config = {
            "name": "MT5_ROUTING_STREAM",
            "subjects": [
                "MT5.ROUTING.TOPOLOGY",
                "MT5.ROUTING.ACCOUNTS",
                "MT5.ROUTING.HEALTH",
                "MT5.ROUTING.METRICS"
            ],
            "retention": "limits",
            "max_age": 3600,  # 1小时
            "max_msgs": 100000,
            "max_bytes": 100 * 1024 * 1024,  # 100MB
            "storage": "memory",  # 使用内存存储提高性能
            "num_replicas": 3,
            "allow_rollup_hdrs": True  # 允许汇总消息
        }
        
        await self._create_stream(config)
        logger.info(f"✅ 路由流创建成功: {config['name']}")
    
    async def _setup_sharded_streams(self, shard_count: int = 16):
        """设置分片流（用于大规模部署）"""
        for shard_id in range(shard_count):
            config = {
                "name": f"MT5_SHARD_{shard_id:02d}",
                "subjects": [
                    f"MT5.SHARD.{shard_id:02d}.TRADES.*",
                    f"MT5.SHARD.{shard_id:02d}.SIGNALS.*"
                ],
                "retention": "limits",
                "max_age": 86400,  # 24小时
                "max_msgs": 1000000,
                "max_bytes": 1 * 1024 * 1024 * 1024,  # 1GB
                "storage": "file",
                "num_replicas": 2,  # 2副本平衡性能和可靠性
                "mirror": {
                    # 可选：镜像到备份集群
                    "name": f"MT5_SHARD_{shard_id:02d}_MIRROR",
                    "external": {
                        "api_prefix": "$JS.backup.API"
                    }
                }
            }
            
            await self._create_stream(config)
        
        logger.info(f"✅ 创建了 {shard_count} 个分片流")
    
    async def _create_stream(self, config: Dict):
        """创建流的通用方法"""
        # 实际创建流的代码
        # 这里应该调用 JetStream API
        pass


class DistributedConsumerStrategy:
    """分布式消费者策略"""
    
    @staticmethod
    def get_consumer_config(account_id: str, master_id: str, 
                          is_local: bool, host_id: str) -> Dict:
        """根据场景返回最优消费者配置"""
        
        base_config = {
            "durable_name": f"{account_id}_follows_{master_id}",
            "deliver_policy": "new",  # 只接收新消息
            "ack_policy": "explicit",
            "replay_policy": "instant",
            "max_deliver": 3,
            "max_ack_pending": 100,
            "inactive_threshold": ***********0  # 5分钟不活跃则暂停
        }
        
        if is_local:
            # 本地消费者：低延迟配置
            return {
                **base_config,
                "filter_subject": f"MT5.LOCAL.{host_id}.TRADES.{master_id}",
                "ack_wait": ********000,  # 10秒
                "deliver_group": None,  # 不使用队列组，每个从账户独立接收
                "rate_limit_bps": 0,  # 不限速
                "headers_only": False
            }
        else:
            # 远程消费者：可靠性优先
            return {
                **base_config,
                "filter_subject": f"MT5.GLOBAL.TRADES.{master_id}",
                "ack_wait": ***********,  # 30秒
                "deliver_group": f"slaves_of_{master_id}",  # 可选：使用队列组
                "max_deliver": 5,  # 更多重试
                "rate_limit_bps": 1000000,  # 1MB/s 限速避免网络拥塞
                "sample_frequency": "100%"  # 接收所有消息
            }


class AccountShardingStrategy:
    """账户分片策略"""
    
    def __init__(self, shard_count: int = 16):
        self.shard_count = shard_count
        self.replication_factor = 2
        
    def get_shard_id(self, account_id: str) -> int:
        """使用一致性哈希获取分片ID"""
        hash_value = hashlib.md5(account_id.encode()).hexdigest()
        return int(hash_value, 16) % self.shard_count
    
    def get_replica_shards(self, primary_shard: int) -> List[int]:
        """获取副本分片"""
        replicas = []
        for i in range(1, self.replication_factor):
            replica_shard = (primary_shard + i) % self.shard_count
            replicas.append(replica_shard)
        return replicas
    
    def get_stream_for_account(self, account_id: str) -> str:
        """获取账户应该使用的流"""
        shard_id = self.get_shard_id(account_id)
        return f"MT5_SHARD_{shard_id:02d}"
    
    def get_subjects_for_account(self, account_id: str, signal_type: str) -> List[str]:
        """获取账户应该发布到的主题"""
        shard_id = self.get_shard_id(account_id)
        subjects = [
            # 主分片
            f"MT5.SHARD.{shard_id:02d}.{signal_type}.{account_id}"
        ]
        
        # 添加副本分片
        for replica_shard in self.get_replica_shards(shard_id):
            subjects.append(
                f"MT5.SHARD.{replica_shard:02d}.{signal_type}.{account_id}.REPLICA"
            )
        
        return subjects


class NetworkTopologyManager:
    """网络拓扑管理器"""
    
    def __init__(self, host_id: str):
        self.host_id = host_id
        self.topology = {
            'hosts': {},
            'datacenters': {},
            'latency_matrix': {},
            'bandwidth_matrix': {}
        }
        
    async def measure_latency(self, target_host: str) -> float:
        """测量到目标主机的延迟"""
        # 发送ping消息并测量响应时间
        start_time = asyncio.get_event_loop().time()
        
        # 实际实现应该发送NATS请求并等待响应
        # 这里简化为随机延迟
        import random
        await asyncio.sleep(random.uniform(0.001, 0.1))
        
        latency = (asyncio.get_event_loop().time() - start_time) * 1000  # 毫秒
        
        # 更新延迟矩阵
        if self.host_id not in self.topology['latency_matrix']:
            self.topology['latency_matrix'][self.host_id] = {}
        
        self.topology['latency_matrix'][self.host_id][target_host] = latency
        
        return latency
    
    def get_nearest_hosts(self, count: int = 3) -> List[str]:
        """获取延迟最低的主机"""
        latencies = self.topology['latency_matrix'].get(self.host_id, {})
        sorted_hosts = sorted(latencies.items(), key=lambda x: x[1])
        return [host for host, _ in sorted_hosts[:count]]
    
    def should_use_local_route(self, target_host: str) -> bool:
        """判断是否应该使用本地路由"""
        # 同一数据中心
        if self._same_datacenter(target_host):
            return True
        
        # 延迟小于阈值（如10ms）
        latency = self.topology['latency_matrix'].get(self.host_id, {}).get(target_host, float('inf'))
        if latency < 10:
            return True
        
        return False
    
    def _same_datacenter(self, target_host: str) -> bool:
        """检查是否在同一数据中心"""
        my_dc = self.topology['hosts'].get(self.host_id, {}).get('datacenter')
        target_dc = self.topology['hosts'].get(target_host, {}).get('datacenter')
        return my_dc and target_dc and my_dc == target_dc


# ========== 最佳实践配置 ==========

def get_recommended_config() -> Dict:
    """获取推荐的分布式配置"""
    return {
        # NATS集群配置
        'nats_cluster': {
            'servers': [
                'nats://nats1.dc1:4222',
                'nats://nats2.dc1:4222',
                'nats://nats3.dc1:4222',
                'nats://nats1.dc2:4222',
                'nats://nats2.dc2:4222'
            ],
            'cluster_name': 'mt5-trading',
            'routes': [
                'nats://nats1.dc1:6222',
                'nats://nats1.dc2:6222'
            ]
        },
        
        # JetStream配置
        'jetstream': {
            'max_memory_store': 4 * 1024 * 1024 * 1024,  # 4GB
            'max_file_store': 100 * 1024 * 1024 * 1024,  # 100GB
            'store_dir': '/data/nats/jetstream'
        },
        
        # 流配置建议
        'streams': {
            'local_stream_per_host': True,
            'global_stream_replicas': 3,
            'shard_count': 16,  # 对于大规模部署
            'retention_days': 7
        },
        
        # 消费者配置建议
        'consumers': {
            'max_ack_pending': 100,
            'ack_wait_seconds': 30,
            'max_deliver': 5,
            'inactive_threshold_minutes': 5
        },
        
        # 性能优化
        'performance': {
            'batch_size': 100,
            'batch_timeout_ms': 50,
            'compression_threshold_bytes': 1024,
            'use_memory_storage_for_routing': True
        }
    }
