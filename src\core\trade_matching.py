"""
工业级交易匹配系统 - 简化版
解决comment字段依赖问题，提供可靠的持仓匹配机制
"""
import asyncio
import time
import uuid
import hashlib
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from enum import Enum

from ..utils.thread_safe_stats import get_stats
from ..utils.logger import get_logger

logger = get_logger(__name__)


class MatchConfidence(Enum):
    """匹配置信度级别"""
    EXACT = 0.95      # 精确匹配（UUID+时间戳）
    HIGH = 0.85       # 高置信度（时间+品种+手数）
    MEDIUM = 0.70     # 中等置信度（时间+品种）
    LOW = 0.50        # 低置信度（仅品种）
    NONE = 0.0        # 无匹配


# Import unified Pydantic models instead of defining dataclasses
from ..messaging.message_types import TradeMapping, MatchResult


class IndustrialTradeMatching:
    """工业级交易匹配系统 - 分布式状态管理"""
    
    def __init__(self, state_manager=None):
        # 分布式状态管理器
        self.state_manager = state_manager
        self.use_distributed_state = state_manager is not None
        
        # 本地缓存（仅用于性能优化，非持久化）
        self._local_cache: Dict[str, TradeMapping] = {}
        self._cache_ttl = 300  # 5分钟缓存TTL
        
        # 统计系统
        self.stats = get_stats("trade_matching")
        
        # 匹配配置
        self.time_window_seconds = 300  # 5分钟匹配窗口
        self.volume_tolerance = 0.01    # 1%手数容忍度
        self.price_tolerance = 0.001    # 0.1%价格容忍度
        
        # 缓存清理
        self._last_cleanup = time.time()
        self._cleanup_interval = 3600  # 1小时清理一次
        
        # 分布式状态键前缀
        self.mapping_prefix = "trade_mapping:"
        self.signal_prefix = "signal_cache:"
        
        logger.info(f"工业级交易匹配系统初始化完成 - 分布式状态: {self.use_distributed_state}")
    
    async def create_signal_mapping(self,
                                   master_account: str,
                                   master_ticket: int,
                                   slave_account: str,
                                   signal: Dict) -> str:
        """创建信号映射 - 开仓时调用"""

        signal_uuid = str(uuid.uuid4())
        mapping_id = f"{master_account}_{master_ticket}_{slave_account}_{int(time.time())}"

        mapping = TradeMapping(
            mapping_id=mapping_id,
            master_account=master_account,
            master_ticket=master_ticket,
            slave_account=slave_account,
            symbol=signal.get('symbol', ''),
            volume=signal.get('volume', 0.0),
            copy_ratio=signal.get('copy_ratio', 1.0),
            signal_uuid=signal_uuid,
            open_time=datetime.now(),
            status="pending",
            metadata={
                'signal_id': signal.get('signal_id', ''),
                'signal_type': signal.get('signal_type', ''),
                'price': signal.get('price', 0.0),
                'creation_time': time.time()
            }
        )

        # 存储映射到分布式状态
        await self._store_mapping(mapping_id, mapping)

        # 缓存信号信息到分布式状态
        cache_key = f"{slave_account}_{signal['symbol']}_{int(time.time() / 60)}"  # 按分钟缓存
        cache_data = {
            'mapping_id': mapping_id,
            'signal_uuid': signal_uuid,
            'symbol': signal['symbol'],
            'volume': signal['volume'],
            'timestamp': time.time(),
            'account': slave_account
        }
        await self._store_signal_cache(cache_key, cache_data)

        await self.stats.increment("mappings_created")

        logger.info(f"创建交易映射: {mapping_id}",
                   master_account=master_account,
                   slave_account=slave_account,
                   symbol=signal['symbol'])

        return signal_uuid

    async def activate_mapping(self, mapping_id: str, slave_ticket: int):
        """激活映射 - 从账户成功开仓后调用"""
        mapping = await self._get_mapping(mapping_id)
        if mapping:
            mapping.slave_ticket = slave_ticket
            mapping.status = "active"
            mapping.metadata['activation_time'] = time.time()
            
            await self._store_mapping(mapping_id, mapping)
            await self.stats.increment("mappings_activated")
            logger.info(f"激活交易映射: {mapping_id} -> 从账户订单: {slave_ticket}")
        else:
            logger.warning(f"映射不存在，无法激活: {mapping_id}")

    async def find_mapping_by_signal_uuid(self, signal_uuid: str) -> Optional[TradeMapping]:
        """根据信号UUID查找映射"""
        # 从分布式状态中查找
        return await self._find_mapping_by_uuid(signal_uuid)
    
    async def update_slave_execution(self, 
                                   signal_uuid: str,
                                   slave_ticket: int,
                                   execution_result: Dict) -> bool:
        """更新从账户执行结果"""
        
        # 查找对应的映射
        mapping = await self._find_mapping_by_uuid(signal_uuid)
        
        if not mapping:
            logger.warning(f"未找到信号映射: {signal_uuid}")
            await self.stats.increment("mapping_not_found")
            return False
        
        # 更新映射信息
        mapping.slave_ticket = slave_ticket
        mapping.status = "active" if execution_result.get('success', False) else "failed"
        mapping.metadata.update({
            'execution_time': time.time(),
            'execution_result': execution_result
        })
        
        # 保存到分布式状态
        await self._store_mapping(mapping.mapping_id, mapping)
        
        await self.stats.increment("slave_executions_updated")
        
        logger.info(f"更新从账户执行: {mapping.mapping_id}",
                   slave_ticket=slave_ticket,
                   status=mapping.status)
        
        return True
    
    async def find_matching_positions(self, 
                                    close_signal: Dict,
                                    slave_account: str,
                                    current_positions: List[Dict]) -> MatchResult:
        """查找匹配的持仓进行平仓"""
        
        symbol = close_signal.get('symbol', '')
        master_account = close_signal.get('master_account', '')
        close_volume = close_signal.get('volume', 0.0)
        
        # 策略1：基于映射表的精确匹配（最高优先级）
        exact_matches = await self._exact_mapping_match(
            master_account, close_signal.get('master_ticket', 0), 
            slave_account, current_positions
        )
        
        if exact_matches.confidence >= MatchConfidence.EXACT.value:
            await self.stats.increment("exact_matches")
            return exact_matches
        
        # 策略2：基于时间+品种+手数的智能匹配
        smart_matches = await self._smart_position_match(
            close_signal, slave_account, current_positions
        )
        
        if smart_matches.confidence >= MatchConfidence.HIGH.value:
            await self.stats.increment("smart_matches")
            return smart_matches
        
        # 策略3：基于品种的模糊匹配
        fuzzy_matches = await self._fuzzy_position_match(
            symbol, close_volume, current_positions
        )
        
        if fuzzy_matches.confidence >= MatchConfidence.MEDIUM.value:
            await self.stats.increment("fuzzy_matches")
            fuzzy_matches.warnings.append("使用模糊匹配，建议人工核实")
            return fuzzy_matches
        
        await self.stats.increment("no_matches")
        return MatchResult(
            confidence=MatchConfidence.NONE.value,
            matched_positions=[],
            execution_plan=[],
            warnings=[f"未找到匹配持仓: {symbol} {close_volume}手"]
        )
    
    async def _exact_mapping_match(self, 
                                 master_account: str,
                                 master_ticket: int,
                                 slave_account: str,
                                 positions: List[Dict]) -> MatchResult:
        """基于映射表的精确匹配"""
        
        matched_positions = []
        total_volume = 0.0
        
        # 从分布式状态查找映射
        mappings = await self._get_all_mappings()
        for mapping in mappings:
            if (mapping.master_account == master_account and 
                mapping.master_ticket == master_ticket and
                mapping.slave_account == slave_account and
                mapping.status == "active"):
                
                for pos in positions:
                    if pos.get('ticket') == mapping.slave_ticket:
                        matched_positions.append({
                            'position': pos,
                            'mapping': mapping,
                            'match_method': 'exact_mapping'
                        })
                        total_volume += pos.get('volume', 0.0)
                        break
        
        confidence = MatchConfidence.EXACT.value if matched_positions else 0.0
        
        return MatchResult(
            confidence=confidence,
            matched_positions=matched_positions,
            execution_plan=self._create_execution_plan(matched_positions, "close_exact")
        )
    
    async def _smart_position_match(self,
                                  close_signal: Dict,
                                  slave_account: str,
                                  positions: List[Dict]) -> MatchResult:
        """基于时间+品种+手数的智能匹配"""
        
        symbol = close_signal.get('symbol', '')
        signal_time = close_signal.get('timestamp', time.time())
        target_volume = close_signal.get('volume', 0.0)
        
        candidates = []
        
        for pos in positions:
            if pos.get('symbol') != symbol:
                continue
                
            pos_time = pos.get('time', 0)
            pos_volume = pos.get('volume', 0.0)
            
            time_diff = abs(signal_time - pos_time)
            if time_diff > self.time_window_seconds:
                continue
                
            time_score = 1.0 - (time_diff / self.time_window_seconds)
            
            volume_diff = abs(target_volume - pos_volume) / max(target_volume, pos_volume, 0.01)
            volume_score = 1.0 - min(volume_diff / self.volume_tolerance, 1.0)
            
            overall_score = time_score * 0.6 + volume_score * 0.4
            
            if overall_score >= 0.7:  # 70%以上才考虑
                candidates.append({
                    'position': pos,
                    'score': overall_score,
                    'time_score': time_score,
                    'volume_score': volume_score,
                    'match_method': 'smart_matching'
                })
        
        if candidates:
            best_match = max(candidates, key=lambda x: x['score'])
            confidence = best_match['score'] * MatchConfidence.HIGH.value
            
            return MatchResult(
                confidence=confidence,
                matched_positions=[best_match],
                execution_plan=self._create_execution_plan([best_match], "close_smart")
            )
        
        return MatchResult(confidence=0.0, matched_positions=[], execution_plan=[])
    
    async def _fuzzy_position_match(self,
                                  symbol: str,
                                  volume: float,
                                  positions: List[Dict]) -> MatchResult:
        """基于品种的模糊匹配"""
        
        symbol_positions = [pos for pos in positions if pos.get('symbol') == symbol]
        
        if symbol_positions:
            best_pos = min(symbol_positions, 
                          key=lambda p: abs(p.get('volume', 0) - volume))
            
            match = {
                'position': best_pos,
                'match_method': 'fuzzy_symbol',
                'volume_diff': abs(best_pos.get('volume', 0) - volume)
            }
            
            confidence = MatchConfidence.MEDIUM.value * 0.8  # 模糊匹配降低置信度
            
            return MatchResult(
                confidence=confidence,
                matched_positions=[match],
                execution_plan=self._create_execution_plan([match], "close_fuzzy"),
                warnings=["使用品种模糊匹配"]
            )
        
        return MatchResult(confidence=0.0, matched_positions=[], execution_plan=[])
    
    # ===== 分布式状态管理方法 =====
    
    async def _store_mapping(self, mapping_id: str, mapping: TradeMapping):
        """存储映射到分布式状态"""
        try:
            if self.use_distributed_state:
                # 使用分布式状态管理器
                key = f"{self.mapping_prefix}{mapping_id}"
                mapping_dict = {
                    'mapping_id': mapping.mapping_id,
                    'master_account': mapping.master_account,
                    'master_ticket': mapping.master_ticket,
                    'slave_account': mapping.slave_account,
                    'slave_ticket': mapping.slave_ticket,
                    'symbol': mapping.symbol,
                    'volume': mapping.volume,
                    'copy_ratio': mapping.copy_ratio,
                    'signal_uuid': mapping.signal_uuid,
                    'open_time': mapping.open_time.isoformat(),
                    'status': mapping.status,
                    'metadata': mapping.metadata
                }
                await self.state_manager.set(key, mapping_dict, ttl=86400)  # 24小时TTL
                logger.debug(f"映射存储到分布式状态: {mapping_id}")
            else:
                # 回退到本地存储（开发模式）
                self._local_cache[mapping_id] = mapping
                logger.debug(f"映射存储到本地缓存: {mapping_id}")
                
        except Exception as e:
            logger.error(f"存储映射失败: {e}")
            # 总是保存到本地作为备份
            self._local_cache[mapping_id] = mapping
    
    async def _get_mapping(self, mapping_id: str) -> Optional[TradeMapping]:
        """从分布式状态获取映射"""
        try:
            # 先尝试本地缓存
            if mapping_id in self._local_cache:
                cached_mapping = self._local_cache[mapping_id]
                # 检查缓存是否过期
                if time.time() - cached_mapping.metadata.get('cache_time', 0) < self._cache_ttl:
                    return cached_mapping
            
            if self.use_distributed_state:
                # 从分布式状态获取
                key = f"{self.mapping_prefix}{mapping_id}"
                mapping_dict = await self.state_manager.get(key)
                
                if mapping_dict:
                    mapping = TradeMapping(
                        mapping_id=mapping_dict['mapping_id'],
                        master_account=mapping_dict['master_account'],
                        master_ticket=mapping_dict['master_ticket'],
                        slave_account=mapping_dict['slave_account'],
                        slave_ticket=mapping_dict.get('slave_ticket'),
                        symbol=mapping_dict['symbol'],
                        volume=mapping_dict['volume'],
                        copy_ratio=mapping_dict['copy_ratio'],
                        signal_uuid=mapping_dict['signal_uuid'],
                        open_time=datetime.fromisoformat(mapping_dict['open_time']),
                        status=mapping_dict['status'],
                        metadata=mapping_dict['metadata']
                    )
                    
                    # 更新本地缓存
                    mapping.metadata['cache_time'] = time.time()
                    self._local_cache[mapping_id] = mapping
                    return mapping
            else:
                # 使用本地存储
                return self._local_cache.get(mapping_id)
                
        except Exception as e:
            logger.error(f"获取映射失败: {e}")
            # 尝试本地缓存
            return self._local_cache.get(mapping_id)
        
        return None
    
    async def _store_signal_cache(self, cache_key: str, cache_data: Dict):
        """存储信号缓存到分布式状态"""
        try:
            if self.use_distributed_state:
                key = f"{self.signal_prefix}{cache_key}"
                await self.state_manager.set(key, cache_data, ttl=3600)  # 1小时TTL
                logger.debug(f"信号缓存存储到分布式状态: {cache_key}")
            else:
                # 本地存储模拟
                logger.debug(f"信号缓存存储到本地: {cache_key}")
                
        except Exception as e:
            logger.error(f"存储信号缓存失败: {e}")
    
    async def _find_mapping_by_uuid(self, signal_uuid: str) -> Optional[TradeMapping]:
        """根据UUID查找映射"""
        try:
            if self.use_distributed_state:
                # 从分布式状态查找（需要实现索引或搜索功能）
                # 简化实现：遍历所有映射
                mappings = await self._get_all_mappings()
                for mapping in mappings:
                    if mapping.signal_uuid == signal_uuid:
                        return mapping
            else:
                # 本地缓存查找
                for mapping in self._local_cache.values():
                    if mapping.signal_uuid == signal_uuid:
                        return mapping
                        
        except Exception as e:
            logger.error(f"根据UUID查找映射失败: {e}")
        
        return None
    
    async def _get_all_mappings(self) -> List[TradeMapping]:
        """获取所有映射"""
        mappings = []
        try:
            if self.use_distributed_state:
                # 从分布式状态获取所有映射
                pattern = f"{self.mapping_prefix}*"
                keys = await self.state_manager.scan_keys(pattern)
                
                for key in keys:
                    mapping_dict = await self.state_manager.get(key)
                    if mapping_dict:
                        mapping = TradeMapping(
                            mapping_id=mapping_dict['mapping_id'],
                            master_account=mapping_dict['master_account'],
                            master_ticket=mapping_dict['master_ticket'],
                            slave_account=mapping_dict['slave_account'],
                            slave_ticket=mapping_dict.get('slave_ticket'),
                            symbol=mapping_dict['symbol'],
                            volume=mapping_dict['volume'],
                            copy_ratio=mapping_dict['copy_ratio'],
                            signal_uuid=mapping_dict['signal_uuid'],
                            open_time=datetime.fromisoformat(mapping_dict['open_time']),
                            status=mapping_dict['status'],
                            metadata=mapping_dict['metadata']
                        )
                        mappings.append(mapping)
            else:
                # 返回本地缓存
                mappings = list(self._local_cache.values())
                
        except Exception as e:
            logger.error(f"获取所有映射失败: {e}")
            # 回退到本地缓存
            mappings = list(self._local_cache.values())
        
        return mappings
    
    def _create_execution_plan(self, matches: List[Dict], plan_type: str) -> List[Dict]:
        """创建执行计划"""
        
        plan = []
        for match in matches:
            pos = match['position']
            plan.append({
                'action': 'close_position',
                'ticket': pos.get('ticket'),
                'volume': pos.get('volume'),
                'symbol': pos.get('symbol'),
                'method': match.get('match_method', 'unknown'),
                'confidence': match.get('score', 0.0)
            })
        
        return plan
    
    async def cleanup_expired_data(self):
        """清理过期数据"""
        
        current_time = time.time()
        
        if current_time - self._last_cleanup < self._cleanup_interval:
            return
        
        expired_signals = []
        for key, signal_data in self.signal_cache.items():
            if current_time - signal_data['timestamp'] > 3600:
                expired_signals.append(key)
        
        for key in expired_signals:
            del self.signal_cache[key]
        
        expired_mappings = []
        for mapping_id, mapping in self.position_mappings.items():
            if (mapping.status in ['closed', 'failed'] and 
                current_time - mapping.metadata.get('close_time', current_time) > 86400):
                expired_mappings.append(mapping_id)
        
        for mapping_id in expired_mappings:
            del self.position_mappings[mapping_id]
        
        self._last_cleanup = current_time
        
        if expired_signals or expired_mappings:
            logger.info(f"🧹 清理过期数据: {len(expired_signals)}个信号, {len(expired_mappings)}个映射")
            await self.stats.increment("cleanup_operations")
    
    async def mark_position_closed(self, mapping_id: str):
        """标记持仓已关闭"""
        
        mapping = await self._get_mapping(mapping_id)
        if mapping:
            mapping.status = "closed"
            mapping.metadata['close_time'] = time.time()
            
            # 保存到分布式状态
            await self._store_mapping(mapping_id, mapping)
            
            logger.info(f"标记持仓已关闭: {mapping_id}")
    
    async def get_matching_stats(self) -> Dict:
        """获取匹配统计信息"""
        
        # 从分布式状态获取统计
        mappings = await self._get_all_mappings()
        active_mappings = len([m for m in mappings if m.status == "active"])
        pending_mappings = len([m for m in mappings if m.status == "pending"])
        
        return {
            'total_mappings': len(mappings),
            'active_mappings': active_mappings,
            'pending_mappings': pending_mappings,
            'cached_signals': len(self._local_cache),  # 本地缓存数量
            'distributed_state': self.use_distributed_state,
            'stats': await self.stats.get_all_stats()
        }


_trade_matching_instance = None


def get_trade_matching(state_manager=None) -> IndustrialTradeMatching:
    """获取交易匹配系统实例"""
    global _trade_matching_instance
    if _trade_matching_instance is None:
        _trade_matching_instance = IndustrialTradeMatching(state_manager=state_manager)
    return _trade_matching_instance