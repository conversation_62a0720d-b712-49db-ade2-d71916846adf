# ACC001 - 主账户配置
# 只包含账户特定的配置，主机分配和跟单关系在其他配置文件中定义

# ============================================================================
# 账户基础信息
# ============================================================================
account:
  id: "ACC001"
  name: "账户1"
  enabled: true
  description: "高频交易账户，具备信号生成能力"
  
  # 账户能力定义（角色由关系管理系统动态分配）
  capabilities:
    can_generate_signals: true      # 能否生成交易信号
    can_follow_signals: true        # 能否跟随交易信号
    signal_priority: "high"         # 信号优先级
    max_followers: 10               # 最大跟随者数量
  
# ============================================================================
# MT5连接配置
# ============================================================================
mt5:
  connection:
    login: ********
    password: Jingenqi0724@ # ${MT5_ACC001_PASSWORD} 
    server: "TradeMaxGlobal-Demo"
    terminal_path: "D:/MetaTrader5/v1-mt5/terminal64.exe"
    
  # 连接参数
  connection_params:
    timeout: 10
    retry_attempts: 3
    retry_delay: 5
    auto_restart: true
    max_restarts: 5
    
  # 连接池配置
  connection_pool:
    enabled: true
    max_connections: 5
    idle_timeout: 300

# ============================================================================
# 交易配置
# ============================================================================
trading:
  # 基础交易参数
  parameters:
    magic_number: 12345
    slippage: 3
    max_deviation: 20
    
  # 手数限制
  volume_limits:
    max_volume: 100.0
    min_volume: 0.01
    default_volume: 1.0
    
  # 允许的交易品种
  allowed_symbols:
    mode: "all"                     # all/whitelist/blacklist
    symbols: []                     # 空数组表示所有品种
    
  # 交易时间限制
  trading_hours:
    enabled: false                  # 不限制交易时间
    start_time: "00:00"
    end_time: "24:00"
    timezone: "UTC"
    exclude_weekends: true
    
  # 交易模式
  trading_mode:
    manual_override: false          # 是否允许手动干预
    auto_trading: true              # 自动交易开关
    copy_signal_generation: true    # 是否生成跟单信号

# ============================================================================
# 风险管理配置
# ============================================================================
risk_management:
  # 基础风险限制
  limits:
    max_daily_loss: 5000.0
    max_positions: 15
    max_lot_size: 20.0
    max_daily_trades: 100
    max_drawdown: 0.20              # 20%
    
  # 止损止盈设置
  stop_loss:
    default_enabled: false
    default_pips: 50
    trailing_stop: false
    
  take_profit:
    default_enabled: false
    default_pips: 100
    
  # 紧急停止
  emergency_stop:
    enabled: true
    loss_threshold: 8000.0
    drawdown_threshold: 0.25        # 25%
    
  # 风险检查
  risk_checks:
    pre_trade: true
    real_time: true
    post_trade: true


# ============================================================================
# 通知配置
# ============================================================================
notifications:
  # Telegram通知
  telegram:
    enabled: true
    chat_id: "**********"
    
  # 通知级别
  levels:
    trade_opened: true
    trade_closed: true
    emergency_stop: true
    risk_alert: true
    system_error: true
    daily_summary: true

# ============================================================================
# 数据存储配置
# ============================================================================
data_storage:
  # 本地数据路径
  local_paths:
    data_directory: "./data/accounts/ACC001"
    logs_directory: "./logs/accounts/ACC001"
    backup_directory: "./backups/accounts/ACC001"
    
  # 数据保留
  retention:
    trade_history_days: 90
    log_files_days: 30
    backup_files_days: 7
    
  # 缓存配置
  cache:
    enabled: true
    ttl_seconds: 600               # 10分钟
    max_size_mb: 100

# ============================================================================
# 高级功能配置
# ============================================================================
advanced:
  # EA支持
  expert_advisors:
    enabled: false
    allowed_eas: []
    
  # 脚本支持
  scripts:
    enabled: false
    allowed_scripts: []
    
  # 自定义指标
  custom_indicators:
    enabled: false
    
# ============================================================================
# 部署配置
# ============================================================================
deployment:
  host_id: "uk-001"                    # 部署到的主机ID
  role: "primary"                      # 部署角色
  priority: 1                          # 部署优先级

# ============================================================================
# 环境特定配置
# ============================================================================
environment_overrides:
  development:
    trading:
      volume_limits:
        max_volume: 1.0             # 开发环境限制手数
    risk_management:
      limits:
        max_daily_loss: 100.0       # 开发环境限制亏损
        
  testing:
    trading:
      volume_limits:
        max_volume: 0.1             # 测试环境更小手数
    risk_management:
      limits:
        max_daily_loss: 50.0
        
  production:
    # 生产环境使用默认配置
    pass

# ============================================================================
# 元数据
# ============================================================================
metadata:
  created_at: "2024-01-01T00:00:00Z"
  updated_at: "2024-01-01T00:00:00Z"
  version: "2.0.0"
  config_schema_version: "1.0"
  tags: ["master", "high-frequency", "primary"]