# MT5 高频分布式交易复制系统

一步建议
连接真实MT5终端 - 集成MetaTrader 5 API
完善交易逻辑 - 实现具体的跟单交易算法
添加风险管理 - 实现止损、止盈和风险控制
部署到生产环境 - 使用Docker在多个服务器上部署
监控和告警 - 集成Prometheus和Grafana监控



<div align="center">

[![Python](https://img.shields.io/badge/Python-3.9%2B-blue.svg)](https://www.python.org/)
[![License](https://img.shields.io/badge/License-MIT-green.svg)](LICENSE)
[![Docker](https://img.shields.io/badge/Docker-Ready-blue.svg)](https://www.docker.com/)
[![Status](https://img.shields.io/badge/Status-Production%20Ready-brightgreen.svg)]()

[English](README_EN.md) | 简体中文

</div>

## 📋 目录

- [简介](#-简介)
- [核心特性](##-核心特性)
- [系统架构](##-系统架构)
- [技术栈](#-技术栈)
- [快速开始](#-快速开始)
- [部署指南](#-部署指南)
- [性能指标](#-性能指标)
- [配置说明](#-配置说明)
- [API文档](#-api文档)
- [监控和维护](#-监控和维护)
- [故障排除](#-故障排除)
- [贡献指南](#-贡献指南)
- [许可证](#-许可证)

## 🌟 简介

MT5高频分布式交易复制系统是一个企业级的MetaTrader 5交易信号复制解决方案，专为高频交易场景设计。系统采用现代微服务架构，支持微秒级信号传输，实现了真正的分布式部署和智能负载均衡。






## 启动流程设计图

```mermaid
graph TB
    subgraph "RPC控制层"
        RPC_Client[RPC Client]
        RPC_Service[MT5 RPC Service]
    end
    
    subgraph "数据流层"
        PubSub[NATS Pub/Sub<br/>快速分发]
        JetStream[JetStream<br/>可靠传输]
    end
    
    subgraph "状态管理层"
        Redis_Cache[Redis缓存<br/>账户状态]
        Redis_Config[Redis配置<br/>跟单关系]
        Redis_Stats[Redis统计<br/>性能数据]
    end
    
    subgraph "业务进程"
        Monitor[监控器]
        Executor[执行器]
        MT5[MT5终端]
    end
    
    %% RPC控制流
    Monitor -->|get_positions| RPC_Client
    RPC_Client -->|NATS RPC| RPC_Service
    RPC_Service -->|直接调用| MT5
    
    %% 数据流
    Monitor -->|交易信号| PubSub
    PubSub -->|快速广播| Executor
    Monitor -->|关键信号| JetStream
    JetStream -->|可靠传输| Executor
    
    %% 状态管理
    RPC_Service -->|更新缓存| Redis_Cache
    Monitor -->|查询配置| Redis_Config
    Executor -->|记录统计| Redis_Stats
    
    style RPC_Client fill:#e1f5fe
    style PubSub fill:#f3e5f5
    style JetStream fill:#fff3e0
    style Redis_Cache fill:#e8f5e8
```


```mermaid
graph TB
    subgraph "主协调器进程"
        Coordinator[MT5Coordinator]
        RequestHandler[MT5RequestHandler]
        ProcessManager[MT5ProcessManager]
        JetStream[JetStream Client]
    end
    
    subgraph "账户监控进程"
        Monitor1[Monitor ACC001]
        Monitor2[Monitor ACC002]
        ProxyClient1[JetStream Proxy]
        ProxyClient2[JetStream Proxy]
    end
    
    subgraph "账户执行进程"
        Executor1[Executor ACC001]
        Executor2[Executor ACC002]
    end
    
    subgraph "MT5终端进程"
        MT5_1[Terminal64.exe ACC001]
        MT5_2[Terminal64.exe ACC002]
    end
    
    subgraph "NATS消息基础设施"
        NATS[(NATS Server)]
        Stream[JetStream持久化]
    end
    
    %% RPC控制流 (Request/Response)
    Monitor1 -->|RPC: get_positions| RequestHandler
    Monitor2 -->|RPC: get_account_info| RequestHandler
    RequestHandler -->|路由到| ProcessManager
    ProcessManager -->|控制| MT5_1
    ProcessManager -->|控制| MT5_2
    
    %% 数据流 (Pub/Sub)
    Monitor1 -->|Pub: 交易信号| Stream
    Monitor2 -->|Pub: 持仓变化| Stream
    Stream -->|Sub: 跟单信号| Executor1
    Stream -->|Sub: 跟单信号| Executor2
    
    %% 基础设施连接
    RequestHandler --> NATS
    ProxyClient1 --> NATS
    ProxyClient2 --> NATS
    JetStream --> NATS
    
    style RequestHandler fill:#e1f5fe
    style Stream fill:#f3e5f5
    style NATS fill:#fff3e0
```


```mermaid
graph TD
    A[MT5Coordinator 系统协调器] --> B[MessageRoutingCoordinator 消息路由]
    A --> C[SignalRouter 信号路由]
    A --> D[MT5ProcessManager 进程管理]
    A --> E[MT5RequestHandler 请求处理]
    
    B --> F[Monitor进程 ACC001]
    B --> G[Monitor进程 ACC002]
    B --> H[Executor进程 ACC001]
    B --> I[Executor进程 ACC002]
    
    J[NATS JetStream] --> K[MT5.MONITOR.*]
    J --> L[MT5.EXECUTE.*]
    J --> M[MT5.REQUEST.*]
    J --> N[MT5.RESULT.*]
    
    F --> K
    G --> K
    H --> L
    I --> L
    E --> M
``` 





```mermaid
  sequenceDiagram
      participant Config as 配置加载器
      participant PM as 进程管理器
      participant Mon as 监控器
      participant Exec as 执行器
      participant NATS as 消息队列
      participant MT5 as MT5终端

      Config->>PM: 加载账户配置
      PM->>MT5: 启动独立进程连接
      MT5-->>PM: 连接确认
      PM->>Mon: 启动监控进程
      PM->>Exec: 启动执行进程
      Mon->>NATS: 订阅监控主题
      Exec->>NATS: 订阅执行主题
      Mon->>NATS: 发布持仓变化信号
      NATS->>Exec: 路由执行命令
      Exec->>MT5: 执行跟单交易
      MT5-->>Exec: 返回执行结果
      Exec->>NATS: 发布结果状态
```

## MT5 分布式进程隔离架构

```mermaid
  graph TD
      A[MT5Coordinator 系统总协调器] --> B[MessageRoutingCoordinator 消息路由协调器]
      A --> C[SignalRouter 信号路由器]
      A --> D[MT5ProcessManager 进程管理器]

      B --> E[Monitor 监控器]
      B --> F[Executor 执行器]

      C --> G[NATS JetStream]

      E --> H[MT5.MONITOR.* 主题]
      F --> I[MT5.EXECUTE.* 主题]
      C --> J[MT5.TRADES.* 主题]
      C --> K[MT5.COPY.* 主题]

      H --> B
      B --> I
      J --> B
      B --> K
```


```mermaid
graph TB
    subgraph "第4层：系统协调层"
        DMC[DistributedMT5Coordinator<br/>分布式系统总指挥]
    end
    
    subgraph "第3层：业务进程层"
        MAP1[MT5AccountProcess<br/>ACC001 - 主账户进程]
        MAP2[MT5AccountProcess<br/>ACC002 - 从账户进程]
        MAP3[MT5AccountProcess<br/>ACC003 - 从账户进程]
    end
    
    subgraph "第2层：进程管理层"
        MPM[MT5ProcessManager<br/>终端进程管理器]
        
        subgraph "独立终端进程"
            T1[Terminal64.exe<br/>ACC001专用]
            T2[Terminal64.exe<br/>ACC002专用]
            T3[Terminal64.exe<br/>ACC003专用]
        end
    end
    
    subgraph "第1层：API封装层"
        MC1[MT5Client<br/>ACC001 API封装]
        MC2[MT5Client<br/>ACC002 API封装]
        MC3[MT5Client<br/>ACC003 API封装]
    end
    
    subgraph "分布式通信"
        NATS[NATS JetStream<br/>消息总线]
        REDIS[Redis<br/>状态存储]
    end
    
    DMC --> MAP1
    DMC --> MAP2
    DMC --> MAP3
    
    MAP1 --> MPM
    MAP2 --> MPM
    MAP3 --> MPM
    
    MPM --> T1
    MPM --> T2
    MPM --> T3
    
    MAP1 --> MC1
    MAP2 --> MC2
    MAP3 --> MC3
    
    MC1 -.-> T1
    MC2 -.-> T2
    MC3 -.-> T3
    
    DMC --> NATS
    MAP1 --> NATS
    MAP2 --> NATS
    MAP3 --> NATS
    
    DMC --> REDIS
    
    style DMC fill:#e1f5fe
    style MPM fill:#f3e5f5
    style T1 fill:#e8f5e8
    style T2 fill:#e8f5e8
    style T3 fill:#e8f5e8
```

## 🏛️ 系统架构

```mermaid
%%{init: {'theme': 'base', 'themeVariables': { 'primaryColor': '#F5F5F5', 'primaryTextColor': '#333', 'lineColor': '#4A90E2', 'nodeBorder': '#4A90E2', 'mainBkg': '#FFFFFF', 'clusterBkg': '#F0F4F8'}}}%%
graph TD
    %% Styling
    classDef entryPoint fill:#D1E8FF,stroke:#4A90E2,stroke-width:2px,color:#333;
    classDef coreInfra fill:#E1F5FE,stroke:#0288D1,stroke-width:1px,color:#333;
    classDef controlPlane fill:#FFF9C4,stroke:#FBC02D,stroke-width:1px,color:#333;
    classDef dataPlane fill:#D1F2EB,stroke:#00796B,stroke-width:1px,color:#333;
    classDef models fill:#FCE4EC,stroke:#D81B60,stroke-width:1px,color:#333;

    %% ------------------- Subgraphs Definition -------------------
    subgraph "启动与配置 (Startup & Config)"
        direction LR
        Start["scripts/start_distributed_system.py<br><b>(系统总入口)</b>"]:::entryPoint
        Config["utils/config_manager.py<br><b>(配置管理器)</b>"]:::coreInfra
        YAML["config/*.yaml<br><b>(YAML配置文件)</b>"]
    end

    subgraph "核心基础设施 (Core Infrastructure)"
        direction LR
        NATS["messaging/jetstream_client.py<br><b>(NATS通信核心)</b>"]:::coreInfra
        Logger["utils/logger.py<br><b>(日志工具)</b>"]:::coreInfra
        Metrics["utils/metrics.py<br><b>(性能指标)</b>"]:::coreInfra
        Models["models/trade_signal.py<br><b>(数据模型)</b>"]:::models
    end

    subgraph "控制平面 (Control Plane) - 系统协调与管理"
        direction TB
        Coordinator["services/coordinator.py<br><b>(系统协调器)</b>"]:::controlPlane
        Registry["distributed/account_registry.py<br><b>(账户注册与发现)</b>"]:::controlPlane
        Failover["distributed/failover_manager.py<br><b>(故障转移管理器)</b>"]:::controlPlane
        Router["distributed/message_router.py<br><b>(增强消息路由器)</b>"]:::controlPlane
    end

    subgraph "数据平面 (Data Plane) - 核心交易流"
        direction TB
        MasterMonitor["monitors/master_monitor.py<br><b>(优化的主账户监控器)</b>"]:::dataPlane
        SlaveExecutor["executors/slave_executor.py<br><b>(优化的从账户执行器)</b>"]:::dataPlane
    end


    %% ------------------- Connections -------------------

    %% 1. Startup and Config Flow
    YAML --> Config
    Start --> Config
    Start --> NATS
    Start --> Coordinator
    Start --> MasterMonitor
    Start --> SlaveExecutor

    %% 2. Core Infrastructure Dependencies (Implied for most, showing key ones)
    Config --> Logger
    NATS --> Logger
    Coordinator --> NATS
    Registry --> NATS
    Failover --> NATS
    Router --> NATS
    MasterMonitor --> NATS
    SlaveExecutor --> NATS

    %% 3. Control Plane Inter-dependencies
    Coordinator --> Registry
    Coordinator --> Failover
    Failover --> Registry
    Router --> Registry
    Router --> Config

    %% 4. Data Plane Dependencies
    MasterMonitor --> Router
    MasterMonitor --> Models
    MasterMonitor --> Metrics
    MasterMonitor --> Logger
    MasterMonitor --> Config

    SlaveExecutor --> Router
    SlaveExecutor --> Models
    SlaveExecutor --> Metrics
    SlaveExecutor --> Logger
    SlaveExecutor --> Config

    %% 5. Control Plane and Data Plane Interaction
    Registry --> MasterMonitor
    Registry --> SlaveExecutor
    Failover --> MasterMonitor
    Failover --> SlaveExecutor
```

## 🛠️ 技术栈

<div align="center">

| 类别 | 技术 | 说明 |
|------|------|------|
| **核心语言** | ![Python](https://img.shields.io/badge/Python-3776AB?style=for-the-badge&logo=python&logoColor=white) | Python 3.9+ 异步编程 |
| **交易平台** | ![MT5](https://img.shields.io/badge/MetaTrader5-F7931A?style=for-the-badge&logo=metatrader5&logoColor=white) | 官方Python API |
| **消息队列** | ![NATS](https://img.shields.io/badge/NATS-199BFC?style=for-the-badge&logo=nats&logoColor=white) | 高性能消息传递 |
| **缓存** | ![Redis](https://img.shields.io/badge/Redis-DC382D?style=for-the-badge&logo=redis&logoColor=white) | 内存数据存储 |
| **API框架** | ![FastAPI](https://img.shields.io/badge/FastAPI-009688?style=for-the-badge&logo=fastapi&logoColor=white) | 现代Web API |
| **监控** | ![Prometheus](https://img.shields.io/badge/Prometheus-E6522C?style=for-the-badge&logo=prometheus&logoColor=white) | 指标收集 |
| **可视化** | ![Grafana](https://img.shields.io/badge/Grafana-F46800?style=for-the-badge&logo=grafana&logoColor=white) | 数据可视化 |
| **容器化** | ![Docker](https://img.shields.io/badge/Docker-2496ED?style=for-the-badge&logo=docker&logoColor=white) | 容器部署 |
| **编排** | ![Kubernetes](https://img.shields.io/badge/Kubernetes-326CE5?style=for-the-badge&logo=kubernetes&logoColor=white) | 容器编排 |

</div>

## 🚀 快速开始

### 前置要求

- Python 3.9 或更高版本
- MetaTrader 5 终端
- Docker 和 Docker Compose (可选)
- 至少 4GB RAM 和 2 CPU 核心

### ⚡ 一键部署

**Linux/Mac:**
```bash
git clone https://github.com/your-org/mt5-python.git
cd mt5-python
./run.sh
```

**Windows:**
```cmd
git clone https://github.com/your-org/mt5-python.git
cd mt5-python
run.bat
```

### 📝 首次配置

1. **编辑配置文件** - 填入您的MT5账户信息
   ```bash
   nano config/config.yaml  # Linux/Mac
   notepad config\config.yaml  # Windows
   ```

2. **设置环境变量** (可选)
   ```bash
   nano .env  # Linux/Mac
   notepad .env  # Windows
   ```

### 🎯 服务访问

部署完成后，您可以访问：
- **API文档**: http://localhost:8000/docs
- **健康检查**: http://localhost:8000/health
- **监控面板**: http://localhost:3000
- **性能指标**: http://localhost:8000/metrics

### 🔧 管理命令

```bash
# 启动服务
./run.sh start        # Linux/Mac
run.bat start         # Windows

# 停止服务
./run.sh stop         # Linux/Mac
run.bat stop          # Windows

# 重启服务
./run.sh restart      # Linux/Mac
run.bat restart       # Windows

# 查看状态
./run.sh status       # Linux/Mac
run.bat status        # Windows

# 查看日志
./run.sh logs         # Linux/Mac
run.bat logs          # Windows
```

## 📦 高级部署

### 🧪 开发环境

```bash
# 使用开发配置启动
./run.sh start  # 自动检测环境
```

### 🏭 生产环境

#### Docker Swarm 部署

```bash
# 初始化Swarm集群
docker swarm init

# 部署服务栈
docker stack deploy -c docker-compose.prod.yml mt5-system
```

#### Kubernetes 部署

```bash
# 创建命名空间
kubectl create namespace mt5-system

# 应用配置
kubectl apply -f k8s/

# 检查部署状态
kubectl get pods -n mt5-system
```

### 🔧 详细配置

完整的部署配置和架构说明，请参考：
- [部署指南](docs/DEPLOYMENT_GUIDE.md) - 详细的部署步骤和配置
- [技术栈文档](docs/TECHNOLOGY_STACK.md) - 技术架构和组件说明

## 📊 性能指标

### 基准测试结果

| 指标 | 数值 | 测试条件 |
|------|------|----------|
| **信号延迟** | < 1ms (P99) | 局域网环境 |
| **吞吐量** | 10,000+ 信号/秒 | 8核16GB服务器 |
| **CPU使用率** | < 40% | 正常负载 |
| **内存占用** | < 1GB | 100个活跃连接 |
| **启动时间** | < 5秒 | 冷启动 |
| **恢复时间** | < 2秒 | 故障恢复 |

### 压力测试结果

```bash
# 运行压力测试
python tests/performance/stress_test.py

# 结果示例：
并发连接数: 1000
总请求数: 1,000,000
成功率: 99.99%
平均延迟: 0.8ms
P95延迟: 1.2ms
P99延迟: 2.5ms
最大延迟: 15ms
```

## ⚙️ 配置说明

### 核心配置文件结构

```yaml
# config/config.yaml
system:
  name: "MT5 Production System"
  version: "2.0.0"
  environment: "production"
  
# 分布式配置
distributed:
  enabled: true
  central_server: "10.0.0.100:8000"
  discovery_interval: 30.0
  
# 账户配置
accounts:
  masters:
    - id: "master_001"
      login: 1234567
      password: "${MT5_MASTER_PASSWORD}"  # 使用环境变量
      server: "ICMarkets-Live"
      
# 性能优化
monitoring:
  base_polling_interval: 0.001  # 1ms
  performance_mode: "aggressive"
  
# 风险管理
risk_management:
  max_positions: 20
  max_daily_loss: 10000
  max_drawdown: 0.15
```

### 环境变量

```bash
# .env 文件
MT5_MASTER_PASSWORD=your_secure_password
NATS_URL=nats://localhost:4222
REDIS_URL=redis://localhost:6379
API_SECRET_KEY=your_api_secret
GRAFANA_ADMIN_PASSWORD=admin_password
```

## 📚 API文档

### REST API 端点

| 方法 | 端点 | 描述 |
|------|------|------|
| GET | `/health` | 健康检查 |
| GET | `/metrics` | Prometheus指标 |
| GET | `/api/accounts` | 获取账户列表 |
| POST | `/api/accounts/{id}/role` | 更改账户角色 |
| GET | `/api/positions` | 获取当前持仓 |
| GET | `/api/performance` | 获取性能统计 |
| POST | `/api/execute/signal` | 手动执行信号 |
| GET | `/api/logs` | 获取系统日志 |

### WebSocket 端点

```javascript
// 实时信号订阅
ws://localhost:8000/ws/signals

// 实时性能指标
ws://localhost:8000/ws/metrics
```

完整的API文档请访问: http://localhost:8000/docs

## 📊 监控和维护

### Grafana 仪表板

系统提供了多个预配置的Grafana仪表板：

1. **系统概览** - 整体健康状态和关键指标
2. **交易性能** - 延迟分析和信号统计
3. **资源使用** - CPU、内存、网络监控
4. **错误分析** - 错误率和异常检测
5. **业务指标** - 盈亏统计和风险指标

### 日志管理

```bash
# 查看实时日志
docker-compose logs -f

# 查看特定服务日志
docker-compose logs -f master-monitor

# 日志轮转配置
logrotate -f /etc/logrotate.d/mt5-system
```

### 备份策略

```bash
# 自动备份脚本
./scripts/backup.sh

# 恢复备份
./scripts/restore.sh backup-2024-01-01.tar.gz
```

## 🔧 故障排除

### 常见问题解决

#### 1. MT5连接失败

```bash
# 检查MT5服务状态
python scripts/check_mt5_connection.py

# 常见原因：
- MT5终端未运行
- 防火墙阻止连接
- 账户凭据错误
- 服务器地址变更
```

#### 2. 高延迟问题

```bash
# 运行延迟诊断
python scripts/diagnose_latency.py

# 优化建议：
- 减少轮询间隔
- 启用批处理模式
- 检查网络质量
- 升级硬件资源
```

#### 3. 内存泄漏

```bash
# 内存分析
python scripts/memory_profiler.py

# 解决方案：
- 启用内存限制
- 定期重启服务
- 检查代码泄漏
```

### 紧急恢复流程

```bash
# 1. 停止所有服务
docker-compose down

# 2. 清理损坏数据
./scripts/cleanup.sh

# 3. 从备份恢复
./scripts/restore.sh latest

# 4. 重启服务
docker-compose up -d

# 5. 验证服务状态
python scripts/health_check.py --comprehensive
```

## 🤝 贡献指南

我们欢迎社区贡献！请查看 [CONTRIBUTING.md](CONTRIBUTING.md) 了解详情。

### 开发流程

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 创建 Pull Request



---

<div align="center">
  <p> Fighting for Warwick Team!</p>
  <p>© 2025 Warwick University. All rights reserved.</p>
</div>