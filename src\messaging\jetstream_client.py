"""
NATS JetStream客户端封装
提供持久化消息传递功能
"""
import asyncio
import json
import time
from typing import Callable, Optional, Dict, Any, List
from dataclasses import dataclass
import nats
from nats.aio.client import Client as NATS
from nats.js.api import StreamConfig, ConsumerConfig, DeliverPolicy, AckPolicy
from nats.js.errors import BadRequestError, NotFoundError

from .message_types import MessageEnvelope
from .message_codec import MessageCodec
from ..utils.logger import get_logger


logger = get_logger(__name__)

# 安全的metrics包装器
class SafeMetrics:
    def __init__(self):
        self._metrics = None
        self._initialized = False

    def _ensure_initialized(self):
        if not self._initialized:
            # 暂时完全禁用metrics以避免阻塞
            logger.debug("Metrics已禁用")
            self._metrics = None
            self._initialized = True

    def increment(self, name: str, value: int = 1, labels: dict = None):
        try:
            self._ensure_initialized()
            if self._metrics:
                self._metrics.increment(name, value, labels)
        except Exception as e:
            logger.debug(f"Metrics increment失败: {e}")

    def set_gauge(self, name: str, value: float, labels: dict = None):
        try:
            self._ensure_initialized()
            if self._metrics:
                self._metrics.set_gauge(name, value, labels)
        except Exception as e:
            logger.debug(f"Metrics gauge失败: {e}")

    def observe(self, name: str, value: float, labels: dict = None):
        try:
            self._ensure_initialized()
            if self._metrics:
                self._metrics.observe(name, value, labels)
        except Exception as e:
            logger.debug(f"Metrics observe失败: {e}")

metrics = SafeMetrics()


@dataclass
class JetStreamConfig:
    """JetStream配置"""
    servers: List[str]
    name: str = "mt5-jetstream"
    user: Optional[str] = None
    password: Optional[str] = None
    token: Optional[str] = None
    max_reconnect_attempts: int = 10
    reconnect_time_wait: float = 2.0
    connect_timeout: float = 10.0
    # JetStream特有配置
    stream_name: str = "MT5_SIGNALS"  # 默认使用新的信号流
    subjects: List[str] = None
    max_age: int = 24 * 60 * 60  # 24小时
    max_msgs: int = 1000000  # 最大消息数
    max_bytes: int = 1024 * 1024 * 1024  # 1GB
    replicas: int = 1  # 副本数量

    def __post_init__(self):
        if self.subjects is None:
            # 新的分层流主题配置 - 对应4层流架构
            self.subjects = [
                "MT5.SIGNALS.*",     # 交易信号流 - 支持优先级分层
                "MT5.RPC.*",         # RPC通信流
                # 使用MT5.CONTROL.*替代MT5.MONITOR.*
                "MT5.CONTROL.*",     # 系统控制流
                "MT5.HEARTBEAT.*",   # 心跳信息
                # 新架构使用4层流，不再使用这些旧主题
                # MT5.SIGNALS.* - 信号流
                # MT5.RPC.* - RPC通信流 
                # MT5.CONTROL.* - 控制流
                # MT5.LOCAL.* - 本地流
            ]


class JetStreamClient:
    """JetStream客户端封装 - 增强分层流架构"""

    def __init__(self, config_or_servers):
        # 兼容多种初始化方式
        if isinstance(config_or_servers, list):
            # 传递服务器列表
            self.config = JetStreamConfig(servers=config_or_servers)
        elif isinstance(config_or_servers, JetStreamConfig):
            # 传递配置对象
            self.config = config_or_servers
        elif isinstance(config_or_servers, dict):
            # 传递字典配置（新增支持）
            servers = config_or_servers.get('servers', [])
            # 如果 servers 不是列表，尝试从其他配置中构建
            if not isinstance(servers, list):
                servers = ["nats://localhost:4222"]  # 默认服务器
            
            # 处理subjects配置
            subjects_config = config_or_servers.get('jetstream', {}).get('subjects', {})
            subjects = []
            if isinstance(subjects_config, dict):
                # 从字典中提取subjects值
                subjects = list(subjects_config.values())
            elif isinstance(subjects_config, list):
                subjects = subjects_config
            else:
                # 使用默认subjects
                subjects = [
                    "MT5.SIGNALS.*", "MT5.RPC.*", "MT5.CONTROL.*"
                    # 使用新的4层分层流架构主题
                ]
            
            self.config = JetStreamConfig(
                servers=servers,
                name=config_or_servers.get('name', 'mt5-jetstream'),
                user=config_or_servers.get('user'),
                password=config_or_servers.get('password'),
                token=config_or_servers.get('token'),
                max_reconnect_attempts=config_or_servers.get('connection', {}).get('max_reconnect', 10),
                reconnect_time_wait=config_or_servers.get('connection', {}).get('reconnect_wait', 2.0),
                stream_name=config_or_servers.get('jetstream', {}).get('stream_name', 'MT5_SIGNALS'),
                subjects=subjects,
                connect_timeout=config_or_servers.get('connection', {}).get('timeout', 10.0),
                max_age=config_or_servers.get('jetstream', {}).get('max_age', 86400),
                max_msgs=config_or_servers.get('jetstream', {}).get('max_msgs', 10000000),
                max_bytes=config_or_servers.get('jetstream', {}).get('max_bytes', 10737418240),
                replicas=config_or_servers.get('jetstream', {}).get('replicas', 1)
            )
        else:
            raise ValueError("config_or_servers must be a list of servers, dict config, or JetStreamConfig object")

        self.nc: Optional[NATS] = None
        self.js = None
        self._connection_status = "disconnected"
        self._last_error = None
        self._consumers: Dict[str, Any] = {}
        self._reconnect_count = 0
        
        # 分层流架构状态
        self._layered_streams: Dict[str, bool] = {}  # 记录已创建的流
        self._priority_consumers: Dict[str, Any] = {}  # 优先级消费者
        self.host_id: Optional[str] = None  # 主机ID，用于本地流
        
    async def connect(self) -> bool:
        """连接到NATS服务器并初始化JetStream - 改进版"""
        # 如果已经连接，检查连接状态
        if self.nc and self.nc.is_connected:
            try:
                # 测试连接是否真的可用
                await self.nc.flush(timeout=1.0)
                return True
            except:
                # 连接不可用，清理并重连
                logger.warning("检测到连接异常，清理并重连")
                await self._cleanup_connection()

        try:
            # 确保完全清理旧连接
            if self.nc:
                await self._cleanup_connection()

            self.nc = NATS()

            await self.nc.connect(
                servers=self.config.servers,
                name=self.config.name,
                user=self.config.user,
                password=self.config.password,
                token=self.config.token,
                max_reconnect_attempts=self.config.max_reconnect_attempts,
                reconnect_time_wait=self.config.reconnect_time_wait,
                connect_timeout=self.config.connect_timeout,
                # 改进连接稳定性参数
                ping_interval=60,  # 减少ping频率
                max_outstanding_pings=3,
                flusher_queue_size=1024,
                pending_size=2 * 1024 * 1024,
                drain_timeout=30,
                # 重新启用回调函数以便更好地处理连接状态
                error_cb=self._error_cb,
                disconnected_cb=self._disconnected_cb,
                reconnected_cb=self._reconnected_cb,
                closed_cb=self._closed_cb
            )
            
            # 获取JetStream上下文
            self.js = self.nc.jetstream()
            
            # 创建或更新Stream
            await self._ensure_stream()
            
            self._connection_status = "connected"
            logger.info(f"JetStream连接成功: {self.config.servers}")

            # 使用安全的metrics调用
            metrics.increment("jetstream_connections_total")
            metrics.set_gauge("jetstream_connection_status", 1)

            return True
            
        except Exception as e:
            self._last_error = str(e)
            logger.error(f"JetStream连接失败: {e}")
            metrics.increment("jetstream_connection_errors_total")
            metrics.set_gauge("jetstream_connection_status", 0)
            return False
    
    async def _ensure_stream(self):
        """确保Stream存在 - 优化版本，处理主题重叠问题"""
        try:
            # 尝试获取Stream信息
            stream_info = await self.js.stream_info(self.config.stream_name)
            logger.info(f"Stream已存在: {self.config.stream_name}, 消息数: {stream_info.state.messages}")
            
            # 检查subjects是否匹配
            existing_subjects = set(stream_info.config.subjects)
            required_subjects = set(self.config.subjects) if self.config.subjects else set()

            # 如果required_subjects为空，说明客户端没有特定的subjects要求
            # 在这种情况下，使用现有的Stream而不是删除它
            if required_subjects and existing_subjects != required_subjects:
                logger.warning(f"Stream subjects不匹配，删除并重新创建")
                logger.info(f"现有subjects: {existing_subjects}")
                logger.info(f"需要subjects: {required_subjects}")

                # 删除现有Stream
                await self.js.delete_stream(self.config.stream_name)
                logger.info(f"已删除现有Stream: {self.config.stream_name}")

                # 重新创建Stream
                await self._create_stream()
            elif not required_subjects:
                logger.info(f"客户端没有特定subjects要求，使用现有Stream: {self.config.stream_name}")
                logger.info(f"现有subjects: {existing_subjects}")
            else:
                logger.info(f"Stream subjects匹配，继续使用现有Stream: {self.config.stream_name}")

        except NotFoundError:
            # Stream不存在，创建新的
            logger.info(f"创建新Stream: {self.config.stream_name}")
            await self._create_stream()

        except Exception as e:
            logger.error(f"Stream检查失败: {e}")
            # 检查是否是主题重叠问题
            if "subjects overlap" in str(e).lower():
                logger.warning("检测到主题重叠，尝试查找并使用现有流...")
                existing_stream = await self._find_existing_stream_with_subjects()
                if existing_stream:
                    logger.info(f"使用现有流: {existing_stream}")
                    # 更新配置以使用现有流
                    self.config.stream_name = existing_stream
                else:
                    # 尝试删除冲突的流并重新创建
                    await self._resolve_subject_overlap()
            else:
                # 其他错误，尝试重新创建Stream
                try:
                    logger.info("尝试重新创建Stream...")
                    await self._create_stream()
                except Exception as create_error:
                    logger.error(f"重新创建Stream失败: {create_error}")
                    raise

    async def _create_stream(self):
        """创建Stream"""
        try:
            stream_config = StreamConfig(
                name=self.config.stream_name,
                subjects=self.config.subjects,
                max_age=self.config.max_age,
                max_msgs=self.config.max_msgs,
                max_bytes=self.config.max_bytes,
                num_replicas=self.config.replicas,
                storage="file",  # 文件存储
                retention="limits"  # 明确指定保留策略
            )

            await self.js.add_stream(stream_config)
            logger.info(f"Stream创建成功: {self.config.stream_name}")

        except BadRequestError as e:
            if "already exists" in str(e).lower():
                logger.info(f"Stream已存在: {self.config.stream_name}")
            elif "subjects overlap" in str(e).lower():
                logger.warning(f"主题重叠错误: {e}")
                # 处理主题重叠
                await self._resolve_subject_overlap()
            else:
                logger.error(f"Stream创建失败: {e}")
                raise
        except Exception as e:
            logger.error(f"Stream创建异常: {e}")
            raise

    async def _find_existing_stream_with_subjects(self) -> str:
        """查找使用相同主题的现有流"""
        try:
            # 获取所有流
            streams = await self.js.streams_info()

            for stream in streams:
                # 检查是否有重叠的主题
                for our_subject in self.config.subjects:
                    for existing_subject in stream.config.subjects:
                        if self._subjects_overlap(our_subject, existing_subject):
                            logger.info(f"找到重叠流: {stream.config.name}, 主题: {existing_subject}")
                            return stream.config.name

            return None

        except Exception as e:
            logger.error(f"查找现有流失败: {e}")
            return None

    def _subjects_overlap(self, subject1: str, subject2: str) -> bool:
        """检查两个主题是否重叠"""
        # 简单的重叠检查
        if subject1 == subject2:
            return True

        # 检查通配符重叠
        if subject1.endswith('*') and subject2.startswith(subject1[:-1]):
            return True
        if subject2.endswith('*') and subject1.startswith(subject2[:-1]):
            return True

        return False

    async def _resolve_subject_overlap(self):
        """解决主题重叠问题"""
        try:
            logger.info("尝试解决主题重叠问题...")

            # 获取所有流
            streams = await self.js.streams_info()

            # 找到冲突的流
            conflicting_streams = []
            for stream in streams:
                for our_subject in self.config.subjects:
                    for existing_subject in stream.config.subjects:
                        if self._subjects_overlap(our_subject, existing_subject):
                            conflicting_streams.append(stream.config.name)
                            break

            if conflicting_streams:
                logger.warning(f"发现冲突流: {conflicting_streams}")

                # 尝试使用第一个冲突流
                if conflicting_streams:
                    self.config.stream_name = conflicting_streams[0]
                    logger.info(f"使用现有冲突流: {self.config.stream_name}")
                    return

            # 如果没有找到合适的流，生成唯一名称
            import time
            unique_name = f"{self.config.stream_name}_{int(time.time())}"
            self.config.stream_name = unique_name
            logger.info(f"使用唯一流名称: {unique_name}")
            await self._create_stream()

        except Exception as e:
            logger.error(f"解决主题重叠失败: {e}")
            raise

    # ==================== 分层流架构方法 ====================
    
    async def create_layered_streams(self, host_id: str) -> bool:
        """创建分层流架构 - 根据优化方案实现4层流"""
        self.host_id = host_id
        
        if not self.js:
            logger.error("JetStream未连接，无法创建分层流")
            return False
        
        try:
            logger.info(f"开始创建分层流架构，主机ID: {host_id}")
            
            # 1. 创建本地高速通道流
            await self._create_local_stream(host_id)
            
            # 2. 创建跨主机交易信号流
            await self._create_signals_stream()
            
            # 3. 创建跨主机RPC通信流
            await self._create_rpc_stream()
            
            # 4. 创建系统控制和监控流
            await self._create_control_stream()
            
            logger.info("✅ 分层流架构创建完成")
            return True
            
        except Exception as e:
            logger.error(f"创建分层流架构失败: {e}")
            return False
    
    async def _create_local_stream(self, host_id: str):
        """创建本地高速通道流 - 内存存储，极致性能"""
        stream_name = f"MT5_LOCAL_{host_id}"
        
        try:
            stream_config = StreamConfig(
                name=stream_name,
                subjects=[
                    f"MT5.LOCAL.{host_id}.PRIORITY.*",
                    f"MT5.LOCAL.{host_id}.INTERNAL.*"
                ],
                max_age=300,  # 5分钟TTL
                max_msgs=100000,  # 本地消息量限制
                storage="memory",  # 内存存储，极致性能
                num_replicas=1,  # 本地单副本
                retention="workqueue"  # 工作队列模式，消费即删除
            )
            
            await self.js.add_stream(stream_config)
            self._layered_streams[stream_name] = True
            logger.info(f"✅ 本地高速流创建成功: {stream_name}")
            
        except BadRequestError as e:
            if "already exists" in str(e).lower():
                self._layered_streams[stream_name] = True
                logger.info(f"本地高速流已存在: {stream_name}")
            else:
                raise
    
    async def _create_signals_stream(self):
        """创建跨主机交易信号流 - 文件存储，高可用"""
        stream_name = "MT5_SIGNALS"
        
        try:
            stream_config = StreamConfig(
                name=stream_name,
                subjects=[
                    "MT5.SIGNALS.CRITICAL.*",  # 紧急信号
                    "MT5.SIGNALS.HIGH.*",      # 高优先级信号
                    "MT5.SIGNALS.NORMAL.*",    # 普通信号
                    "MT5.SIGNALS.LOW.*"        # 低优先级信号
                ],
                max_age=3600,  # 1小时保留
                max_msgs=1000000,  # 100万条消息
                storage="file",  # 文件存储，可靠性
                num_replicas=3,  # 高可用三副本
                retention="limits"  # 持久化保留
            )
            
            await self.js.add_stream(stream_config)
            self._layered_streams[stream_name] = True
            logger.info(f"✅ 交易信号流创建成功: {stream_name}")
            
        except BadRequestError as e:
            if "already exists" in str(e).lower():
                self._layered_streams[stream_name] = True
                logger.info(f"交易信号流已存在: {stream_name}")
            else:
                raise
    
    async def _create_rpc_stream(self):
        """创建跨主机RPC通信流 - 双副本保证可靠性"""
        stream_name = "MT5_RPC"
        
        try:
            stream_config = StreamConfig(
                name=stream_name,
                subjects=[
                    "MT5.RPC.EXECUTE.*",  # RPC执行请求
                    "MT5.RPC.QUERY.*"     # RPC查询请求
                ],
                max_age=600,  # 10分钟TTL
                max_msgs=50000,  # RPC消息限制
                storage="file",  # 持久化存储
                num_replicas=2,  # 双副本保证可靠性
                retention="workqueue"  # 工作队列，消费即删除
            )
            
            await self.js.add_stream(stream_config)
            self._layered_streams[stream_name] = True
            logger.info(f"✅ RPC通信流创建成功: {stream_name}")
            
        except BadRequestError as e:
            if "already exists" in str(e).lower():
                self._layered_streams[stream_name] = True
                logger.info(f"RPC通信流已存在: {stream_name}")
            else:
                raise
    
    async def _create_control_stream(self):
        """创建系统控制和监控流 - 24小时保留"""
        stream_name = "MT5_CONTROL"
        
        try:
            stream_config = StreamConfig(
                name=stream_name,
                subjects=[
                    "MT5.CONTROL.*"     # 系统控制和监控数据
                ],
                max_age=86400,  # 24小时保留
                max_msgs=500000,  # 监控数据限制
                storage="file",  # 文件存储
                num_replicas=1,  # 单副本即可
                retention="limits"  # 保留限制
            )
            
            await self.js.add_stream(stream_config)
            self._layered_streams[stream_name] = True
            logger.info(f"✅ 控制监控流创建成功: {stream_name}")
            
        except BadRequestError as e:
            if "already exists" in str(e).lower():
                self._layered_streams[stream_name] = True
                logger.info(f"控制监控流已存在: {stream_name}")
            else:
                raise
    
    async def setup_priority_consumers(self, priority_queue=None) -> bool:
        """设置优先级消费者 - 集成priority_queue.py"""
        if not self.js:
            logger.error("JetStream未连接，无法设置优先级消费者")
            return False
        
        try:
            logger.info("开始设置优先级消费者...")
            
            # 导入优先级队列模块
            from .priority_queue import MessagePriority
            
            # 1. 关键信号消费者 - 最高优先级，广播模式
            await self._create_priority_consumer(
                "MT5.SIGNALS.CRITICAL.*",
                MessagePriority.CRITICAL,
                "critical_consumer",
                broadcast=True
            )
            
            # 2. 高优先级信号消费者 - 广播模式
            await self._create_priority_consumer(
                "MT5.SIGNALS.HIGH.*",
                MessagePriority.HIGH,
                "high_consumer",
                broadcast=True
            )
            
            # 3. 普通优先级信号消费者 - 广播模式
            await self._create_priority_consumer(
                "MT5.SIGNALS.NORMAL.*",
                MessagePriority.NORMAL,
                "normal_consumer",
                broadcast=True
            )
            
            # 4. RPC执行消费者 - 队列组负载均衡
            await self._create_priority_consumer(
                "MT5.RPC.EXECUTE.*",
                MessagePriority.HIGH,
                "rpc_execution_consumer",
                queue_group="rpc_execution_workers"
            )
            
            # 5. RPC查询消费者 - 队列组负载均衡
            await self._create_priority_consumer(
                "MT5.RPC.QUERY.*",
                MessagePriority.NORMAL,
                "rpc_query_consumer",
                queue_group="rpc_query_workers"
            )
            
            # 6. 本地高速消费者
            if self.host_id:
                await self._create_priority_consumer(
                    f"MT5.LOCAL.{self.host_id}.*",
                    MessagePriority.CRITICAL,
                    f"local_consumer_{self.host_id}",
                    queue_group=f"local_workers_{self.host_id}"
                )
            
            logger.info("✅ 优先级消费者设置完成")
            return True
            
        except Exception as e:
            logger.error(f"设置优先级消费者失败: {e}")
            return False
    
    async def _create_priority_consumer(self, subject_filter: str, priority, consumer_name: str,
                                       broadcast: bool = False, queue_group: str = None):
        """创建优先级消费者"""
        try:
            # 确定消费者名称
            full_consumer_name = f"{consumer_name}_{self.host_id}" if self.host_id else consumer_name
            
            # 创建消费者配置
            consumer_config = ConsumerConfig(
                name=full_consumer_name,
                durable_name=full_consumer_name,
                deliver_policy=DeliverPolicy.NEW,  # 新消息优先
                ack_policy=AckPolicy.EXPLICIT,
                filter_subject=subject_filter,
                max_deliver=1 if priority.value == 0 else 3,  # 关键信号不重试
                ack_wait=5 if priority.value == 0 else 30,    # 关键信号快速超时
                max_ack_pending=100
            )
            
            # 根据主题确定流名称
            stream_name = self._get_stream_name_for_subject(subject_filter)
            
            # 尝试创建消费者
            try:
                await self.js.add_consumer(stream_name, consumer_config)
                logger.info(f"创建消费者: {full_consumer_name} (流: {stream_name})")
            except BadRequestError as e:
                if "already exists" in str(e).lower():
                    logger.info(f"消费者已存在: {full_consumer_name}")
                else:
                    raise
            
            # 记录消费者
            self._priority_consumers[full_consumer_name] = {
                'subject': subject_filter,
                'priority': priority,
                'stream': stream_name,
                'queue_group': queue_group,
                'broadcast': broadcast
            }
            
        except Exception as e:
            logger.error(f"创建优先级消费者失败: {consumer_name} - {e}")
            raise
    
    def _get_stream_name_for_subject(self, subject: str) -> str:
        """根据主题确定对应的流名称"""
        if "MT5.SIGNALS." in subject:
            return "MT5_SIGNALS"
        elif "MT5.RPC." in subject:
            return "MT5_RPC"
        elif "MT5.LOCAL." in subject:
            return f"MT5_LOCAL_{self.host_id}"
        elif "MT5.CONTROL." in subject:
            return "MT5_CONTROL"
        else:
            # 默认使用原有的流
            return self.config.stream_name
    
    async def publish_with_priority(self, signal_data: dict, priority=None) -> bool:
        """根据优先级发布消息 - 集成现有优先级系统"""
        try:
            from .priority_queue import PriorityAnalyzer, MessagePriority
            
            # 如果没有指定优先级，自动分析
            if priority is None:
                priority = PriorityAnalyzer.get_command_priority(
                    method=signal_data.get('command_type', 'unknown'),
                    params=signal_data
                )
            
            # 根据优先级构造主题
            account_id = signal_data.get('account_id', 'unknown')
            
            # 判断消息类型和目标
            if signal_data.get('target_host_id') == self.host_id:
                # 本地消息 - 使用本地高速通道
                subject = f"MT5.LOCAL.{self.host_id}.PRIORITY.{priority.name}.{account_id}"
            else:
                # 跨主机消息 - 使用信号流
                subject = f"MT5.SIGNALS.{priority.name}.{account_id}"
            
            # 添加优先级元数据
            headers = {
                'priority': str(priority.value),
                'priority_name': priority.name,
                'timestamp': str(time.time()),
                'host_id': self.host_id or 'unknown'
            }
            
            # 发布消息
            success = await self.publish(subject, signal_data, headers)
            
            if success:
                logger.debug(f"优先级消息发布成功: {priority.name} -> {subject}")
            else:
                logger.warning(f"优先级消息发布失败: {priority.name} -> {subject}")
            
            return success
            
        except Exception as e:
            logger.error(f"优先级消息发布异常: {e}")
            return False
    
    def get_layered_streams_status(self) -> Dict[str, Any]:
        """获取分层流状态"""
        return {
            'host_id': self.host_id,
            'layered_streams': dict(self._layered_streams),
            'priority_consumers': {
                name: {
                    'subject': info['subject'],
                    'priority': info['priority'].name,
                    'stream': info['stream'],
                    'queue_group': info.get('queue_group'),
                    'broadcast': info.get('broadcast', False)
                }
                for name, info in self._priority_consumers.items()
            },
            'total_streams': len(self._layered_streams),
            'total_consumers': len(self._priority_consumers)
        }
    
    async def disconnect(self):
        """断开连接 - 改进版"""
        if self.nc:
            try:
                # 先停止所有订阅
                for consumer in list(self._consumers.values()):
                    try:
                        if hasattr(consumer, 'stop'):
                            await consumer.stop()
                    except:
                        pass

                # 清空消费者记录
                self._consumers.clear()

                # 如果连接还活着，优雅关闭
                if self.nc.is_connected:
                    try:
                        await asyncio.wait_for(self.nc.drain(), timeout=3.0)
                    except asyncio.TimeoutError:
                        logger.warning("连接排空超时，强制关闭")
                    except Exception as e:
                        logger.warning(f"连接排空失败: {e}")

                    await self.nc.close()

                self._connection_status = "disconnected"
                logger.info("JetStream连接已断开")
                metrics.set_gauge("jetstream_connection_status", 0)

            except Exception as e:
                logger.error(f"断开JetStream连接失败: {e}")
            finally:
                # 确保完全清理
                self.nc = None
                self.js = None
                self._consumers.clear()
                # 短暂等待确保资源释放
                await asyncio.sleep(0.05)

    async def _cleanup_connection(self):
        """清理连接资源"""
        try:
            if self.nc:
                # 停止所有消费者
                for consumer in list(self._consumers.values()):
                    try:
                        if hasattr(consumer, 'stop'):
                            await consumer.stop()
                    except:
                        pass

                # 如果连接还活着，尝试关闭
                if self.nc.is_connected:
                    try:
                        await asyncio.wait_for(self.nc.close(), timeout=2.0)
                    except:
                        pass

                self.nc = None

            self.js = None
            self._consumers.clear()
            self._connection_status = "disconnected"

        except Exception as e:
            logger.debug(f"清理连接资源时出错: {e}")

    async def publish(self, subject: str, data: Any, headers: Dict[str, str] = None,
                     max_retries: int = 3) -> bool:
        """发布持久化消息 - 增强版本，支持重试和错误恢复"""
        if not self.js:
            logger.error("JetStream未连接")
            return False

        for attempt in range(max_retries + 1):
            try:
                start_time = time.perf_counter()

                # Use unified encoder
                payload = MessageCodec.encode(data)

                # 发布消息到JetStream
                ack = await self.js.publish(subject, payload, headers=headers)

                # 记录指标
                publish_time = (time.perf_counter() - start_time) * 1000
                metrics.observe("jetstream_publish_duration_ms", publish_time)
                metrics.increment("jetstream_messages_published_total")

                if attempt > 0:
                    logger.info(f"JetStream消息发布成功 (重试 {attempt} 次): {subject}, seq={ack.seq}")
                else:
                    logger.debug(f"JetStream消息发布成功: {subject}, seq={ack.seq}")
                return True

            except Exception as e:
                error_msg = str(e)

                if "no response from stream" in error_msg:
                    logger.warning(f"流不存在，跳过消息发布: {subject} - {error_msg}")
                    return False

                if attempt < max_retries:
                    wait_time = (2 ** attempt) * 0.1  # 指数退避
                    logger.warning(f"JetStream消息发布失败，{wait_time:.1f}s后重试 ({attempt + 1}/{max_retries}): {subject} - {e}")
                    await asyncio.sleep(wait_time)

                    # 检查连接状态，如果断开则尝试重连
                    if not self.is_connected():
                        logger.info("检测到连接断开，尝试重连...")
                        if not await self.connect():
                            logger.error("重连失败，放弃发布")
                            break
                else:
                    logger.error(f"JetStream消息发布最终失败: {subject} - {e}")
                    metrics.increment("jetstream_publish_errors_total")
                    return False

        return False
    
    async def publish_message(self, message: MessageEnvelope) -> bool:
        """发布消息信封"""
        headers = message.headers.copy()
        headers.update({
            'message-id': message.id,
            'timestamp': str(message.timestamp)
        })
        
        if message.ttl:
            headers['ttl'] = str(message.ttl)
        
        return await self.publish(message.subject, message.payload, headers)

    async def add_stream(self, name=None, subjects=None, retention='limits',
                        max_age=3600, max_msgs=100000, **kwargs) -> bool:
        """添加流"""
        if not self.js:
            logger.error("JetStream未连接")
            return False

        try:
            from nats.js.api import StreamConfig

            # 如果传入的是字典，解析参数
            if isinstance(name, dict):
                config_dict = name
                name = config_dict.get('name')
                subjects = config_dict.get('subjects', [])
                retention = config_dict.get('retention', 'limits')
                max_age = config_dict.get('max_age', 3600)
                max_msgs = config_dict.get('max_msgs', 100000)

            # 创建流配置
            stream_config = StreamConfig(
                name=name,
                subjects=subjects or [],
                retention=retention,
                max_age=max_age,
                max_msgs=max_msgs
            )

            await self.js.add_stream(stream_config)
            logger.info(f"流创建成功: {name}")
            return True
        except Exception as e:
            if "already exists" not in str(e).lower():
                logger.error(f"流创建失败: {name} - {e}")
            return False

    async def subscribe(self, subject: str, callback: Callable, queue_group: str = None):
        """订阅消息（使用JetStream） - 返回订阅对象"""
        if not self.js:
            logger.error("JetStream未连接")
            return None

        try:
            # 创建消费者名称
            consumer_name = f"consumer_{subject.replace('.', '_').replace('*', 'wildcard')}"
            if queue_group:
                consumer_name = f"{queue_group}_{consumer_name}"

            # 检查是否已经存在订阅
            if consumer_name in self._consumers:
                existing_sub = self._consumers[consumer_name]
                try:
                    # 检查现有订阅是否仍然有效
                    if hasattr(existing_sub, 'is_valid') and existing_sub.is_valid:
                        logger.debug(f"复用现有订阅: {subject}")
                        return existing_sub
                except:
                    pass

                # 清理无效订阅
                try:
                    await existing_sub.unsubscribe()
                except:
                    pass
                del self._consumers[consumer_name]

            # 包装回调函数以处理JetStream消息格式和自动解码
            async def js_callback(msg):
                try:
                    # Auto decode message data
                    decoded_data = MessageCodec.decode(msg.data)
                    if decoded_data is not None:
                        # 创建包含解码数据的消息对象
                        class DecodedMessage:
                            def __init__(self, original_msg, data):
                                self.data = data
                                self.subject = original_msg.subject
                                self.reply = getattr(original_msg, 'reply', None)
                                self.headers = getattr(original_msg, 'headers', {})
                                self._original = original_msg

                            async def ack(self):
                                try:
                                    await self._original.ack()
                                except Exception as e:
                                    logger.warning(f"消息ACK失败: {e}")

                            async def nak(self):
                                try:
                                    await self._original.nak()
                                except Exception as e:
                                    logger.warning(f"消息NAK失败: {e}")

                        decoded_msg = DecodedMessage(msg, decoded_data)
                        await callback(decoded_msg)
                    else:
                        logger.error(f"消息解码失败，跳过处理: {subject}")
                        await msg.nak()
                        return

                    # 消息处理成功，不需要手动ACK（回调中已处理）

                except Exception as e:
                    logger.error(f"消息处理失败: {e}")
                    try:
                        # 检查消息是否已经被ACK
                        if not getattr(msg, '_ackd', False):
                            await msg.nak()
                    except Exception as nak_error:
                        logger.warning(f"NAK失败: {nak_error}")

            # 使用JetStream的push订阅并返回订阅对象
            try:
                subscription = await self.js.subscribe(
                    subject,
                    cb=js_callback,
                    durable=consumer_name,
                    queue=queue_group
                )

                # 存储订阅
                self._consumers[consumer_name] = subscription

                logger.info(f"订阅成功: {subject}")
                return subscription

            except Exception as e:
                error_msg = str(e).lower()
                if "already bound" in error_msg or "consumer is already bound" in error_msg:
                    # 消费者已绑定，尝试删除后重新创建
                    logger.warning(f"消费者已绑定，尝试重新创建: {consumer_name}")
                    try:
                        # 尝试删除现有消费者
                        stream_name = self.config.stream_name  # 使用配置中的流名称
                        await self.js.delete_consumer(stream_name, consumer_name)
                        await asyncio.sleep(0.1)  # 短暂等待

                        subscription = await self.js.subscribe(
                            subject,
                            cb=js_callback,
                            durable=consumer_name,
                            queue=queue_group
                        )

                        self._consumers[consumer_name] = subscription
                        logger.info(f"重新订阅成功: {subject}")
                        return subscription

                    except Exception as retry_e:
                        logger.error(f"重新订阅失败: {subject} - {retry_e}")
                        return None
                else:
                    raise e
        except Exception as e:
            logger.error(f"订阅失败: {subject} - {e}")
            return None


    async def subscribe_durable(self, subject: str, callback: Callable,
                              consumer_name: str, queue_group: str = None) -> bool:
        """订阅持久化消息"""
        if not self.js:
            logger.error("JetStream未连接")
            return False
        
        try:
            # 创建持久化消费者配置
            consumer_config = ConsumerConfig(
                name=consumer_name,
                durable_name=consumer_name,
                deliver_policy=DeliverPolicy.ALL,
                ack_policy=AckPolicy.EXPLICIT,
                filter_subject=subject,
                max_deliver=3,  # 最大重试次数
                ack_wait=30,    # 确认等待时间(秒)
                max_ack_pending=1000  # 最大未确认消息数
            )
            
            # 创建或获取消费者
            try:
                consumer = await self.js.consumer_info(self.config.stream_name, consumer_name)
                logger.info(f"使用现有消费者: {consumer_name}")
            except NotFoundError:
                consumer = await self.js.add_consumer(self.config.stream_name, consumer_config)
                logger.info(f"创建新消费者: {consumer_name}")
            
            # 包装回调函数
            async def message_handler(msg):
                start_time = time.perf_counter()
                try:
                    # Use unified decoder to parse message
                    data = MessageCodec.decode(msg.data)
                    if data is None:
                        logger.error(f"消息解码失败: {msg.subject}")
                        await msg.nak()
                        return

                    headers = msg.headers or {}
                    message = MessageEnvelope(
                        id=headers.get('message-id', ''),
                        subject=msg.subject,
                        payload=data,
                        headers=dict(headers),
                        timestamp=float(headers.get('timestamp', time.time()))
                    )

                    await callback(message)
                    
                    await msg.ack()

                    process_time = (time.perf_counter() - start_time) * 1000
                    metrics.observe("jetstream_message_process_duration_ms", process_time)
                    metrics.increment("jetstream_messages_processed_total")
                    
                except Exception as e:
                    logger.error(f"JetStream消息处理失败: {e}")
                    metrics.increment("jetstream_message_process_errors_total")
                    try:
                        await msg.nak()
                    except Exception as nak_error:
                        logger.warning(f"NAK失败: {nak_error}")
            
            # 创建订阅
            subscription = await self.js.subscribe(
                subject=subject,
                cb=message_handler,
                stream=self.config.stream_name,
                config=consumer_config,
                manual_ack=True
            )
            
            self._consumers[consumer_name] = subscription
            logger.info(f"JetStream订阅成功: {subject}, consumer={consumer_name}")
            metrics.increment("jetstream_subscriptions_total")
            
            return True
            
        except Exception as e:
            logger.error(f"JetStream订阅失败: {e}")
            metrics.increment("jetstream_subscription_errors_total")
            return False
    
    async def unsubscribe(self, consumer_name: str) -> bool:
        """取消订阅"""
        if consumer_name not in self._consumers:
            return True
        
        try:
            subscription = self._consumers[consumer_name]
            await subscription.unsubscribe()
            del self._consumers[consumer_name]
            
            logger.info(f"取消JetStream订阅成功: {consumer_name}")
            metrics.increment("jetstream_unsubscriptions_total")
            return True
            
        except Exception as e:
            logger.error(f"取消JetStream订阅失败: {e}")
            return False
    
    async def get_stream_info(self) -> Optional[Dict[str, Any]]:
        """获取Stream信息"""
        if not self.js:
            return None
        
        try:
            info = await self.js.stream_info(self.config.stream_name)
            return {
                'name': info.config.name,
                'subjects': info.config.subjects,
                'messages': info.state.messages,
                'bytes': info.state.bytes,
                'first_seq': info.state.first_seq,
                'last_seq': info.state.last_seq,
                'consumer_count': info.state.consumer_count
            }
        except Exception as e:
            logger.error(f"获取Stream信息失败: {e}")
            return None
    
    async def get_consumer_info(self, consumer_name: str) -> Optional[Dict[str, Any]]:
        """获取消费者信息"""
        if not self.js:
            return None
        
        try:
            info = await self.js.consumer_info(self.config.stream_name, consumer_name)
            return {
                'name': info.name,
                'stream_name': info.stream_name,
                'num_pending': info.num_pending,
                'num_redelivered': info.num_redelivered,
                'num_ack_pending': info.num_ack_pending,
                'delivered': info.delivered.stream_seq if info.delivered else 0,
                'ack_floor': info.ack_floor.stream_seq if info.ack_floor else 0
            }
        except Exception as e:
            logger.error(f"获取消费者信息失败: {e}")
            return None
    
    async def request(self, subject: str, data: Dict[str, Any], timeout: float = 10.0) -> Optional[Dict[str, Any]]:
        """
        发送请求并等待响应 - 使用JetStream的发布/订阅机制实现request/response模式
        
        Args:
            subject: 请求主题
            data: 请求数据
            timeout: 超时时间（秒）
            
        Returns:
            响应数据字典，如果超时或失败返回None
        """
        if not self.nc or not self.nc.is_connected:
            logger.error("JetStream未连接，无法发送请求")
            return None
        
        try:
            start_time = time.perf_counter()
            
            # Use underlying NATS client request method
            # JetStream can still use basic NATS request/response pattern
            payload = MessageCodec.encode(data)  # MessageCodec.encode returns bytes
            
            response = await self.nc.request(subject, payload, timeout=timeout)
            
            # 解析响应
            response_data =MessageCodec.decode(response.data.decode('utf-8'))
            
            # 记录指标
            request_time = (time.perf_counter() - start_time) * 1000
            metrics.observe("jetstream_request_duration_ms", request_time)
            metrics.increment("jetstream_requests_total")
            
            logger.debug(f"JetStream请求成功: {subject}")
            return response_data
            
        except asyncio.TimeoutError:
            logger.error(f"JetStream请求超时: {subject}")
            metrics.increment("jetstream_request_timeouts_total")
            return None
        except Exception as e:
            logger.error(f"JetStream请求失败: {e}")
            logger.error(f"请求详情: subject={subject}, timeout={timeout}")
            logger.error(f"连接状态: connected={self.nc.is_connected if self.nc else 'None'}")
            import traceback
            logger.error(f"异常堆栈: {traceback.format_exc()}")
            metrics.increment("jetstream_request_errors_total")
            return None
    
    async def _error_cb(self, e):
        """错误回调"""
        self._last_error = str(e)
        logger.error(f"JetStream错误: {e}")
        metrics.increment("jetstream_errors_total")

    async def _disconnected_cb(self):
        """断开连接回调"""
        self._connection_status = "disconnected"
        logger.warning("JetStream连接已断开")
        metrics.set_gauge("jetstream_connection_status", 0)

    async def _reconnected_cb(self):
        """重新连接回调"""
        self._connection_status = "connected"
        self._reconnect_count += 1
        logger.info("JetStream重新连接成功")
        metrics.increment("jetstream_reconnections_total")
        metrics.set_gauge("jetstream_connection_status", 1)

        # 重新连接后重新初始化Stream
        try:
            await self._ensure_stream()
        except Exception as e:
            logger.error(f"重新连接后Stream初始化失败: {e}")

    async def _closed_cb(self):
        """连接关闭回调"""
        self._connection_status = "closed"
        logger.info("JetStream连接已关闭")
        metrics.set_gauge("jetstream_connection_status", 0)
    
    def is_connected(self) -> bool:
        """检查连接状态"""
        return self.nc is not None and self.nc.is_connected
    
    def get_status(self) -> Dict[str, Any]:
        """获取客户端状态"""
        return {
            'connection_status': self._connection_status,
            'is_connected': self.is_connected(),
            'last_error': self._last_error,
            'reconnect_count': self._reconnect_count,
            'consumers': list(self._consumers.keys()),
            'servers': self.config.servers,
            'stream_name': self.config.stream_name
        }
    
    async def health_check(self) -> bool:
        """健康检查"""
        if not self.is_connected():
            return False
        
        try:
            # 检查Stream状态
            stream_info = await self.get_stream_info()
            if not stream_info:
                return False
            
            # 发送测试消息
            test_subject = "health.check"
            test_data = {"timestamp": time.time()}
            
            await self.publish(test_subject, test_data)
            return True
            
        except Exception as e:
            logger.error(f"JetStream健康检查失败: {e}")
            return False

    async def create_consumer(self, stream_name: str, consumer_name: str, filter_subject: str = None) -> Any:
        """创建JetStream消费者"""
        try:
            if not self.js:
                raise RuntimeError("JetStream未启用")

            from nats.js.api import ConsumerConfig

            # 创建消费者配置
            config_dict = {
                "durable_name": consumer_name,
                "deliver_policy": "new",
                "ack_policy": "explicit"
            }

            if filter_subject:
                config_dict["filter_subject"] = filter_subject

            consumer_config = ConsumerConfig(**config_dict)

            # 创建消费者
            consumer_info = await self.js.add_consumer(
                stream=stream_name,
                config=consumer_config
            )

            logger.info(f"消费者创建成功: {consumer_name} (流: {stream_name})")
            return consumer_info

        except Exception as e:
            logger.error(f"消费者创建失败: {consumer_name} - {e}")
            raise