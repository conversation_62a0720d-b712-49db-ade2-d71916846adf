# 角色定义配置文件
# 定义不同角色的行为和配置

# ============================================================================
# 角色定义
# ============================================================================
roles:
  # 主账户角色
  master:
    name: "主账户"
    description: "负责生成交易信号的账户"
    
    # 角色能力
    capabilities:
      - "generate_signals"
      - "monitor_positions"
      - "send_notifications"
      
    # 默认配置
    default_config:
      monitoring:
        polling_interval: 0.1  # 100ms高频监控
        adaptive_polling: true
        signal_generation: true
        
      risk_management:
        scale_factor: 1.0
        max_positions: 10
        max_lot_size: 10.0
        
      messaging:
        publish_signals: true
        subscribe_signals: false
        signal_priority: "high"
        
    # 性能要求
    requirements:
      min_cpu_cores: 2
      min_memory_mb: 4096
      min_network_latency_ms: 50
      
  # 从账户角色  
  slave:
    name: "从账户"
    description: "接收并执行交易信号的账户"
    
    # 角色能力
    capabilities:
      - "execute_signals"
      - "risk_check"
      - "position_sync"
      
    # 默认配置
    default_config:
      monitoring:
        polling_interval: 0.2  # 200ms监控
        adaptive_polling: true
        signal_generation: false
        
      risk_management:
        scale_factor: 0.5  # 默认50%手数
        max_positions: 5
        max_lot_size: 5.0
        
      messaging:
        publish_signals: false
        subscribe_signals: true
        signal_priority: "normal"
        
      execution:
        execution_delay_ms: 0
        risk_check_enabled: true
        position_sync_enabled: true
        
    # 性能要求
    requirements:
      min_cpu_cores: 1
      min_memory_mb: 2048
      min_network_latency_ms: 100
      
  # 观察者角色
  observer:
    name: "观察者"
    description: "只监控不交易的账户"
    
    # 角色能力
    capabilities:
      - "monitor_only"
      - "collect_metrics"
      
    # 默认配置
    default_config:
      monitoring:
        polling_interval: 1.0  # 1秒监控
        adaptive_polling: false
        signal_generation: false
        
      risk_management:
        scale_factor: 0.0  # 不交易
        max_positions: 0
        max_lot_size: 0.0
        
      messaging:
        publish_signals: false
        subscribe_signals: true
        signal_priority: "low"
        
    # 性能要求
    requirements:
      min_cpu_cores: 1
      min_memory_mb: 1024
      min_network_latency_ms: 200
      
  # 禁用角色
  disabled:
    name: "禁用"
    description: "暂时禁用的账户"
    
    # 角色能力
    capabilities: []
    
    # 默认配置
    default_config:
      monitoring:
        polling_interval: 0
        adaptive_polling: false
        signal_generation: false
        
      risk_management:
        scale_factor: 0.0
        max_positions: 0
        max_lot_size: 0.0
        
      messaging:
        publish_signals: false
        subscribe_signals: false
        
    # 性能要求
    requirements:
      min_cpu_cores: 0
      min_memory_mb: 0
      min_network_latency_ms: 0

# ============================================================================
# 角色切换规则
# ============================================================================
role_switching:
  # 切换策略
  strategies:
    manual:
      name: "手动切换"
      description: "通过API或配置文件手动切换"
      enabled: true
      
    scheduled:
      name: "定时切换"
      description: "按照时间表自动切换"
      enabled: true
      
    performance_based:
      name: "性能驱动切换"
      description: "基于性能指标自动切换"
      enabled: false
      
    load_balanced:
      name: "负载均衡切换"
      description: "基于系统负载自动切换"
      enabled: true
      
  # 切换限制
  constraints:
    min_master_accounts: 1  # 最少主账户数量
    max_master_accounts: 5  # 最多主账户数量
    cooldown_period: 300    # 切换冷却时间(秒)
    
  # 切换验证
  validation:
    check_account_health: true
    check_network_connectivity: true
    check_mt5_connection: true
    check_risk_limits: true
    
# ============================================================================
# 角色兼容性矩阵
# ============================================================================
compatibility:
  # 同一主机上的角色兼容性
  same_host:
    master:
      - slave
      - observer
    slave:
      - master
      - slave
      - observer
    observer:
      - master
      - slave
      - observer
    disabled:
      - disabled
      
  # 跨主机的角色兼容性
  cross_host:
    master:
      - slave
      - observer
    slave:
      - master
    observer:
      - master
      - slave
    disabled: []

# ============================================================================
# 角色优先级
# ============================================================================
priorities:
  # 角色优先级 (数字越大优先级越高)
  role_priority:
    master: 100
    slave: 50
    observer: 10
    disabled: 0
    
  # 切换优先级
  switch_priority:
    emergency_stop: 1000
    manual_override: 500
    scheduled_switch: 100
    performance_based: 50
    load_balanced: 25
    
# ============================================================================
# 角色模板
# ============================================================================
templates:
  # 高频主账户模板
  high_frequency_master:
    base_role: "master"
    overrides:
      monitoring:
        polling_interval: 0.05  # 50ms
      risk_management:
        max_positions: 20
        
  # 保守从账户模板
  conservative_slave:
    base_role: "slave"
    overrides:
      risk_management:
        scale_factor: 0.3  # 30%手数
        max_positions: 3
        
  # 激进从账户模板
  aggressive_slave:
    base_role: "slave"
    overrides:
      risk_management:
        scale_factor: 1.0  # 100%手数
        max_positions: 10
