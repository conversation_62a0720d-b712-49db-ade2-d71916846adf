"""
MT5跟单系统FastAPI应用
"""
import asyncio
import time
from typing import Optional
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
import socket

from .routes import router, set_global_managers
from .openapi_config import add_openapi_enhancements, OPENAPI_TAGS
from ..manager.copy_manager import CopyManager
from ..config.settings import ConfigManager, HostInfo
from ..config.redis_client import RedisClient
from ..messaging.nats_client import NATSClient, NATSConfig
from ..distributed import DistributedCoordinator
from ..utils.logger import get_logger
from ..utils.metrics import get_metrics_collector


logger = get_logger(__name__)
metrics = get_metrics_collector()


# 创建FastAPI应用
app = FastAPI(
    title="MT5高频交易复制系统",
    description="分布式高频MT5交易复制系统",
    version="2.0.0",
    docs_url="/docs",
    redoc_url="/redoc",
    openapi_tags=OPENAPI_TAGS
)

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 生产环境应该配置具体的域名
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 包含路由
app.include_router(router)

# 添加OpenAPI增强功能
add_openapi_enhancements(app)

# 全局变量
copy_manager: Optional[CopyManager] = None
config_manager: Optional[ConfigManager] = None
redis_client: Optional[RedisClient] = None
nats_client: Optional[NATSClient] = None
distributed_coordinator: Optional[DistributedCoordinator] = None


@app.on_event("startup")
async def startup_event():
    """启动事件"""
    global copy_manager, config_manager, redis_client, nats_client, distributed_coordinator
    
    try:
        logger.info("正在启动MT5跟单系统API...")
        
        # 设置启动时间
        app.state.start_time = time.time()
        
        # 初始化配置管理器
        config_manager = ConfigManager("config/config.yaml")
        await config_manager.load_config()
        
        # 初始化Redis客户端
        redis_settings = config_manager.get_redis_settings()
        redis_client = RedisClient(
            url=redis_settings.url,
            db=redis_settings.db,
            password=redis_settings.password,
            max_connections=redis_settings.max_connections
        )
        
        if not await redis_client.connect():
            raise Exception("Redis连接失败")
        
        # 初始化NATS客户端
        nats_settings = config_manager.get_nats_settings()
        nats_config = NATSConfig(
            servers=nats_settings.servers,
            name="mt5-copier-api",
            max_reconnect_attempts=nats_settings.max_reconnect_attempts
        )
        
        nats_client = NATSClient(nats_config)
        if not await nats_client.connect():
            raise Exception("NATS连接失败")
        
        # 初始化跟单管理器
        copy_manager = CopyManager(config_manager, nats_client, redis_client)
        await copy_manager.start()

        # 初始化分布式协调器
        distributed_settings = config_manager.get_distributed_settings()
        if distributed_settings.enabled:
            # 创建本地主机信息
            local_host_info = HostInfo(
                host_id=f"api-{socket.gethostname()}",
                hostname=socket.gethostname(),
                ip_address=socket.gethostbyname(socket.gethostname()),
                port=8000,
                capabilities=["api"],
                region="default",
                status="online"
            )

            distributed_coordinator = DistributedCoordinator(
                redis_client, nats_client, distributed_settings, local_host_info
            )
            await distributed_coordinator.start()
            logger.info("分布式协调器已启动")
        
        # 设置路由中的全局管理器
        set_global_managers(copy_manager, config_manager, distributed_coordinator)

        logger.info("MT5跟单系统API启动成功")
        
    except Exception as e:
        logger.error(f"启动失败: {e}")
        raise


@app.on_event("shutdown")
async def shutdown_event():
    """停止事件"""
    global copy_manager, redis_client, nats_client, distributed_coordinator
    
    logger.info("正在停止MT5跟单系统API...")
    
    try:
        if copy_manager:
            await copy_manager.stop()
        
        if distributed_coordinator:
            await distributed_coordinator.stop()
        
        if nats_client:
            await nats_client.disconnect()
        
        if redis_client:
            await redis_client.disconnect()
        
        logger.info("MT5跟单系统API已停止")
        
    except Exception as e:
        logger.error(f"停止过程中发生错误: {e}")


# 添加启动时间追踪
@app.middleware("http")
async def track_request_time(request, call_next):
    """跟踪请求时间"""
    start_time = time.time()
    response = await call_next(request)
    process_time = time.time() - start_time
    
    # 记录请求时间
    metrics.histogram('api_request_duration_seconds', process_time)
    
    # 添加响应头
    response.headers["X-Process-Time"] = str(process_time)
    
    return response


# 添加错误处理
@app.exception_handler(Exception)
async def global_exception_handler(request, exc):
    """全局异常处理"""
    logger.error(f"未处理的异常: {exc}", exc_info=True)
    metrics.increment('api_errors_total')
    
    return {
        "error": "内部服务器错误",
        "message": "请查看服务器日志获取详细信息",
        "timestamp": time.time()
    }


if __name__ == "__main__":
    import uvicorn
    
    # 开发环境启动
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )