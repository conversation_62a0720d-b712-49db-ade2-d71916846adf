#!/usr/bin/env python3
"""
远程主机设置脚本
用于配置和启动远程主机的MT5交易系统
"""
import os
import sys
import yaml
import argparse
from pathlib import Path

def setup_remote_host_config(host_id, host_ip, nats_servers, redis_host):
    """设置远程主机配置"""
    
    # 配置文件路径
    config_path = Path("config/optimized_system.yaml")
    
    if not config_path.exists():
        print(f"❌ 配置文件不存在: {config_path}")
        return False
    
    # 读取配置
    with open(config_path, 'r', encoding='utf-8') as f:
        config = yaml.safe_load(f)
    
    # 更新主机配置
    config['host']['id'] = host_id
    config['host']['ip_address'] = host_ip
    
    # 更新NATS配置
    if isinstance(nats_servers, str):
        nats_servers = [nats_servers]
    config['messaging']['jetstream']['servers'] = nats_servers
    
    # 更新Redis配置
    config['storage']['redis']['host'] = redis_host
    
    # 保存配置
    with open(config_path, 'w', encoding='utf-8') as f:
        yaml.dump(config, f, default_flow_style=False, allow_unicode=True)
    
    print(f"✅ 远程主机配置已更新:")
    print(f"   主机ID: {host_id}")
    print(f"   主机IP: {host_ip}")
    print(f"   NATS服务器: {nats_servers}")
    print(f"   Redis主机: {redis_host}")
    
    return True

def create_remote_startup_script(host_id, accounts):
    """创建远程主机启动脚本"""
    
    script_content = f"""#!/bin/bash
# 远程主机 {host_id} 启动脚本

echo "🚀 启动远程主机 {host_id} 的MT5交易系统..."

# 检查Python环境
if ! command -v python &> /dev/null; then
    echo "❌ Python未安装"
    exit 1
fi

# 检查依赖
if ! python -c "import nats, redis, yaml" &> /dev/null; then
    echo "❌ 缺少必要的Python依赖，请运行: pip install -r requirements.txt"
    exit 1
fi

# 启动系统
echo "启动分布式组件..."
python scripts/start.py --mode distributed --accounts {','.join(accounts)} &

# 等待启动
sleep 5

echo "✅ 远程主机 {host_id} 启动完成"
echo "账户: {', '.join(accounts)}"

# 显示状态
python scripts/check_system_status.py
"""
    
    script_path = Path(f"start_remote_{host_id}.sh")
    with open(script_path, 'w', encoding='utf-8') as f:
        f.write(script_content)
    
    # 设置执行权限
    os.chmod(script_path, 0o755)
    
    print(f"✅ 远程启动脚本已创建: {script_path}")
    return script_path

def main():
    parser = argparse.ArgumentParser(description="设置远程主机配置")
    parser.add_argument("--host-id", required=True, help="远程主机ID")
    parser.add_argument("--host-ip", required=True, help="远程主机IP地址")
    parser.add_argument("--nats-servers", required=True, help="NATS服务器地址（逗号分隔）")
    parser.add_argument("--redis-host", required=True, help="Redis主机地址")
    parser.add_argument("--accounts", required=True, help="远程主机账户（逗号分隔）")
    
    args = parser.parse_args()
    
    # 解析参数
    host_id = args.host_id
    host_ip = args.host_ip
    nats_servers = args.nats_servers.split(',')
    redis_host = args.redis_host
    accounts = args.accounts.split(',')
    
    print(f"🔧 设置远程主机: {host_id}")
    print(f"IP地址: {host_ip}")
    print(f"账户: {', '.join(accounts)}")
    
    # 设置配置
    if setup_remote_host_config(host_id, host_ip, nats_servers, redis_host):
        # 创建启动脚本
        script_path = create_remote_startup_script(host_id, accounts)
        
        print(f"\n🎯 下一步操作:")
        print(f"1. 将整个项目目录复制到远程主机 {host_ip}")
        print(f"2. 在远程主机上运行: ./{script_path}")
        print(f"3. 或者手动运行: python scripts/start.py --mode distributed --accounts {','.join(accounts)}")
        
        return True
    
    return False

if __name__ == "__main__":
    main()
