"""
MT5跟单系统API路由
"""
import time
from typing import List, Optional, Dict, Any
from fastapi import APIRouter, HTTPException, BackgroundTasks, Depends
from datetime import datetime

from .models import (
    CreatePairingRequest, UpdatePairingRequest, PairingResponse,
    RegisterHostRequest, RegisterAccountRequest, SystemStatusResponse,
    CrossHostPairingRequest, SystemHealthResponse, HostStatusResponse,
    HealthCheckResponse, RoleSwitchRequest, PerformanceMetrics, TradingMetrics
)
# 使用新的架构组件替代遗留组件
from ..relationships.relationship_manager import RelationshipManager
from ..core.config_manager import get_config_manager
from ..distributed.service_registry import ServiceRegistry
from ..core.mt5_coordinator import MT5Coordinator
from ..utils.logger import get_logger
from ..utils.metrics import get_metrics_collector, get_performance_monitor


logger = get_logger(__name__)
metrics = get_metrics_collector()

# 创建路由器
router = APIRouter()

# 全局变量（将由main.py初始化）
relationship_manager: Optional[RelationshipManager] = None
mt5_coordinator: Optional[MT5Coordinator] = None
service_registry: Optional[ServiceRegistry] = None


# 依赖注入
async def get_relationship_manager() -> RelationshipManager:
    """获取关系管理器"""
    if relationship_manager is None:
        raise HTTPException(status_code=503, detail="关系管理器未初始化")
    return relationship_manager

async def get_mt5_coordinator() -> MT5Coordinator:
    """获取MT5协调器"""
    if mt5_coordinator is None:
        raise HTTPException(status_code=503, detail="MT5协调器未初始化")
    return mt5_coordinator


async def get_config_manager() -> ConfigManager:
    """获取配置管理器"""
    if config_manager is None:
        raise HTTPException(status_code=503, detail="配置管理器未初始化")
    return config_manager


async def get_distributed_coordinator() -> DistributedCoordinator:
    """获取分布式协调器"""
    if distributed_coordinator is None:
        raise HTTPException(status_code=503, detail="分布式协调器未初始化")
    return distributed_coordinator


def set_global_managers(cm: CopyManager, config_mgr: ConfigManager, dist_coord: DistributedCoordinator):
    """设置全局管理器实例"""
    global copy_manager, config_manager, distributed_coordinator
    copy_manager = cm
    config_manager = config_mgr
    distributed_coordinator = dist_coord


# 配对管理API
@router.post("/api/pairings", response_model=PairingResponse)
async def create_pairing(
    request: CreatePairingRequest,
    manager: CopyManager = Depends(get_copy_manager)
):
    """创建账户配对"""
    try:
        # 验证策略
        if request.strategy not in [s.value for s in CopyStrategy]:
            raise HTTPException(status_code=400, detail=f"无效的策略: {request.strategy}")
        
        # 创建配对
        pairing_id = await manager.create_pairing(
            master_account=request.master_account,
            slave_accounts=request.slave_accounts,
            config=request.dict(exclude={'master_account', 'slave_accounts'})
        )
        
        metrics.increment('api_pairings_created_total')
        
        return PairingResponse(
            pairing_id=pairing_id,
            status="created",
            created_at=datetime.now().isoformat(),
            message="配对创建成功"
        )
        
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"创建配对失败: {e}")
        raise HTTPException(status_code=500, detail="内部服务器错误")


@router.get("/api/pairings")
async def list_pairings(manager: CopyManager = Depends(get_copy_manager)):
    """获取所有配对"""
    try:
        pairings = await manager.list_pairings()
        return {
            "pairings": [pairing.to_dict() for pairing in pairings],
            "total": len(pairings)
        }
    except Exception as e:
        logger.error(f"获取配对列表失败: {e}")
        raise HTTPException(status_code=500, detail="内部服务器错误")


@router.get("/api/pairings/{pairing_id}")
async def get_pairing(
    pairing_id: str,
    manager: CopyManager = Depends(get_copy_manager)
):
    """获取配对详情"""
    try:
        pairing = await manager.get_pairing(pairing_id)
        if not pairing:
            raise HTTPException(status_code=404, detail="配对不存在")
        
        return pairing.to_dict()
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取配对失败: {e}")
        raise HTTPException(status_code=500, detail="内部服务器错误")


@router.put("/api/pairings/{pairing_id}")
async def update_pairing(
    pairing_id: str,
    request: UpdatePairingRequest,
    manager: CopyManager = Depends(get_copy_manager)
):
    """更新配对"""
    try:
        # 过滤空值
        updates = {k: v for k, v in request.dict().items() if v is not None}
        
        if not updates:
            raise HTTPException(status_code=400, detail="没有提供更新数据")
        
        success = await manager.update_pairing(pairing_id, updates)
        
        if success:
            metrics.increment('api_pairings_updated_total')
            return {"message": "配对更新成功"}
        else:
            raise HTTPException(status_code=400, detail="更新失败")
            
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"更新配对失败: {e}")
        raise HTTPException(status_code=500, detail="内部服务器错误")


@router.delete("/api/pairings/{pairing_id}")
async def delete_pairing(
    pairing_id: str,
    manager: CopyManager = Depends(get_copy_manager)
):
    """删除配对"""
    try:
        success = await manager.delete_pairing(pairing_id)
        
        if success:
            metrics.increment('api_pairings_deleted_total')
            return {"message": "配对删除成功"}
        else:
            raise HTTPException(status_code=404, detail="配对不存在")
            
    except Exception as e:
        logger.error(f"删除配对失败: {e}")
        raise HTTPException(status_code=500, detail="内部服务器错误")


@router.get("/api/pairings/{pairing_id}/status")
async def get_pairing_status(
    pairing_id: str,
    manager: CopyManager = Depends(get_copy_manager)
):
    """获取配对状态"""
    try:
        status = await manager.get_pairing_status(pairing_id)
        if not status:
            raise HTTPException(status_code=404, detail="配对不存在")
        
        return status
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取配对状态失败: {e}")
        raise HTTPException(status_code=500, detail="内部服务器错误")


@router.post("/api/pairings/{pairing_id}/enable")
async def enable_pairing(
    pairing_id: str,
    manager: CopyManager = Depends(get_copy_manager)
):
    """启用配对"""
    try:
        success = await manager.enable_pairing(pairing_id)
        
        if success:
            metrics.increment('api_pairings_enabled_total')
            return {"message": "配对已启用"}
        else:
            raise HTTPException(status_code=400, detail="启用失败")
            
    except Exception as e:
        logger.error(f"启用配对失败: {e}")
        raise HTTPException(status_code=500, detail="内部服务器错误")


@router.post("/api/pairings/{pairing_id}/disable")
async def disable_pairing(
    pairing_id: str,
    manager: CopyManager = Depends(get_copy_manager)
):
    """禁用配对"""
    try:
        success = await manager.disable_pairing(pairing_id)
        
        if success:
            metrics.increment('api_pairings_disabled_total')
            return {"message": "配对已禁用"}
        else:
            raise HTTPException(status_code=400, detail="禁用失败")
            
    except Exception as e:
        logger.error(f"禁用配对失败: {e}")
        raise HTTPException(status_code=500, detail="内部服务器错误")


# 主机管理API
@router.post("/api/hosts/register")
async def register_host(
    request: RegisterHostRequest,
    coordinator: DistributedCoordinator = Depends(get_distributed_coordinator)
):
    """注册主机"""
    try:
        host_info = HostInfo(
            host_id=request.host_id,
            hostname=request.hostname,
            ip_address=request.ip_address,
            port=request.port,
            capabilities=request.capabilities,
            max_accounts=request.max_accounts,
            status=request.status
        )
        
        success = await coordinator.register_host(host_info)
        
        if success:
            metrics.increment('api_hosts_registered_total')
            return {"message": "主机注册成功", "host_id": request.host_id}
        else:
            raise HTTPException(status_code=400, detail="注册失败")
            
    except Exception as e:
        logger.error(f"注册主机失败: {e}")
        raise HTTPException(status_code=500, detail="内部服务器错误")


@router.get("/api/hosts")
async def list_hosts(
    coordinator: DistributedCoordinator = Depends(get_distributed_coordinator)
):
    """获取主机列表"""
    try:
        hosts = await coordinator.get_host_list()
        return {
            "hosts": [host.to_dict() for host in hosts],
            "total": len(hosts)
        }
    except Exception as e:
        logger.error(f"获取主机列表失败: {e}")
        raise HTTPException(status_code=500, detail="内部服务器错误")


@router.delete("/api/hosts/{host_id}")
async def unregister_host(
    host_id: str,
    coordinator: DistributedCoordinator = Depends(get_distributed_coordinator)
):
    """注销主机"""
    try:
        success = await coordinator.unregister_host(host_id)
        
        if success:
            metrics.increment('api_hosts_unregistered_total')
            return {"message": "主机注销成功"}
        else:
            raise HTTPException(status_code=404, detail="主机不存在")
            
    except Exception as e:
        logger.error(f"注销主机失败: {e}")
        raise HTTPException(status_code=500, detail="内部服务器错误")


@router.get("/api/hosts/{host_id}/status", response_model=HostStatusResponse)
async def get_host_status(
    host_id: str,
    coordinator: DistributedCoordinator = Depends(get_distributed_coordinator)
):
    """获取主机状态"""
    try:
        status = await coordinator.get_host_status(host_id)
        if not status:
            raise HTTPException(status_code=404, detail="主机不存在")
        
        return status
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取主机状态失败: {e}")
        raise HTTPException(status_code=500, detail="内部服务器错误")


# 账户管理API
@router.post("/api/accounts/register")
async def register_account(
    request: RegisterAccountRequest,
    coordinator: DistributedCoordinator = Depends(get_distributed_coordinator)
):
    """注册账户"""
    try:
        account_info = {
            "account_id": request.account_id,
            "login": request.login,
            "password": request.password,
            "server": request.server,
            "host_id": request.host_id,
            "account_type": request.account_type,
            "terminal_path": request.terminal_path,
            "max_lot_size": request.max_lot_size,
            "risk_level": request.risk_level,
            "allowed_symbols": request.allowed_symbols
        }
        
        success = await coordinator.register_account(account_info)
        
        if success:
            metrics.increment('api_accounts_registered_total')
            return {"message": "账户注册成功", "account_id": request.account_id}
        else:
            raise HTTPException(status_code=400, detail="注册失败")
            
    except Exception as e:
        logger.error(f"注册账户失败: {e}")
        raise HTTPException(status_code=500, detail="内部服务器错误")


@router.get("/api/accounts")
async def list_accounts(
    coordinator: DistributedCoordinator = Depends(get_distributed_coordinator)
):
    """获取账户列表"""
    try:
        accounts = await coordinator.get_account_list()
        return {
            "accounts": accounts,
            "total": len(accounts)
        }
    except Exception as e:
        logger.error(f"获取账户列表失败: {e}")
        raise HTTPException(status_code=500, detail="内部服务器错误")


# 系统状态API
@router.get("/api/system/status", response_model=SystemStatusResponse)
async def get_system_status(
    coordinator: DistributedCoordinator = Depends(get_distributed_coordinator)
):
    """获取系统状态"""
    try:
        status = await coordinator.get_system_status()
        return status
        
    except Exception as e:
        logger.error(f"获取系统状态失败: {e}")
        raise HTTPException(status_code=500, detail="内部服务器错误")


@router.get("/api/system/health", response_model=SystemHealthResponse)
async def get_system_health(
    coordinator: DistributedCoordinator = Depends(get_distributed_coordinator)
):
    """获取系统健康状态"""
    try:
        health = await coordinator.get_system_health()
        return health
        
    except Exception as e:
        logger.error(f"获取系统健康状态失败: {e}")
        raise HTTPException(status_code=500, detail="内部服务器错误")


# 性能监控API
@router.get("/api/metrics/performance", response_model=PerformanceMetrics)
async def get_performance_metrics():
    """获取性能指标"""
    try:
        performance_monitor = get_performance_monitor()
        metrics_data = await performance_monitor.get_current_metrics()
        
        return PerformanceMetrics(
            cpu_usage=metrics_data.get('cpu_usage', 0),
            memory_usage=metrics_data.get('memory_usage', 0),
            latency_p50=metrics_data.get('latency_p50', 0),
            latency_p95=metrics_data.get('latency_p95', 0),
            latency_p99=metrics_data.get('latency_p99', 0),
            signals_per_second=metrics_data.get('signals_per_second', 0),
            throughput=metrics_data.get('throughput', 0),
            error_rate=metrics_data.get('error_rate', 0)
        )
        
    except Exception as e:
        logger.error(f"获取性能指标失败: {e}")
        raise HTTPException(status_code=500, detail="内部服务器错误")


@router.get("/api/metrics/trading", response_model=TradingMetrics)
async def get_trading_metrics(
    manager: CopyManager = Depends(get_copy_manager)
):
    """获取交易指标"""
    try:
        trading_stats = await manager.get_trading_statistics()
        
        return TradingMetrics(
            total_signals=trading_stats.get('total_signals', 0),
            successful_executions=trading_stats.get('successful_executions', 0),
            failed_executions=trading_stats.get('failed_executions', 0),
            average_execution_time=trading_stats.get('average_execution_time', 0),
            total_volume=trading_stats.get('total_volume', 0),
            active_positions=trading_stats.get('active_positions', 0),
            daily_pnl=trading_stats.get('daily_pnl', 0)
        )
        
    except Exception as e:
        logger.error(f"获取交易指标失败: {e}")
        raise HTTPException(status_code=500, detail="内部服务器错误")


# 跨主机管理API
@router.post("/api/pairings/cross-host")
async def create_cross_host_pairing(
    request: CrossHostPairingRequest,
    coordinator: DistributedCoordinator = Depends(get_distributed_coordinator)
):
    """创建跨主机配对"""
    try:
        pairing_id = await coordinator.create_cross_host_pairing(
            master_host=request.master_host,
            master_account=request.master_account,
            slave_host=request.slave_host,
            slave_account=request.slave_account,
            config={
                "strategy": request.strategy,
                "lot_multiplier": request.lot_multiplier,
                "max_lot": request.max_lot,
                "min_lot": request.min_lot,
                "enabled": request.enabled
            }
        )
        
        metrics.increment('api_cross_host_pairings_created_total')
        
        return {
            "pairing_id": pairing_id,
            "message": "跨主机配对创建成功"
        }
        
    except Exception as e:
        logger.error(f"创建跨主机配对失败: {e}")
        raise HTTPException(status_code=500, detail="内部服务器错误")


@router.delete("/api/pairings/cross-host/{pairing_id}")
async def delete_cross_host_pairing(
    pairing_id: str,
    coordinator: DistributedCoordinator = Depends(get_distributed_coordinator)
):
    """删除跨主机配对"""
    try:
        success = await coordinator.delete_cross_host_pairing(pairing_id)
        
        if success:
            metrics.increment('api_cross_host_pairings_deleted_total')
            return {"message": "跨主机配对删除成功"}
        else:
            raise HTTPException(status_code=404, detail="配对不存在")
            
    except Exception as e:
        logger.error(f"删除跨主机配对失败: {e}")
        raise HTTPException(status_code=500, detail="内部服务器错误")


@router.get("/api/pairings/cross-host")
async def list_cross_host_pairings(
    coordinator: DistributedCoordinator = Depends(get_distributed_coordinator)
):
    """获取跨主机配对列表"""
    try:
        pairings = await coordinator.get_cross_host_pairings()
        return {
            "pairings": pairings,
            "total": len(pairings)
        }
    except Exception as e:
        logger.error(f"获取跨主机配对列表失败: {e}")
        raise HTTPException(status_code=500, detail="内部服务器错误")


# 角色管理API
@router.post("/api/roles/switch")
async def switch_role(
    request: RoleSwitchRequest,
    manager: CopyManager = Depends(get_copy_manager)
):
    """切换角色"""
    try:
        success = await manager.switch_terminal_role(
            request.terminal_id,
            request.role
        )
        
        if success:
            metrics.increment('api_role_switches_total')
            return {"message": f"角色切换成功: {request.role}"}
        else:
            raise HTTPException(status_code=400, detail="角色切换失败")
            
    except Exception as e:
        logger.error(f"角色切换失败: {e}")
        raise HTTPException(status_code=500, detail="内部服务器错误")


# 健康检查
@router.get("/health", response_model=HealthCheckResponse)
async def health_check():
    """健康检查"""
    try:
        # 基本健康检查
        components = {}
        issues = []
        
        # 检查跟单管理器
        if copy_manager:
            components["copy_manager"] = "healthy"
        else:
            components["copy_manager"] = "unhealthy"
            issues.append("跟单管理器未初始化")
        
        # 检查配置管理器
        if config_manager:
            components["config_manager"] = "healthy"
        else:
            components["config_manager"] = "unhealthy"
            issues.append("配置管理器未初始化")
        
        # 检查分布式协调器
        if distributed_coordinator:
            components["distributed_coordinator"] = "healthy"
        else:
            components["distributed_coordinator"] = "unhealthy"
            issues.append("分布式协调器未初始化")
        
        # 确定总体状态
        overall_status = "healthy" if not issues else "unhealthy"
        
        return HealthCheckResponse(
            status=overall_status,
            timestamp=time.time(),
            components=components,
            issues=issues
        )
        
    except Exception as e:
        logger.error(f"健康检查失败: {e}")
        return HealthCheckResponse(
            status="unhealthy",
            timestamp=time.time(),
            components={"error": "unknown"},
            issues=[str(e)]
        )


@router.get("/")
async def root():
    """根端点"""
    return {
        "message": "MT5高频异步轮询交易复制系统",
        "version": "2.0.0",
        "status": "运行中",
        "timestamp": time.time()
    }