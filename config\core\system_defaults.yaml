# 系统默认值配置
# 这是所有硬编码值的唯一权威来源
# 所有模块都应该从这里读取默认值，而不是硬编码

# ============================================================================
# 跟单交易默认值
# ============================================================================
copy_trading:
  # 基础跟单参数
  default_copy_ratio: 1.0              # 默认跟单比例
  default_max_volume: 10.0             # 默认最大手数
  default_min_volume: 0.01             # 默认最小手数
  default_max_positions: 10            # 默认最大持仓数
  default_volume_round_to: 0.01        # 默认手数取整精度
  
  # 风险管理默认值
  default_risk_multiplier: 1.0         # 默认风险倍数
  default_max_drawdown: 0.15           # 默认最大回撤 (15%)
  default_stop_loss_multiplier: 1.0    # 默认止损倍数
  default_take_profit_multiplier: 1.0  # 默认止盈倍数
  
  # 执行参数默认值
  default_delay_ms: 100                # 默认执行延迟 (毫秒)
  default_timeout_ms: 5000             # 默认执行超时 (毫秒)
  default_retry_attempts: 3            # 默认重试次数
  default_retry_delay_ms: 1000         # 默认重试延迟 (毫秒)
  
  # 时间限制默认值
  default_active_hours:
    start: "00:00"
    end: "24:00"
    timezone: "UTC"
  
  default_active_days:
    - "monday"
    - "tuesday"
    - "wednesday"
    - "thursday"
    - "friday"

# ============================================================================
# 风险管理默认值
# ============================================================================
risk_management:
  # 基础风险限制
  default_max_daily_loss: 1000.0       # 默认每日最大亏损
  default_max_daily_trades: 100        # 默认每日最大交易数
  default_max_exposure_per_symbol: 5.0 # 默认单品种最大敞口
  default_max_total_exposure: 50.0     # 默认总敞口限制
  
  # 紧急停止默认值
  default_emergency_stop_loss: 2000.0  # 默认紧急停损金额
  default_emergency_stop_drawdown: 0.3 # 默认紧急停损回撤 (30%)
  
  # 时间限制默认值
  default_max_trades_per_hour: 50      # 默认每小时最大交易数
  default_max_trades_per_minute: 10    # 默认每分钟最大交易数

# ============================================================================
# MT5连接默认值
# ============================================================================
mt5_connection:
  # 连接参数
  default_timeout_seconds: 30          # 默认连接超时
  default_retry_attempts: 3            # 默认连接重试次数
  default_retry_delay_seconds: 5       # 默认重试延迟
  default_max_restarts: 5              # 默认最大重启次数
  
  # 交易参数
  default_magic_number: 12345          # 默认魔术数字
  default_slippage: 3                  # 默认滑点
  default_max_deviation: 20            # 默认最大偏差

# ============================================================================
# 消息传递默认值
# ============================================================================
messaging:
  # NATS配置
  default_nats_servers:
    - "nats://localhost:4222"
  
  default_connection_timeout: 10       # 默认连接超时 (秒)
  default_reconnect_attempts: 5        # 默认重连次数
  default_reconnect_delay: 2           # 默认重连延迟 (秒)
  
  # JetStream配置
  default_stream_retention: 86400      # 默认流保留时间 (秒)
  default_max_messages: 10000          # 默认最大消息数
  default_max_bytes: 104857600         # 默认最大字节数 (100MB)
  
  # 优先级队列配置
  priority_queue:
    max_sizes:
      system_critical: 100            # 系统级风险队列
      risk_command: 500               # 订单级风控队列  
      signal_execution: 1000          # 策略信号执行队列
      realtime_query: 800             # 实时数据查询队列
      background_task: 2000           # 后台任务队列
    
    worker_counts:
      priority_workers: 6             # 通用优先级工作线程数
      critical_workers: 2             # 专用关键优先级工作线程数
    
    retry:
      max_retries: 3                  # 最大重试次数
      retry_delay_ms: 1000            # 重试延迟（毫秒）
  
  # 消息处理
  default_batch_size: 10               # 默认批处理大小
  default_batch_timeout_ms: 1000       # 默认批处理超时 (毫秒)
  default_ack_timeout_seconds: 30      # 默认确认超时 (秒)

# ============================================================================
# 监控默认值
# ============================================================================
monitoring:
  # 性能监控
  default_polling_interval_ms: 1000    # 默认轮询间隔 (毫秒)
  default_metrics_collection_interval: 60  # 默认指标收集间隔 (秒)
  default_health_check_interval: 30    # 默认健康检查间隔 (秒)
  
  # 日志配置
  default_log_level: "INFO"            # 默认日志级别
  default_log_rotation_size: 10485760  # 默认日志轮转大小 (10MB)
  default_log_retention_days: 30       # 默认日志保留天数
  
  # 告警配置
  default_alert_threshold_error_rate: 0.05  # 默认错误率告警阈值 (5%)
  default_alert_threshold_latency_ms: 1000  # 默认延迟告警阈值 (毫秒)
  default_alert_cooldown_minutes: 5    # 默认告警冷却时间 (分钟)

# ============================================================================
# 数据存储默认值
# ============================================================================
data_storage:
  # 本地存储
  default_data_directory: "./data"
  default_logs_directory: "./logs"
  default_backup_directory: "./backups"
  
  # 数据保留
  default_trade_history_days: 90       # 默认交易历史保留天数
  default_log_files_days: 30           # 默认日志文件保留天数
  default_backup_files_days: 7         # 默认备份文件保留天数
  
  # 缓存配置
  default_cache_ttl_seconds: 600       # 默认缓存TTL (10分钟)
  default_cache_max_size_mb: 100       # 默认缓存最大大小 (MB)

# ============================================================================
# 系统性能默认值
# ============================================================================
performance:
  # 进程配置
  default_worker_processes: 4          # 默认工作进程数
  default_thread_pool_size: 10         # 默认线程池大小
  default_queue_max_size: 1000         # 默认队列最大大小
  
  # 资源限制
  default_max_memory_mb: 1024          # 默认最大内存使用 (MB)
  default_max_cpu_percent: 80          # 默认最大CPU使用率 (%)
  default_max_open_files: 1000         # 默认最大打开文件数
  
  # 超时配置
  default_operation_timeout_seconds: 30    # 默认操作超时 (秒)
  default_shutdown_timeout_seconds: 10     # 默认关闭超时 (秒)
  default_startup_timeout_seconds: 60      # 默认启动超时 (秒)

# ============================================================================
# 开发和测试默认值
# ============================================================================
development:
  # 开发环境限制
  dev_max_volume: 1.0                  # 开发环境最大手数
  dev_max_daily_loss: 100.0            # 开发环境每日最大亏损
  dev_max_positions: 3                 # 开发环境最大持仓数
  
  # 测试环境限制
  test_max_volume: 0.1                 # 测试环境最大手数
  test_max_daily_loss: 50.0            # 测试环境每日最大亏损
  test_max_positions: 1                # 测试环境最大持仓数
  
  # 调试配置
  debug_log_level: "DEBUG"             # 调试日志级别
  debug_verbose_logging: true          # 详细日志记录
  debug_performance_tracking: true     # 性能跟踪

# ============================================================================
# 安全默认值
# ============================================================================
security:
  # 访问控制
  default_session_timeout_minutes: 30  # 默认会话超时 (分钟)
  default_max_login_attempts: 5        # 默认最大登录尝试次数
  default_lockout_duration_minutes: 15 # 默认锁定时长 (分钟)
  
  # 加密配置
  default_encryption_enabled: false    # 默认加密开关
  default_key_rotation_days: 90        # 默认密钥轮转天数
  
  # 审计配置
  default_audit_enabled: true          # 默认审计开关
  default_audit_retention_days: 365    # 默认审计日志保留天数

# ============================================================================
# 版本和兼容性
# ============================================================================
version:
  config_version: "2.0.0"              # 配置文件版本
  system_version: "2.0.0"              # 系统版本
  min_compatible_version: "1.5.0"      # 最小兼容版本
  
  # 特性开关
  feature_flags:
    enable_advanced_risk_management: true
    enable_performance_monitoring: true
    enable_distributed_processing: true
    enable_real_time_analytics: true
