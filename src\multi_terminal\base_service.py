# 🚨 AUTO-MIGRATED: Old messaging components replaced with HybridQueueManager
# See MIGRATION_GUIDE.md for details

"""
多终端系统基础服务
提供所有模块共用的基础功能，避免重复代码
"""
import asyncio
import json
import time
import subprocess
import psutil
from typing import Dict, List, Optional, Callable, Any
from pathlib import Path

from .models import Terminal, TerminalRole, TerminalStatus, RoleChangeEvent, SystemSummary
from ..messaging.hybrid_queue_manager import HybridQueueManager
from ..config.redis_client import RedisClient
from ..utils.logger import get_logger


logger = get_logger(__name__)


class BaseTerminalService:
    """基础终端服务"""
    
    def __init__(self, host_id: str, queue_manager: HybridQueueManager, redis_client: RedisClient):
        self.host_id = host_id
        self.nats = queue_manager
        self.redis = redis_client
        
        # 终端管理
        self.terminals: Dict[str, Terminal] = {}
        self.running = False
        
        # 端口管理
        self.base_port = 17000
        self.used_ports: set = set()
        
        # 监控任务
        self.monitor_task: Optional[asyncio.Task] = None
        self.heartbeat_task: Optional[asyncio.Task] = None
        
        # 回调函数
        self.callbacks: Dict[str, List[Callable]] = {
            'terminal_started': [],
            'terminal_stopped': [],
            'terminal_error': [],
            'role_changed': []
        }
        
        # Redis键前缀
        self.redis_prefix = "mt5:base"
    
    async def start(self):
        """启动基础服务"""
        if self.running:
            return
            
        logger.info("启动基础终端服务")
        self.running = True
        
        try:
            # 加载终端数据
            await self.load_terminals()
            
            # 启动监控任务
            self.monitor_task = asyncio.create_task(self._monitor_loop())
            self.heartbeat_task = asyncio.create_task(self._heartbeat_loop())
            
            logger.info(f"基础终端服务已启动，管理 {len(self.terminals)} 个终端")
            
        except Exception as e:
            logger.error(f"启动基础终端服务失败: {e}")
            await self.stop()
            raise
    
    async def stop(self):
        """停止基础服务"""
        if not self.running:
            return
            
        logger.info("停止基础终端服务")
        self.running = False
        
        # 停止监控任务
        for task in [self.monitor_task, self.heartbeat_task]:
            if task and not task.done():
                task.cancel()
                try:
                    await task
                except asyncio.CancelledError:
                    pass
        
        # 保存数据
        await self.save_terminals()
        
        logger.info("基础终端服务已停止")
    
    # ==================== 终端管理 ====================
    
    async def register_terminal(self, account_config, terminal_path: Optional[str] = None) -> str:
        """注册终端"""
        try:
            terminal_id = f"{self.host_id}_{account_config.id}"
            
            if terminal_id in self.terminals:
                logger.warning(f"终端已存在: {terminal_id}")
                return terminal_id
            
            # 分配端口
            port = self._allocate_port()
            
            # 创建数据目录
            data_path = Path(f"data/terminals/{terminal_id}")
            data_path.mkdir(parents=True, exist_ok=True)
            
            terminal = Terminal(
                terminal_id=terminal_id,
                account_id=account_config.id,
                login=account_config.login,
                password=account_config.password,
                server=account_config.server,
                terminal_path=terminal_path or getattr(account_config, 'terminal_path', ''),
                data_path=str(data_path),
                port=port,
                status=TerminalStatus.STOPPED,
                role=TerminalRole.IDLE
            )
            
            self.terminals[terminal_id] = terminal
            
            logger.info(f"终端已注册: {terminal_id}")
            
            return terminal_id
            
        except Exception as e:
            logger.error(f"注册终端失败: {e}")
            raise
    
    async def start_terminal(self, terminal_id: str) -> bool:
        """启动终端"""
        if terminal_id not in self.terminals:
            logger.error(f"终端不存在: {terminal_id}")
            return False
        
        terminal = self.terminals[terminal_id]
        
        if terminal.status == TerminalStatus.RUNNING:
            logger.info(f"终端已在运行: {terminal_id}")
            return True
        
        try:
            logger.info(f"启动终端: {terminal_id}")
            terminal.status = TerminalStatus.STARTING
            
            # 构建启动命令
            cmd = [
                terminal.terminal_path,
                f"/portable",
                f"/config:{terminal.data_path}",
                f"/login:{terminal.login}",
                f"/password:{terminal.password}",
                f"/server:{terminal.server}"
            ]
            
            # 启动进程
            process = subprocess.Popen(
                cmd,
                cwd=Path(terminal.terminal_path).parent,
                stdout=subprocess.DEVNULL,
                stderr=subprocess.DEVNULL
            )
            
            terminal.pid = process.pid
            terminal.status = TerminalStatus.RUNNING
            terminal.last_heartbeat = time.time()
            terminal.start_count += 1
            
            # 等待MT5初始化
            await asyncio.sleep(5)
            
            # 验证连接
            if await self._verify_terminal_connection(terminal):
                logger.info(f"终端启动成功: {terminal_id}")
                await self._notify_callbacks('terminal_started', terminal_id)
                return True
            else:
                terminal.status = TerminalStatus.ERROR
                terminal.error_count += 1
                logger.error(f"终端连接验证失败: {terminal_id}")
                await self._notify_callbacks('terminal_error', terminal_id)
                return False
                
        except Exception as e:
            terminal.status = TerminalStatus.ERROR
            terminal.error_count += 1
            logger.error(f"启动终端失败 {terminal_id}: {e}")
            await self._notify_callbacks('terminal_error', terminal_id)
            return False
    
    async def stop_terminal(self, terminal_id: str) -> bool:
        """停止终端"""
        if terminal_id not in self.terminals:
            return False
        
        terminal = self.terminals[terminal_id]
        
        try:
            # 停止进程
            if terminal.pid:
                try:
                    process = psutil.Process(terminal.pid)
                    process.terminate()
                    process.wait(timeout=10)
                except (psutil.NoSuchProcess, psutil.TimeoutExpired):
                    try:
                        process.kill()
                    except psutil.NoSuchProcess:
                        pass
            
            terminal.status = TerminalStatus.STOPPED
            terminal.pid = None
            terminal.role = TerminalRole.IDLE
            
            # 释放端口
            if terminal.port in self.used_ports:
                self.used_ports.remove(terminal.port)
            
            logger.info(f"终端已停止: {terminal_id}")
            await self._notify_callbacks('terminal_stopped', terminal_id)
            return True
            
        except Exception as e:
            logger.error(f"停止终端失败 {terminal_id}: {e}")
            return False
    
    async def get_terminal_status(self, terminal_id: str) -> Optional[Dict]:
        """获取终端状态"""
        if terminal_id not in self.terminals:
            return None
        
        terminal = self.terminals[terminal_id]
        return terminal.to_dict()
    
    async def get_all_terminals_status(self) -> List[Dict]:
        """获取所有终端状态"""
        return [terminal.to_dict() for terminal in self.terminals.values()]
    
    async def set_terminal_role(self, terminal_id: str, role: TerminalRole) -> bool:
        """设置终端角色"""
        if terminal_id not in self.terminals:
            return False
        
        terminal = self.terminals[terminal_id]
        old_role = terminal.role
        terminal.role = role
        
        logger.info(f"终端角色已设置: {terminal_id} {old_role.value} -> {role.value}")
        await self._notify_callbacks('role_changed', terminal_id, old_role.value, role.value)
        
        return True
    
    # ==================== 数据持久化 ====================
    
    async def save_terminals(self):
        """保存终端数据"""
        try:
            terminals_data = {
                terminal_id: terminal.to_dict()
                for terminal_id, terminal in self.terminals.items()
            }
            
            await self.redis.set(
                f"{self.redis_prefix}:terminals",
                json.dumps(terminals_data, default=str)
            )
            
        except Exception as e:
            logger.error(f"保存终端数据失败: {e}")
    
    async def load_terminals(self):
        """加载终端数据"""
        try:
            terminals_data = await self.redis.get(f"{self.redis_prefix}:terminals")
            if terminals_data:
                terminals_dict = json.loads(terminals_data)
                for terminal_id, terminal_data in terminals_dict.items():
                    terminal = Terminal.from_dict(terminal_data)
                    self.terminals[terminal_id] = terminal
                    
                    # 恢复端口使用
                    if terminal.port > 0:
                        self.used_ports.add(terminal.port)
                
                logger.info(f"已加载 {len(self.terminals)} 个终端")
                
        except Exception as e:
            logger.error(f"加载终端数据失败: {e}")
    
    # ==================== 回调管理 ====================
    
    def add_callback(self, event_type: str, callback: Callable):
        """添加回调函数"""
        if event_type in self.callbacks:
            self.callbacks[event_type].append(callback)
    
    async def _notify_callbacks(self, event_type: str, *args, **kwargs):
        """通知回调函数"""
        if event_type in self.callbacks:
            for callback in self.callbacks[event_type]:
                try:
                    if asyncio.iscoroutinefunction(callback):
                        await callback(*args, **kwargs)
                    else:
                        callback(*args, **kwargs)
                except Exception as e:
                    logger.error(f"回调函数执行失败: {e}")
    
    # ==================== 工具方法 ====================
    
    def _allocate_port(self) -> int:
        """分配端口"""
        port = self.base_port
        while port in self.used_ports:
            port += 1
        self.used_ports.add(port)
        return port
    
    async def _verify_terminal_connection(self, terminal: Terminal) -> bool:
        """验证终端连接"""
        try:
            # 简化验证逻辑
            await asyncio.sleep(2)
            return True
        except Exception as e:
            logger.error(f"验证终端连接失败: {e}")
            return False
    
    async def _monitor_loop(self):
        """监控循环"""
        while self.running:
            try:
                for terminal in self.terminals.values():
                    if terminal.status == TerminalStatus.RUNNING and terminal.pid:
                        try:
                            process = psutil.Process(terminal.pid)
                            if not process.is_running():
                                terminal.status = TerminalStatus.ERROR
                                terminal.pid = None
                                terminal.error_count += 1
                                logger.warning(f"终端进程已停止: {terminal.terminal_id}")
                                await self._notify_callbacks('terminal_error', terminal.terminal_id)
                                
                        except psutil.NoSuchProcess:
                            terminal.status = TerminalStatus.ERROR
                            terminal.pid = None
                            terminal.error_count += 1
                
                # 定期保存数据
                await self.save_terminals()
                
                await asyncio.sleep(10)  # 每10秒检查一次
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"监控循环失败: {e}")
                await asyncio.sleep(5)
    
    async def _heartbeat_loop(self):
        """心跳循环"""
        while self.running:
            try:
                current_time = time.time()
                for terminal in self.terminals.values():
                    if terminal.status == TerminalStatus.RUNNING:
                        terminal.last_heartbeat = current_time
                        terminal.uptime = current_time - terminal.last_heartbeat
                
                await asyncio.sleep(30)  # 每30秒更新心跳
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"心跳循环失败: {e}")
                await asyncio.sleep(10)
