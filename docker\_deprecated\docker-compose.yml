# MT5分布式跟单系统 - 自动化部署配置
# 基于正确架构设计原则的生产就绪部署
# 使用 optimized_system.yaml 和 copy_relationships.yaml 配置
# 支持进程隔离的多账户跟单系统

services:
  # Redis - 优化的Hash数据存储
  redis:
    image: redis:7-alpine
    container_name: mt5-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_optimized_data:/data
      - ./redis.conf:/etc/redis/redis.conf:ro
    command: redis-server /etc/redis/redis.conf
    environment:
      - REDIS_REPLICATION_MODE=master
      - REDIS_OPTIMIZATION_ENABLED=true
    sysctls:
      - net.core.somaxconn=65535
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 3
      start_period: 15s
    restart: unless-stopped
    networks:
      - mt5-optimized-network
    labels:
      - "mt5.service=redis"
      - "mt5.optimization=hash-enabled"

  # NATS - 高可用消息队列服务
  nats:
    image: nats:2.10-alpine
    container_name: mt5-nats
    ports:
      - "4222:4222"  # NATS客户端端口
      - "8222:8222"  # HTTP监控端口
      - "6222:6222"  # 集群端口
    volumes:
      - nats_optimized_data:/data
      - ./nats-simple.conf:/etc/nats/nats.conf:ro
    command:
      - "--config=/etc/nats/nats.conf"
    healthcheck:
      test: ["CMD", "sh", "-c", "echo 'PING' | nc localhost 4222 | grep -q PONG || exit 1"]
      interval: 15s
      timeout: 10s
      retries: 5
      start_period: 30s
    restart: unless-stopped
    networks:
      - mt5-optimized-network
    labels:
      - "mt5.service=nats"
      - "mt5.optimization=jetstream-enabled"

  # Prometheus - 优化的监控和指标收集
  prometheus:
    image: prom/prometheus:latest
    container_name: mt5-prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - ./monitoring/rules:/etc/prometheus/rules:ro
      - prometheus_optimized_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=7d'
      - '--web.enable-lifecycle'
      - '--web.enable-admin-api'
      - '--storage.tsdb.wal-compression'
    healthcheck:
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost:9090/-/healthy"]
      interval: 15s
      timeout: 10s
      retries: 3
      start_period: 20s
    restart: unless-stopped
    networks:
      - mt5-optimized-network
    labels:
      - "mt5.service=prometheus"
      - "mt5.optimization=wal-compression"

  # Pushgateway - 优化的指标推送网关
  pushgateway:
    image: prom/pushgateway:latest
    container_name: mt5-pushgateway
    ports:
      - "9091:9091"
    environment:
      - PUSHGATEWAY_OPTIMIZATION=enabled
    healthcheck:
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost:9091/-/healthy"]
      interval: 15s
      timeout: 10s
      retries: 3
      start_period: 15s
    restart: unless-stopped
    networks:
      - mt5-optimized-network
    labels:
      - "mt5.service=pushgateway"
      - "mt5.optimization=hash-metrics"

  # Grafana - 优化的数据可视化和仪表板
  grafana:
    image: grafana/grafana:latest
    container_name: mt5-grafana
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=
      - GF_USERS_ALLOW_SIGN_UP=false
      - GF_AUTH_ANONYMOUS_ENABLED=true
      - GF_AUTH_ANONYMOUS_ORG_ROLE=Admin

      - GF_FEATURE_TOGGLES_ENABLE=publicDashboards
      - GF_ANALYTICS_REPORTING_ENABLED=false
    volumes:
      - grafana_optimized_data:/var/lib/grafana
    depends_on:
      - prometheus
      - redis
    healthcheck:
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost:3000/api/health"]
      interval: 15s
      timeout: 10s
      retries: 3
      start_period: 30s
    restart: unless-stopped
    networks:
      - mt5-optimized-network
    labels:
      - "mt5.service=grafana"
      - "mt5.optimization=performance-enabled"

  # Redis Commander - 优化的Redis管理界面
  redis-commander:
    image: rediscommander/redis-commander:latest
    container_name: mt5-redis-commander
    ports:
      - "8081:8081"
    environment:
      - REDIS_HOSTS=mt5-redis:redis:6379
      - REDIS_COMMANDER_OPTIMIZATION=hash-support
    depends_on:
      - redis
    restart: unless-stopped
    networks:
      - mt5-optimized-network
    labels:
      - "mt5.service=redis-commander"
      - "mt5.optimization=hash-viewer"

  
  # MT5 API服务 - 统一管理接口
  mt5-api:
    build:
      context: ..
      dockerfile: docker/Dockerfile
    container_name: mt5-api-service
    ports:
      - "8000:8000"
    volumes:
      - ../config:/app/config:ro
      - ../logs:/app/logs
      - ../.env:/app/.env:ro
      - ../docker/deployment-config.yaml:/app/deployment-config.yaml:ro
    environment:
      - PYTHONPATH=/app
      - REDIS_HOST=redis
      - NATS_HOST=nats
      - PROMETHEUS_PUSHGATEWAY_URL=http://pushgateway:9091
      - MT5_CONFIG_PATH=/app/config/optimized_system.yaml
      - COPY_RELATIONSHIPS_CONFIG=/app/config/copy_relationships.yaml
      - HOST_ID=docker-api-gateway
      - MT5_NODE_TYPE=api
      - MT5_DEPLOYMENT_MODE=production
    depends_on:
      - redis
      - nats
      - mt5-master-acc001
      - mt5-slave-acc002
      - mt5-slave-acc003
    restart: unless-stopped
    networks:
      - mt5-optimized-network
    labels:
      - "mt5.service=api-gateway"
      - "mt5.api.type=unified-management"
    command: ["api"]

  # MT5分布式系统协调器 - 运行分布式控制平面
  mt5-coordinator:
    build:
      context: ..
      dockerfile: docker/Dockerfile
    container_name: mt5-coordinator
    volumes:
      - ../config:/app/config:ro
      - ../logs:/app/logs
      - ../.env:/app/.env:ro
      - ../docker/deployment-config.yaml:/app/deployment-config.yaml:ro
    environment:
      - PYTHONPATH=/app
      - REDIS_HOST=redis
      - NATS_HOST=nats
      - PROMETHEUS_PUSHGATEWAY_URL=http://pushgateway:9091
      - MT5_CONFIG_PATH=/app/config/optimized_system.yaml
      - COPY_RELATIONSHIPS_CONFIG=/app/config/copy_relationships.yaml
      - HOST_ID=docker-coordinator-node
      - MT5_NODE_TYPE=coordinator
      - MT5_CLUSTER_ROLE=control_plane
      - MT5_DEPLOYMENT_MODE=production
    depends_on:
      - redis
      - nats
      - pushgateway
    restart: unless-stopped
    networks:
      - mt5-optimized-network
    labels:
      - "mt5.service=distributed-coordinator"
      - "mt5.deployment=distributed"
      - "mt5.node.type=coordinator"
      - "mt5.cluster.role=control_plane"
    command: ["coordinator"]
    profiles:
      - distributed  # 用于分布式部署

  # Redis Hash Manager Service - 新增优化服务
  redis-hash-manager:
    build:
      context: ..
      dockerfile: docker/Dockerfile.hash-manager
    container_name: mt5-hash-manager
    ports:
      - "8082:8082"
    environment:
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - HASH_MANAGER_PORT=8082
    depends_on:
      - redis
    restart: unless-stopped
    networks:
      - mt5-optimized-network
    labels:
      - "mt5.service=hash-manager"
      - "mt5.optimization=api-service"

volumes:
  redis_optimized_data:
    driver: local
    labels:
      - "mt5.data=redis-optimized"
  nats_optimized_data:
    driver: local
    labels:
      - "mt5.data=nats-optimized"
  prometheus_optimized_data:
    driver: local
    labels:
      - "mt5.data=prometheus-optimized"
  grafana_optimized_data:
    driver: local
    labels:
      - "mt5.data=grafana-optimized"

networks:
  mt5-optimized-network:
    driver: bridge
    name: mt5-optimized-network
    labels:
      - "mt5.network=optimized"
