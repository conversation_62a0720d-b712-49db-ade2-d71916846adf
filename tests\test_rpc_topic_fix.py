#!/usr/bin/env python3
"""
RPC主题修复验证测试
专门验证RPC主题匹配问题是否解决
"""

import asyncio
import sys
import time
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# 导入组件
try:
    from src.core.mt5_rpc_client import MT5RPCClient
    from src.messaging.jetstream_client import JetStreamClient
    
    print("RPC主题修复验证组件导入成功")
    
except ImportError as e:
    print(f"导入组件失败: {e}")
    sys.exit(1)


async def test_rpc_topic_matching():
    """测试RPC主题匹配"""
    print("开始RPC主题匹配验证测试...")
    
    try:
        # 创建JetStream客户端
        config = {
            'servers': ['nats://localhost:4222'],
            'stream_name': 'RPC_TOPIC_TEST',
            'subjects': ['RPC.TOPIC.>']
        }
        
        client = JetStreamClient(config)
        connected = await client.connect()
        
        if not connected:
            print("  无法连接到NATS服务器")
            return False
        
        print("  JetStream连接成功")
        
        # 创建RPC客户端
        rpc_client = MT5RPCClient(client)
        print("  RPC客户端创建成功")
        
        # 创建简单的响应处理器
        response_received = False
        response_data = None
        
        async def simple_responder(msg):
            nonlocal response_received, response_data
            print(f"  收到请求: {msg.subject}")
            
            # 解析请求
            try:
                from src.messaging.message_codec import MessageCodec
                request = MessageCodec.decode(msg.data)
                print(f"  请求内容: {request}")
                
                # 发送简单响应
                response = {
                    "status": "success",
                    "data": {"message": "Topic matching works!"},
                    "timestamp": time.time()
                }
                
                response_bytes = MessageCodec.encode(response)
                await msg.respond(response_bytes)
                
                response_received = True
                response_data = response
                print("  响应已发送")
                
            except Exception as e:
                print(f"  响应处理错误: {e}")
        
        # 订阅MT5.REQUEST.*主题
        subscription = await client.nc.subscribe("MT5.REQUEST.*", cb=simple_responder)
        print("  订阅MT5.REQUEST.*主题成功")
        
        # 等待订阅生效
        await asyncio.sleep(1)
        
        # 测试RPC调用
        print("  发送RPC请求...")
        
        try:
            response = await rpc_client.call_rpc(
                account_id='TOPIC_TEST_001',
                method='test_topic_matching',
                params={'test': 'data'},
                timeout=5.0
            )
            
            print(f"  收到响应: {response}")
            
            # 检查响应
            if response and response.get('status') == 'success':
                print("  ✅ RPC主题匹配成功!")
                success = True
            else:
                print(f"  ❌ RPC响应异常: {response}")
                success = False
                
        except Exception as e:
            print(f"  ❌ RPC调用异常: {e}")
            success = False
        
        # 取消订阅
        await subscription.unsubscribe()
        
        # 断开连接
        await client.disconnect()
        
        # 检查RPC客户端指标
        metrics = rpc_client.get_metrics()
        print(f"\n  RPC客户端指标:")
        print(f"    总调用: {metrics['total_calls']}")
        print(f"    成功调用: {metrics['successful_calls']}")
        print(f"    失败调用: {metrics['failed_calls']}")
        print(f"    客户端成功率: {metrics['success_rate_percent']:.1f}%")
        
        return success and response_received
        
    except Exception as e:
        print(f"  RPC主题匹配测试失败: {e}")
        return False


async def test_old_vs_new_topic():
    """测试旧主题vs新主题"""
    print("\n对比旧主题vs新主题...")
    
    try:
        # 创建JetStream客户端
        config = {
            'servers': ['nats://localhost:4222'],
            'stream_name': 'TOPIC_COMPARE_TEST',
            'subjects': ['TOPIC.COMPARE.>']
        }
        
        client = JetStreamClient(config)
        connected = await client.connect()
        
        if not connected:
            print("  无法连接到NATS服务器")
            return False
        
        print("  JetStream连接成功")
        
        # 测试旧主题格式（应该失败）
        old_topic_received = False
        
        async def old_topic_responder(msg):
            nonlocal old_topic_received
            old_topic_received = True
            print(f"  ❌ 意外收到旧主题请求: {msg.subject}")
        
        # 订阅旧主题格式
        old_subscription = await client.nc.subscribe("mt5.*.rpc", cb=old_topic_responder)
        print("  订阅旧主题格式: mt5.*.rpc")
        
        # 测试新主题格式（应该成功）
        new_topic_received = False
        
        async def new_topic_responder(msg):
            nonlocal new_topic_received
            new_topic_received = True
            print(f"  ✅ 收到新主题请求: {msg.subject}")
            
            # 发送响应
            try:
                from src.messaging.message_codec import MessageCodec
                response = {
                    "status": "success",
                    "data": {"message": "New topic format works!"},
                    "timestamp": time.time()
                }
                response_bytes = MessageCodec.encode(response)
                await msg.respond(response_bytes)
            except Exception as e:
                print(f"  响应错误: {e}")
        
        # 订阅新主题格式
        new_subscription = await client.nc.subscribe("MT5.REQUEST.*", cb=new_topic_responder)
        print("  订阅新主题格式: MT5.REQUEST.*")
        
        # 等待订阅生效
        await asyncio.sleep(1)
        
        # 创建RPC客户端并测试
        rpc_client = MT5RPCClient(client)
        
        print("  发送RPC请求到新主题格式...")
        
        try:
            response = await rpc_client.call_rpc(
                account_id='COMPARE_TEST_001',
                method='test_new_topic',
                params={'test': 'new_format'},
                timeout=3.0
            )
            
            print(f"  新主题响应: {response}")
            
        except Exception as e:
            print(f"  新主题调用异常: {e}")
        
        # 等待一下看是否有意外的旧主题消息
        await asyncio.sleep(1)
        
        # 取消订阅
        await old_subscription.unsubscribe()
        await new_subscription.unsubscribe()
        
        # 断开连接
        await client.disconnect()
        
        print(f"\n  主题对比结果:")
        print(f"    旧主题(mt5.*.rpc)收到消息: {'是' if old_topic_received else '否'}")
        print(f"    新主题(MT5.REQUEST.*)收到消息: {'是' if new_topic_received else '否'}")
        
        # 成功条件：新主题收到消息，旧主题没有收到
        return new_topic_received and not old_topic_received
        
    except Exception as e:
        print(f"  主题对比测试失败: {e}")
        return False


async def main():
    """主函数"""
    print("RPC主题修复验证测试")
    print("=" * 50)
    
    # 测试1：RPC主题匹配
    test1_success = await test_rpc_topic_matching()
    
    # 测试2：旧主题vs新主题
    test2_success = await test_old_vs_new_topic()
    
    print(f"\n" + "=" * 50)
    print("RPC主题修复验证结果:")
    print(f"  主题匹配测试: {'✅ 通过' if test1_success else '❌ 失败'}")
    print(f"  主题格式对比: {'✅ 通过' if test2_success else '❌ 失败'}")
    
    overall_success = test1_success and test2_success
    
    if overall_success:
        print("\n🎉 RPC主题修复验证成功!")
        print("主题匹配问题已完全解决")
        print("RPC客户端现在使用正确的主题格式: MT5.REQUEST.{account_id}")
        return 0
    else:
        print("\n⚠️ RPC主题修复验证部分失败")
        if test1_success:
            print("主题匹配工作正常，但格式对比有问题")
        else:
            print("主题匹配仍有问题，需要进一步调试")
        return 1


if __name__ == '__main__':
    result = asyncio.run(main())
    sys.exit(result)
