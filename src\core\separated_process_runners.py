#!/usr/bin/env python3
"""
分离的进程启动函数 - 严格职责分离架构
用于启动独立的监控器和执行器进程
"""

import asyncio
import logging
import multiprocessing as mp
import sys
import time
from typing import Dict, Any, Optional, Union

# 导入分离的组件
from .mt5_account_monitor import MT5AccountMonitor
from .mt5_account_executor import MT5AccountExecutor
from ..messaging.jetstream_client import JetStreamClient
from ..utils.logger import get_logger

logger = get_logger(__name__)


# ==================== 跨进程管理器代理 ====================

class CrossProcessManagerProxy:
    """跨进程管理器代理 - 通过JetStream与主进程中的进程管理器通信"""

    def __init__(self, account_id: str, jetstream_client=None):
        self.account_id = account_id
        self.jetstream_client = jetstream_client
        self.logger = get_logger(__name__)

    def is_account_connected(self, account_id: str) -> bool:
        """检查账户是否连接 - 同步版本，用于兼容现有代码"""
        try:
            if not self.jetstream_client:
                self.logger.debug(f"JetStream客户端未可用，使用本地管理器: {account_id}")
                return True

            if hasattr(self.jetstream_client, 'is_connected'):
                connected = self.jetstream_client.is_connected()
                if connected:
                    self.logger.debug(f"通过JetStream代理检查连接状态: {account_id} - 已连接")
                    return True
                else:
                    self.logger.debug(f"JetStream客户端存在但未连接，假设账户通过其他方式连接: {account_id}")
                    return True
            else:
                self.logger.debug(f"非标准JetStream客户端，假设连接正常: {account_id}")
                return True

        except Exception as e:
            self.logger.error(f"检查账户连接状态失败: {account_id}, 错误: {e}")
            return True

    def get_account_status(self, account_id: str) -> dict:
        """获取账户状态 - 同步版本，用于兼容现有代码"""
        try:
            connected = self.is_account_connected(account_id)

            status = {
                'connected': connected,
                'status': 'running' if connected else 'disconnected',
                'proxy_type': 'jetstream',
                'account_id': account_id
            }

            self.logger.debug(f"获取账户状态: {account_id} -> {status}")
            return status

        except Exception as e:
            self.logger.error(f"获取账户状态失败: {account_id}, 错误: {e}")
            return {'connected': False, 'status': 'error', 'error': str(e)}


# ==================== 错误处理增强 ====================

class MT5ErrorHandler:
    """MT5 API错误处理器"""
    
    # MT5错误码映射
    MT5_ERROR_CODES = {
        0: "成功",
        1: "没有错误，但结果未知",
        2: "一般错误",
        3: "参数错误",
        4: "无效的交易请求结构",
        5: "旧版本的客户端终端",
        6: "没有权限",
        7: "市场关闭",
        8: "没有价格",
        9: "订单锁定中",
        10: "买入请求错误",
    }
    
    RETRYABLE_ERRORS = {2, 8, 9, 64, 65, 128, 129, 130, 134, 135, 136, 137, 138}
    NETWORK_ERRORS = {64, 65, 128, 129, 130, 134, 135, 136, 137, 138}
    
    @staticmethod
    def is_retryable_error(retcode: int) -> bool:
        """判断错误是否可重试"""
        return retcode in MT5ErrorHandler.RETRYABLE_ERRORS
    
    @staticmethod
    def is_network_error(retcode: int) -> bool:
        """判断是否为网络错误"""
        return retcode in MT5ErrorHandler.NETWORK_ERRORS
    
    @staticmethod
    def get_error_message(retcode: int) -> str:
        """获取错误信息"""
        return MT5ErrorHandler.MT5_ERROR_CODES.get(retcode, f"未知错误码: {retcode}")
    
    @staticmethod
    def should_increase_delay(retcode: int) -> bool:
        """判断是否应该增加重试延迟"""
        return retcode in MT5ErrorHandler.NETWORK_ERRORS


async def retry_with_backoff(func, max_retries: int = 3, initial_delay: float = 1.0, 
                           max_delay: float = 30.0, backoff_factor: float = 2.0, 
                           account_id: str = "unknown", fallback_func=None):
    """
    指数退避重试装饰器，支持fallback策略避免无限重试
    """
    last_exception = None
    delay = initial_delay
    
    for attempt in range(max_retries + 1):
        try:
            result = await func()
            if attempt > 0:
                logger.info(f"重试成功 [{account_id}] 尝试次数: {attempt + 1}")
            return result
            
        except Exception as e:
            last_exception = e
            
            if attempt == max_retries:
                logger.error(f"重试失败 [{account_id}] 已达最大重试次数: {max_retries + 1}")
                if fallback_func is not None:
                    try:
                        logger.warning(f"启用fallback策略 [{account_id}]: 尝试使用备用方案")
                        fallback_result = await fallback_func() if asyncio.iscoroutinefunction(fallback_func) else fallback_func()
                        logger.info(f"fallback成功 [{account_id}]: 使用备用方案完成操作")
                        return fallback_result
                    except Exception as fallback_error:
                        logger.error(f"fallback失败 [{account_id}]: {fallback_error}")
                break
            
            if hasattr(e, 'retcode'):
                if not MT5ErrorHandler.is_retryable_error(e.retcode):
                    logger.warning(f"不可重试的错误 [{account_id}]: {e}")
                    if fallback_func is not None:
                        try:
                            logger.warning(f"不可重试错误，启用fallback策略 [{account_id}]")
                            fallback_result = await fallback_func() if asyncio.iscoroutinefunction(fallback_func) else fallback_func()
                            logger.info(f"fallback成功 [{account_id}]: 使用备用方案处理不可重试错误")
                            return fallback_result
                        except Exception as fallback_error:
                            logger.error(f"fallback失败 [{account_id}]: {fallback_error}")
                    break
                    
                if MT5ErrorHandler.should_increase_delay(e.retcode):
                    delay = min(delay * backoff_factor, max_delay)
            
            logger.warning(f"重试 [{account_id}] 尝试 {attempt + 1}/{max_retries + 1}, 等待 {delay:.1f}s: {e}")
            await asyncio.sleep(delay)
            
            delay = min(delay * backoff_factor, max_delay)
    
    raise last_exception


class MT5APIException(Exception):
    """MT5 API异常"""
    def __init__(self, message: str, retcode: Optional[int] = None, account_id: str = "unknown"):
        super().__init__(message)
        self.retcode = retcode
        self.account_id = account_id
        self.timestamp = time.time()


def validate_trade_params(symbol: str, volume: float, order_type: Union[str, int]) -> None:
    """验证交易参数"""
    if not symbol or not isinstance(symbol, str):
        raise MT5APIException("交易品种不能为空")
    
    if volume <= 0:
        raise MT5APIException(f"交易手数必须大于0: {volume}")
    
    if volume > 100:  # 安全限制
        raise MT5APIException(f"交易手数过大，超过安全限制: {volume}")
    
    valid_order_types = ['BUY', 'SELL', 0, 1]
    if order_type not in valid_order_types:
        raise MT5APIException(f"无效的订单类型: {order_type}")


def handle_mt5_response(response, operation: str, account_id: str = "unknown"):
    """统一处理MT5响应"""
    if not response:
        raise MT5APIException(f"{operation}失败: 无响应", account_id=account_id)
    
    if response.status != 'success':
        error_msg = response.error_message or f"{operation}失败"
        raise MT5APIException(error_msg, account_id=account_id)
    
    if not response.data:
        raise MT5APIException(f"{operation}失败: 无数据返回", account_id=account_id)
    
    return response.data


# ==================== 性能优化组件 ====================

class APICallThrottler:
    """API调用频率控制器"""
    
    def __init__(self, max_calls_per_second: float = 10.0):
        self.max_calls_per_second = max_calls_per_second
        self.min_interval = 1.0 / max_calls_per_second
        self.last_call_times = {}
        self.call_counts = {}
        self.lock = asyncio.Lock()
    
    async def throttle(self, account_id: str, operation: str = "default"):
        """限流控制"""
        async with self.lock:
            key = f"{account_id}:{operation}"
            current_time = time.time()
            
            last_call = self.last_call_times.get(key, 0)
            time_since_last = current_time - last_call
            
            if time_since_last < self.min_interval:
                wait_time = self.min_interval - time_since_last
                logger.debug(f"API限流等待 [{account_id}:{operation}]: {wait_time:.3f}s")
                await asyncio.sleep(wait_time)
            
            self.last_call_times[key] = time.time()
            self.call_counts[key] = self.call_counts.get(key, 0) + 1
    
    def get_stats(self, account_id: str) -> Dict[str, Any]:
        """获取限流统计"""
        stats = {}
        for key, count in self.call_counts.items():
            if key.startswith(f"{account_id}:"):
                operation = key.split(":", 1)[1]
                stats[operation] = count
        return stats


class DataCache:
    """数据缓存器 """
    
    def __init__(self, default_ttl: float = 1.0):
        self.default_ttl = default_ttl
        self.cache = {}
        self.timestamps = {}
        self.lock = asyncio.Lock()
    
    async def get(self, key: str, ttl: Optional[float] = None) -> Optional[Any]:
        """获取缓存数据"""
        async with self.lock:
            if key not in self.cache:
                return None
            
            timestamp = self.timestamps.get(key, 0)
            ttl = ttl or self.default_ttl
            
            if time.time() - timestamp > ttl:
                del self.cache[key]
                del self.timestamps[key]
                return None
            
            return self.cache[key]
    
    async def set(self, key: str, value: Any) -> None:
        """设置缓存数据"""
        async with self.lock:
            self.cache[key] = value
            self.timestamps[key] = time.time()
    
    async def clear_expired(self) -> None:
        """清理过期缓存"""
        async with self.lock:
            current_time = time.time()
            expired_keys = []
            
            for key, timestamp in self.timestamps.items():
                if current_time - timestamp > self.default_ttl:  # TTL过期后立即清理
                    expired_keys.append(key)
            
            for key in expired_keys:
                self.cache.pop(key, None)
                self.timestamps.pop(key, None)
    
    def get_stats(self) -> Dict[str, Any]:
        """获取缓存统计"""
        return {
            'cache_size': len(self.cache),
            'hit_ratio': getattr(self, '_hit_count', 0) / max(getattr(self, '_access_count', 1), 1)
        }


_throttler = APICallThrottler(max_calls_per_second=8.0)  # 保守的限流设置
_data_cache = DataCache(default_ttl=0.3)  # 300ms缓存


async def optimized_api_call(account_id: str, operation: str, api_func, 
                           cache_key: Optional[str] = None, 
                           cache_ttl: Optional[float] = None):
    """ API调用包装器"""
    if cache_key:
        cached_result = await _data_cache.get(cache_key, cache_ttl)
        if cached_result is not None:
            logger.debug(f"缓存命中 [{account_id}:{operation}]: {cache_key}")
            return cached_result
    
    await _throttler.throttle(account_id, operation)

    start_time = time.time()
    try:
        result = await api_func()
        
        if cache_key and result is not None:
            await _data_cache.set(cache_key, result)
        
        duration = time.time() - start_time
        if duration > 1.0:  # 超过1秒的调用
            logger.warning(f"慢API调用 [{account_id}:{operation}]: {duration:.3f}s")
        
        return result
        
    except Exception as e:
        duration = time.time() - start_time
        logger.error(f"API调用失败 [{account_id}:{operation}] ({duration:.3f}s): {e}")
        raise


class PerformanceMonitor:
    """性能监控器"""
    
    def __init__(self):
        self.call_times = {}
        self.error_counts = {}
        self.lock = asyncio.Lock()
    
    async def record_call(self, account_id: str, operation: str, duration: float, success: bool):
        """记录API调用性能"""
        async with self.lock:
            key = f"{account_id}:{operation}"
            
            if key not in self.call_times:
                self.call_times[key] = []
            
            self.call_times[key].append(duration)
            
            if len(self.call_times[key]) > 100:
                self.call_times[key] = self.call_times[key][-100:]
            
            if not success:
                self.error_counts[key] = self.error_counts.get(key, 0) + 1
    
    def get_performance_stats(self, account_id: str) -> Dict[str, Any]:
        """获取性能统计"""
        stats = {}
        
        for key, times in self.call_times.items():
            if key.startswith(f"{account_id}:"):
                operation = key.split(":", 1)[1]
                if times:
                    stats[operation] = {
                        'avg_duration': sum(times) / len(times),
                        'max_duration': max(times),
                        'min_duration': min(times),
                        'call_count': len(times),
                        'error_count': self.error_counts.get(key, 0)
                    }
        return stats

    def get_stats(self) -> Dict[str, Any]:
        """获取所有统计数据"""
        all_stats = {}
        for key, times in self.call_times.items():
            if times:
                all_stats[key] = {
                    'avg_duration': sum(times) / len(times),
                    'max_duration': max(times),
                    'min_duration': min(times),
                    'call_count': len(times),
                    'error_count': self.error_counts.get(key, 0)
                }
        return all_stats

    def record_call(self, operation: str, duration: float, success: bool = True):
        """同步记录调用"""
        key = f"system:{operation}"

        if key not in self.call_times:
            self.call_times[key] = []

        self.call_times[key].append(duration)

        if len(self.call_times[key]) > 100:
            self.call_times[key] = self.call_times[key][-100:]
        if not success:
            self.error_counts[key] = self.error_counts.get(key, 0) + 1


_performance_monitor = PerformanceMonitor()


def run_account_monitor_process(account_id: str, config: Dict[str, Any], message_queue: mp.Queue):
    """
    账户监控器进程入口函数 - 严格职责分离
    """
    try:
        try:
            import setproctitle
            setproctitle.setproctitle(f"mt5_monitor_{account_id}")
        except ImportError:
            pass

        logging.basicConfig(
            level=logging.INFO,
            format=f'%(asctime)s - MONITOR-{account_id} - %(levelname)s - %(message)s'
        )
        logger.info(f"启动账户监控器进程: {account_id}")

        asyncio.run(_run_monitor_async(account_id, config, message_queue))

    except Exception as e:
        logger.error(f"监控器进程运行异常 {account_id}: {e}")
        sys.exit(1)


def run_account_executor_process(account_id: str, config: Dict[str, Any], message_queue: mp.Queue):
    """
    账户执行器进程入口函数 - 严格职责分离
    """
    try:
        try:
            import setproctitle
            setproctitle.setproctitle(f"mt5_executor_{account_id}")
        except ImportError:
            pass

        logging.basicConfig(
            level=logging.INFO,
            format=f'%(asctime)s - EXECUTOR-{account_id} - %(levelname)s - %(message)s'
        )
        logger.info(f"启动账户执行器进程: {account_id}")

        asyncio.run(_run_executor_async(account_id, config, message_queue))

    except Exception as e:
        logger.error(f"执行器进程运行异常 {account_id}: {e}")
        sys.exit(1)


async def _run_monitor_async(account_id: str, config: Dict[str, Any], message_queue: mp.Queue):
    """
    异步运行监控器
    """
    monitor = None
    event_publisher = None
    jetstream_client_for_mt5 = None
    
    try:
        nats_config = config.get('nats', {})
        logger.info(f"监控器收到的NATS配置: {nats_config}")
        
        has_valid_nats = (nats_config and 
                         nats_config.get('servers') and 
                         len(nats_config['servers']) > 0)
        
        if has_valid_nats:
            try:
                event_publisher = JetStreamClient(nats_config)
                await event_publisher.connect()
                logger.info(f"监控器事件发布器连接成功: {account_id}")
            except Exception as e:
                logger.error(f"NATS连接失败，监控器无法启动: {account_id} - {e}")
                raise RuntimeError(f"监控器需要NATS连接才能工作: {e}")
        else:
            logger.error(f"未配置NATS，监控器无法启动: {account_id}")
            raise RuntimeError("监控器需要NATS配置才能工作")
        
        if has_valid_nats:
            try:
                jetstream_client_for_mt5 = JetStreamClient(nats_config)
                await jetstream_client_for_mt5.connect()
                logger.info(f"监控器MT5客户端JetStream连接成功: {account_id}")
            except Exception as e:
                logger.error(f"监控器MT5客户端NATS连接失败: {account_id} - {e}")
                jetstream_client_for_mt5 = None

        mt5_client = await _create_mt5_data_provider(account_id, config, jetstream_client_for_mt5)

        rpc_client = None
        if jetstream_client_for_mt5:
            try:
                from .mt5_rpc_client import MT5RPCClient
                rpc_client = MT5RPCClient(jetstream_client_for_mt5)
                logger.info(f"监控器RPC客户端创建成功: {account_id}")
            except Exception as e:
                logger.warning(f"监控器RPC客户端创建失败: {account_id} - {e}")

        monitor = MT5AccountMonitor(
            account_id=account_id,
            account_config=config,
            mt5_client=mt5_client,
            event_publisher=event_publisher,
            rpc_client=rpc_client
        )
        
        if await monitor.start_monitoring():
            logger.info(f"监控器成功启动，开始监控: {account_id}")
            
            await _handle_monitor_messages(monitor, message_queue)
        else:
            logger.error(f"监控器启动失败: {account_id}")
            
    except Exception as e:
        logger.error(f"监控器异步运行异常: {e}")
        raise
    finally:
        # 清理资源
        if monitor:
            await monitor.stop_monitoring()
        if event_publisher and hasattr(event_publisher, 'close'):
            await event_publisher.close()
        if jetstream_client_for_mt5 and hasattr(jetstream_client_for_mt5, 'close'):
            await jetstream_client_for_mt5.close()


async def _run_executor_async(account_id: str, config: Dict[str, Any], message_queue: mp.Queue):
    """
    异步运行执行器
    """
    executor = None
    command_subscriber = None
    result_publisher = None
    
    try:
        # 1. 初始化JetStream通信
        nats_config = config.get('nats', {})
        logger.info(f"执行器收到的NATS配置: {nats_config}")
        
        has_valid_nats = (nats_config and 
                         nats_config.get('servers') and 
                         len(nats_config['servers']) > 0)
        
        if has_valid_nats:
            try:
                command_subscriber = JetStreamClient(nats_config)
                result_publisher = JetStreamClient(nats_config)

                await command_subscriber.connect()
                await result_publisher.connect()
                logger.info(f"执行器通信连接成功: {account_id}")
            except Exception as e:
                logger.error(f"NATS连接失败，执行器无法启动: {account_id} - {e}")
                raise RuntimeError(f"执行器需要NATS连接才能工作: {e}")
        else:
            logger.error(f"未配置NATS，执行器无法启动: {account_id}")
            raise RuntimeError("执行器需要NATS配置才能工作")
        
        jetstream_client_for_mt5 = command_subscriber if (has_valid_nats and hasattr(command_subscriber, 'connect')) else None
        mt5_client = await _create_mt5_trade_executor(account_id, config, jetstream_client_for_mt5)

        # 创建RPC客户端  
        rpc_client = None
        if jetstream_client_for_mt5:
            try:
                from .mt5_rpc_client import MT5RPCClient
                rpc_client = MT5RPCClient(jetstream_client_for_mt5)
                logger.info(f"执行器RPC客户端创建成功: {account_id}")
            except Exception as e:
                logger.warning(f"执行器RPC客户端创建失败: {account_id} - {e}")

        executor = MT5AccountExecutor(
            account_id=account_id,
            account_config=config,
            mt5_client=mt5_client,
            command_subscriber=command_subscriber,
            result_publisher=result_publisher,
            rpc_client=rpc_client
        )
        
        if await executor.start_executor():
            logger.info(f"执行器成功启动，准备接收命令: {account_id}")
            
            await _handle_executor_messages(executor, message_queue)
        else:
            logger.error(f"执行器启动失败: {account_id}")
            
    except Exception as e:
        logger.error(f"执行器异步运行异常: {e}")
        raise
    finally:
        if executor:
            await executor.stop_executor()
        if command_subscriber and hasattr(command_subscriber, 'close'):
            await command_subscriber.close()
        if result_publisher and hasattr(result_publisher, 'close'):
            await result_publisher.close()


async def _create_mt5_data_provider(account_id: str, config: Dict[str, Any], jetstream_client=None):
    """
    创建MT5数据提供器 
    使用NATS消息系统与主进程的MT5终端通信
    """
    try:
        class MT5JetStreamProxyClient:
            """MT5 JetStream代理客户端 - 通过JetStream与主进程中的MT5终端通信"""

            def __init__(self, account_id: str, config: Dict[str, Any], jetstream_client=None):
                self.account_id = account_id
                self.config = config
                self.jetstream_client = jetstream_client

                self.process_manager = CrossProcessManagerProxy(account_id, jetstream_client)

                if not self.jetstream_client:
                    try:
                        from .mt5_process_manager import MT5ProcessManager
                        self.fallback_manager = MT5ProcessManager()
                        self.fallback_manager.start_manager()

                        self.fallback_manager.add_account(account_id, config)
                        logger.info(f"使用本地进程管理器作为备用: {account_id}")
                    except Exception as e:
                        logger.warning(f"创建备用管理器失败: {e}")
                        self.fallback_manager = None
                else:
                    self.fallback_manager = None

                logger.info(f"MT5 JetStream代理客户端初始化: {account_id}")
            
            async def get_positions(self):
                """获取持仓信息"""
                try:
                    logger.info(f"开始获取持仓: {self.account_id}")
                    
                    if self.jetstream_client and hasattr(self.jetstream_client, 'request'):
                        try:
                            request_data = {
                                'account_id': self.account_id,
                                'command': 'get_positions',
                                'timestamp': time.time()
                            }

                            logger.info(f"发送JetStream请求: {self.account_id}")
                            logger.info(f"请求数据: {request_data}")
                            logger.info(f"目标主题: MT5.REQUEST.{self.account_id}")
                            logger.info(f"JetStream客户端状态: {self.jetstream_client.is_connected() if hasattr(self.jetstream_client, 'is_connected') else 'unknown'}")

                            start_time = time.time()

                            logger.info(f"准备发送JetStream请求...")
                            logger.info(f" - 客户端连接状态: {self.jetstream_client.is_connected() if hasattr(self.jetstream_client, 'is_connected') else 'unknown'}")
                            logger.info(f" - 底层NATS连接: {self.jetstream_client.nc.is_connected if self.jetstream_client.nc else 'None'}")

                            try:
                                response = await asyncio.wait_for(
                                    self.jetstream_client.request(
                                        f"MT5.REQUEST.{self.account_id}",
                                        request_data,
                                        timeout=8.0
                                    ),
                                    timeout=12.0  # 外层超时保护
                                )
                                request_time = time.time() - start_time

                                logger.info(f"收到JetStream响应: {self.account_id} -> 状态: {response.get('status') if response else 'None'}, 耗时: {request_time:.2f}s")

                                if response is None:
                                    logger.error(f"JetStream请求返回None - 这是问题所在！")
                                    logger.error(f" - 请求主题: MT5.REQUEST.{self.account_id}")
                                    logger.error(f" - 请求数据: {request_data}")
                                    logger.error(f" - 客户端状态: {self.jetstream_client.is_connected() if hasattr(self.jetstream_client, 'is_connected') else 'unknown'}")

                            except asyncio.TimeoutError as e:
                                request_time = time.time() - start_time
                                logger.error(f"JetStream请求超时: {self.account_id}, 耗时: {request_time:.2f}s")
                                logger.error(f" - 超时异常: {e}")
                                response = None
                            except Exception as e:
                                request_time = time.time() - start_time
                                logger.error(f"JetStream请求异常: {self.account_id}, 耗时: {request_time:.2f}s")
                                logger.error(f" - 异常类型: {type(e).__name__}")
                                logger.error(f" - 异常信息: {e}")
                                import traceback
                                logger.error(f" - 异常堆栈: {traceback.format_exc()}")
                                response = None

                            if response and response.get('status') == 'success':
                                positions_data = response.get('data', {})

                                if isinstance(positions_data, dict) and 'positions' in positions_data:
                                    result = positions_data['positions']
                                elif isinstance(positions_data, list):
                                    result = positions_data
                                else:
                                    result = []
                                
                                logger.info(f"JetStream获取持仓成功: {self.account_id}, 数量: {len(result)}")
                                return result
                            else:
                                error_msg = response.get('error', '未知错误') if response else '无响应'
                                logger.warning(f"JetStream请求失败: {self.account_id}, 错误: {error_msg}")
                                
                        except asyncio.TimeoutError:
                            logger.warning(f"JetStream请求超时: {self.account_id} (12秒超时)")
                        except Exception as e:
                            logger.warning(f"JetStream通信失败: {self.account_id}, 错误: {e}")
                            import traceback
                            logger.debug(f"详细错误信息: {traceback.format_exc()}")

                    if self.fallback_manager:
                        try:
                            logger.info(f"尝试备用方案: {self.account_id}")
                            start_time = time.time()
                            response = await asyncio.wait_for(
                                self.fallback_manager.send_command_async(
                                    self.account_id, 'get_positions'
                                ),
                                timeout=8.0
                            )
                            fallback_time = time.time() - start_time

                            if response.status == 'success':
                                positions_data = response.data
                                if positions_data and 'positions' in positions_data:
                                    result = positions_data['positions']
                                elif isinstance(positions_data, list):
                                    result = positions_data
                                else:
                                    result = []
                                
                                logger.info(f"备用方案获取持仓成功: {self.account_id}, 数量: {len(result)}, 耗时: {fallback_time:.2f}s")
                                return result
                            else:
                                logger.warning(f"备用方案获取持仓失败: {self.account_id}, 错误: {response.error_message}")
                                
                        except asyncio.TimeoutError:
                            logger.warning(f"备用方案超时: {self.account_id} (8秒超时)")
                        except Exception as e:
                            logger.warning(f"备用方案异常: {self.account_id}, 错误: {e}")
                    else:
                        logger.warning(f"没有可用的备用方案: {self.account_id}")

                    logger.warning(f"所有方案都失败，返回空持仓: {self.account_id}")
                    return []

                except Exception as e:
                    logger.error(f"获取持仓信息异常: {self.account_id}, 错误: {e}")
                    return []
            
            def is_connected(self):
                """检查连接状态"""
                if self.fallback_manager:
                    return self.fallback_manager.is_account_connected(self.account_id)
                else:
                    return self.process_manager.is_account_connected(self.account_id)
            
            async def get_account_info(self):
                """获取账户信息"""
                try:
                    if self.jetstream_client:
                        try:
                            request_data = {
                                'account_id': self.account_id,
                                'command': 'get_account_info',
                                'timestamp': time.time()
                            }
                            
                            response = await self.jetstream_client.request(
                                f"MT5.REQUEST.{self.account_id}",
                                request_data,
                                timeout=5.0
                            )
                            
                            if response and response.get('status') == 'success':
                                return response.get('data')
                        except Exception as e:
                            logger.warning(f"NATS获取账户信息失败: {e}")
                    
                    if self.fallback_manager:
                        try:
                            response = await self.fallback_manager.send_command_async(
                                self.account_id, 'get_account_info'
                            )
                            
                            if response.status == 'success':
                                return response.data
                        except Exception as e:
                            logger.error(f"备用方案获取账户信息失败: {e}")
                    
                    return None
                        
                except Exception as e:
                    logger.error(f"获取账户信息失败: {e}")
                    return None
        
        nats_config = config.get('nats', {})
        nats_client = None
        
        # 在监控器进程中，我们可能已经有了事件发布器
        # 这里简化处理，直接创建代理客户端
        proxy_client = MT5JetStreamProxyClient(account_id, config, jetstream_client)
        
        logger.info(f"MT5数据提供器创建完成: {account_id}")
        return proxy_client
        
    except Exception as e:
        logger.error(f"创建MT5数据提供器失败: {e}")
        raise


async def _create_mt5_trade_executor(account_id: str, config: Dict[str, Any], jetstream_client=None):
    """
    创建MT5交易执行器 - 修复进程间通信问题
    使用NATS消息系统与主进程的MT5终端通信
    """
    try:
        # 创建基于JetStream的代理执行器
        class MT5JetStreamProxyExecutor:
            """MT5 JetStream代理执行器 - 通过JetStream与主进程中的MT5终端通信"""
            
            def __init__(self, account_id: str, config: Dict[str, Any], jetstream_client=None):
                self.account_id = account_id
                self.config = config
                self.jetstream_client = jetstream_client

                # 创建跨进程的进程管理器代理
                self.process_manager = CrossProcessManagerProxy(account_id, jetstream_client)

                # 如果没有JetStream客户端，创建本地进程管理器作为备用
                if not self.jetstream_client:
                    try:
                        from .mt5_process_manager import MT5ProcessManager
                        self.fallback_manager = MT5ProcessManager()
                        self.fallback_manager.start_manager()
                        # 尝试添加账户到本地管理器
                        self.fallback_manager.add_account(account_id, config)
                        logger.info(f"执行器使用本地进程管理器作为备用: {account_id}")
                    except Exception as e:
                        logger.warning(f"创建执行器备用管理器失败: {e}")
                        self.fallback_manager = None
                else:
                    self.fallback_manager = None

                logger.info(f"MT5 JetStream代理执行器初始化: {account_id}")
            
            async def send_order(self, trade_request):
                """发送交易订单 - 优先使用JetStream，失败时使用本地管理器"""
                try:
                    # 方案1: 通过JetStream向主进程发送交易请求
                    if self.jetstream_client:
                        try:
                            request_data = {
                                'account_id': self.account_id,
                                'command': 'send_order',
                                'request': {
                                    'action': trade_request.action.value if hasattr(trade_request.action, 'value') else trade_request.action,
                                    'symbol': trade_request.symbol,
                                    'volume': trade_request.volume,
                                    'type': trade_request.type.value if hasattr(trade_request.type, 'value') else trade_request.type,
                                    'price': trade_request.price,
                                    'sl': trade_request.sl,
                                    'tp': trade_request.tp,
                                    'deviation': getattr(trade_request, 'deviation', 20),
                                    'magic': getattr(trade_request, 'magic', 12345),
                                    'comment': getattr(trade_request, 'comment', f'Copy_{self.account_id}')
                                },
                                'timestamp': time.time()
                            }
                            
                            # 发送请求到主进程
                            response = await self.jetstream_client.request(
                                f"MT5.REQUEST.{self.account_id}",
                                request_data,
                                timeout=10.0
                            )
                            
                            if response and response.get('status') == 'success':
                                # 构造返回结果
                                result_data = response.get('data', {})
                                from .mt5_client import TradeResult
                                return TradeResult(
                                    retcode=result_data.get('retcode', 10020),
                                    deal=result_data.get('deal', 0),
                                    order=result_data.get('order', 0),
                                    volume=result_data.get('volume', 0.0),
                                    price=result_data.get('price', 0.0),
                                    comment=result_data.get('comment', '')
                                )
                            else:
                                logger.warning(f"JetStream交易请求失败，尝试备用方案: {self.account_id}")
                        except Exception as e:
                            logger.warning(f"JetStream交易请求失败，尝试备用方案: {e}")
                    
                    # 方案2: 使用本地进程管理器（备用方案）
                    if self.fallback_manager:
                        try:
                            request_dict = {
                                'action': trade_request.action.value if hasattr(trade_request.action, 'value') else trade_request.action,
                                'symbol': trade_request.symbol,
                                'volume': trade_request.volume,
                                'type': trade_request.type.value if hasattr(trade_request.type, 'value') else trade_request.type,
                                'price': trade_request.price,
                                'sl': trade_request.sl,
                                'tp': trade_request.tp,
                                'deviation': getattr(trade_request, 'deviation', 20),
                                'magic': getattr(trade_request, 'magic', 12345),
                                'comment': getattr(trade_request, 'comment', f'Copy_{self.account_id}')
                            }
                            
                            response = await self.fallback_manager.send_command_async(
                                self.account_id, 'send_order', {'request': request_dict}
                            )
                            
                            if response.status == 'success':
                                data = response.data or {}
                                from .mt5_client import TradeResult
                                return TradeResult(
                                    retcode=data.get('retcode', 10020),
                                    deal=data.get('deal', 0),
                                    order=data.get('order', 0),
                                    volume=data.get('volume', 0.0),
                                    price=data.get('price', 0.0),
                                    comment=data.get('comment', '')
                                )
                            else:
                                logger.error(f"备用方案交易失败: {response.error_message}")
                        except Exception as e:
                            logger.error(f"备用方案交易失败: {e}")
                    
                    # 方案3: 返回失败结果 
                    from .mt5_client import TradeResult
                    return TradeResult(
                        retcode=10020,  # TRADE_RETCODE_ERROR
                        comment="所有交易方案都失败"
                    )
                        
                except Exception as e:
                    logger.error(f"发送交易订单失败: {e}")
                    from .mt5_client import TradeResult
                    return TradeResult(retcode=10020, comment=f"交易异常: {str(e)}")
            
            async def close_position(self, position_id: int, volume: Optional[float] = None):
                """平仓操作"""
                try:
                    # 方案1: 通过JetStream
                    if self.jetstream_client:
                        try:
                            request_data = {
                                'account_id': self.account_id,
                                'command': 'close_position',
                                'position_id': position_id,
                                'volume': volume,
                                'timestamp': time.time()
                            }
                            
                            response = await self.jetstream_client.request(
                                f"MT5.REQUEST.{self.account_id}",
                                request_data,
                                timeout=10.0
                            )
                            
                            if response and response.get('status') == 'success':
                                result_data = response.get('data', {})
                                from .mt5_client import TradeResult
                                return TradeResult(
                                    retcode=result_data.get('retcode', 10020),
                                    deal=result_data.get('deal', 0),
                                    volume=result_data.get('volume', 0.0),
                                    comment=result_data.get('comment', '')
                                )
                        except Exception as e:
                            logger.warning(f"NATS平仓请求失败: {e}")
                    
                    # 方案2: 使用本地管理器
                    if self.fallback_manager:
                        try:
                            params = {'position_id': position_id}
                            if volume is not None:
                                params['volume'] = volume
                                
                            response = await self.fallback_manager.send_command_async(
                                self.account_id, 'close_position', params
                            )
                            
                            if response.status == 'success':
                                data = response.data or {}
                                from .mt5_client import TradeResult
                                return TradeResult(
                                    retcode=data.get('retcode', 10020),
                                    deal=data.get('deal', 0),
                                    volume=data.get('volume', 0.0),
                                    comment=data.get('comment', '')
                                )
                        except Exception as e:
                            logger.error(f"备用方案平仓失败: {e}")
                    
                    # 返回失败结果
                    from .mt5_client import TradeResult
                    return TradeResult(retcode=10020, comment="平仓失败")
                        
                except Exception as e:
                    logger.error(f"平仓操作失败: {e}")
                    from .mt5_client import TradeResult
                    return TradeResult(retcode=10020, comment=f"平仓异常: {str(e)}")
            
            async def get_account_info(self):
                """获取账户信息"""
                try:
                    # 方案1: 通过JetStream
                    if self.jetstream_client:
                        try:
                            request_data = {
                                'account_id': self.account_id,
                                'command': 'get_account_info',
                                'timestamp': time.time()
                            }
                            
                            response = await self.jetstream_client.request(
                                f"MT5.REQUEST.{self.account_id}",
                                request_data,
                                timeout=5.0
                            )
                            
                            if response and response.get('status') == 'success':
                                return response.get('data')
                        except Exception as e:
                            logger.warning(f"NATS获取账户信息失败: {e}")
                    
                    # 方案2: 使用本地管理器
                    if self.fallback_manager:
                        try:
                            response = await self.fallback_manager.send_command_async(
                                self.account_id, 'get_account_info'
                            )
                            
                            if response.status == 'success':
                                return response.data
                        except Exception as e:
                            logger.error(f"备用方案获取账户信息失败: {e}")
                    
                    return None
                        
                except Exception as e:
                    logger.error(f"获取账户信息失败: {e}")
                    return None
            
            async def connect(self):
                """连接MT5"""
                try:
                    # JetStream模式下，连接已经在上层处理
                    if self.jetstream_client:
                        logger.info(f"JetStream代理执行器模式，无需单独连接: {self.account_id}")
                        return True
                    
                    # 备用管理器模式下，尝试连接
                    if self.fallback_manager:
                        # 备用管理器在初始化时已经连接
                        connected = self.fallback_manager.is_account_connected(self.account_id)
                        if connected:
                            logger.info(f"备用管理器已连接: {self.account_id}")
                            return True
                        else:
                            logger.warning(f"备用管理器连接失败: {self.account_id}")
                            return False
                    
                    # 默认返回True（假设连接成功）
                    return True
                        
                except Exception as e:
                    logger.error(f"连接MT5代理执行器失败: {e}")
                    return False
            
            def is_connected(self):
                """检查连接状态"""
                if self.fallback_manager:
                    return self.fallback_manager.is_account_connected(self.account_id)
                else:
                    # 如果没有备用管理器，假设连接正常（通过JetStream通信）
                    return True
            
            async def verify_connection(self):
                """验证连接状态"""
                try:
                    account_info = await self.get_account_info()
                    if account_info:
                        logger.info(f"连接验证成功: {self.account_id}")
                        return True
                    else:
                        logger.warning(f"连接验证失败: {self.account_id}")
                        return False
                except Exception as e:
                    logger.error(f"连接验证异常: {e}")
                    return False
            
            async def disconnect(self):
                """断开连接"""
                try:
                    if self.fallback_manager:
                        # 停止备用管理器
                        try:
                            self.fallback_manager.remove_account(self.account_id)
                            self.fallback_manager.stop_manager()
                            logger.info(f"断开备用管理器连接: {self.account_id}")
                        except Exception as e:
                            logger.warning(f"断开备用管理器连接失败: {e}")
                    # JetStream客户端的断开由上层管理
                    logger.info(f"MT5代理执行器已断开: {self.account_id}")
                except Exception as e:
                    logger.error(f"断开MT5代理执行器失败: {e}")
        
        proxy_executor = MT5JetStreamProxyExecutor(account_id, config, jetstream_client)
        
        logger.info(f"MT5交易执行器创建完成: {account_id}")
        return proxy_executor
        
    except Exception as e:
        logger.error(f"创建MT5交易执行器失败: {e}")
        raise


async def _handle_monitor_messages(monitor, message_queue: mp.Queue):
    """
    处理监控器消息队列
    """
    running = True
    
    while running:
        try:
            if not message_queue.empty():
                message = message_queue.get_nowait()
                msg_type = message.get('type')
                
                if msg_type == 'stop':
                    logger.info("监控器收到停止消息")
                    running = False
                elif msg_type == 'get_stats':
                    stats = monitor.get_monitoring_stats()
                    logger.info(f"监控器统计: {stats}")
                else:
                    logger.warning(f"监控器收到未知消息: {msg_type}")
            
            await asyncio.sleep(0.1)
            
        except Exception as e:
            logger.error(f"处理监控器消息异常: {e}")


async def _handle_executor_messages(executor, message_queue: mp.Queue):
    """
    处理执行器消息队列
    """
    running = True
    
    while running:
        try:
            if not message_queue.empty():
                message = message_queue.get_nowait()
                msg_type = message.get('type')
                
                if msg_type == 'stop':
                    logger.info("执行器收到停止消息")
                    running = False
                elif msg_type == 'get_stats':
                    stats = executor.get_execution_stats()
                    logger.info(f"执行器统计: {stats}")
                else:
                    logger.warning(f"执行器收到未知消息: {msg_type}")
            
            await asyncio.sleep(0.1)
            
        except Exception as e:
            logger.error(f"处理执行器消息异常: {e}")

