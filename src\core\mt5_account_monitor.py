#!/usr/bin/env python3
"""
MT5账户监控器 - 严格职责分离架构
仅负责监控MT5账户状态和持仓变化
通过消息系统发布监控事件，不执行任何交易操作
"""

import asyncio
import time
from typing import Dict, Any, Optional, Set, List
from dataclasses import dataclass
from datetime import datetime

# 移除基类依赖，直接实现监控功能
from ..messaging.message_types import TradeSignal, OrderType, SignalType
from ..utils.logger import get_logger
from ..messaging.jetstream_client import JetStreamClient

logger = get_logger(__name__)


@dataclass
class MonitoringEvent:
    """监控事件"""
    event_type: str
    account_id: str
    timestamp: float
    data: Dict[str, Any]
    event_id: str


class MT5AccountMonitor:
    """
    MT5账户监控器 - 严格职责分离实现
    
    职责：
    监控持仓变化
    监控账户状态
    发布监控事件
    
    职责边界：
    不执行任何交易
    不修改MT5状态  
    不处理跟单逻辑
    """
    
    def __init__(self, account_id: str, account_config: Dict[str, Any],
                 mt5_client, event_publisher: JetStreamClient, rpc_client=None):
        # 基础属性
        self.account_config = account_config
        self.running = False
        self.start_time = None
        self.account_id = account_id
        self.mt5_client = mt5_client  # 保留作为备用，逐步迁移到RPC
        self.event_publisher = event_publisher
        self.connection_pool = None  # 可选的连接池支持

        self.rpc_client = rpc_client
        if self.rpc_client:
            logger.info(f"监控器使用RPC架构: {self.account_id}")
        else:
            logger.warning(f"监控器使用传统架构: {self.account_id}")
        
        self.current_positions: Dict[int, Dict] = {}
        self.last_position_check = 0
        
        try:
            from .config_manager import get_config_manager
            config_mgr = get_config_manager()

            self.position_check_interval = config_mgr.get('monitoring.position_check_interval', 0.5)

            self.sleep_interval = config_mgr.get('monitoring.sleep_interval', 0.2)
            self.polling_interval = config_mgr.get('monitoring.position_monitoring.polling_interval', 0.5)

            logger.info(f"监控配置加载: {self.account_id} - 持仓检查间隔:{self.position_check_interval}s, 睡眠间隔:{self.sleep_interval}s")

        except Exception as e:
            logger.warning(f"配置加载失败，使用默认值: {e}")
            self.position_check_interval = 0.5  # 默认0.5秒间隔
            self.sleep_interval = 0.2
            self.polling_interval = 0.5
        
        self.monitoring_stats = {
            'events_published': 0,
            'position_changes_detected': 0,
            'monitoring_errors': 0,
            'last_check_time': 0
        }
        
        self.event_topic = f"MT5.MONITOR.{self.account_id}"
        
        logger.info(f"MT5账户监控器初始化: {account_id} - 纯监控模式")
    
    async def start(self):
        """启动监控器 - 实现启动接口"""  
        return await self.start_monitoring()
    
    async def start_monitoring(self) -> bool:
        """启动监控 - 仅监控，不执行"""
        if self.running:
            logger.warning(f"监控器已在运行: {self.account_id}")
            return False
        
        try:
            if not self.mt5_client:
                logger.error(f"MT5客户端未初始化: {self.account_id}")
                return False
            
            if hasattr(self.mt5_client, 'process_manager') and hasattr(self.mt5_client.process_manager, 'is_account_connected'):
                if not self.mt5_client.process_manager.is_account_connected(self.account_id):
                    logger.warning(f"MT5连接未就绪，监控器将在连接建立后开始工作: {self.account_id}")
            
            self.running = True
            self.start_time = time.time()
            
            asyncio.create_task(self._monitoring_loop())
            
            await self._publish_monitoring_event("monitor_started", {
                "account_id": self.account_id,
                "start_time": self.start_time
            })
            
            logger.info(f"MT5账户监控器已启动: {self.account_id}")
            return True
            
        except Exception as e:
            logger.error(f"启动监控器失败: {e}")
            self.running = False
            return False
    
    async def stop_monitoring(self):
        """停止监控"""
        if not self.running:
            return
        
        self.running = False
        
        await self._publish_monitoring_event("monitor_stopped", {
            "account_id": self.account_id,
            "stop_time": time.time(),
            "stats": self.monitoring_stats
        })
        
        logger.info(f"MT5账户监控器已停止: {self.account_id}")

    async def _ensure_connection(self) -> bool:
        """确保MT5连接正常"""
        try:
            if not self.mt5_client:
                logger.error(f"MT5客户端未初始化: {self.account_id}")
                return False

            if hasattr(self.mt5_client, 'process_manager'):
                process_manager = self.mt5_client.process_manager

                if hasattr(process_manager, 'is_account_connected'):
                    is_connected = process_manager.is_account_connected(self.account_id)
                    if not is_connected:
                        logger.warning(f"账户未连接到MT5: {self.account_id}")
                        return False
                    else:
                        logger.debug(f"账户连接正常: {self.account_id}")
                        return True

                if hasattr(process_manager, 'get_account_status'):
                    status = process_manager.get_account_status(self.account_id)
                    if status and status.get('connected', False):
                        logger.debug(f"进程状态正常: {self.account_id}")
                        return True
                    else:
                        logger.warning(f"进程状态异常: {self.account_id}, 状态: {status}")
                        return False

                logger.warning(f"进程管理器缺少连接检查方法: {self.account_id}")
                return False
            else:
                logger.error(f"MT5客户端缺少进程管理器: {self.account_id}")
                return False

        except Exception as e:
            logger.error(f"连接检查失败: {self.account_id}, 错误: {e}")
            return False

    async def _monitoring_loop(self):
        """监控主循环"""
        logger.info(f"监控循环启动: {self.account_id}")

        loop_count = 0
        while self.running:
            try:
                current_time = time.time()
                loop_count += 1
                
                if loop_count % 50 == 0:   
                    logger.info(f"监控循环运行中: {self.account_id}, 循环次数: {loop_count}, 间隔: {self.position_check_interval}s")

                if current_time - self.last_position_check >= self.position_check_interval:
                    logger.info(f"触发持仓检查: {self.account_id}, 距离上次检查: {current_time - self.last_position_check:.2f}s")
                    await self._check_position_changes()
                    self.last_position_check = current_time
                    self.monitoring_stats['last_check_time'] = current_time
                else:
                    wait_time = self.position_check_interval - (current_time - self.last_position_check)
                    if loop_count % 100 == 0:  
                        logger.info(f"等待下次检查: {self.account_id}, 还需等待: {wait_time:.2f}s")
                
                await asyncio.sleep(self.sleep_interval)
                
            except Exception as e:
                logger.error(f"监控循环异常: {e}")
                self.monitoring_stats['monitoring_errors'] += 1
                await asyncio.sleep(1)
    
    async def _check_position_changes(self):
        """检查持仓变化 """
        try:
            logger.info(f"触发持仓检查: {self.account_id}, 距离上次检查: {time.time() - getattr(self, 'last_check_time', 0):.2f}s")
            
            connection_ready = await self._ensure_connection()
            if not connection_ready:
                logger.debug(f"连接未完全就绪，但继续尝试获取数据: {self.account_id}")
            
            logger.info(f"正在检查持仓变化: {self.account_id}")
            
            try:
                if self.rpc_client:
                    positions_result = await self.rpc_client.get_positions(self.account_id)

                    if positions_result.get("status") == "success":
                        positions_list = positions_result.get("positions", [])
                        logger.debug(f"RPC获取持仓成功: {self.account_id}, 持仓数量: {len(positions_list)}")
                    else:
                        logger.warning(f"RPC获取持仓失败: {self.account_id}, 错误: {positions_result.get('error')}")
                        positions_list = []
                else:
                    positions_list = await self.mt5_client.get_positions()

                    if positions_list is None:
                        logger.info(f"获取到持仓数据: {self.account_id}, 类型: {type(positions_list)}, 长度: None")
                        logger.warning(f"持仓数据为None: {self.account_id}")
                        positions_list = []
                    elif isinstance(positions_list, list):
                        actual_len = len(positions_list)
                        logger.info(f"获取到持仓数据: {self.account_id}, 类型: {type(positions_list)}, 长度: {actual_len}")
                    else:
                        logger.info(f"获取到持仓数据: {self.account_id}, 类型: {type(positions_list)}, 长度: 未知类型")
                        logger.warning(f"持仓数据类型异常: {self.account_id}, 类型: {type(positions_list)}")
                        positions_list = []
                    
            except Exception as pos_error:
                logger.error(f"获取持仓数据失败: {self.account_id}, 错误: {pos_error}")
                positions_list = []
            
            if not positions_list:
                current_positions_data = {}
                logger.info(f"持仓列表为空: {self.account_id}")
            else:
                logger.info(f"发现持仓: {self.account_id}, 数量: {len(positions_list)}")
                
                current_positions_data = {}
                for i, pos in enumerate(positions_list):
                    if isinstance(pos, dict):
                        ticket = pos.get('ticket') or pos.get('position_id') or f"pos_{i}"
                        current_positions_data[ticket] = pos
                        logger.info(f"  持仓{i+1}: ticket={ticket}, symbol={pos.get('symbol', 'N/A')}, volume={pos.get('volume', 'N/A')}")
                    else:
                        logger.warning(f"无效的持仓数据格式: {self.account_id}, 索引: {i}, 类型: {type(pos)}")
                
                logger.info(f"转换后的持仓数据: {self.account_id}, 持仓数: {len(current_positions_data)}")
            
            changes = self._detect_position_changes(current_positions_data)
            logger.info(f"检测到的变化数量: {self.account_id}, 变化数: {len(changes)}")
            
            if changes:
                logger.info(f"发现持仓变化: {self.account_id}")
                for change in changes:
                    logger.info(f"  变化类型: {change.get('type', 'unknown')}, ticket: {change.get('ticket', 'N/A')}")
                
                await self._publish_position_events(changes)
                
                self.monitoring_stats['position_changes_detected'] += len(changes)
                logger.info(f"已发布 {len(changes)} 个持仓变化事件: {self.account_id}")
            else:
                if not hasattr(self, '_no_change_count'):
                    self._no_change_count = 0
                self._no_change_count += 1
                
                if self._no_change_count % 10 == 0:
                    logger.debug(f"持续无持仓变化: {self.account_id}, 检查次数: {self._no_change_count}")
            
            self.last_check_time = time.time()
            self.current_positions = current_positions_data

        except Exception as e:
            logger.error(f"检查持仓变化异常: {self.account_id}, 错误: {e}")
            self.monitoring_stats['monitoring_errors'] += 1
    
    async def _publish_position_events(self, changes):
        """发布持仓变化事件"""
        try:
            for change in changes:
                await self._publish_position_change_event(change)
        except Exception as e:
            logger.error(f"发布持仓事件失败: {self.account_id}, 错误: {e}")
    
    async def _ensure_connection(self):
        """确保连接状态 """
        try:
            if not self.mt5_client:
                logger.warning(f"MT5客户端未初始化: {self.account_id}")
                return False

            if hasattr(self.mt5_client, 'is_connected'):
                try:
                    connected = self.mt5_client.is_connected()
                    if not connected:
                        logger.warning(f"MT5连接已断开: {self.account_id}")
                        return False

                    if hasattr(self.mt5_client, 'terminal_info'):
                        try:
                            terminal_info = self.mt5_client.terminal_info()
                            if not terminal_info:
                                logger.warning(f"无法获取终端信息，连接可能异常: {self.account_id}")
                                return False
                        except Exception as e:
                            logger.warning(f"终端信息检查失败: {self.account_id}, 错误: {e}")
                            return False

                    logger.debug(f"连接状态检查通过: {self.account_id}")
                    return True

                except Exception as e:
                    logger.warning(f"连接状态检查异常: {self.account_id}, 错误: {e}")
                    return False
            else:
                logger.warning(f"MT5客户端缺少is_connected方法: {self.account_id}")

                try:
                    if hasattr(self.mt5_client, 'account_info'):
                        account_info = self.mt5_client.account_info()
                        if account_info:
                            logger.debug(f"通过account_info验证连接正常: {self.account_id}")
                            return True
                        else:
                            logger.warning(f"无法获取账户信息，连接异常: {self.account_id}")
                            return False
                    else:
                        logger.error(f"MT5客户端缺少必要的连接检查方法: {self.account_id}")
                        return False

                except Exception as e:
                    logger.error(f"替代连接检查失败: {self.account_id}, 错误: {e}")
                    return False

        except Exception as e:
            logger.error(f"连接检查严重异常: {self.account_id}, 错误: {e}")
            return False
    
    def _detect_position_changes(self, new_positions: Dict[int, Dict]) -> List[Dict]:
        """检测持仓变化"""
        changes = []
        
        for ticket, position in new_positions.items():
            if ticket not in self.current_positions:
                changes.append({
                    'type': 'position_opened',
                    'ticket': ticket,
                    'position': position,
                    'timestamp': time.time()
                })
        
        for ticket, position in self.current_positions.items():
            if ticket not in new_positions:
                changes.append({
                    'type': 'position_closed',
                    'ticket': ticket,
                    'position': position,
                    'timestamp': time.time()
                })
        
        for ticket, new_pos in new_positions.items():
            if ticket in self.current_positions:
                old_pos = self.current_positions[ticket]
                if (new_pos.get('sl') != old_pos.get('sl') or 
                    new_pos.get('tp') != old_pos.get('tp')):
                    changes.append({
                        'type': 'position_modified',
                        'ticket': ticket,
                        'old_position': old_pos,
                        'new_position': new_pos,
                        'timestamp': time.time()
                    })
        
        return changes
    
    async def _publish_position_change_event(self, change: Dict):
        """发布持仓变化事件"""
        try:
            event = MonitoringEvent(
                event_type=change['type'],
                account_id=self.account_id,
                timestamp=change['timestamp'],
                data=change,
                event_id=f"{self.account_id}_{change['ticket']}_{int(change['timestamp'] * 1000)}"
            )
            
            await self.event_publisher.publish(
                subject=self.event_topic,
                data={
                    'event_type': event.event_type,
                    'account_id': event.account_id,
                    'timestamp': event.timestamp,
                    'data': event.data,
                    'event_id': event.event_id
                }
            )
            
            self.monitoring_stats['events_published'] += 1
            
            logger.debug(f"发布持仓变化事件: {change['type']} - {change['ticket']}")
            
        except Exception as e:
            logger.error(f"发布持仓变化事件失败: {e}")
    
    async def _publish_monitoring_event(self, event_type: str, data: Dict):
        """发布监控事件"""
        try:
            event = MonitoringEvent(
                event_type=event_type,
                account_id=self.account_id,
                timestamp=time.time(),
                data=data,
                event_id=f"{self.account_id}_{event_type}_{int(time.time() * 1000)}"
            )
            
            await self.event_publisher.publish(
                subject=self.event_topic,
                data={
                    'event_type': event.event_type,
                    'account_id': event.account_id,
                    'timestamp': event.timestamp,
                    'data': event.data,
                    'event_id': event.event_id
                }
            )
            
            self.monitoring_stats['events_published'] += 1
            
        except Exception as e:
            logger.error(f"发布监控事件失败: {e}")
    
    def get_monitoring_stats(self) -> Dict[str, Any]:
        """获取监控统计"""
        uptime = time.time() - (self.start_time or time.time())

        return {
            **self.monitoring_stats,
            'account_id': self.account_id,
            'running': self.running,
            'uptime_seconds': uptime,
            'positions_count': len(self.current_positions),
            'event_topic': self.event_topic,
            'rpc_enabled': self.rpc_client is not None
        }

    async def health_check_rpc(self) -> Dict[str, Any]:
        """使用RPC进行健康检查"""
        if not self.rpc_client:
            return {"status": "failed", "error": "RPC client not available"}

        try:
            health_result = await self.rpc_client.health_check(self.account_id)
            return health_result
        except Exception as e:
            logger.error(f"RPC健康检查失败: {self.account_id}, 错误: {e}")
            return {"status": "failed", "error": str(e)}
    
    async def monitor_positions(self):
        """监控持仓 - 父类接口实现"""
        return await self._check_position_changes()
    
    async def monitor_account_status(self):
        """监控账户状态 - 通过进程隔离MT5客户端"""
        if not self.mt5_client or not self.mt5_client.is_connected():
            await self._publish_monitoring_event("account_disconnected", {
                "account_id": self.account_id,
                "timestamp": time.time(),
                "reason": "MT5进程连接丢失"
            })
            return False
        
        # 额外检查：尝试获取账户信息验证连接健康
        try:
            # 优先使用RPC架构获取账户信息
            if self.rpc_client:
                account_result = await self.rpc_client.get_account_info(self.account_id)
                account_info = account_result if account_result.get("status") == "success" else None
            else:
                # 备用：使用传统MT5客户端
                account_info = await self.mt5_client.get_account_info()

            if not account_info:
                await self._publish_monitoring_event("account_unhealthy", {
                    "account_id": self.account_id,
                    "timestamp": time.time(),
                    "reason": "无法获取账户信息"
                })
                return False
            return True
        except Exception as e:
            await self._publish_monitoring_event("account_error", {
                "account_id": self.account_id,
                "timestamp": time.time(),
                "error": str(e)
            })
            return False