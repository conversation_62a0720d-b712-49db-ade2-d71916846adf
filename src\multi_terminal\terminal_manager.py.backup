"""
企业级终端管理器
基于BaseTerminalService，提供高级功能：负载均衡、多平台支持、云端集成
"""
import asyncio
import platform
import subprocess
from dataclasses import dataclass, field
from typing import Dict, List, Optional, Set
from pathlib import Path

from .base_service import BaseTerminalService
from .models import Terminal, TerminalRole, TerminalStatus
from .terminal_pool import TerminalPool, PoolConfig
from ..messaging.nats_client import NATSClient
from ..config.redis_client import RedisClient
from ..config.settings import AccountSettings
from ..utils.logger import get_logger


logger = get_logger(__name__)


@dataclass
class EnterpriseTerminalConfig:
    """企业级终端配置"""
    id: str
    name: str
    terminal_type: str = "local"  # local, cloud, hybrid
    path: Optional[str] = None  # 本地终端路径
    data_path: Optional[str] = None  # 数据目录
    max_accounts: int = 10
    auto_restart: bool = True
    health_check_interval: int = 30  # 秒
    
    # 云端配置
    cloud_config: Optional[Dict] = None
    
    # 性能限制
    max_cpu_percent: float = 80.0
    max_memory_mb: int = 2048
    
    # 负载均衡权重
    load_weight: float = 1.0
    priority: int = 0  # 0=最高优先级
    
    # 地理位置 (用于延迟优化)
    region: str = "default"
    datacenter: str = "default"


class EnterpriseTerminalManager(BaseTerminalService):
    """企业级终端管理器"""
    
    def __init__(self, host_id: str, nats_client: NATSClient, redis_client: RedisClient):
        super().__init__(host_id, nats_client, redis_client)
        
        # 企业级配置
        self.terminal_configs: Dict[str, EnterpriseTerminalConfig] = {}
        self.account_terminal_map: Dict[str, str] = {}  # account_id -> terminal_id
        
        # 负载均衡
        self.load_balancer_enabled = True
        self.rebalance_interval = 300  # 5分钟
        self.load_balancer_task: Optional[asyncio.Task] = None
        
        # 健康检查
        self.health_check_tasks: Dict[str, asyncio.Task] = {}
        self.restart_attempts: Dict[str, int] = {}
        self.max_restart_attempts = 3
        
        # 统计
        self.stats = {
            'total_terminals': 0,
            'active_terminals': 0,
            'total_accounts': 0,
            'restarts': 0,
            'failures': 0,
            'load_balance_operations': 0
        }
        
        # 连接池 (企业级配置)
        pool_config = PoolConfig(
            min_connections=10,
            max_connections=100,
            max_idle_time=600,
            validation_interval=30
        )
        self.enterprise_pool = TerminalPool(config=pool_config)
        
        # Redis键
        self.redis_prefix = "mt5:enterprise"
    
    async def start(self):
        """启动企业级终端管理器"""
        await super().start()
        
        try:
            # 启动企业级连接池
            await self.enterprise_pool.start()
            
            # 加载企业级配置
            await self.load_enterprise_configs()
            
            # 启动负载均衡器
            if self.load_balancer_enabled:
                self.load_balancer_task = asyncio.create_task(self._load_balancer_loop())
            
            # 启动已配置的终端
            for config in self.terminal_configs.values():
                await self.add_enterprise_terminal(config)
            
            logger.info(f"企业级终端管理器已启动，管理 {len(self.terminals)} 个终端")
            
        except Exception as e:
            logger.error(f"启动企业级终端管理器失败: {e}")
            await self.stop()
            raise
    
    async def stop(self):
        """停止企业级终端管理器"""
        # 停止负载均衡器
        if self.load_balancer_task and not self.load_balancer_task.done():
            self.load_balancer_task.cancel()
            try:
                await self.load_balancer_task
            except asyncio.CancelledError:
                pass
        
        # 停止健康检查任务
        for task in self.health_check_tasks.values():
            if not task.done():
                task.cancel()
                try:
                    await task
                except asyncio.CancelledError:
                    pass
        
        # 关闭企业级连接池
        await self.enterprise_pool.close()
        
        # 保存企业级配置
        await self.save_enterprise_configs()
        
        await super().stop()
        
        logger.info("企业级终端管理器已停止")
    
    # ==================== 企业级终端管理 ====================
    
    async def add_enterprise_terminal(self, config: EnterpriseTerminalConfig) -> str:
        """添加企业级终端"""
        try:
            if config.id in self.terminals:
                logger.warning(f"企业级终端已存在: {config.id}")
                return config.id
            
            # 创建账户配置对象
            account_config = type('AccountConfig', (), {
                'id': config.id,
                'login': 0,  # 将在实际使用时设置
                'password': '',
                'server': '',
                'terminal_path': config.path or ''
            })()
            
            # 注册基础终端
            terminal_id = await self.register_terminal(account_config, config.path)
            
            # 保存企业级配置
            self.terminal_configs[terminal_id] = config
            
            # 启动终端 (如果是本地类型)
            if config.terminal_type == "local" and config.path:
                success = await self.start_enterprise_terminal(terminal_id)
                if success:
                    # 启动健康检查
                    await self.start_health_check(terminal_id, config)
            
            self.stats['total_terminals'] += 1
            
            logger.info(f"企业级终端已添加: {terminal_id}")
            return terminal_id
            
        except Exception as e:
            logger.error(f"添加企业级终端失败: {e}")
            raise
    
    async def start_enterprise_terminal(self, terminal_id: str) -> bool:
        """启动企业级终端 (支持多平台)"""
        if terminal_id not in self.terminals:
            return False
        
        terminal = self.terminals[terminal_id]
        config = self.terminal_configs.get(terminal_id)
        
        if not config or not config.path:
            logger.error(f"终端配置不完整: {terminal_id}")
            return False
        
        try:
            logger.info(f"启动企业级终端: {terminal_id}")
            terminal.status = TerminalStatus.STARTING
            
            # 根据平台构建启动命令
            cmd = self._build_platform_command(config)
            
            # 启动进程
            process = subprocess.Popen(
                cmd,
                cwd=Path(config.path).parent if config.path else None,
                stdout=subprocess.DEVNULL,
                stderr=subprocess.DEVNULL
            )
            
            terminal.pid = process.pid
            terminal.status = TerminalStatus.RUNNING
            terminal.start_count += 1
            
            # 等待初始化
            await asyncio.sleep(5)
            
            # 验证启动
            if process.poll() is None:
                # 添加到企业级连接池
                connection = await self._create_enterprise_connection(terminal, config)
                if connection:
                    await self.enterprise_pool.add_connection(terminal_id, connection)
                
                self.stats['active_terminals'] += 1
                logger.info(f"企业级终端启动成功: {terminal_id}")
                return True
            else:
                terminal.status = TerminalStatus.ERROR
                terminal.error_count += 1
                self.stats['failures'] += 1
                logger.error(f"企业级终端启动失败: {terminal_id}")
                return False
                
        except Exception as e:
            terminal.status = TerminalStatus.ERROR
            terminal.error_count += 1
            self.stats['failures'] += 1
            logger.error(f"启动企业级终端失败 {terminal_id}: {e}")
            return False
    
    def _build_platform_command(self, config: EnterpriseTerminalConfig) -> List[str]:
        """构建平台特定的启动命令"""
        system = platform.system()
        
        if system == "Windows":
            cmd = [config.path]
            if config.data_path:
                cmd.extend(["/portable", config.data_path])
        elif system == "Linux":
            # Linux下使用Wine运行MT5
            cmd = ["wine", config.path]
            if config.data_path:
                cmd.extend(["/portable", config.data_path])
        elif system == "Darwin":  # macOS
            cmd = [config.path]
            if config.data_path:
                cmd.extend(["/portable", config.data_path])
        else:
            raise ValueError(f"不支持的操作系统: {system}")
        
        return cmd
    
    async def _create_enterprise_connection(self, terminal: Terminal, config: EnterpriseTerminalConfig):
        """创建企业级连接"""
        try:
            # 企业级连接对象
            connection = {
                "terminal_id": terminal.terminal_id,
                "config": config,
                "type": "enterprise",
                "platform": platform.system(),
                "region": config.region,
                "datacenter": config.datacenter,
                "max_accounts": config.max_accounts,
                "load_weight": config.load_weight,
                "priority": config.priority,
                "created_at": asyncio.get_event_loop().time()
            }
            
            return connection
            
        except Exception as e:
            logger.error(f"创建企业级连接失败: {e}")
            return None
    
    # ==================== 负载均衡 ====================
    
    async def assign_account_to_terminal(self, account: AccountSettings) -> Optional[str]:
        """智能分配账户到最佳终端"""
        try:
            # 如果账户已有终端，检查是否仍然可用
            if account.id in self.account_terminal_map:
                terminal_id = self.account_terminal_map[account.id]
                if terminal_id in self.terminals:
                    terminal = self.terminals[terminal_id]
                    if terminal.status == TerminalStatus.RUNNING:
                        return terminal_id
            
            # 找到最佳终端
            best_terminal_id = await self._find_best_terminal(account)
            
            if best_terminal_id:
                self.account_terminal_map[account.id] = best_terminal_id
                self.stats['total_accounts'] += 1
                self.stats['load_balance_operations'] += 1
                
                logger.info(f"账户 {account.id} 已分配到终端 {best_terminal_id}")
                return best_terminal_id
            else:
                logger.warning(f"没有可用终端分配给账户 {account.id}")
                return None
                
        except Exception as e:
            logger.error(f"分配账户到终端失败: {e}")
            return None
    
    async def _find_best_terminal(self, account: AccountSettings) -> Optional[str]:
        """找到最佳终端"""
        best_terminal_id = None
        best_score = float('-inf')
        
        for terminal_id, terminal in self.terminals.items():
            if terminal.status != TerminalStatus.RUNNING:
                continue
            
            config = self.terminal_configs.get(terminal_id)
            if not config:
                continue
            
            # 计算负载分数
            score = await self._calculate_terminal_score(terminal_id, terminal, config, account)
            
            if score > best_score:
                best_score = score
                best_terminal_id = terminal_id
        
        return best_terminal_id
    
    async def _calculate_terminal_score(self, terminal_id: str, terminal: Terminal, 
                                       config: EnterpriseTerminalConfig, account: AccountSettings) -> float:
        """计算终端适合度分数"""
        score = 0.0
        
        # 基础权重
        score += config.load_weight * 100
        
        # 优先级 (数字越小优先级越高)
        score += (10 - config.priority) * 50
        
        # 当前负载 (账户数量)
        current_accounts = len([aid for aid, tid in self.account_terminal_map.items() if tid == terminal_id])
        load_ratio = current_accounts / config.max_accounts
        score += (1 - load_ratio) * 200  # 负载越低分数越高
        
        # 地理位置匹配 (如果账户有地理偏好)
        if hasattr(account, 'preferred_region') and account.preferred_region == config.region:
            score += 100
        
        # 性能指标 (错误率)
        if terminal.start_count > 0:
            error_rate = terminal.error_count / terminal.start_count
            score += (1 - error_rate) * 50
        
        return score
    
    async def _load_balancer_loop(self):
        """负载均衡循环"""
        while self.running:
            try:
                await self._rebalance_terminals()
                await asyncio.sleep(self.rebalance_interval)
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"负载均衡循环失败: {e}")
                await asyncio.sleep(60)
    
    async def _rebalance_terminals(self):
        """重新平衡终端负载"""
        try:
            # 检查是否需要重新平衡
            if not self._needs_rebalancing():
                return
            
            logger.info("开始负载重新平衡")
            
            # 获取过载的终端
            overloaded_terminals = self._get_overloaded_terminals()
            
            # 重新分配账户
            for terminal_id in overloaded_terminals:
                await self._redistribute_accounts(terminal_id)
            
            self.stats['load_balance_operations'] += 1
            logger.info("负载重新平衡完成")
            
        except Exception as e:
            logger.error(f"负载重新平衡失败: {e}")
    
    def _needs_rebalancing(self) -> bool:
        """检查是否需要重新平衡"""
        # 简化的重新平衡逻辑
        terminal_loads = {}
        
        for account_id, terminal_id in self.account_terminal_map.items():
            if terminal_id not in terminal_loads:
                terminal_loads[terminal_id] = 0
            terminal_loads[terminal_id] += 1
        
        if not terminal_loads:
            return False
        
        # 检查负载差异
        max_load = max(terminal_loads.values())
        min_load = min(terminal_loads.values())
        
        return (max_load - min_load) > 3  # 如果差异超过3个账户就重新平衡
    
    def _get_overloaded_terminals(self) -> List[str]:
        """获取过载的终端"""
        overloaded = []
        
        for terminal_id, config in self.terminal_configs.items():
            current_accounts = len([aid for aid, tid in self.account_terminal_map.items() if tid == terminal_id])
            if current_accounts > config.max_accounts * 0.8:  # 超过80%容量
                overloaded.append(terminal_id)
        
        return overloaded
    
    async def _redistribute_accounts(self, overloaded_terminal_id: str):
        """重新分配过载终端的账户"""
        # 获取该终端的账户
        accounts_to_move = [
            account_id for account_id, terminal_id in self.account_terminal_map.items()
            if terminal_id == overloaded_terminal_id
        ]
        
        # 移动部分账户到其他终端
        for account_id in accounts_to_move[:2]:  # 最多移动2个账户
            # 这里需要实际的账户对象，简化处理
            # best_terminal = await self._find_best_terminal(account)
            # if best_terminal and best_terminal != overloaded_terminal_id:
            #     self.account_terminal_map[account_id] = best_terminal
            pass
    
    # ==================== 健康检查 ====================
    
    async def start_health_check(self, terminal_id: str, config: EnterpriseTerminalConfig):
        """启动健康检查"""
        if terminal_id in self.health_check_tasks:
            return
        
        task = asyncio.create_task(self._health_check_loop(terminal_id, config))
        self.health_check_tasks[terminal_id] = task
    
    async def _health_check_loop(self, terminal_id: str, config: EnterpriseTerminalConfig):
        """健康检查循环"""
        while self.running and terminal_id in self.terminals:
            try:
                await self._check_terminal_health(terminal_id, config)
                await asyncio.sleep(config.health_check_interval)
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"健康检查失败 {terminal_id}: {e}")
                await asyncio.sleep(30)
    
    async def _check_terminal_health(self, terminal_id: str, config: EnterpriseTerminalConfig):
        """检查终端健康状态"""
        terminal = self.terminals.get(terminal_id)
        if not terminal:
            return
        
        # 检查进程状态
        if terminal.pid:
            try:
                import psutil
                process = psutil.Process(terminal.pid)
                
                # 检查CPU和内存使用
                cpu_percent = process.cpu_percent()
                memory_mb = process.memory_info().rss / 1024 / 1024
                
                if cpu_percent > config.max_cpu_percent:
                    logger.warning(f"终端 {terminal_id} CPU使用率过高: {cpu_percent}%")
                
                if memory_mb > config.max_memory_mb:
                    logger.warning(f"终端 {terminal_id} 内存使用过高: {memory_mb}MB")
                
                # 检查进程是否还在运行
                if not process.is_running():
                    logger.error(f"终端进程已停止: {terminal_id}")
                    await self._handle_terminal_failure(terminal_id, config)
                
            except psutil.NoSuchProcess:
                logger.error(f"终端进程不存在: {terminal_id}")
                await self._handle_terminal_failure(terminal_id, config)
    
    async def _handle_terminal_failure(self, terminal_id: str, config: EnterpriseTerminalConfig):
        """处理终端故障"""
        if not config.auto_restart:
            return
        
        restart_count = self.restart_attempts.get(terminal_id, 0)
        if restart_count >= self.max_restart_attempts:
            logger.error(f"终端 {terminal_id} 重启次数超限，停止自动重启")
            return
        
        logger.info(f"尝试重启终端 {terminal_id} (第 {restart_count + 1} 次)")
        
        # 停止终端
        await self.stop_terminal(terminal_id)
        
        # 等待一段时间
        await asyncio.sleep(10)
        
        # 重新启动
        success = await self.start_enterprise_terminal(terminal_id)
        
        if success:
            self.restart_attempts[terminal_id] = 0
            self.stats['restarts'] += 1
            logger.info(f"终端 {terminal_id} 重启成功")
        else:
            self.restart_attempts[terminal_id] = restart_count + 1
            logger.error(f"终端 {terminal_id} 重启失败")
    
    # ==================== 数据持久化 ====================
    
    async def save_enterprise_configs(self):
        """保存企业级配置"""
        try:
            configs_data = {}
            for terminal_id, config in self.terminal_configs.items():
                configs_data[terminal_id] = {
                    "id": config.id,
                    "name": config.name,
                    "terminal_type": config.terminal_type,
                    "path": config.path,
                    "data_path": config.data_path,
                    "max_accounts": config.max_accounts,
                    "auto_restart": config.auto_restart,
                    "health_check_interval": config.health_check_interval,
                    "cloud_config": config.cloud_config,
                    "max_cpu_percent": config.max_cpu_percent,
                    "max_memory_mb": config.max_memory_mb,
                    "load_weight": config.load_weight,
                    "priority": config.priority,
                    "region": config.region,
                    "datacenter": config.datacenter
                }
            
            await self.redis.set(
                f"{self.redis_prefix}:configs",
                json.dumps(configs_data, default=str)
            )
            
            # 保存账户映射
            await self.redis.set(
                f"{self.redis_prefix}:account_map",
                json.dumps(self.account_terminal_map)
            )
            
        except Exception as e:
            logger.error(f"保存企业级配置失败: {e}")
    
    async def load_enterprise_configs(self):
        """加载企业级配置"""
        try:
            # 加载配置
            configs_data = await self.redis.get(f"{self.redis_prefix}:configs")
            if configs_data:
                configs_dict = json.loads(configs_data)
                for terminal_id, config_data in configs_dict.items():
                    config = EnterpriseTerminalConfig(**config_data)
                    self.terminal_configs[terminal_id] = config
            
            # 加载账户映射
            account_map_data = await self.redis.get(f"{self.redis_prefix}:account_map")
            if account_map_data:
                self.account_terminal_map = json.loads(account_map_data)
            
            logger.info(f"已加载 {len(self.terminal_configs)} 个企业级终端配置")
            
        except Exception as e:
            logger.error(f"加载企业级配置失败: {e}")
    
    # ==================== 统计和监控 ====================
    
    async def get_enterprise_stats(self) -> Dict:
        """获取企业级统计"""
        # 获取连接池统计
        pool_stats = await self.enterprise_pool.get_stats()
        
        # 计算负载分布
        load_distribution = {}
        for account_id, terminal_id in self.account_terminal_map.items():
            if terminal_id not in load_distribution:
                load_distribution[terminal_id] = 0
            load_distribution[terminal_id] += 1
        
        return {
            **self.stats,
            "pool_stats": pool_stats,
            "load_distribution": load_distribution,
            "restart_attempts": dict(self.restart_attempts),
            "health_check_tasks": len(self.health_check_tasks)
        }
