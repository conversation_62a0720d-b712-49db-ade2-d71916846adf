# MT5分布式交易系统 - 应用服务
# 包含MT5协调器、API网关等应用组件

version: '3.10'

services:
  # ============================================================================
  # MT5 API网关 - 统一管理接口
  # ============================================================================
  mt5-api:
    build:
      context: ../..
      dockerfile: docker/Dockerfile
    image: mt5-trading-system:latest
    container_name: mt5-api-service
    ports:
      - "${MT5_API_PORT:-8000}:8000"
    volumes:
      - ../../config:/app/config:ro
      - ../../logs:/app/logs
      - ../../.env:/app/.env:ro
    environment:
      - PYTHONPATH=/app
      - MT5_ENVIRONMENT=${MT5_ENVIRONMENT:-development}
      - MT5_CONFIG_PATH=/app/config/core/system.yaml
      - HOST_ID=${MT5_HOST_ID:-docker-api-gateway}
      - MT5_NODE_TYPE=api
      - MT5_DEPLOYMENT_MODE=${MT5_DEPLOYMENT_MODE:-development}
      
      # 基础设施连接
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_DB=${REDIS_DB:-0}
      - NATS_HOST=nats
      - NATS_PORT=4222
      - PROMETHEUS_PUSHGATEWAY_URL=http://pushgateway:9091
      
      # 安全配置
      - AUTH_TOKEN_SECRET=${AUTH_TOKEN_SECRET:-}
      - MT5_ACC001_PASSWORD=${MT5_ACC001_PASSWORD:-}
      - MT5_ACC002_PASSWORD=${MT5_ACC002_PASSWORD:-}
      - MT5_ACC003_PASSWORD=${MT5_ACC003_PASSWORD:-}
      
    depends_on:
      - redis
      - nats
      - mt5-coordinator
    restart: unless-stopped
    networks:
      - mt5-network
    labels:
      - "mt5.service=api-gateway"
      - "mt5.component=application"
      - "mt5.api.type=unified-management"
    command: ["python", "-m", "src.api.main"]
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  # ============================================================================
  # MT5分布式系统协调器 - 核心控制平面
  # ============================================================================
  mt5-coordinator:
    build:
      context: ../..
      dockerfile: docker/Dockerfile
    image: mt5-trading-system:latest
    container_name: mt5-coordinator
    volumes:
      - ../../config:/app/config:ro
      - ../../logs:/app/logs
      - ../../.env:/app/.env:ro
    environment:
      - PYTHONPATH=/app
      - MT5_ENVIRONMENT=${MT5_ENVIRONMENT:-development}
      - MT5_CONFIG_PATH=/app/config/core/system.yaml
      - HOST_ID=${MT5_HOST_ID:-docker-coordinator-node}
      - MT5_NODE_TYPE=coordinator
      - MT5_CLUSTER_ROLE=control_plane
      - MT5_DEPLOYMENT_MODE=${MT5_DEPLOYMENT_MODE:-development}
      
      # 基础设施连接
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_DB=${REDIS_DB:-0}
      - NATS_HOST=nats
      - NATS_PORT=4222
      - PROMETHEUS_PUSHGATEWAY_URL=http://pushgateway:9091
      
      # 安全配置
      - AUTH_TOKEN_SECRET=${AUTH_TOKEN_SECRET:-}
      - MT5_ACC001_PASSWORD=${MT5_ACC001_PASSWORD:-}
      - MT5_ACC002_PASSWORD=${MT5_ACC002_PASSWORD:-}
      - MT5_ACC003_PASSWORD=${MT5_ACC003_PASSWORD:-}
      
    depends_on:
      - redis
      - nats
      - pushgateway
    restart: unless-stopped
    networks:
      - mt5-network
    labels:
      - "mt5.service=distributed-coordinator"
      - "mt5.component=application"
      - "mt5.deployment=distributed"
      - "mt5.node.type=coordinator"
      - "mt5.cluster.role=control_plane"
    command: ["python", "main.py", "--host-id", "${MT5_HOST_ID:-docker-coordinator-node}", "--config", "/app/config/core/system.yaml"]
    healthcheck:
      test: ["CMD", "python", "-c", "import requests; requests.get('http://localhost:8001/health', timeout=5)"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  # ============================================================================
  # MT5账户管理器 - 账户生命周期管理
  # ============================================================================
  mt5-account-manager:
    build:
      context: ../..
      dockerfile: docker/Dockerfile
    image: mt5-trading-system:latest
    container_name: mt5-account-manager
    volumes:
      - ../../config:/app/config:ro
      - ../../logs:/app/logs
      - ../../.env:/app/.env:ro
      - ../../data:/app/data  # 账户数据持久化
    environment:
      - PYTHONPATH=/app
      - MT5_ENVIRONMENT=${MT5_ENVIRONMENT:-development}
      - MT5_CONFIG_PATH=/app/config/core/system.yaml
      - HOST_ID=${MT5_HOST_ID:-docker-account-manager}
      - MT5_NODE_TYPE=account_manager
      - MT5_DEPLOYMENT_MODE=${MT5_DEPLOYMENT_MODE:-development}
      
      # 基础设施连接
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_DB=${REDIS_DB:-0}
      - NATS_HOST=nats
      - NATS_PORT=4222
      - PROMETHEUS_PUSHGATEWAY_URL=http://pushgateway:9091
      
      # 账户密码
      - MT5_ACC001_PASSWORD=${MT5_ACC001_PASSWORD:-}
      - MT5_ACC002_PASSWORD=${MT5_ACC002_PASSWORD:-}
      - MT5_ACC003_PASSWORD=${MT5_ACC003_PASSWORD:-}
      
    depends_on:
      - redis
      - nats
      - mt5-coordinator
    restart: unless-stopped
    networks:
      - mt5-network
    labels:
      - "mt5.service=account-manager"
      - "mt5.component=application"
      - "mt5.node.type=account_manager"
    command: ["python", "-m", "src.manager.account_manager"]
    healthcheck:
      test: ["CMD", "python", "-c", "import sys; sys.exit(0)"]  # 简单健康检查
      interval: 60s
      timeout: 10s
      retries: 3
      start_period: 60s

  # ============================================================================
  # Redis Hash Manager - 性能优化服务
  # ============================================================================
  redis-hash-manager:
    build:
      context: ../..
      dockerfile: docker/Dockerfile.hash-manager
    image: mt5-hash-manager:latest
    container_name: mt5-hash-manager
    ports:
      - "${HASH_MANAGER_PORT:-8082}:8082"
    environment:
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_DB=${REDIS_DB:-0}
      - HASH_MANAGER_PORT=8082
    depends_on:
      - redis
    restart: unless-stopped
    networks:
      - mt5-network
    labels:
      - "mt5.service=hash-manager"
      - "mt5.component=optimization"
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8082/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s

  mt5-relationship-api:
    build:
      context: ../..
      dockerfile: docker/Dockerfile
    image: mt5-trading-system:latest
    container_name: mt5-relationship-api
    ports:
      - "8001:8001"
    environment:
      - PYTHONPATH=/app
      - MT5_RELATIONSHIPS_STORAGE=/app/data/relationships
    command: ["python", "-m", "src.relationships.web_api", "--port", "8001"]

# ============================================================================
# 外部网络（继承自基础设施）
# ============================================================================
networks:
  mt5-network:
    external: true
    name: mt5-network


