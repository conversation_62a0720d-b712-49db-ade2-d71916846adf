#!/usr/bin/env python3
"""
密码设置和管理工具
用于初始化、设置和管理MT5账户密码
"""

import asyncio
import getpass
import sys
import os
from pathlib import Path

# 添加项目路径
sys.path.append(str(Path(__file__).parent.parent.parent))

from src.security.password_manager import (
    SecurePasswordManager, 
    PasswordConfig, 
    PasswordStorageType,
    get_account_password,
    set_account_password
)
from src.utils.logger import get_logger

logger = get_logger(__name__)

class PasswordSetupTool:
    """密码设置工具"""
    
    def __init__(self):
        self.password_manager = SecurePasswordManager()
        
    async def interactive_setup(self):
        """交互式密码设置"""
        print("🔐 MT5密码管理工具")
        print("=" * 50)
        
        while True:
            print("\n选择操作:")
            print("1. 设置账户密码")
            print("2. 查看账户密码")
            print("3. 设置主密码")
            print("4. 批量导入密码")
            print("5. 测试密码存储")
            print("6. 清理密码缓存")
            print("0. 退出")
            
            choice = input("\n请选择 (0-6): ").strip()
            
            if choice == "0":
                break
            elif choice == "1":
                await self.set_account_password()
            elif choice == "2":
                await self.view_account_password()
            elif choice == "3":
                await self.set_master_password()
            elif choice == "4":
                await self.batch_import_passwords()
            elif choice == "5":
                await self.test_password_storage()
            elif choice == "6":
                await self.clear_password_cache()
            else:
                print("❌ 无效选择，请重新输入")
    
    async def set_account_password(self):
        """设置账户密码"""
        print("\n📝 设置账户密码")
        
        account_id = input("账户ID (如 ACC001): ").strip().upper()
        if not account_id:
            print("❌ 账户ID不能为空")
            return
            
        password = getpass.getpass("请输入密码: ")
        if not password:
            print("❌ 密码不能为空")
            return
            
        confirm_password = getpass.getpass("请确认密码: ")
        if password != confirm_password:
            print("❌ 两次输入的密码不一致")
            return
        
        # 选择存储类型
        print("\n选择存储类型:")
        print("1. 加密文件 (推荐)")
        print("2. 环境变量")
        print("3. 系统密钥环")
        print("4. HashiCorp Vault")
        print("5. AWS Secrets Manager")
        print("6. Azure Key Vault")
        
        storage_choice = input("请选择 (1-6): ").strip()
        storage_map = {
            "1": PasswordStorageType.ENCRYPTED_FILE,
            "2": PasswordStorageType.ENVIRONMENT,
            "3": PasswordStorageType.KEYRING,
            "4": PasswordStorageType.VAULT,
            "5": PasswordStorageType.AWS_SECRETS,
            "6": PasswordStorageType.AZURE_KEYVAULT
        }
        
        storage_type = storage_map.get(storage_choice, PasswordStorageType.ENCRYPTED_FILE)
        
        try:
            config = PasswordConfig(
                account_id=account_id,
                storage_type=storage_type,
                storage_config={"use_master_password": True} if storage_type == PasswordStorageType.ENCRYPTED_FILE else {},
                fallback_storage=PasswordStorageType.ENVIRONMENT
            )
            
            await self.password_manager.set_password(account_id, password, config)
            print(f"✅ 密码设置成功: {account_id} ({storage_type.value})")
            
        except Exception as e:
            print(f"❌ 密码设置失败: {e}")
    
    async def view_account_password(self):
        """查看账户密码"""
        print("\n👀 查看账户密码")
        
        account_id = input("账户ID (如 ACC001): ").strip().upper()
        if not account_id:
            print("❌ 账户ID不能为空")
            return
        
        try:
            password = await get_account_password(account_id)
            if password:
                # 出于安全考虑，只显示密码的前3位和后3位
                masked_password = password[:3] + "*" * (len(password) - 6) + password[-3:] if len(password) > 6 else "*" * len(password)
                print(f"✅ 账户 {account_id} 密码: {masked_password}")
                
                show_full = input("是否显示完整密码? (y/N): ").strip().lower()
                if show_full == 'y':
                    print(f"🔓 完整密码: {password}")
            else:
                print(f"❌ 未找到账户 {account_id} 的密码")
                
        except Exception as e:
            print(f"❌ 获取密码失败: {e}")
    
    async def set_master_password(self):
        """设置主密码"""
        print("\n🔑 设置主密码")
        print("主密码用于加密存储的所有账户密码")
        
        master_password = getpass.getpass("请输入主密码: ")
        if not master_password:
            print("❌ 主密码不能为空")
            return
            
        confirm_password = getpass.getpass("请确认主密码: ")
        if master_password != confirm_password:
            print("❌ 两次输入的主密码不一致")
            return
        
        # 设置环境变量
        os.environ['MT5_MASTER_PASSWORD'] = master_password
        print("✅ 主密码设置成功")
        print("💡 提示: 请将主密码保存到安全位置，或设置为系统环境变量")
        print("   Windows: set MT5_MASTER_PASSWORD=你的主密码")
        print("   Linux/Mac: export MT5_MASTER_PASSWORD=你的主密码")
    
    async def batch_import_passwords(self):
        """批量导入密码"""
        print("\n📂 批量导入密码")
        
        file_path = input("CSV文件路径 (格式: account_id,password): ").strip()
        if not file_path or not os.path.exists(file_path):
            print("❌ 文件不存在")
            return
        
        try:
            import csv
            success_count = 0
            error_count = 0
            
            with open(file_path, 'r', encoding='utf-8') as f:
                reader = csv.reader(f)
                for row_num, row in enumerate(reader, 1):
                    if len(row) != 2:
                        print(f"❌ 第{row_num}行格式错误，跳过")
                        error_count += 1
                        continue
                    
                    account_id, password = row[0].strip().upper(), row[1].strip()
                    if not account_id or not password:
                        print(f"❌ 第{row_num}行数据不完整，跳过")
                        error_count += 1
                        continue
                    
                    try:
                        await set_account_password(account_id, password)
                        print(f"✅ {account_id} 密码导入成功")
                        success_count += 1
                    except Exception as e:
                        print(f"❌ {account_id} 密码导入失败: {e}")
                        error_count += 1
            
            print(f"\n📊 导入结果: 成功 {success_count}, 失败 {error_count}")
            
        except Exception as e:
            print(f"❌ 批量导入失败: {e}")
    
    async def test_password_storage(self):
        """测试密码存储"""
        print("\n🧪 测试密码存储")
        
        test_account = "TEST_ACCOUNT"
        test_password = "test_password_123"
        
        try:
            # 测试设置密码
            await set_account_password(test_account, test_password)
            print("✅ 密码设置测试通过")
            
            # 测试获取密码
            retrieved_password = await get_account_password(test_account)
            if retrieved_password == test_password:
                print("✅ 密码获取测试通过")
            else:
                print("❌ 密码获取测试失败: 密码不匹配")
            
            # 清理测试数据
            print("🧹 清理测试数据...")
            
        except Exception as e:
            print(f"❌ 测试失败: {e}")
    
    async def clear_password_cache(self):
        """清理密码缓存"""
        print("\n🧹 清理密码缓存")
        
        # 这里可以添加清理内存中密码的逻辑
        print("✅ 密码缓存已清理")

async def main():
    """主函数"""
    tool = PasswordSetupTool()
    
    if len(sys.argv) > 1:
        # 命令行模式
        command = sys.argv[1]
        
        if command == "set" and len(sys.argv) >= 4:
            account_id = sys.argv[2].upper()
            password = sys.argv[3]
            await set_account_password(account_id, password)
            print(f"✅ 密码设置成功: {account_id}")
            
        elif command == "get" and len(sys.argv) >= 3:
            account_id = sys.argv[2].upper()
            password = await get_account_password(account_id)
            if password:
                print(f"✅ 账户 {account_id} 密码: {password}")
            else:
                print(f"❌ 未找到账户 {account_id} 的密码")
                
        elif command == "test":
            tool = PasswordSetupTool()
            await tool.test_password_storage()
            
        else:
            print("用法:")
            print("  python password_setup.py set <account_id> <password>")
            print("  python password_setup.py get <account_id>")
            print("  python password_setup.py test")
            print("  python password_setup.py  # 交互模式")
    else:
        # 交互模式
        await tool.interactive_setup()

if __name__ == "__main__":
    asyncio.run(main())