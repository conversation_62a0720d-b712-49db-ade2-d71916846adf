# 分布式MT5交易系统部署指南

## 概述

本指南介绍如何部署跨主机的分布式MT5交易系统，实现真正的跨地域、跨主机的主从账户交易。

## 架构图

```
┌─────────────────────────────────────────────────────────────┐
│                    NATS JetStream 集群                      │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐         │
│  │   Node 1    │  │   Node 2    │  │   Node 3    │         │
│  │************ │  │************ │  │************ │         │
│  └─────────────┘  └─────────────┘  └─────────────┘         │
└─────────────────────────────────────────────────────────────┘
         ↑                    ↑                    ↑
         │                    │                    │
    ┌────┴────┐         ┌────┴────┐         ┌────┴────┐
    │ Host A  │         │ Host B  │         │ Host C  │
    │ Master  │         │ Slaves  │         │ Slaves  │
    │ ACC001  │         │ ACC002  │         │ ACC003  │
    └─────────┘         └─────────┘         └─────────┘
```

## 前置要求

### 硬件要求
- **CPU**: 4核心以上
- **内存**: 8GB以上
- **存储**: 100GB以上SSD
- **网络**: 稳定的互联网连接，延迟<50ms

### 软件要求
- **操作系统**: Windows 10/11 或 Windows Server 2019/2022
- **Python**: 3.10+
- **MetaTrader 5**: 最新版本
- **Docker**: 20.10+ (用于NATS和Redis)

## 部署步骤

### 1. 基础设施部署

#### 1.1 部署NATS JetStream集群

```bash
# 在每个基础设施节点上运行
docker run -d --name nats-js \
  -p 4222:4222 \
  -p 8222:8222 \
  -p 6222:6222 \
  -v /data/nats:/data \
  nats:latest \
  -js \
  -sd /data \
  --cluster_name MT5_CLUSTER \
  --cluster nats://0.0.0.0:6222 \
  --routes nats://************:6222,nats://************:6222,nats://************:6222
```

#### 1.2 部署Redis Sentinel集群

创建 `docker-compose.yml`:

```yaml
version: '3.8'
services:
  redis-master:
    image: redis:7-alpine
    command: redis-server --appendonly yes
    volumes:
      - redis-data:/data
    ports:
      - "6379:6379"

  redis-sentinel:
    image: redis:7-alpine
    command: redis-sentinel /etc/redis/sentinel.conf
    volumes:
      - ./sentinel.conf:/etc/redis/sentinel.conf
    ports:
      - "26379:26379"
    depends_on:
      - redis-master

volumes:
  redis-data:
```

### 2. 交易主机配置

#### 2.1 安装Python依赖

```bash
# 克隆项目
git clone https://github.com/your-repo/mt5-python.git
cd mt5-python

# 创建虚拟环境
python -m venv venv
venv\Scripts\activate

# 安装依赖
pip install -r requirements.txt
```

#### 2.2 配置环境变量

创建 `.env` 文件：

```bash
# 主机配置
MT5_HOST_ID=host-001
MT5_CONFIG_PATH=config/

# 账户密码
ACC001_PASSWORD=your_master_password
ACC002_PASSWORD=your_slave_password

# 基础设施地址
NATS_SERVERS=nats://************:4222,nats://************:4222,nats://************:4222
REDIS_SENTINELS=************:26379,************:26379,************:26379
```

#### 2.3 配置系统文件

复制并修改配置文件：

```bash
# 复制示例配置
cp config/distributed_example.yaml config/system.yaml

# 根据实际情况修改配置
# - 更新host_id
# - 更新IP地址
# - 配置账户信息
```

### 3. 启动系统

#### 3.1 启动主机A (主账户)

```bash
# 设置主机ID
set MT5_HOST_ID=host-001

# 启动系统
python -m src.distributed_main
```

#### 3.2 启动主机B (从账户)

```bash
# 设置主机ID
set MT5_HOST_ID=host-002

# 修改配置文件中的账户为从账户
# 启动系统
python -m src.distributed_main
```

#### 3.3 启动主机C (从账户)

```bash
# 设置主机ID
set MT5_HOST_ID=host-003

# 修改配置文件中的账户为从账户
# 启动系统
python -m src.distributed_main
```

### 4. 创建跨主机配对

使用API或配置文件创建配对：

```python
# 通过API创建配对
import asyncio
from src.distributed_main import DistributedTradingSystem

async def create_pairing():
    system = DistributedTradingSystem("config/")
    await system.start()
    
    # 创建跨主机配对
    pairing_id = await system.create_cross_host_pairing(
        master_account="ACC001",  # 主机A上的主账户
        slave_account="ACC002",   # 主机B上的从账户
        strategy="proportional",
        lot_multiplier=1.0
    )
    
    print(f"配对创建成功: {pairing_id}")

asyncio.run(create_pairing())
```

## 网络配置

### 防火墙规则

```bash
# NATS端口
netsh advfirewall firewall add rule name="NATS Client" dir=in action=allow protocol=TCP localport=4222
netsh advfirewall firewall add rule name="NATS Cluster" dir=in action=allow protocol=TCP localport=6222
netsh advfirewall firewall add rule name="NATS Monitor" dir=in action=allow protocol=TCP localport=8222

# Redis端口
netsh advfirewall firewall add rule name="Redis" dir=in action=allow protocol=TCP localport=6379
netsh advfirewall firewall add rule name="Redis Sentinel" dir=in action=allow protocol=TCP localport=26379

# MT5监控端口
netsh advfirewall firewall add rule name="MT5 Monitor" dir=in action=allow protocol=TCP localport=8000-8100
```

### 网络延迟优化

1. **使用专线连接**: 确保主机间有稳定的专线连接
2. **QoS配置**: 为交易流量配置高优先级
3. **DNS优化**: 使用内网DNS解析

## 监控和维护

### 系统监控

```bash
# 查看系统状态
curl http://localhost:8000/api/status

# 查看跨主机延迟
curl http://localhost:8000/api/latency

# 查看配对状态
curl http://localhost:8000/api/pairings
```

### 日志监控

```bash
# 实时查看日志
tail -f logs/mt5_distributed.log

# 查看错误日志
grep "ERROR" logs/mt5_distributed.log
```

### 性能调优

1. **批量处理**: 调整批量大小和超时时间
2. **连接池**: 优化MT5连接池大小
3. **消息压缩**: 启用大消息压缩
4. **缓存策略**: 优化Redis缓存配置

## 故障处理

### 常见问题

1. **连接超时**: 检查网络连接和防火墙
2. **消息丢失**: 检查NATS集群状态
3. **账户登录失败**: 验证MT5账户信息
4. **跨主机延迟高**: 检查网络质量

### 故障恢复

1. **主机故障**: 系统自动切换到备用主机
2. **网络中断**: 自动重连和消息重发
3. **服务重启**: 状态自动恢复

## 安全建议

1. **网络隔离**: 使用VPN或专线
2. **访问控制**: 限制IP访问范围
3. **数据加密**: 启用端到端加密
4. **定期备份**: 备份配置和日志

## 性能基准

- **信号延迟**: <10ms (同地域)
- **跨主机延迟**: <50ms (跨地域)
- **吞吐量**: >1000 信号/秒
- **可用性**: 99.9%

## 扩展部署

### 添加新主机

1. 部署基础软件
2. 配置网络连接
3. 修改配置文件
4. 启动系统
5. 创建新配对

### 负载均衡

1. 使用多个主账户分散负载
2. 配置从账户分组
3. 实现智能路由

## Docker部署配置

### NATS集群配置

创建 `docker-compose-nats.yml`:

```yaml
version: '3.8'
services:
  nats-1:
    image: nats:latest
    command: >
      -js -sd /data
      --cluster_name MT5_CLUSTER
      --cluster nats://0.0.0.0:6222
      --routes nats://nats-2:6222,nats://nats-3:6222
    ports:
      - "4222:4222"
      - "8222:8222"
      - "6222:6222"
    volumes:
      - nats1-data:/data
    networks:
      - mt5-network

  nats-2:
    image: nats:latest
    command: >
      -js -sd /data
      --cluster_name MT5_CLUSTER
      --cluster nats://0.0.0.0:6222
      --routes nats://nats-1:6222,nats://nats-3:6222
    ports:
      - "4223:4222"
      - "8223:8222"
      - "6223:6222"
    volumes:
      - nats2-data:/data
    networks:
      - mt5-network

  nats-3:
    image: nats:latest
    command: >
      -js -sd /data
      --cluster_name MT5_CLUSTER
      --cluster nats://0.0.0.0:6222
      --routes nats://nats-1:6222,nats://nats-2:6222
    ports:
      - "4224:4222"
      - "8224:8222"
      - "6224:6222"
    volumes:
      - nats3-data:/data
    networks:
      - mt5-network

volumes:
  nats1-data:
  nats2-data:
  nats3-data:

networks:
  mt5-network:
    driver: bridge
```

### Redis Sentinel配置

创建 `docker-compose-redis.yml`:

```yaml
version: '3.8'
services:
  redis-master:
    image: redis:7-alpine
    command: redis-server --appendonly yes --replica-announce-ip ************
    ports:
      - "6379:6379"
    volumes:
      - redis-master-data:/data
    networks:
      - mt5-network

  redis-slave-1:
    image: redis:7-alpine
    command: redis-server --slaveof redis-master 6379 --replica-announce-ip ************
    ports:
      - "6380:6379"
    volumes:
      - redis-slave1-data:/data
    networks:
      - mt5-network
    depends_on:
      - redis-master

  redis-sentinel-1:
    image: redis:7-alpine
    command: redis-sentinel /etc/redis/sentinel.conf
    ports:
      - "26379:26379"
    volumes:
      - ./sentinel.conf:/etc/redis/sentinel.conf
    networks:
      - mt5-network
    depends_on:
      - redis-master

volumes:
  redis-master-data:
  redis-slave1-data:

networks:
  mt5-network:
    external: true
```

## 技术支持

如有问题，请联系技术支持或查看详细文档。
