# 密码管理配置文件
# 支持多种安全存储方式和跨主机部署

# 全局配置
global:
  # 默认存储类型
  default_storage: "encrypted_file"
  default_fallback: "environment"
  
  # 主密码保护（可选）
  master_password_enabled: true
  master_password_env_var: "MT5_MASTER_PASSWORD"
  
  # 密码轮换
  password_rotation:
    enabled: false
    interval_days: 30
    notification_days: 7

# 账户密码配置
passwords:
  ACC001:
    storage_type: "encrypted_file"
    fallback_storage: "environment"
    storage_config:
      use_master_password: true
      file_path: "config/security/passwords.enc"
    rotation_enabled: false
    
  ACC002:
    storage_type: "encrypted_file"
    fallback_storage: "environment"
    storage_config:
      use_master_password: true
      file_path: "config/security/passwords.enc"
    rotation_enabled: false
    
  # 生产环境示例配置
  PROD_ACC001:
    storage_type: "vault"
    fallback_storage: "encrypted_file"
    storage_config:
      vault_url: "https://vault.company.com:8200"
      secret_path: "secret/trading/mt5"
      vault_token_env: "VAULT_TOKEN"
    rotation_enabled: true
    rotation_interval_days: 30
    
  # AWS部署示例
  AWS_ACC001:
    storage_type: "aws_secrets"
    fallback_storage: "environment"
    storage_config:
      region: "us-east-1"
      secret_prefix: "mt5-trading"
    rotation_enabled: true
    
  # Azure部署示例
  AZURE_ACC001:
    storage_type: "azure_keyvault"
    fallback_storage: "encrypted_file"
    storage_config:
      vault_url: "https://mt5-vault.vault.azure.net/"
      secret_prefix: "mt5"
    rotation_enabled: true

# 存储后端配置
storage_backends:
  encrypted_file:
    encryption_algorithm: "Fernet"
    key_derivation: "PBKDF2"
    iterations: 100000
    file_permissions: "0600"
    backup_enabled: true
    backup_count: 3
    
  keyring:
    service_name: "MT5TradingSystem"
    username_format: "{account_id}"
    
  vault:
    default_mount_point: "secret"
    auth_method: "token"  # token, userpass, aws, etc.
    verify_ssl: true
    timeout: 30
    
  aws_secrets:
    kms_key_id: null  # 使用默认KMS密钥
    version_stage: "AWSCURRENT"
    
  azure_keyvault:
    authentication: "default_credential"  # default_credential, service_principal
    
  redis_encrypted:
    ssl: true
    socket_timeout: 5
    connection_pool_max_connections: 10

# 安全策略
security_policies:
  # 密码要求
  password_requirements:
    min_length: 12
    require_uppercase: true
    require_lowercase: true
    require_numbers: true
    require_special_chars: true
    
  # 访问控制
  access_control:
    max_failed_attempts: 3
    lockout_duration_minutes: 15
    audit_log_enabled: true
    
  # 传输安全
  transport_security:
    tls_min_version: "1.2"
    certificate_validation: true
    
  # 内存保护
  memory_protection:
    clear_passwords_after_use: true
    disable_swap: true
    memory_lock: true

# 监控和审计
monitoring:
  # 审计日志
  audit_logging:
    enabled: true
    log_level: "INFO"
    log_file: "logs/security/password_audit.log"
    log_rotation: true
    max_size_mb: 100
    backup_count: 10
    
  # 指标收集
  metrics:
    enabled: true
    password_access_count: true
    failed_access_count: true
    storage_backend_latency: true
    
  # 告警
  alerts:
    enabled: true
    failed_access_threshold: 5
    storage_backend_failure: true
    password_rotation_due: true

# 备份和恢复
backup:
  # 自动备份
  auto_backup:
    enabled: true
    interval_hours: 24
    retention_days: 30
    backup_location: "backups/security"
    encryption_enabled: true
    
  # 灾难恢复
  disaster_recovery:
    multi_region_backup: false
    cross_cloud_backup: false
    recovery_test_interval_days: 90

# 部署环境配置
environments:
  development:
    storage_type: "encrypted_file"
    master_password_required: false
    audit_logging: false
    
  testing:
    storage_type: "encrypted_file"
    master_password_required: true
    audit_logging: true
    password_rotation: false
    
  staging:
    storage_type: "vault"
    master_password_required: true
    audit_logging: true
    password_rotation: true
    
  production:
    storage_type: "vault"
    fallback_storage: "aws_secrets"
    master_password_required: true
    audit_logging: true
    password_rotation: true
    monitoring_alerts: true