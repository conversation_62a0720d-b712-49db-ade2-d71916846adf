# 混合消息队列配置
# 支持NATS + Redis Streams双写备份和本地降级

queue_manager:
  # 主后端配置（优先使用）
  primary_backend: "nats"
  
  # 备份后端列表（按优先级排序）
  backup_backends:
    - "redis_streams"
  
  # 本地降级支持
  local_fallback: true
  
  # 双写配置
  enable_dual_write: true      # 启用双写备份
  dual_write_async: true       # 异步双写（不阻塞主路径）
  ignore_backup_failures: true # 忽略备份写入失败
  
  # 故障转移配置
  failover_strategy: "circuit_breaker"  # immediate, delayed, manual, circuit_breaker
  health_check_interval: 5.0   # 健康检查间隔（秒）
  failure_threshold: 3         # 连续失败次数阈值
  recovery_threshold: 2        # 恢复检查次数
  circuit_breaker_timeout: 30.0 # 熔断器超时时间（秒）
  
  # 性能阈值
  max_latency_ms: 100.0       # 最大延迟阈值
  max_error_rate: 0.05        # 最大错误率阈值（5%）
  sync_timeout: 2.0           # 同步超时时间
  
  # NATS配置
  nats_servers:
    - "nats://localhost:4222"
    - "nats://localhost:4223"  # 开发环境备用
  nats_user: null
  nats_password: null
  nats_token: null
  
  # Redis配置
  redis_host: "localhost"
  redis_port: 6379
  redis_db: 0
  redis_password: null
  
  # 工作器配置
  worker_count: 4  # 本地队列工作器数量

# 路由规则配置
messaging:
  routing:
    # 路由规则
    rules:
      # 系统级关键消息
      - source: "coordinator"
        target: "*"
        host_id: "*" 
        priority: "SYSTEM_CRITICAL"
        route_type: "broadcast"
        enabled: true
        retry_count: 1
        timeout_seconds: 15.0
        weight: 1
      
      # 监控器到执行器（本地路由）
      - source: "monitor"
        target: "executor"
        host_id: "*"
        priority: "RISK_COMMAND"
        route_type: "local"
        enabled: true
        retry_count: 2
        timeout_seconds: 10.0
        weight: 1
      
      # 跨主机信号路由
      - source: "*"
        target: "*"
        host_id: "*"
        priority: "SIGNAL_EXECUTION"
        route_type: "remote"
        enabled: true
        retry_count: 3
        timeout_seconds: 30.0
        weight: 1
      
      # 实时查询路由
      - source: "api"
        target: "*"
        host_id: "*"
        priority: "REALTIME_QUERY"
        route_type: "remote"
        enabled: true
        retry_count: 2
        timeout_seconds: 20.0
        weight: 1
      
      # 后台任务路由
      - source: "scheduler"
        target: "*"
        host_id: "*"
        priority: "BACKGROUND_TASK"
        route_type: "local"
        enabled: true
        retry_count: 1
        timeout_seconds: 60.0
        weight: 1
    
    # API速率限制配置
    rate_limits:
      # 默认速率限制（所有账户）
      - account_pattern: "*"
        max_calls_per_second: 80
        burst_size: 10
        window_size: 1
        cooldown_period: 5
      
      # VIP账户速率限制
      - account_pattern: "VIP_*"
        max_calls_per_second: 120
        burst_size: 15
        window_size: 1
        cooldown_period: 3
      
      # 测试账户速率限制
      - account_pattern: "TEST_*"
        max_calls_per_second: 20
        burst_size: 5
        window_size: 1
        cooldown_period: 10

# 监控和告警配置
monitoring:
  # 队列深度告警
  queue_depth_warning: 1000
  queue_depth_critical: 5000
  
  # 延迟告警
  latency_warning_ms: 50
  latency_critical_ms: 200
  
  # 错误率告警
  error_rate_warning: 0.02  # 2%
  error_rate_critical: 0.10 # 10%
  
  # 故障转移告警
  alert_on_failover: true
  alert_on_backend_degraded: true
  
  # 指标收集间隔
  metrics_collection_interval: 10  # 秒

# 日志配置
logging:
  # 队列管理器日志级别
  queue_manager_level: "INFO"
  
  # 路由器日志级别  
  router_level: "INFO"
  
  # 详细调试（开发模式）
  debug_mode: false
  
  # 记录消息内容（调试用，生产环境应关闭）
  log_message_content: false