# MT5 交易复制系统 Docker 镜像
FROM python:3.9-slim

# 设置环境变量
ENV PYTHONUNBUFFERED=1 \
    PYTHONDONTWRITEBYTECODE=1 \
    PYTHONPATH=/app \
    MT5_CONFIG_PATH=/app/config/optimized_system.yaml

# 设置工作目录
WORKDIR /app

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    gcc \
    g++ \
    curl \
    procps \
    net-tools \
    iputils-ping \
    dos2unix \
    && rm -rf /var/lib/apt/lists/*

# 复制Docker专用依赖文件（不包含MetaTrader5）
COPY requirements-docker.txt requirements.txt

# 安装Python依赖
RUN pip install --no-cache-dir --upgrade pip \
    && pip install --no-cache-dir -r requirements.txt

# 创建模拟的MetaTrader5模块来解决依赖问题
RUN echo "# Mock MetaTrader5 module for Docker environment" > /usr/local/lib/python3.9/site-packages/MetaTrader5.py && \
    echo "# Constants" >> /usr/local/lib/python3.9/site-packages/MetaTrader5.py && \
    echo "TRADE_ACTION_DEAL = 1" >> /usr/local/lib/python3.9/site-packages/MetaTrader5.py && \
    echo "ORDER_TYPE_BUY = 0" >> /usr/local/lib/python3.9/site-packages/MetaTrader5.py && \
    echo "ORDER_TYPE_SELL = 1" >> /usr/local/lib/python3.9/site-packages/MetaTrader5.py && \
    echo "ORDER_FILLING_FOK = 0" >> /usr/local/lib/python3.9/site-packages/MetaTrader5.py && \
    echo "ORDER_FILLING_IOC = 1" >> /usr/local/lib/python3.9/site-packages/MetaTrader5.py && \
    echo "ORDER_FILLING_RETURN = 2" >> /usr/local/lib/python3.9/site-packages/MetaTrader5.py && \
    echo "def initialize(): return True" >> /usr/local/lib/python3.9/site-packages/MetaTrader5.py && \
    echo "def shutdown(): pass" >> /usr/local/lib/python3.9/site-packages/MetaTrader5.py && \
    echo "def login(login, password, server): return True" >> /usr/local/lib/python3.9/site-packages/MetaTrader5.py && \
    echo "def account_info(): return None" >> /usr/local/lib/python3.9/site-packages/MetaTrader5.py && \
    echo "def positions_get(): return []" >> /usr/local/lib/python3.9/site-packages/MetaTrader5.py && \
    echo "def orders_get(): return []" >> /usr/local/lib/python3.9/site-packages/MetaTrader5.py && \
    echo "def order_send(request): return type('Result', (), {'retcode': 10009, 'deal': 0, 'order': 0})()" >> /usr/local/lib/python3.9/site-packages/MetaTrader5.py && \
    echo "def symbol_info(symbol): return None" >> /usr/local/lib/python3.9/site-packages/MetaTrader5.py && \
    echo "def symbol_info_tick(symbol): return None" >> /usr/local/lib/python3.9/site-packages/MetaTrader5.py

# 复制源代码
COPY src/ ./src/
COPY config/ ./config/
COPY scripts/ ./scripts/
COPY main.py ./

# 启动脚本和配置文件
COPY docker/entrypoint.py /entrypoint.py
COPY docker/deployment-config.yaml /app/docker/deployment-config.yaml
RUN chmod +x /entrypoint.py

# 创建数据目录
RUN mkdir -p /app/data /app/logs

# 创建非root用户
RUN groupadd -r mt5user && useradd -r -g mt5user mt5user \
    && chown -R mt5user:mt5user /app

# 切换到非root用户
USER mt5user

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD python scripts/health_check.py --config $MT5_CONFIG_PATH || exit 1

# 暴露端口
EXPOSE 8000 9090

# 设置入口点
ENTRYPOINT ["python", "/entrypoint.py"]
CMD ["api"]