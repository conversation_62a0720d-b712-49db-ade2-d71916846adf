#!/usr/bin/env python3
"""
MT5连接池管理器
高效管理MT5连接，提供连接复用和负载均衡
集成配置中心，支持分布式环境
"""
import asyncio
import time
from typing import Dict, List, Optional, Any, Callable
from dataclasses import dataclass
from enum import Enum
from contextlib import asynccontextmanager

from ..utils.logger import get_logger
from ..utils.metrics import get_metrics_collector

logger = get_logger(__name__)
metrics = get_metrics_collector()


@dataclass
class BatchCommand:
    """批量命令结构"""
    command_id: str
    command_type: str  # 'order_send', 'positions_get', 'history_orders_get', etc.
    parameters: Dict[str, Any]
    priority: int = 1  # 1=high, 2=medium, 3=low
    timeout: float = 30.0
    retry_count: int = 0
    max_retries: int = 3


@dataclass 
class BatchResult:
    """批量结果结构"""
    command_id: str
    success: bool
    result: Any = None
    error: Optional[str] = None
    execution_time: float = 0.0
    retry_count: int = 0


class ConnectionStatus(Enum):
    """连接状态"""
    IDLE = "idle"           # 空闲
    BUSY = "busy"           # 忙碌
    ERROR = "error"         # 错误
    CLOSED = "closed"       # 已关闭


@dataclass
class ConnectionInfo:
    """连接信息"""
    connection_id: str
    account_id: str
    server: str
    login: int
    created_at: float
    last_used_at: float
    use_count: int
    status: ConnectionStatus
    connection_object: Any  # 实际的MT5连接对象
    error_count: int = 0
    max_errors: int = 3


class MT5ConnectionPool:
    """MT5连接池"""
    
    def __init__(self, max_connections: int = 10, max_idle_time: int = 300):
        """
        初始化连接池
        """
        # 从配置中心获取配置（如果可用）
        try:
            from ..config.config_center import get_config
            self.max_connections = get_config("connection_pool.max_connections", max_connections)
            self.max_idle_time = get_config("connection_pool.max_idle_time", max_idle_time)
        except ImportError:
            self.max_connections = max_connections
            self.max_idle_time = max_idle_time
        
        # 连接池
        self._connections: Dict[str, ConnectionInfo] = {}
        self._idle_connections: asyncio.Queue = asyncio.Queue(maxsize=self.max_connections)
        self._busy_connections: Dict[str, ConnectionInfo] = {}
        
        # 锁
        self._pool_lock = asyncio.Lock()
        self._connection_locks: Dict[str, asyncio.Lock] = {}
        
        # 统计信息
        self._stats = {
            'total_created': 0,
            'total_destroyed': 0,
            'total_borrowed': 0,
            'total_returned': 0,
            'current_active': 0,
            'current_idle': 0,
            'peak_connections': 0
        }
        
        # 连接工厂
        self._connection_factory: Optional[Callable] = None
        self._connection_validator: Optional[Callable] = None
        
        # 清理任务
        self._cleanup_task: Optional[asyncio.Task] = None
        self._running = False
        
        # 批量执行配置
        self.batch_size = 50
        self.batch_timeout = 5.0  # 秒
        self.command_queue = asyncio.Queue()
        self.batch_processor_running = False
        
        logger.info(f"✅ MT5连接池初始化完成 (最大连接数: {max_connections})")
    
    def set_connection_factory(self, factory: Callable):
        """设置连接工厂函数"""
        self._connection_factory = factory
        logger.info("✅ 连接工厂已设置")
    
    def set_connection_validator(self, validator: Callable):
        """设置连接验证函数"""
        self._connection_validator = validator
        logger.info("✅ 连接验证器已设置")
    
    async def start(self):
        """启动连接池"""
        if self._running:
            logger.warning("⚠️ 连接池已在运行")
            return
        
        self._running = True
        
        # 启动清理任务
        self._cleanup_task = asyncio.create_task(self._cleanup_worker())
        
        # 启动批量处理器
        self.batch_processor_running = True
        self._batch_processor_task = asyncio.create_task(self._batch_processor())
        
        logger.info("✅ 连接池已启动")
    
    async def stop(self):
        """停止连接池"""
        if not self._running:
            return
        
        self._running = False
        self.batch_processor_running = False
        
        # 停止清理任务
        if self._cleanup_task:
            self._cleanup_task.cancel()
            try:
                await self._cleanup_task
            except asyncio.CancelledError:
                pass
        
        # 停止批量处理器
        if hasattr(self, '_batch_processor_task') and self._batch_processor_task:
            self._batch_processor_task.cancel()
            try:
                await self._batch_processor_task
            except asyncio.CancelledError:
                pass
        
        # 关闭所有连接
        await self._close_all_connections()
        
        logger.info("✅ 连接池已停止")
    
    async def _batch_processor(self):
        """批量处理器 - 收集批量命令并执行"""
        logger.info("🔄 启动批量命令处理器")
        
        while self.batch_processor_running:
            try:
                commands_batch = []
                batch_start = time.time()
                
                # 收集批量命令直到达到批次大小或超时
                while len(commands_batch) < self.batch_size:
                    try:
                        remaining_time = self.batch_timeout - (time.time() - batch_start)
                        if remaining_time <= 0:
                            break
                        
                        command = await asyncio.wait_for(
                            self.command_queue.get(),
                            timeout=remaining_time
                        )
                        commands_batch.append(command)
                        
                    except asyncio.TimeoutError:
                        break
                    except Exception as e:
                        logger.error(f"批量命令收集错误: {e}")
                        break
                
                # 如果有命令需要处理
                if commands_batch:
                    await self._execute_command_batch(commands_batch)
                
                # 如果队列为空，短暂休眠
                if self.command_queue.empty():
                    await asyncio.sleep(0.01)
                    
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"批量处理器错误: {e}")
                await asyncio.sleep(0.1)
        
        logger.info("🛑 批量命令处理器停止")
    
    async def _execute_command_batch(self, commands_batch: List[BatchCommand]):
        """执行批量命令"""
        start_time = time.time()
        
        # 按优先级排序
        commands_batch.sort(key=lambda cmd: cmd.priority)
        
        # 分组执行（按命令类型分组以优化性能）
        command_groups = {}
        for command in commands_batch:
            cmd_type = command.command_type
            if cmd_type not in command_groups:
                command_groups[cmd_type] = []
            command_groups[cmd_type].append(command)
        
        # 并发执行不同类型的命令组
        group_tasks = []
        for cmd_type, commands in command_groups.items():
            task = asyncio.create_task(self._execute_command_group(cmd_type, commands))
            group_tasks.append(task)
        
        # 等待所有组执行完成
        await asyncio.gather(*group_tasks, return_exceptions=True)
        
        execution_time = time.time() - start_time
        logger.debug(f"批量执行完成: {len(commands_batch)} 命令, 用时 {execution_time:.3f}s")
        
        # 更新指标
        metrics.increment_counter("connection_pool_batch_executed_total")
        metrics.set_gauge("connection_pool_batch_execution_time", execution_time)
    
    async def _execute_command_group(self, command_type: str, commands: List[BatchCommand]):
        """执行同类型命令组"""
        for command in commands:
            try:
                # 获取连接
                connection = await self.borrow_connection(
                    account_id=command.parameters.get('account_id', 'default'),
                    server=command.parameters.get('server'),
                    login=command.parameters.get('login')
                )
                
                if not connection:
                    # 设置错误结果
                    self._set_command_result(command, False, None, "无法获取连接")
                    continue
                
                try:
                    # 执行命令 - 调用实际的MT5连接对象方法
                    result = await self._execute_single_command(connection, command)
                    self._set_command_result(command, True, result, None)
                    
                except Exception as e:
                    # 命令执行失败，可能需要重试
                    if command.retry_count < command.max_retries:
                        command.retry_count += 1
                        await self.command_queue.put(command)  # 重新加入队列
                        logger.debug(f"命令重试: {command.command_id} (第{command.retry_count}次)")
                    else:
                        self._set_command_result(command, False, None, str(e))
                        logger.error(f"命令执行失败: {command.command_id} - {e}")
                
                finally:
                    # 归还连接
                    await self.return_connection(connection.connection_id)
                    
            except Exception as e:
                self._set_command_result(command, False, None, f"命令处理异常: {e}")
                logger.error(f"命令处理异常: {command.command_id} - {e}")
    
    async def _execute_single_command(self, connection: ConnectionInfo, command: BatchCommand) -> Any:
        """执行单个命令 - 调用实际MT5连接对象的方法"""
        command_start = time.time()
        
        try:
            mt5_connection = connection.connection_object
            
            # 这里调用实际的MT5连接对象方法，而不是模拟
            # 假设MT5连接对象有统一的execute_command方法
            if hasattr(mt5_connection, 'execute_command'):
                result = await mt5_connection.execute_command(
                    command.command_type, 
                    command.parameters
                )
            elif hasattr(mt5_connection, command.command_type):
                # 直接调用对应的方法
                method = getattr(mt5_connection, command.command_type)
                result = await method(**command.parameters)
            else:
                # 回退到基本的方法调用
                result = await self._call_mt5_method(mt5_connection, command)
            
            execution_time = time.time() - command_start
            logger.debug(f"命令执行完成: {command.command_id} ({command.command_type}) - {execution_time:.3f}s")
            
            return result
            
        except Exception as e:
            execution_time = time.time() - command_start
            logger.error(f"命令执行失败: {command.command_id} ({command.command_type}) - {execution_time:.3f}s - {e}")
            raise
    
    async def _call_mt5_method(self, mt5_connection, command: BatchCommand) -> Any:
        """调用MT5方法的通用接口"""
        # 这里实现对实际MT5 API的调用逻辑
        # 具体实现取决于你使用的MT5库（如MetaTrader5库）
        
        # 示例：如果使用MetaTrader5库
        try:
            if command.command_type == 'order_send':
                # 调用实际的MT5订单发送
                import MetaTrader5 as mt5
                result = mt5.order_send(command.parameters)
                return result
            elif command.command_type == 'positions_get':
                # 调用实际的MT5持仓查询
                import MetaTrader5 as mt5
                symbol = command.parameters.get('symbol')
                result = mt5.positions_get(symbol=symbol) if symbol else mt5.positions_get()
                return result
            elif command.command_type == 'history_orders_get':
                # 调用实际的MT5历史订单查询
                import MetaTrader5 as mt5
                date_from = command.parameters.get('date_from')
                date_to = command.parameters.get('date_to')
                result = mt5.history_orders_get(date_from, date_to)
                return result
            elif command.command_type == 'account_info':
                # 调用实际的MT5账户信息查询
                import MetaTrader5 as mt5
                result = mt5.account_info()
                return result._asdict() if result else None
            else:
                raise ValueError(f"不支持的命令类型: {command.command_type}")
                
        except ImportError:
            # 如果MetaTrader5库不可用，记录警告并返回空结果
            logger.warning(f"MetaTrader5库不可用，无法执行命令: {command.command_type}")
            return None
        except Exception as e:
            logger.error(f"MT5方法调用失败: {command.command_type} - {e}")
            raise
    
    def _set_command_result(self, command: BatchCommand, success: bool, result: Any, error: str):
        """设置命令执行结果"""
        batch_result = BatchResult(
            command_id=command.command_id,
            success=success,
            result=result,
            error=error,
            execution_time=time.time(),
            retry_count=command.retry_count
        )
        
        # 存储结果供调用者获取
        if not hasattr(self, '_command_results'):
            self._command_results = {}
        
        self._command_results[command.command_id] = batch_result
        
        logger.debug(f"命令结果: {command.command_id} - 成功: {success}")
    
    async def batch_execute_commands(self, commands: List[BatchCommand]) -> List[BatchResult]:
        """批量执行命令的公共接口"""
        if not self._running or not self.batch_processor_running:
            raise RuntimeError("连接池未启动或批量处理器未运行")
        
        # 为每个命令生成唯一ID（如果没有）
        for command in commands:
            if not command.command_id:
                command.command_id = f"cmd_{int(time.time() * 1000000)}_{id(command)}"
        
        # 将命令添加到队列
        for command in commands:
            await self.command_queue.put(command)
        
        # 等待所有命令执行完成
        results = []
        timeout_per_command = 30.0  # 每个命令最大等待时间
        
        for command in commands:
            start_wait = time.time()
            
            while True:
                # 检查结果是否可用
                if (hasattr(self, '_command_results') and 
                    command.command_id in self._command_results):
                    result = self._command_results.pop(command.command_id)
                    results.append(result)
                    break
                
                # 检查超时
                if time.time() - start_wait > timeout_per_command:
                    error_result = BatchResult(
                        command_id=command.command_id,
                        success=False,
                        error="命令执行超时",
                        execution_time=time.time() - start_wait
                    )
                    results.append(error_result)
                    break
                
                # 短暂等待
                await asyncio.sleep(0.01)
        
        return results
    
    @asynccontextmanager
    async def get_connection(self, account_id: str, server: str = None, login: int = None):
        """
        获取连接（上下文管理器）
        
        Args:
            account_id: 账户ID
            server: 服务器
            login: 登录号
        """
        connection = None
        try:
            connection = await self.borrow_connection(account_id, server, login)
            yield connection
        finally:
            if connection:
                await self.return_connection(connection.connection_id)
    
    async def borrow_connection(self, account_id: str, server: str = None, 
                               login: int = None) -> Optional[ConnectionInfo]:
        """
        借用连接
        
        Args:
            account_id: 账户ID
            server: 服务器
            login: 登录号
            
        Returns:
            连接信息或None
        """
        async with self._pool_lock:
            try:
                # 1. 尝试从空闲连接中找到匹配的连接
                connection = await self._find_idle_connection(account_id, server, login)
                
                if connection:
                    # 验证连接
                    if await self._validate_connection(connection):
                        # 移动到忙碌连接池
                        await self._move_to_busy(connection)
                        
                        # 更新统计
                        self._stats['total_borrowed'] += 1
                        
                        logger.debug(f"📤 借用现有连接: {connection.connection_id}")
                        return connection
                    else:
                        # 连接无效，销毁它
                        await self._destroy_connection(connection)
                
                # 2. 创建新连接
                if len(self._connections) < self.max_connections:
                    connection = await self._create_connection(account_id, server, login)
                    
                    if connection:
                        # 直接移动到忙碌连接池
                        await self._move_to_busy(connection)
                        
                        # 更新统计
                        self._stats['total_borrowed'] += 1
                        
                        logger.debug(f"📤 借用新连接: {connection.connection_id}")
                        return connection
                
                # 3. 连接池已满，等待空闲连接
                logger.warning(f"⚠️ 连接池已满，等待空闲连接 (账户: {account_id})")
                
                # 等待空闲连接（带超时）
                try:
                    connection = await asyncio.wait_for(
                        self._wait_for_idle_connection(account_id, server, login),
                        timeout=30.0
                    )
                    
                    if connection:
                        await self._move_to_busy(connection)
                        self._stats['total_borrowed'] += 1
                        return connection
                        
                except asyncio.TimeoutError:
                    logger.error(f"❌ 等待连接超时 (账户: {account_id})")
                    metrics.increment_counter("connection_pool_timeout_total")
                
                return None
                
            except Exception as e:
                logger.error(f"❌ 借用连接失败: {e}")
                metrics.increment_counter("connection_pool_borrow_error_total")
                return None
    
    async def return_connection(self, connection_id: str):
        """
        归还连接
        
        Args:
            connection_id: 连接ID
        """
        async with self._pool_lock:
            try:
                connection = self._busy_connections.get(connection_id)
                
                if not connection:
                    logger.warning(f"⚠️ 尝试归还不存在的连接: {connection_id}")
                    return
                
                # 验证连接状态
                if await self._validate_connection(connection):
                    # 移动到空闲连接池
                    await self._move_to_idle(connection)
                    
                    # 更新统计
                    self._stats['total_returned'] += 1
                    
                    logger.debug(f"📥 归还连接: {connection_id}")
                else:
                    # 连接无效，销毁它
                    await self._destroy_connection(connection)
                    logger.debug(f"🗑️ 销毁无效连接: {connection_id}")
                
            except Exception as e:
                logger.error(f"❌ 归还连接失败: {e}")
                metrics.increment_counter("connection_pool_return_error_total")
    
    async def _find_idle_connection(self, account_id: str, server: str = None, 
                                   login: int = None) -> Optional[ConnectionInfo]:
        """查找匹配的空闲连接"""
        try:
            # 从空闲队列中查找匹配的连接
            temp_connections = []
            
            while not self._idle_connections.empty():
                connection = await self._idle_connections.get()
                
                # 检查是否匹配
                if (connection.account_id == account_id and
                    (server is None or connection.server == server) and
                    (login is None or connection.login == login)):
                    
                    # 找到匹配的连接，将其他连接放回队列
                    for temp_conn in temp_connections:
                        await self._idle_connections.put(temp_conn)
                    
                    return connection
                else:
                    temp_connections.append(connection)
            
            # 没找到匹配的连接，将所有连接放回队列
            for temp_conn in temp_connections:
                await self._idle_connections.put(temp_conn)
            
            return None
            
        except Exception as e:
            logger.error(f"❌ 查找空闲连接失败: {e}")
            return None
    
    async def _create_connection(self, account_id: str, server: str = None, 
                                login: int = None) -> Optional[ConnectionInfo]:
        """创建新连接"""
        try:
            if not self._connection_factory:
                logger.error("❌ 连接工厂未设置")
                return None
            
            # 生成连接ID
            connection_id = f"{account_id}_{int(time.time() * 1000000)}"
            
            # 使用工厂创建连接
            connection_object = await self._connection_factory(account_id, server, login)
            
            if not connection_object:
                logger.error(f"❌ 连接工厂返回空连接 (账户: {account_id})")
                return None
            
            # 创建连接信息
            connection_info = ConnectionInfo(
                connection_id=connection_id,
                account_id=account_id,
                server=server or "unknown",
                login=login or 0,
                created_at=time.time(),
                last_used_at=time.time(),
                use_count=0,
                status=ConnectionStatus.IDLE,
                connection_object=connection_object
            )
            
            # 添加到连接池
            self._connections[connection_id] = connection_info
            self._connection_locks[connection_id] = asyncio.Lock()
            
            # 更新统计
            self._stats['total_created'] += 1
            self._stats['current_active'] += 1
            self._stats['peak_connections'] = max(
                self._stats['peak_connections'], 
                len(self._connections)
            )
            
            # 更新指标
            metrics.increment_counter("connection_pool_created_total")
            metrics.set_gauge("connection_pool_active_connections", len(self._connections))
            
            logger.info(f"✅ 创建新连接: {connection_id} (账户: {account_id})")
            return connection_info
            
        except Exception as e:
            logger.error(f"❌ 创建连接失败: {e}")
            metrics.increment_counter("connection_pool_create_error_total")
            return None
    
    async def _validate_connection(self, connection: ConnectionInfo) -> bool:
        """验证连接"""
        try:
            if not self._connection_validator:
                # 没有验证器，假设连接有效
                return True
            
            # 使用验证器验证连接
            is_valid = await self._connection_validator(connection.connection_object)
            
            if not is_valid:
                connection.error_count += 1
                logger.debug(f"⚠️ 连接验证失败: {connection.connection_id}")
                
                # 如果错误次数过多，标记为错误状态
                if connection.error_count >= connection.max_errors:
                    connection.status = ConnectionStatus.ERROR
            else:
                # 重置错误计数
                connection.error_count = 0
            
            return is_valid
            
        except Exception as e:
            logger.error(f"❌ 连接验证异常: {e}")
            connection.error_count += 1
            return False
    
    async def _move_to_busy(self, connection: ConnectionInfo):
        """移动连接到忙碌池"""
        connection.status = ConnectionStatus.BUSY
        connection.last_used_at = time.time()
        connection.use_count += 1
        
        self._busy_connections[connection.connection_id] = connection
        
        # 更新统计
        self._stats['current_idle'] = self._idle_connections.qsize()
        
        # 更新指标
        metrics.set_gauge("connection_pool_idle_connections", self._stats['current_idle'])
        metrics.set_gauge("connection_pool_busy_connections", len(self._busy_connections))
    
    async def _move_to_idle(self, connection: ConnectionInfo):
        """移动连接到空闲池"""
        connection.status = ConnectionStatus.IDLE
        connection.last_used_at = time.time()
        
        # 从忙碌池移除
        self._busy_connections.pop(connection.connection_id, None)
        
        # 添加到空闲池
        await self._idle_connections.put(connection)
        
        # 更新统计
        self._stats['current_idle'] = self._idle_connections.qsize()
        
        # 更新指标
        metrics.set_gauge("connection_pool_idle_connections", self._stats['current_idle'])
        metrics.set_gauge("connection_pool_busy_connections", len(self._busy_connections))
    
    async def _destroy_connection(self, connection: ConnectionInfo):
        """销毁连接"""
        try:
            # 从所有池中移除
            self._connections.pop(connection.connection_id, None)
            self._busy_connections.pop(connection.connection_id, None)
            self._connection_locks.pop(connection.connection_id, None)
            
            # 关闭连接对象
            if hasattr(connection.connection_object, 'close'):
                await connection.connection_object.close()
            elif hasattr(connection.connection_object, 'shutdown'):
                await connection.connection_object.shutdown()
            
            connection.status = ConnectionStatus.CLOSED
            
            # 更新统计
            self._stats['total_destroyed'] += 1
            self._stats['current_active'] -= 1
            
            # 更新指标
            metrics.increment_counter("connection_pool_destroyed_total")
            metrics.set_gauge("connection_pool_active_connections", len(self._connections))
            
            logger.debug(f"🗑️ 销毁连接: {connection.connection_id}")
            
        except Exception as e:
            logger.error(f"❌ 销毁连接失败: {e}")
    
    async def _wait_for_idle_connection(self, account_id: str, server: str = None, 
                                       login: int = None) -> Optional[ConnectionInfo]:
        """等待空闲连接"""
        # 这里可以实现更复杂的等待逻辑
        # 目前简单地等待任何空闲连接
        try:
            connection = await self._idle_connections.get()
            return connection
        except Exception:
            return None
    
    async def _cleanup_worker(self):
        """清理工作线程"""
        logger.info("🔄 启动连接池清理工作线程")
        
        while self._running:
            try:
                await asyncio.sleep(60)  # 每分钟清理一次
                await self._cleanup_idle_connections()
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"❌ 连接池清理异常: {e}")
        
        logger.info("🛑 连接池清理工作线程停止")
    
    async def _cleanup_idle_connections(self):
        """清理空闲连接"""
        current_time = time.time()
        connections_to_remove = []
        
        # 检查空闲连接
        temp_connections = []
        
        while not self._idle_connections.empty():
            connection = await self._idle_connections.get()
            
            # 检查是否超时
            if current_time - connection.last_used_at > self.max_idle_time:
                connections_to_remove.append(connection)
            else:
                temp_connections.append(connection)
        
        # 将未超时的连接放回队列
        for connection in temp_connections:
            await self._idle_connections.put(connection)
        
        # 销毁超时的连接
        for connection in connections_to_remove:
            await self._destroy_connection(connection)
            logger.info(f"🧹 清理超时连接: {connection.connection_id}")
    
    async def _close_all_connections(self):
        """关闭所有连接"""
        # 关闭所有连接
        for connection in list(self._connections.values()):
            await self._destroy_connection(connection)
        
        # 清空队列
        while not self._idle_connections.empty():
            try:
                await self._idle_connections.get()
            except:
                break
        
        self._busy_connections.clear()
        logger.info("🧹 所有连接已关闭")
    
    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        return {
            **self._stats,
            'current_busy': len(self._busy_connections),
            'current_idle': self._idle_connections.qsize(),
            'total_connections': len(self._connections),
            'max_connections': self.max_connections,
            'pool_utilization': len(self._connections) / self.max_connections * 100
        }


# 全局连接池实例
_connection_pools: Dict[str, MT5ConnectionPool] = {}

def get_connection_pool(pool_name: str = "default") -> MT5ConnectionPool:
    """获取连接池实例"""
    if pool_name not in _connection_pools:
        _connection_pools[pool_name] = MT5ConnectionPool()
    return _connection_pools[pool_name]
