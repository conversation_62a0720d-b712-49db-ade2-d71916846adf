#!/usr/bin/env python3
"""
端到端消息流测试
测试真实组件间的消息传递和通信
"""

import asyncio
import json
import os
import sys
import tempfile
import time
import yaml
from pathlib import Path
from typing import Dict, Any

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# 导入真实组件
try:
    from src.core.mt5_account_monitor import MT5AccountMonitor
    from src.core.mt5_account_executor import MT5AccountExecutor
    from src.core.mt5_rpc_client import MT5RPCClient
    from src.core.mt5_request_handler import MT5RequestHandler
    from src.messaging.jetstream_client import JetStreamClient
    from src.messaging.nats_manager import NATSManager
    from src.core.config_manager import get_stream_config_manager
    
    REAL_COMPONENTS_AVAILABLE = True
    print("✅ 真实组件导入成功")
    
except ImportError as e:
    print(f"❌ 导入真实组件失败: {e}")
    sys.exit(1)


class EndToEndMessageFlowTest:
    """端到端消息流测试类"""
    
    def __init__(self):
        self.test_data_dir = Path(tempfile.mkdtemp(prefix="e2e_mt5_test_"))
        self.jetstream_clients = []
        self.components = []
        print(f"测试数据目录: {self.test_data_dir}")
    
    def cleanup(self):
        """清理测试资源"""
        import shutil
        try:
            # 停止所有组件
            for component in self.components:
                if hasattr(component, 'stop') and asyncio.iscoroutinefunction(component.stop):
                    asyncio.create_task(component.stop())
                elif hasattr(component, 'running'):
                    component.running = False
            
            # 断开所有JetStream连接
            for client in self.jetstream_clients:
                if hasattr(client, 'disconnect'):
                    asyncio.create_task(client.disconnect())
            
            shutil.rmtree(self.test_data_dir)
            print("🧹 测试环境清理完成")
        except Exception as e:
            print(f"⚠️ 清理警告: {e}")
    
    async def setup_jetstream_infrastructure(self):
        """设置JetStream基础设施"""
        print("\n🏗️ 设置JetStream基础设施...")
        
        try:
            # 创建主JetStream客户端
            main_config = {
                'servers': ['nats://localhost:4222'],
                'stream_name': 'E2E_TEST_MAIN',
                'subjects': ['E2E.TEST.>']
            }
            
            main_client = JetStreamClient(main_config)
            connected = await main_client.connect()
            
            if not connected:
                print("  ❌ 无法连接到NATS服务器")
                return None, None, None
            
            print("  ✅ 主JetStream客户端连接成功")
            self.jetstream_clients.append(main_client)
            
            # 创建监控器专用客户端
            monitor_config = {
                'servers': ['nats://localhost:4222'],
                'stream_name': 'E2E_TEST_MONITOR',
                'subjects': ['E2E.MONITOR.>']
            }
            
            monitor_client = JetStreamClient(monitor_config)
            await monitor_client.connect()
            print("  ✅ 监控器JetStream客户端连接成功")
            self.jetstream_clients.append(monitor_client)
            
            # 创建执行器专用客户端
            executor_config = {
                'servers': ['nats://localhost:4222'],
                'stream_name': 'E2E_TEST_EXECUTOR',
                'subjects': ['E2E.EXECUTOR.>']
            }
            
            executor_client = JetStreamClient(executor_config)
            await executor_client.connect()
            print("  ✅ 执行器JetStream客户端连接成功")
            self.jetstream_clients.append(executor_client)
            
            return main_client, monitor_client, executor_client
            
        except Exception as e:
            print(f"  ❌ JetStream基础设施设置失败: {e}")
            return None, None, None
    
    async def test_monitor_to_executor_message_flow(self):
        """测试监控器到执行器的消息流"""
        print("\n📡 测试监控器到执行器的消息流...")
        
        # 设置基础设施
        main_client, monitor_client, executor_client = await self.setup_jetstream_infrastructure()
        if not main_client:
            return False
        
        try:
            # 创建RPC客户端
            rpc_client = MT5RPCClient(main_client)
            
            # 创建监控器
            monitor_config = {
                'login': '11111',
                'server': 'E2E-TestServer',
                'password': 'test_password'
            }
            
            monitor = MT5AccountMonitor(
                account_id='E2E_MONITOR_001',
                account_config=monitor_config,
                event_publisher=monitor_client,
                rpc_client=rpc_client,
                host_id='e2e_test_host'
            )
            
            print("  ✅ 监控器创建成功")
            self.components.append(monitor)
            
            # 创建执行器
            executor_config = {
                'login': '22222',
                'server': 'E2E-TestServer',
                'password': 'test_password'
            }
            
            executor = MT5AccountExecutor(
                account_id='E2E_EXECUTOR_001',
                account_config=executor_config,
                command_subscriber=executor_client,
                result_publisher=executor_client,
                rpc_client=rpc_client
            )
            
            print("  ✅ 执行器创建成功")
            self.components.append(executor)
            
            # 测试消息发布和订阅
            print("  📤 测试消息发布...")
            
            test_message = {
                'type': 'trade_signal',
                'account_id': 'E2E_MONITOR_001',
                'symbol': 'EURUSD',
                'action': 'buy',
                'volume': 0.1,
                'price': 1.1234,
                'timestamp': time.time()
            }
            
            # 监控器发布消息 - 使用正确的方法
            from src.messaging.message_envelope import MessageEnvelope

            message_envelope = MessageEnvelope(
                subject='E2E.MONITOR.TRADE_SIGNAL',
                data=test_message,
                headers={'source': 'monitor_test'}
            )

            await monitor_client.publish_message(message_envelope)
            
            print("  ✅ 监控器消息发布成功")
            
            # 执行器订阅消息
            received_messages = []
            
            async def message_handler(msg):
                try:
                    data = json.loads(msg.data.decode())
                    received_messages.append(data)
                    print(f"  📥 执行器接收到消息: {data['type']}")
                except Exception as e:
                    print(f"  ⚠️ 消息处理错误: {e}")
            
            # 订阅消息
            await executor_client.subscribe(
                subject='E2E.MONITOR.TRADE_SIGNAL',
                callback=message_handler
            )
            
            # 等待消息传递
            await asyncio.sleep(2)
            
            # 验证消息接收
            if received_messages:
                print(f"  ✅ 消息流测试成功: 接收到 {len(received_messages)} 条消息")
                return True
            else:
                print("  ⚠️ 未接收到消息，可能是订阅延迟")
                return False
                
        except Exception as e:
            print(f"  ❌ 监控器到执行器消息流测试失败: {e}")
            return False
    
    async def test_rpc_communication(self):
        """测试RPC通信"""
        print("\n🔌 测试RPC通信...")
        
        # 设置基础设施
        main_client, _, _ = await self.setup_jetstream_infrastructure()
        if not main_client:
            return False
        
        try:
            # 创建RPC客户端
            rpc_client = MT5RPCClient(main_client)
            
            # 创建模拟的MT5ProcessManager
            class MockMT5ProcessManager:
                def __init__(self):
                    self.call_count = 0
                
                async def handle_request(self, request_data):
                    self.call_count += 1
                    return {
                        "status": "success",
                        "data": f"RPC响应 #{self.call_count}",
                        "timestamp": time.time()
                    }
            
            mock_manager = MockMT5ProcessManager()
            
            # 创建RPC请求处理器
            rpc_handler = MT5RequestHandler(main_client, mock_manager)
            
            print("  ✅ RPC组件创建成功")
            self.components.extend([rpc_client, rpc_handler])
            
            # 启动RPC处理器
            await rpc_handler.start()
            print("  ✅ RPC处理器启动成功")
            
            # 测试RPC调用
            print("  📞 测试RPC调用...")
            
            test_request = {
                'method': 'get_account_info',
                'params': {
                    'account_id': 'E2E_TEST_001'
                }
            }
            
            try:
                response = await rpc_client.call_rpc(
                    account_id='E2E_TEST_001',
                    method='get_account_info',
                    params=test_request['params'],
                    timeout=5.0
                )
                
                if response and response.get('status') == 'success':
                    print(f"  ✅ RPC调用成功: {response['data']}")
                    return True
                else:
                    print(f"  ⚠️ RPC调用返回异常: {response}")
                    return False
                    
            except asyncio.TimeoutError:
                print("  ⚠️ RPC调用超时")
                return False
            except Exception as e:
                print(f"  ❌ RPC调用失败: {e}")
                return False
                
        except Exception as e:
            print(f"  ❌ RPC通信测试失败: {e}")
            return False
    
    async def run_all_tests(self):
        """运行所有端到端测试"""
        print("🚀 开始端到端消息流测试...")
        
        test_results = {}
        
        # 运行各项测试
        test_results['message_flow'] = await self.test_monitor_to_executor_message_flow()
        test_results['rpc_communication'] = await self.test_rpc_communication()
        
        # 统计结果
        passed = sum(1 for result in test_results.values() if result)
        total = len(test_results)
        success_rate = (passed / total) * 100
        
        print(f"\n📊 端到端测试结果:")
        print(f"总测试数: {total}")
        print(f"通过: {passed}")
        print(f"失败: {total - passed}")
        print(f"成功率: {success_rate:.1f}%")
        
        print(f"\n📋 详细结果:")
        for test_name, result in test_results.items():
            status = "✅ 通过" if result else "❌ 失败"
            print(f"  {test_name}: {status}")
        
        return success_rate >= 50  # 50%以上算成功


async def main():
    """主函数"""
    if not REAL_COMPONENTS_AVAILABLE:
        print("❌ 真实组件不可用，无法运行测试")
        return False
    
    test_suite = EndToEndMessageFlowTest()
    
    try:
        success = await test_suite.run_all_tests()
        
        if success:
            print("\n🎉 端到端消息流测试成功!")
        else:
            print("\n⚠️ 端到端消息流测试部分失败")
        
        return success
        
    finally:
        test_suite.cleanup()


if __name__ == '__main__':
    result = asyncio.run(main())
    sys.exit(0 if result else 1)
