#!/usr/bin/env python3
"""
故障恢复测试
测试组件故障时的恢复机制
"""

import asyncio
import json
import os
import sys
import tempfile
import time
import signal
import multiprocessing as mp
from pathlib import Path
from typing import Dict, Any, List

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# 导入真实组件
try:
    from src.core.mt5_account_monitor import MT5AccountMonitor
    from src.core.mt5_account_executor import MT5AccountExecutor
    from src.core.mt5_rpc_client import MT5RPCClient
    from src.core.mt5_request_handler import MT5RequestHandler
    from src.messaging.jetstream_client import JetStreamClient
    from src.messaging.nats_manager import NATSManager
    
    REAL_COMPONENTS_AVAILABLE = True
    print("✅ 故障恢复测试组件导入成功")
    
except ImportError as e:
    print(f"❌ 导入组件失败: {e}")
    sys.exit(1)


class FaultRecoveryTest:
    """故障恢复测试类"""
    
    def __init__(self):
        self.test_data_dir = Path(tempfile.mkdtemp(prefix="fault_recovery_test_"))
        self.jetstream_clients = []
        self.components = []
        self.processes = []
        print(f"测试数据目录: {self.test_data_dir}")
    
    def cleanup(self):
        """清理测试资源"""
        import shutil
        try:
            # 停止所有组件
            for component in self.components:
                if hasattr(component, 'running'):
                    component.running = False
            
            # 终止所有进程
            for process in self.processes:
                if process.is_alive():
                    process.terminate()
                    process.join(timeout=3)
            
            # 断开所有JetStream连接
            for client in self.jetstream_clients:
                if hasattr(client, 'disconnect'):
                    asyncio.create_task(client.disconnect())
            
            shutil.rmtree(self.test_data_dir)
            print("🧹 故障恢复测试环境清理完成")
        except Exception as e:
            print(f"⚠️ 清理警告: {e}")
    
    async def setup_fault_recovery_infrastructure(self):
        """设置故障恢复测试基础设施"""
        print("\n🏗️ 设置故障恢复测试基础设施...")
        
        try:
            # 创建主JetStream客户端
            main_config = {
                'servers': ['nats://localhost:4222'],
                'stream_name': 'FAULT_RECOVERY_MAIN',
                'subjects': ['FAULT.RECOVERY.>']
            }
            
            main_client = JetStreamClient(main_config)
            connected = await main_client.connect()
            
            if not connected:
                print("  ❌ 无法连接到NATS服务器")
                return None
            
            print("  ✅ 故障恢复基础设施设置成功")
            self.jetstream_clients.append(main_client)
            
            return main_client
            
        except Exception as e:
            print(f"  ❌ 故障恢复基础设施设置失败: {e}")
            return None
    
    async def test_jetstream_connection_recovery(self):
        """测试JetStream连接恢复"""
        print("\n🔌 测试JetStream连接恢复...")
        
        try:
            # 创建JetStream客户端
            config = {
                'servers': ['nats://localhost:4222'],
                'stream_name': 'CONNECTION_RECOVERY_TEST',
                'subjects': ['RECOVERY.TEST.>']
            }
            
            client = JetStreamClient(config)
            connected = await client.connect()
            
            if not connected:
                print("  ❌ 初始连接失败")
                return False
            
            print("  ✅ 初始连接成功")
            self.jetstream_clients.append(client)
            
            # 测试正常消息发送
            test_message = {
                'type': 'connection_test',
                'timestamp': time.time(),
                'test_id': 'initial_test'
            }
            
            await client.publish_message(
                subject='RECOVERY.TEST.INITIAL',
                data=test_message
            )
            print("  ✅ 初始消息发送成功")
            
            # 模拟连接断开（通过断开客户端）
            print("  🔌 模拟连接断开...")
            await client.disconnect()
            
            # 等待一段时间
            await asyncio.sleep(2)
            
            # 尝试重新连接
            print("  🔄 尝试重新连接...")
            reconnected = await client.connect()
            
            if reconnected:
                print("  ✅ 重新连接成功")
                
                # 测试恢复后的消息发送
                recovery_message = {
                    'type': 'recovery_test',
                    'timestamp': time.time(),
                    'test_id': 'recovery_test'
                }
                
                await client.publish_message(
                    subject='RECOVERY.TEST.RECOVERY',
                    data=recovery_message
                )
                print("  ✅ 恢复后消息发送成功")
                
                return True
            else:
                print("  ❌ 重新连接失败")
                return False
                
        except Exception as e:
            print(f"  ❌ JetStream连接恢复测试失败: {e}")
            return False
    
    async def test_component_failure_recovery(self):
        """测试组件故障恢复"""
        print("\n🔧 测试组件故障恢复...")
        
        # 设置基础设施
        main_client = await self.setup_fault_recovery_infrastructure()
        if not main_client:
            return False
        
        try:
            # 创建RPC客户端
            rpc_client = MT5RPCClient(main_client)
            
            # 创建监控器
            monitor_config = {
                'login': '50001',
                'server': 'FaultTest-Server',
                'password': 'fault_test_password'
            }
            
            monitor = MT5AccountMonitor(
                account_id='FAULT_MONITOR_001',
                account_config=monitor_config,
                event_publisher=main_client,
                rpc_client=rpc_client,
                host_id='fault_test_host'
            )
            
            print("  ✅ 监控器创建成功")
            self.components.append(monitor)
            
            # 测试正常操作
            print("  📊 测试正常操作...")
            
            normal_operations = 0
            for i in range(5):
                try:
                    data = {
                        'account_id': monitor.account_id,
                        'operation': f'normal_op_{i}',
                        'timestamp': time.time()
                    }
                    
                    await main_client.publish_message(
                        subject='FAULT.RECOVERY.NORMAL',
                        data=data
                    )
                    normal_operations += 1
                    await asyncio.sleep(0.1)
                    
                except Exception as e:
                    print(f"    ⚠️ 正常操作失败: {e}")
            
            print(f"  ✅ 正常操作完成: {normal_operations}/5")
            
            # 模拟组件故障（设置running=False）
            print("  💥 模拟组件故障...")
            monitor.running = False
            
            # 等待故障期间
            await asyncio.sleep(1)
            
            # 模拟故障恢复
            print("  🔄 模拟故障恢复...")
            monitor.running = True
            
            # 测试恢复后操作
            print("  📊 测试恢复后操作...")
            
            recovery_operations = 0
            for i in range(5):
                try:
                    data = {
                        'account_id': monitor.account_id,
                        'operation': f'recovery_op_{i}',
                        'timestamp': time.time()
                    }
                    
                    await main_client.publish_message(
                        subject='FAULT.RECOVERY.RECOVERY',
                        data=data
                    )
                    recovery_operations += 1
                    await asyncio.sleep(0.1)
                    
                except Exception as e:
                    print(f"    ⚠️ 恢复操作失败: {e}")
            
            print(f"  ✅ 恢复后操作完成: {recovery_operations}/5")
            
            # 评估恢复效果
            recovery_rate = recovery_operations / 5
            print(f"  📊 故障恢复率: {recovery_rate*100:.1f}%")
            
            return recovery_rate >= 0.8  # 80%恢复率算成功
            
        except Exception as e:
            print(f"  ❌ 组件故障恢复测试失败: {e}")
            return False
    
    async def test_rpc_failure_recovery(self):
        """测试RPC故障恢复"""
        print("\n📞 测试RPC故障恢复...")
        
        # 设置基础设施
        main_client = await self.setup_fault_recovery_infrastructure()
        if not main_client:
            return False
        
        try:
            # 创建RPC客户端
            rpc_client = MT5RPCClient(main_client)
            
            # 创建故障恢复模拟管理器
            class FaultRecoveryManager:
                def __init__(self):
                    self.request_count = 0
                    self.failure_mode = False
                
                async def handle_request(self, request_data):
                    self.request_count += 1
                    
                    if self.failure_mode:
                        # 模拟故障
                        raise Exception("Simulated RPC failure")
                    
                    return {
                        "status": "success",
                        "data": f"RPC响应 #{self.request_count}",
                        "timestamp": time.time()
                    }
                
                def enable_failure_mode(self):
                    self.failure_mode = True
                
                def disable_failure_mode(self):
                    self.failure_mode = False
            
            fault_manager = FaultRecoveryManager()
            
            # 创建RPC处理器
            rpc_handler = MT5RequestHandler(main_client, fault_manager)
            await rpc_handler.start()
            
            print("  ✅ RPC组件创建成功")
            self.components.extend([rpc_client, rpc_handler])
            
            # 测试正常RPC调用
            print("  📞 测试正常RPC调用...")
            
            normal_calls = 0
            for i in range(3):
                try:
                    response = await rpc_client.call_rpc(
                        method='normal_test',
                        params={'call_id': i},
                        timeout=3.0
                    )
                    
                    if response and response.get('status') == 'success':
                        normal_calls += 1
                        
                except Exception as e:
                    print(f"    ⚠️ 正常调用失败: {e}")
            
            print(f"  ✅ 正常RPC调用: {normal_calls}/3")
            
            # 启用故障模式
            print("  💥 启用RPC故障模式...")
            fault_manager.enable_failure_mode()
            
            # 测试故障期间的调用
            print("  📞 测试故障期间RPC调用...")
            
            failure_calls = 0
            for i in range(3):
                try:
                    response = await rpc_client.call_rpc(
                        method='failure_test',
                        params={'call_id': i},
                        timeout=2.0
                    )
                    
                    if response:
                        failure_calls += 1
                        
                except Exception:
                    # 期望失败
                    pass
            
            print(f"  ⚠️ 故障期间调用（期望失败）: {failure_calls}/3")
            
            # 禁用故障模式（恢复）
            print("  🔄 禁用故障模式（恢复）...")
            fault_manager.disable_failure_mode()
            
            # 等待恢复
            await asyncio.sleep(1)
            
            # 测试恢复后的调用
            print("  📞 测试恢复后RPC调用...")
            
            recovery_calls = 0
            for i in range(3):
                try:
                    response = await rpc_client.call_rpc(
                        method='recovery_test',
                        params={'call_id': i},
                        timeout=3.0
                    )
                    
                    if response and response.get('status') == 'success':
                        recovery_calls += 1
                        
                except Exception as e:
                    print(f"    ⚠️ 恢复调用失败: {e}")
            
            print(f"  ✅ 恢复后RPC调用: {recovery_calls}/3")
            
            # 停止RPC处理器
            await rpc_handler.stop()
            
            # 评估RPC恢复效果
            recovery_rate = recovery_calls / 3
            print(f"  📊 RPC故障恢复率: {recovery_rate*100:.1f}%")
            
            return recovery_rate >= 0.66  # 66%恢复率算成功
            
        except Exception as e:
            print(f"  ❌ RPC故障恢复测试失败: {e}")
            return False
    
    async def test_message_queue_recovery(self):
        """测试消息队列恢复"""
        print("\n📬 测试消息队列恢复...")
        
        # 设置基础设施
        main_client = await self.setup_fault_recovery_infrastructure()
        if not main_client:
            return False
        
        try:
            # 创建消息队列测试
            received_messages = []
            
            async def message_handler(msg):
                try:
                    data = json.loads(msg.data.decode())
                    received_messages.append(data)
                    print(f"    📥 接收消息: {data.get('message_id', 'unknown')}")
                except Exception as e:
                    print(f"    ⚠️ 消息处理错误: {e}")
            
            # 订阅消息
            await main_client.subscribe_to_subject(
                subject='FAULT.RECOVERY.QUEUE.>',
                callback=message_handler
            )
            
            print("  ✅ 消息队列订阅成功")
            
            # 发送正常消息
            print("  📤 发送正常消息...")
            
            for i in range(5):
                message = {
                    'message_id': f'normal_{i}',
                    'timestamp': time.time(),
                    'content': f'正常消息 {i}'
                }
                
                await main_client.publish_message(
                    subject='FAULT.RECOVERY.QUEUE.NORMAL',
                    data=message
                )
                await asyncio.sleep(0.1)
            
            # 等待消息处理
            await asyncio.sleep(2)
            normal_received = len(received_messages)
            print(f"  ✅ 正常消息接收: {normal_received}/5")
            
            # 模拟队列故障（断开连接）
            print("  💥 模拟队列故障...")
            await main_client.disconnect()
            
            # 尝试发送故障期间的消息（应该失败）
            print("  📤 尝试发送故障期间消息...")
            
            failure_sent = 0
            for i in range(3):
                try:
                    message = {
                        'message_id': f'failure_{i}',
                        'timestamp': time.time(),
                        'content': f'故障期间消息 {i}'
                    }
                    
                    await main_client.publish_message(
                        subject='FAULT.RECOVERY.QUEUE.FAILURE',
                        data=message
                    )
                    failure_sent += 1
                    
                except Exception:
                    # 期望失败
                    pass
            
            print(f"  ⚠️ 故障期间发送（期望失败）: {failure_sent}/3")
            
            # 恢复连接
            print("  🔄 恢复队列连接...")
            reconnected = await main_client.connect()
            
            if not reconnected:
                print("  ❌ 队列恢复失败")
                return False
            
            # 重新订阅
            await main_client.subscribe_to_subject(
                subject='FAULT.RECOVERY.QUEUE.>',
                callback=message_handler
            )
            
            # 发送恢复后消息
            print("  📤 发送恢复后消息...")
            
            recovery_start_count = len(received_messages)
            
            for i in range(5):
                message = {
                    'message_id': f'recovery_{i}',
                    'timestamp': time.time(),
                    'content': f'恢复后消息 {i}'
                }
                
                await main_client.publish_message(
                    subject='FAULT.RECOVERY.QUEUE.RECOVERY',
                    data=message
                )
                await asyncio.sleep(0.1)
            
            # 等待消息处理
            await asyncio.sleep(2)
            recovery_received = len(received_messages) - recovery_start_count
            print(f"  ✅ 恢复后消息接收: {recovery_received}/5")
            
            # 评估队列恢复效果
            recovery_rate = recovery_received / 5
            print(f"  📊 消息队列恢复率: {recovery_rate*100:.1f}%")
            
            return recovery_rate >= 0.8  # 80%恢复率算成功
            
        except Exception as e:
            print(f"  ❌ 消息队列恢复测试失败: {e}")
            return False
    
    async def run_all_tests(self):
        """运行所有故障恢复测试"""
        print("🚀 开始故障恢复测试...")
        
        test_results = {}
        
        # 运行各项测试
        test_results['connection_recovery'] = await self.test_jetstream_connection_recovery()
        test_results['component_recovery'] = await self.test_component_failure_recovery()
        test_results['rpc_recovery'] = await self.test_rpc_failure_recovery()
        test_results['queue_recovery'] = await self.test_message_queue_recovery()
        
        # 统计结果
        passed = sum(1 for result in test_results.values() if result)
        total = len(test_results)
        success_rate = (passed / total) * 100
        
        print(f"\n📊 故障恢复测试结果:")
        print(f"总测试数: {total}")
        print(f"通过: {passed}")
        print(f"失败: {total - passed}")
        print(f"成功率: {success_rate:.1f}%")
        
        print(f"\n📋 详细结果:")
        for test_name, result in test_results.items():
            status = "✅ 通过" if result else "❌ 失败"
            print(f"  {test_name}: {status}")
        
        return success_rate >= 75  # 75%以上算成功


async def main():
    """主函数"""
    if not REAL_COMPONENTS_AVAILABLE:
        print("❌ 故障恢复测试组件不可用，无法运行测试")
        return False
    
    test_suite = FaultRecoveryTest()
    
    try:
        success = await test_suite.run_all_tests()
        
        if success:
            print("\n🎉 故障恢复测试成功!")
        else:
            print("\n⚠️ 故障恢复测试部分失败")
        
        return success
        
    finally:
        test_suite.cleanup()


if __name__ == '__main__':
    result = asyncio.run(main())
    sys.exit(0 if result else 1)
