#!/usr/bin/env python3
"""
MT5 分布式跟单系统 - 真实系统级 MVP 测试
使用现有的完整架构进行端到端验证

测试架构：
[信号注入] → [NATS JetStream] → [监控器/执行器] → [MT5 Demo账户] → [结果验证]

核心验证点：
1. NATS JetStream 消息传递
2. 主账户监控器信号发布
3. 从账户执行器信号接收和执行
4. 真实 MT5 Demo 账户交易
5. 端到端延迟和可靠性
"""

import asyncio
import os
import sys
import time
import yaml
import json
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, asdict
from enum import Enum

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# 导入系统核心组件
from src.utils.logger import get_logger, setup_logging
from src.core.config_manager import ConfigManager
from src.messaging.jetstream_client import JetStreamClient, JetStreamConfig
from src.messaging.message_types import TradeSignal, TradeAction
from src.core.mt5_client import MT5Client, create_mt5_client
from src.monitors.master_monitor import OptimizedMasterMonitor
from src.executors.slave_executor import OptimizedSlaveExecutor
from src.core.trading_system_helper import TradingSystemHelper

# 导入 MetaTrader5
try:
    import MetaTrader5 as mt5
    MT5_AVAILABLE = True
except ImportError:
    MT5_AVAILABLE = False
    print("⚠️ MetaTrader5 模块未安装，将跳过真实 MT5 测试")

logger = get_logger(__name__)

class MVPTestPhase(Enum):
    """MVP 测试阶段"""
    INIT = "初始化"
    INFRASTRUCTURE = "基础设施"
    CONNECTIONS = "连接测试"
    SIGNAL_INJECTION = "信号注入"
    MONITORING = "监控验证"
    EXECUTION = "执行验证"
    END_TO_END = "端到端"
    CLEANUP = "清理"

@dataclass
class MVPTestResult:
    """MVP 测试结果"""
    phase: MVPTestPhase
    success: bool
    duration: float
    details: Dict[str, Any]
    error: Optional[str] = None

class SystemMVPTester:
    """系统级 MVP 测试器"""
    
    def __init__(self):
        self.test_results: List[MVPTestResult] = []
        self.start_time = time.time()
        
        # 核心组件
        self.config_manager: Optional[ConfigManager] = None
        self.jetstream_client: Optional[JetStreamClient] = None
        self.master_monitor: Optional[OptimizedMasterMonitor] = None
        self.slave_executor: Optional[OptimizedSlaveExecutor] = None
        self.mt5_clients: Dict[str, MT5Client] = {}
        
        # 测试配置
        self.test_config = {
            'accounts': ['ACC001', 'ACC002'],
            'master_account': 'ACC001',
            'slave_accounts': ['ACC002'],
            'test_symbol': 'EURUSD',
            'test_volume': 0.01,
            'nats_servers': ['nats://localhost:4222'],
            'stream_name': 'MT5_MVP_TEST_STREAM',
            'timeout': 30.0
        }
        
        # 运行状态
        self.running = False
        self.cleanup_tasks = []
    
    async def run_full_mvp_test(self):
        """运行完整的系统级 MVP 测试"""
        logger.info("🚀 开始系统级 MT5 分布式跟单 MVP 测试")
        
        try:
            # 设置日志
            setup_logging({'level': 'INFO', 'format': 'json'})
            
            # 执行测试阶段
            await self._test_phase_init()
            await self._test_phase_infrastructure()
            await self._test_phase_connections()
            await self._test_phase_signal_injection()
            await self._test_phase_monitoring()
            await self._test_phase_execution()
            await self._test_phase_end_to_end()
            
        except Exception as e:
            logger.error(f"MVP 测试执行异常: {e}")
            self._add_result(MVPTestPhase.END_TO_END, False, 0, {}, str(e))
        
        finally:
            await self._test_phase_cleanup()
            await self._generate_mvp_report()
    
    async def _test_phase_init(self):
        """阶段1: 初始化测试"""
        phase = MVPTestPhase.INIT
        start_time = time.time()
        
        try:
            logger.info(f"📋 {phase.value} - 加载配置和环境检查")
            
            # 检查环境变量
            required_env_vars = ['MT5_ACC001_PASSWORD', 'MT5_ACC002_PASSWORD']
            missing_vars = [var for var in required_env_vars if not os.getenv(var)]
            
            if missing_vars:
                raise Exception(f"缺少环境变量: {missing_vars}")
            
            # 加载配置
            self.config_manager = ConfigManager("config/system.yaml")
            
            # 检查账户配置文件
            for account_id in self.test_config['accounts']:
                config_file = f"config/accounts/{account_id}.yaml"
                if not Path(config_file).exists():
                    raise Exception(f"账户配置文件不存在: {config_file}")
            
            # 检查 MT5 可用性
            if not MT5_AVAILABLE:
                raise Exception("MetaTrader5 模块不可用")
            
            details = {
                'config_loaded': True,
                'accounts_found': len(self.test_config['accounts']),
                'mt5_available': MT5_AVAILABLE,
                'env_vars_set': len(required_env_vars) - len(missing_vars)
            }
            
            self._add_result(phase, True, time.time() - start_time, details)
            logger.info(f"✅ {phase.value} 完成")
            
        except Exception as e:
            self._add_result(phase, False, time.time() - start_time, {}, str(e))
            logger.error(f"❌ {phase.value} 失败: {e}")
            raise
    
    async def _test_phase_infrastructure(self):
        """阶段2: 基础设施测试"""
        phase = MVPTestPhase.INFRASTRUCTURE
        start_time = time.time()
        
        try:
            logger.info(f"🔧 {phase.value} - 初始化 NATS JetStream")
            
            # 创建 JetStream 配置
            jetstream_config = JetStreamConfig(
                servers=self.test_config['nats_servers'],
                stream_name=self.test_config['stream_name'],
                subjects=[
                    "MT5.TRADES.*",
                    "MT5.MONITOR.*", 
                    "MT5.EXECUTE.*",
                    "MT5.RESULT.*",
                    "MT5.TEST.*"
                ],
                max_age=3600,
                max_msgs=100000
            )
            
            # 连接 JetStream
            self.jetstream_client = JetStreamClient(jetstream_config)
            connected = await self.jetstream_client.connect()
            
            if not connected:
                raise Exception("JetStream 连接失败")
            
            # 创建测试流
            stream_created = await self.jetstream_client.create_stream(
                name=self.test_config['stream_name'],
                subjects=jetstream_config.subjects,
                retention='limits',
                max_age=3600,
                max_msgs=100000
            )
            
            details = {
                'jetstream_connected': connected,
                'stream_created': stream_created,
                'stream_name': self.test_config['stream_name'],
                'subjects': jetstream_config.subjects
            }
            
            self._add_result(phase, True, time.time() - start_time, details)
            logger.info(f"✅ {phase.value} 完成")
            
        except Exception as e:
            self._add_result(phase, False, time.time() - start_time, {}, str(e))
            logger.error(f"❌ {phase.value} 失败: {e}")
            raise
    
    async def _test_phase_connections(self):
        """阶段3: MT5 连接测试"""
        phase = MVPTestPhase.CONNECTIONS
        start_time = time.time()
        
        try:
            logger.info(f"🔌 {phase.value} - 测试 MT5 Demo 账户连接")
            
            connection_results = {}
            
            for account_id in self.test_config['accounts']:
                logger.info(f"连接测试: {account_id}")
                
                # 加载账户配置
                with open(f"config/accounts/{account_id}.yaml", 'r', encoding='utf-8') as f:
                    account_config = yaml.safe_load(f)
                
                # 获取连接参数
                mt5_config = account_config['mt5']['connection']
                login = mt5_config['login']
                password = os.getenv(f'MT5_{account_id}_PASSWORD')
                server = mt5_config['server']
                
                # 创建进程隔离的 MT5 客户端
                mt5_client = create_mt5_client(
                    account_id=account_id,
                    login=login,
                    password=password,
                    server=server,
                    terminal_path=mt5_config.get('terminal_path')
                )

                # 测试连接（进程隔离模式）
                connected = await mt5_client.connect()
                
                if connected:
                    # 获取账户信息
                    account_info = await mt5_client.get_account_info()
                    self.mt5_clients[account_id] = mt5_client
                    
                    connection_results[account_id] = {
                        'connected': True,
                        'balance': account_info.get('balance', 0) if account_info else 0,
                        'currency': account_info.get('currency', 'USD') if account_info else 'USD'
                    }
                    
                    logger.info(f"✅ {account_id} 连接成功")
                else:
                    connection_results[account_id] = {
                        'connected': False,
                        'error': 'Connection failed'
                    }
                    logger.error(f"❌ {account_id} 连接失败")
            
            # 检查连接结果
            successful_connections = sum(1 for r in connection_results.values() if r['connected'])
            total_connections = len(connection_results)
            
            if successful_connections == 0:
                raise Exception("所有 MT5 连接失败")
            
            details = {
                'total_accounts': total_connections,
                'successful_connections': successful_connections,
                'connection_results': connection_results
            }
            
            success = successful_connections == total_connections
            self._add_result(phase, success, time.time() - start_time, details)
            
            if success:
                logger.info(f"✅ {phase.value} 完成")
            else:
                logger.warning(f"⚠️ {phase.value} 部分成功")
            
        except Exception as e:
            self._add_result(phase, False, time.time() - start_time, {}, str(e))
            logger.error(f"❌ {phase.value} 失败: {e}")
            raise

    async def _test_phase_signal_injection(self):
        """阶段4: 信号注入测试"""
        phase = MVPTestPhase.SIGNAL_INJECTION
        start_time = time.time()

        try:
            logger.info(f"📡 {phase.value} - 测试信号注入和传递")

            # 创建测试信号
            test_signal = {
                'signal_id': f'mvp_test_{int(time.time())}',
                'account_id': self.test_config['master_account'],
                'symbol': self.test_config['test_symbol'],
                'action': 'BUY',
                'volume': self.test_config['test_volume'],
                'price': 1.1000,
                'timestamp': time.time(),
                'signal_type': 'position_open',
                'test_mode': True
            }

            # 发布测试信号到主账户主题
            master_topic = f"MT5.TRADES.{self.test_config['master_account']}"

            published = await self.jetstream_client.publish(
                subject=master_topic,
                data=test_signal
            )

            if not published:
                raise Exception("信号发布失败")

            # 等待信号传播
            await asyncio.sleep(1.0)

            # 验证信号是否可以被订阅接收
            received_signals = []

            async def signal_handler(msg):
                try:
                    from src.messaging.message_codec import decode_message
                    signal_data = decode_message(msg.data)
                    received_signals.append(signal_data)
                    await msg.ack()
                except Exception as e:
                    logger.error(f"信号处理失败: {e}")
                    await msg.nak()

            # 订阅信号
            await self.jetstream_client.subscribe(
                subject=master_topic,
                callback=signal_handler
            )

            # 等待接收
            await asyncio.sleep(2.0)

            details = {
                'signal_published': published,
                'master_topic': master_topic,
                'test_signal': test_signal,
                'signals_received': len(received_signals),
                'received_signals': received_signals
            }

            success = published and len(received_signals) > 0
            self._add_result(phase, success, time.time() - start_time, details)

            if success:
                logger.info(f"✅ {phase.value} 完成")
            else:
                logger.warning(f"⚠️ {phase.value} 部分成功")

        except Exception as e:
            self._add_result(phase, False, time.time() - start_time, {}, str(e))
            logger.error(f"❌ {phase.value} 失败: {e}")

    async def _test_phase_monitoring(self):
        """阶段5: 监控器测试"""
        phase = MVPTestPhase.MONITORING
        start_time = time.time()

        try:
            logger.info(f"👁️ {phase.value} - 测试主账户监控器")

            master_account = self.test_config['master_account']
            mt5_client = self.mt5_clients.get(master_account)

            if not mt5_client:
                raise Exception(f"主账户 MT5 客户端未连接: {master_account}")

            # 创建主账户监控器
            self.master_monitor = OptimizedMasterMonitor(
                account_id=master_account,
                jetstream_client=self.jetstream_client,
                config_manager=self.config_manager,
                mt5_client=mt5_client,
                host_id="mvp-test-host"
            )

            # 启动监控器
            await self.master_monitor.start()

            # 等待监控器稳定
            await asyncio.sleep(3.0)

            # 检查监控器状态
            monitor_stats = self.master_monitor.get_stats()

            details = {
                'monitor_created': True,
                'monitor_started': self.master_monitor.running,
                'monitor_stats': monitor_stats,
                'trade_subject': self.master_monitor.trade_subject
            }

            success = self.master_monitor.running
            self._add_result(phase, success, time.time() - start_time, details)

            if success:
                logger.info(f"✅ {phase.value} 完成")
                self.cleanup_tasks.append(self.master_monitor.stop)
            else:
                logger.error(f"❌ {phase.value} 失败")

        except Exception as e:
            self._add_result(phase, False, time.time() - start_time, {}, str(e))
            logger.error(f"❌ {phase.value} 失败: {e}")

    async def _test_phase_execution(self):
        """阶段6: 执行器测试"""
        phase = MVPTestPhase.EXECUTION
        start_time = time.time()

        try:
            logger.info(f"⚡ {phase.value} - 测试从账户执行器")

            slave_account = self.test_config['slave_accounts'][0]
            mt5_client = self.mt5_clients.get(slave_account)

            if not mt5_client:
                raise Exception(f"从账户 MT5 客户端未连接: {slave_account}")

            # 加载从账户配置
            with open(f"config/accounts/{slave_account}.yaml", 'r', encoding='utf-8') as f:
                slave_config = yaml.safe_load(f)

            # 创建从账户执行器
            self.slave_executor = OptimizedSlaveExecutor(
                account_id=slave_account,
                jetstream_client=self.jetstream_client,
                config_manager=self.config_manager,
                mt5_client=mt5_client,
                host_id="mvp-test-host"
            )

            # 启动执行器
            await self.slave_executor.start()

            # 等待执行器稳定
            await asyncio.sleep(3.0)

            # 检查执行器状态
            executor_stats = self.slave_executor.stats

            details = {
                'executor_created': True,
                'executor_started': self.slave_executor.running,
                'executor_stats': executor_stats,
                'subscribed_masters': list(self.slave_executor.subscribed_masters)
            }

            success = self.slave_executor.running
            self._add_result(phase, success, time.time() - start_time, details)

            if success:
                logger.info(f"✅ {phase.value} 完成")
                self.cleanup_tasks.append(self.slave_executor.stop)
            else:
                logger.error(f"❌ {phase.value} 失败")

        except Exception as e:
            self._add_result(phase, False, time.time() - start_time, {}, str(e))
            logger.error(f"❌ {phase.value} 失败: {e}")

    async def _test_phase_end_to_end(self):
        """阶段7: 端到端测试"""
        phase = MVPTestPhase.END_TO_END
        start_time = time.time()

        try:
            logger.info(f"🎯 {phase.value} - 端到端跟单测试")

            if not self.master_monitor or not self.slave_executor:
                raise Exception("监控器或执行器未启动")

            # 在主账户执行一个小额测试交易
            master_account = self.test_config['master_account']
            master_client = self.mt5_clients[master_account]

            # 执行测试交易
            test_result = await self._execute_test_trade(master_client, master_account)

            if not test_result['success']:
                raise Exception(f"测试交易失败: {test_result['error']}")

            # 等待跟单执行
            await asyncio.sleep(5.0)

            # 检查从账户是否有对应的跟单交易
            slave_account = self.test_config['slave_accounts'][0]
            slave_client = self.mt5_clients[slave_account]

            # 获取从账户持仓
            slave_positions = await slave_client.get_positions()

            # 分析结果
            master_order_id = test_result.get('order_id')
            copy_trade_found = False

            if slave_positions:
                # 检查是否有相关的跟单交易
                for position in slave_positions:
                    if (position.get('symbol') == self.test_config['test_symbol'] and
                        abs(position.get('volume', 0) - self.test_config['test_volume']) < 0.001):
                        copy_trade_found = True
                        break

            details = {
                'master_trade_executed': test_result['success'],
                'master_order_id': master_order_id,
                'slave_positions_count': len(slave_positions) if slave_positions else 0,
                'copy_trade_found': copy_trade_found,
                'test_symbol': self.test_config['test_symbol'],
                'test_volume': self.test_config['test_volume']
            }

            success = test_result['success'] and copy_trade_found
            self._add_result(phase, success, time.time() - start_time, details)

            if success:
                logger.info(f"✅ {phase.value} 完成 - 跟单成功")
            else:
                logger.warning(f"⚠️ {phase.value} 部分成功 - 跟单可能未执行")

        except Exception as e:
            self._add_result(phase, False, time.time() - start_time, {}, str(e))
            logger.error(f"❌ {phase.value} 失败: {e}")

    async def _execute_test_trade(self, mt5_client: MT5Client, account_id: str) -> Dict[str, Any]:
        """执行测试交易"""
        try:
            logger.info(f"📈 在 {account_id} 执行测试交易")

            # 获取当前价格
            symbol_info = await mt5_client.get_symbol_info(self.test_config['test_symbol'])
            if not symbol_info:
                return {'success': False, 'error': '无法获取品种信息'}

            tick_info = await mt5_client.get_tick_info(self.test_config['test_symbol'])
            if not tick_info:
                return {'success': False, 'error': '无法获取价格信息'}

            # 构建订单请求
            order_request = {
                'symbol': self.test_config['test_symbol'],
                'volume': self.test_config['test_volume'],
                'action': 'BUY',
                'order_type': 'MARKET',
                'price': tick_info.get('ask', 0),
                'comment': f'MVP测试-{account_id}',
                'magic': 999999
            }

            # 发送订单
            result = await mt5_client.send_order(order_request)

            if result and result.get('retcode') == 10009:  # TRADE_RETCODE_DONE
                logger.info(f"✅ 测试交易成功: 订单 {result.get('order')}")
                return {
                    'success': True,
                    'order_id': result.get('order'),
                    'result': result
                }
            else:
                error_msg = f"交易失败: {result.get('retcode')} - {result.get('comment')}"
                logger.error(error_msg)
                return {'success': False, 'error': error_msg}

        except Exception as e:
            error_msg = f"执行测试交易异常: {e}"
            logger.error(error_msg)
            return {'success': False, 'error': error_msg}

    async def _test_phase_cleanup(self):
        """阶段8: 清理测试"""
        phase = MVPTestPhase.CLEANUP
        start_time = time.time()

        try:
            logger.info(f"🧹 {phase.value} - 清理测试环境")

            cleanup_results = []

            # 执行清理任务
            for cleanup_task in self.cleanup_tasks:
                try:
                    if asyncio.iscoroutinefunction(cleanup_task):
                        await cleanup_task()
                    else:
                        cleanup_task()
                    cleanup_results.append(True)
                except Exception as e:
                    logger.error(f"清理任务失败: {e}")
                    cleanup_results.append(False)

            # 关闭 MT5 连接
            for account_id, mt5_client in self.mt5_clients.items():
                try:
                    await mt5_client.disconnect()
                    logger.info(f"✅ {account_id} MT5 连接已关闭")
                except Exception as e:
                    logger.error(f"关闭 {account_id} MT5 连接失败: {e}")

            # 关闭 JetStream 连接
            if self.jetstream_client:
                try:
                    await self.jetstream_client.disconnect()
                    logger.info("✅ JetStream 连接已关闭")
                except Exception as e:
                    logger.error(f"关闭 JetStream 连接失败: {e}")

            details = {
                'cleanup_tasks_executed': len(cleanup_results),
                'cleanup_tasks_successful': sum(cleanup_results),
                'mt5_connections_closed': len(self.mt5_clients),
                'jetstream_disconnected': True
            }

            success = all(cleanup_results)
            self._add_result(phase, success, time.time() - start_time, details)

            logger.info(f"✅ {phase.value} 完成")

        except Exception as e:
            self._add_result(phase, False, time.time() - start_time, {}, str(e))
            logger.error(f"❌ {phase.value} 失败: {e}")

    def _add_result(self, phase: MVPTestPhase, success: bool, duration: float,
                   details: Dict[str, Any], error: str = None):
        """添加测试结果"""
        result = MVPTestResult(
            phase=phase,
            success=success,
            duration=duration,
            details=details,
            error=error
        )
        self.test_results.append(result)

    async def _generate_mvp_report(self):
        """生成 MVP 测试报告"""
        total_time = time.time() - self.start_time

        print("\n" + "="*80)
        print("🎯 MT5 分布式跟单系统 - 系统级 MVP 测试报告")
        print("="*80)

        # 统计结果
        success_count = sum(1 for r in self.test_results if r.success)
        failed_count = sum(1 for r in self.test_results if not r.success)
        total_count = len(self.test_results)

        print(f"\n📊 测试统计:")
        print(f"   总计: {total_count}")
        print(f"   成功: {success_count} ✅")
        print(f"   失败: {failed_count} ❌")
        print(f"   成功率: {(success_count/total_count*100):.1f}%")
        print(f"   总耗时: {total_time:.2f} 秒")

        # 详细结果
        print(f"\n📋 详细结果:")
        for i, result in enumerate(self.test_results, 1):
            status_icon = "✅" if result.success else "❌"
            print(f"   {i:2d}. {status_icon} {result.phase.value} ({result.duration:.2f}s)")

            if result.error:
                print(f"       错误: {result.error}")

            if result.details:
                # 显示关键详情
                key_details = {}
                for key, value in result.details.items():
                    if isinstance(value, (bool, int, float, str)) and len(str(value)) < 50:
                        key_details[key] = value

                if key_details:
                    print(f"       详情: {key_details}")
            print()

        # 系统架构验证结果
        print("🏗️ 系统架构验证:")
        infrastructure_ok = any(r.success for r in self.test_results if r.phase == MVPTestPhase.INFRASTRUCTURE)
        connections_ok = any(r.success for r in self.test_results if r.phase == MVPTestPhase.CONNECTIONS)
        monitoring_ok = any(r.success for r in self.test_results if r.phase == MVPTestPhase.MONITORING)
        execution_ok = any(r.success for r in self.test_results if r.phase == MVPTestPhase.EXECUTION)
        end_to_end_ok = any(r.success for r in self.test_results if r.phase == MVPTestPhase.END_TO_END)

        print(f"   📡 NATS JetStream 基础设施: {'✅' if infrastructure_ok else '❌'}")
        print(f"   🔌 MT5 Demo 账户连接: {'✅' if connections_ok else '❌'}")
        print(f"   👁️ 主账户监控器: {'✅' if monitoring_ok else '❌'}")
        print(f"   ⚡ 从账户执行器: {'✅' if execution_ok else '❌'}")
        print(f"   🎯 端到端跟单: {'✅' if end_to_end_ok else '❌'}")

        # 总体评估
        print("\n🎯 总体评估:")
        if failed_count == 0:
            print("   🎉 所有测试通过！分布式跟单系统已准备好生产部署。")
            print("   📈 建议下一步：")
            print("     - 进行更大规模的压力测试")
            print("     - 添加更多交易品种和策略")
            print("     - 部署到生产环境进行小额真实资金测试")
        elif end_to_end_ok:
            print("   ⚠️ 核心功能正常，但存在部分问题需要修复。")
            print("   🔧 建议修复失败的测试项后再进行生产部署。")
        else:
            print("   ❌ 系统存在关键问题，需要修复后再进行测试。")
            print("   🚨 不建议进行生产部署。")

        # 性能指标
        print("\n⚡ 性能指标:")
        avg_phase_time = total_time / len(self.test_results) if self.test_results else 0
        print(f"   平均阶段耗时: {avg_phase_time:.2f} 秒")

        # 找出最慢的阶段
        slowest_phase = max(self.test_results, key=lambda r: r.duration) if self.test_results else None
        if slowest_phase:
            print(f"   最慢阶段: {slowest_phase.phase.value} ({slowest_phase.duration:.2f}s)")

        # 安全提醒
        print("\n🔒 安全提醒:")
        print("   - 本测试使用 Demo 账户，生产环境请谨慎操作")
        print("   - 建议在小额资金环境下进行初步生产测试")
        print("   - 确保所有风险控制措施已正确配置")
        print("   - 定期监控系统运行状态和交易结果")

        print("\n" + "="*80)

        # 保存报告到文件
        await self._save_mvp_report(total_time, success_count, failed_count)

    async def _save_mvp_report(self, total_time: float, success_count: int, failed_count: int):
        """保存 MVP 测试报告到文件"""
        try:
            report_dir = Path("test_reports")
            report_dir.mkdir(exist_ok=True)

            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            report_file = report_dir / f"system_mvp_report_{timestamp}.json"

            report_data = {
                'timestamp': timestamp,
                'test_type': 'system_mvp',
                'total_time': total_time,
                'statistics': {
                    'total': len(self.test_results),
                    'success': success_count,
                    'failed': failed_count,
                    'success_rate': (success_count / len(self.test_results) * 100) if self.test_results else 0
                },
                'test_results': [
                    {
                        'phase': result.phase.value,
                        'success': result.success,
                        'duration': result.duration,
                        'error': result.error,
                        'details': result.details
                    }
                    for result in self.test_results
                ],
                'test_config': self.test_config,
                'system_info': {
                    'mt5_available': MT5_AVAILABLE,
                    'python_version': sys.version,
                    'platform': sys.platform
                }
            }

            with open(report_file, 'w', encoding='utf-8') as f:
                json.dump(report_data, f, indent=2, ensure_ascii=False, default=str)

            logger.info(f"📄 MVP 测试报告已保存: {report_file}")

        except Exception as e:
            logger.error(f"保存 MVP 测试报告失败: {e}")

# 主函数和辅助函数
async def main():
    """主测试函数"""
    print("🚀 启动 MT5 分布式跟单系统 - 系统级 MVP 测试")

    # 安全确认
    if not await _safety_confirmation():
        print("❌ 测试已取消")
        return

    # 创建测试器
    tester = SystemMVPTester()

    # 运行测试
    await tester.run_full_mvp_test()

async def _safety_confirmation() -> bool:
    """安全确认"""
    print("\n⚠️ 安全确认:")
    print("   本测试将启动完整的分布式跟单系统")
    print("   包括 NATS JetStream、监控器、执行器和真实 MT5 Demo 账户")
    print("   测试过程中会执行真实的交易操作（最小手数）")
    print("   请确保:")
    print("   1. 使用的是 Demo 账户，不是真实资金账户")
    print("   2. 已设置正确的环境变量")
    print("   3. NATS 服务器正在运行 (localhost:4222)")
    print("   4. MT5 客户端已安装并可正常运行")
    print("   5. 网络连接正常")

    # 在实际使用中，可以添加用户输入确认
    # 这里为了自动化测试，直接返回 True
    return True

def run_sync():
    """同步运行入口"""
    asyncio.run(main())

if __name__ == "__main__":
    run_sync()
