# 🚨 AUTO-MIGRATED: Old messaging components replaced with HybridQueueManager
# See MIGRATION_GUIDE.md for details

"""
终端管理Web API
提供Web界面管理多MT5终端和角色配置
"""
from fastapi import FastAPI, HTTPException, BackgroundTasks
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from fastapi.responses import HTMLResponse
from pydantic import BaseModel, Field
from typing import List, Dict, Optional, Any
import asyncio
import time

from ..multi_terminal.terminal_manager import MultiTerminalManager, MT5Terminal
from ..multi_terminal.role_manager import DynamicRoleManager, RoleConfiguration
from ..config.settings import ConfigManager, AccountSettings
from ..config.redis_client import RedisClient
from ..messaging.hybrid_queue_manager import HybridQueueManager, NATSConfig
from ..utils.logger import get_logger


logger = get_logger(__name__)


# 请求模型
class TerminalRegistrationRequest(BaseModel):
    """终端注册请求"""
    account_id: str
    login: int
    password: str
    server: str
    terminal_path: str
    name: Optional[str] = None


class RoleConfigurationRequest(BaseModel):
    """角色配置请求"""
    config_id: str
    name: str
    description: str = ""
    master_terminals: List[str]
    slave_terminals: List[str]
    strategy: str = "proportional"
    lot_multiplier: float = Field(default=1.0, ge=0.01, le=100.0)


class RoleSwitchRequest(BaseModel):
    """角色切换请求"""
    config_id: str


# 响应模型
class TerminalStatusResponse(BaseModel):
    """终端状态响应"""
    terminal_id: str
    account_id: str
    login: int
    server: str
    status: str
    role: str
    pid: Optional[int]
    uptime: float


class RoleConfigurationResponse(BaseModel):
    """角色配置响应"""
    config_id: str
    name: str
    description: str
    master_terminals: List[str]
    slave_terminals: List[str]
    strategy: str
    lot_multiplier: float
    created_time: float
    last_used: float
    is_current: bool


# 创建FastAPI应用
app = FastAPI(
    title="MT5终端管理系统",
    description="多MT5终端动态角色管理",
    version="1.0.0"
)

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 全局变量
terminal_manager: Optional[MultiTerminalManager] = None
role_manager: Optional[DynamicRoleManager] = None
config_manager: Optional[ConfigManager] = None


@app.on_event("startup")
async def startup_event():
    """启动事件"""
    global terminal_manager, role_manager, config_manager
    
    try:
        logger.info("启动终端管理API...")
        
        # 初始化配置管理器
        config_manager = ConfigManager("config/config.yaml")
        await config_manager.load_config()
        
        # 初始化Redis客户端
        redis_settings = config_manager.get_redis_settings()
        redis_client = RedisClient(
            url=redis_settings.url,
            db=redis_settings.db,
            password=redis_settings.password
        )
        
        if not await redis_client.connect():
            raise Exception("Redis连接失败")
        
        # 初始化NATS客户端
        nats_settings = config_manager.get_nats_settings()
        nats_config = NATSConfig(
            servers=nats_settings.servers,
            name="terminal-management-api"
        )
        
        queue_manager = HybridQueueManager(nats_config)
        if not await queue_manager.connect():
            raise Exception("NATS连接失败")
        
        # 初始化管理器
        terminal_manager = MultiTerminalManager("web_host")
        await terminal_manager.start()
        
        role_manager = DynamicRoleManager(terminal_manager, queue_manager, redis_client)
        await role_manager.start()
        
        logger.info("终端管理API启动成功")
        
    except Exception as e:
        logger.error(f"启动失败: {e}")
        raise


@app.on_event("shutdown")
async def shutdown_event():
    """停止事件"""
    global terminal_manager, role_manager
    
    if role_manager:
        await role_manager.stop()
    if terminal_manager:
        await terminal_manager.stop()


# 终端管理API
@app.get("/api/terminals", response_model=List[TerminalStatusResponse])
async def get_terminals():
    """获取所有终端状态"""
    try:
        terminals = await terminal_manager.get_all_terminals_status()
        return [TerminalStatusResponse(**terminal) for terminal in terminals]
    except Exception as e:
        logger.error(f"获取终端列表失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/api/terminals/register")
async def register_terminal(request: TerminalRegistrationRequest):
    """注册新终端"""
    try:
        account_config = AccountSettings(
            id=request.account_id,
            name=request.name or request.account_id,
            login=request.login,
            password=request.password,
            server=request.server,
            terminal_path=request.terminal_path,
            risk_settings={}
        )
        
        terminal_id = await terminal_manager.register_terminal(account_config)
        
        return {
            "success": True,
            "terminal_id": terminal_id,
            "message": "终端注册成功"
        }
        
    except Exception as e:
        logger.error(f"注册终端失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/api/terminals/{terminal_id}/start")
async def start_terminal(terminal_id: str, background_tasks: BackgroundTasks):
    """启动终端"""
    try:
        # 在后台启动终端
        background_tasks.add_task(terminal_manager.start_terminal, terminal_id)
        
        return {
            "success": True,
            "message": f"终端 {terminal_id} 正在启动"
        }
        
    except Exception as e:
        logger.error(f"启动终端失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/api/terminals/{terminal_id}/stop")
async def stop_terminal(terminal_id: str):
    """停止终端"""
    try:
        success = await terminal_manager.stop_terminal(terminal_id)
        
        if success:
            return {
                "success": True,
                "message": f"终端 {terminal_id} 已停止"
            }
        else:
            raise HTTPException(status_code=404, detail="终端不存在")
            
    except Exception as e:
        logger.error(f"停止终端失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/api/terminals/{terminal_id}/status")
async def get_terminal_status(terminal_id: str):
    """获取终端状态"""
    try:
        status = await terminal_manager.get_terminal_status(terminal_id)
        
        if status:
            return TerminalStatusResponse(**status)
        else:
            raise HTTPException(status_code=404, detail="终端不存在")
            
    except Exception as e:
        logger.error(f"获取终端状态失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


# 角色配置API
@app.get("/api/role-configs", response_model=List[RoleConfigurationResponse])
async def get_role_configurations():
    """获取所有角色配置"""
    try:
        configs = await role_manager.list_configurations()
        current_config = await role_manager.get_current_configuration()
        current_id = current_config.config_id if current_config else None
        
        responses = []
        for config in configs:
            responses.append(RoleConfigurationResponse(
                config_id=config.config_id,
                name=config.name,
                description=config.description,
                master_terminals=config.master_terminals,
                slave_terminals=config.slave_terminals,
                strategy=config.strategy,
                lot_multiplier=config.lot_multiplier,
                created_time=config.created_time,
                last_used=config.last_used,
                is_current=(config.config_id == current_id)
            ))
        
        return responses
        
    except Exception as e:
        logger.error(f"获取角色配置失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/api/role-configs")
async def create_role_configuration(request: RoleConfigurationRequest):
    """创建角色配置"""
    try:
        config = RoleConfiguration(
            config_id=request.config_id,
            name=request.name,
            description=request.description,
            master_terminals=request.master_terminals,
            slave_terminals=request.slave_terminals,
            strategy=request.strategy,
            lot_multiplier=request.lot_multiplier
        )
        
        success = await role_manager.create_role_configuration(config)
        
        if success:
            return {
                "success": True,
                "config_id": request.config_id,
                "message": "角色配置创建成功"
            }
        else:
            raise HTTPException(status_code=400, detail="创建角色配置失败")
            
    except Exception as e:
        logger.error(f"创建角色配置失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/api/role-configs/switch")
async def switch_role_configuration(request: RoleSwitchRequest, background_tasks: BackgroundTasks):
    """切换角色配置"""
    try:
        # 在后台执行切换
        background_tasks.add_task(role_manager.switch_to_configuration, request.config_id)
        
        return {
            "success": True,
            "message": f"正在切换到配置 {request.config_id}"
        }
        
    except Exception as e:
        logger.error(f"切换角色配置失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.delete("/api/role-configs/{config_id}")
async def delete_role_configuration(config_id: str):
    """删除角色配置"""
    try:
        success = await role_manager.delete_configuration(config_id)
        
        if success:
            return {
                "success": True,
                "message": f"角色配置 {config_id} 已删除"
            }
        else:
            raise HTTPException(status_code=404, detail="角色配置不存在或无法删除")
            
    except Exception as e:
        logger.error(f"删除角色配置失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/api/role-configs/current")
async def get_current_configuration():
    """获取当前角色配置"""
    try:
        current_config = await role_manager.get_current_configuration()
        
        if current_config:
            return RoleConfigurationResponse(
                config_id=current_config.config_id,
                name=current_config.name,
                description=current_config.description,
                master_terminals=current_config.master_terminals,
                slave_terminals=current_config.slave_terminals,
                strategy=current_config.strategy,
                lot_multiplier=current_config.lot_multiplier,
                created_time=current_config.created_time,
                last_used=current_config.last_used,
                is_current=True
            )
        else:
            return None
            
    except Exception as e:
        logger.error(f"获取当前配置失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/api/switch-history")
async def get_switch_history():
    """获取切换历史"""
    try:
        history = await role_manager.get_switch_history()
        return [
            {
                "event_id": event.event_id,
                "timestamp": event.timestamp,
                "old_config": event.old_config,
                "new_config": event.new_config,
                "affected_terminals": event.affected_terminals,
                "status": event.status,
                "error_message": event.error_message
            }
            for event in history
        ]
        
    except Exception as e:
        logger.error(f"获取切换历史失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/api/system/status")
async def get_system_status():
    """获取系统状态"""
    try:
        terminals = await terminal_manager.get_all_terminals_status()
        current_config = await role_manager.get_current_configuration()
        
        # 统计信息
        total_terminals = len(terminals)
        running_terminals = len([t for t in terminals if t['status'] == 'running'])
        master_terminals = len([t for t in terminals if t['role'] == 'master'])
        slave_terminals = len([t for t in terminals if t['role'] == 'slave'])
        idle_terminals = len([t for t in terminals if t['role'] == 'idle'])
        
        return {
            "total_terminals": total_terminals,
            "running_terminals": running_terminals,
            "stopped_terminals": total_terminals - running_terminals,
            "master_terminals": master_terminals,
            "slave_terminals": slave_terminals,
            "idle_terminals": idle_terminals,
            "current_config": current_config.name if current_config else None,
            "current_config_id": current_config.config_id if current_config else None,
            "switching": role_manager.switching,
            "uptime": time.time() - (role_manager.role_configurations.get(current_config.config_id).last_used if current_config else time.time())
        }
        
    except Exception as e:
        logger.error(f"获取系统状态失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


# 静态文件服务
app.mount("/static", StaticFiles(directory="src/web/static"), name="static")


@app.get("/", response_class=HTMLResponse)
async def get_index():
    """主页"""
    return """
    <!DOCTYPE html>
    <html>
    <head>
        <title>MT5终端管理系统</title>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1">
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
        <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
        <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
    </head>
    <body>
        <div class="container-fluid">
            <h1 class="mt-3">MT5终端管理系统</h1>
            
            <ul class="nav nav-tabs" id="mainTabs" role="tablist">
                <li class="nav-item" role="presentation">
                    <button class="nav-link active" id="terminals-tab" data-bs-toggle="tab" data-bs-target="#terminals" type="button" role="tab">终端管理</button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="configs-tab" data-bs-toggle="tab" data-bs-target="#configs" type="button" role="tab">角色配置</button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="status-tab" data-bs-toggle="tab" data-bs-target="#status" type="button" role="tab">系统状态</button>
                </li>
            </ul>
            
            <div class="tab-content" id="mainTabContent">
                <div class="tab-pane fade show active" id="terminals" role="tabpanel">
                    <div class="mt-3">
                        <h3>终端列表</h3>
                        <div id="terminals-list"></div>
                    </div>
                </div>
                
                <div class="tab-pane fade" id="configs" role="tabpanel">
                    <div class="mt-3">
                        <h3>角色配置</h3>
                        <div id="configs-list"></div>
                    </div>
                </div>
                
                <div class="tab-pane fade" id="status" role="tabpanel">
                    <div class="mt-3">
                        <h3>系统状态</h3>
                        <div id="status-info"></div>
                    </div>
                </div>
            </div>
        </div>
        
        <script>
            // 这里可以添加JavaScript代码来实现动态界面
            // 由于篇幅限制，这里只提供基本框架
            console.log('MT5终端管理系统已加载');
        </script>
    </body>
    </html>
    """


if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        "src.web.terminal_management_api:app",
        host="0.0.0.0",
        port=8001,
        reload=True
    )
