#!/usr/bin/env python3
"""
自动迁移脚本 - 强制替换所有旧消息队列组件
执行硬迁移，无向后兼容
"""

import os
import re
import sys
from pathlib import Path
from typing import List, Dict, Tuple
import shutil

# 项目根目录
PROJECT_ROOT = Path(__file__).parent.parent.parent
sys.path.insert(0, str(PROJECT_ROOT))

# 迁移映射表
IMPORT_REPLACEMENTS = {
    # 导入语句替换
    r'from\s+\.\.messaging\.nats_manager\s+import\s+NATSManager': 
        'from ..messaging.hybrid_queue_manager import HybridQueueManager',
    
    r'from\s+\.\.messaging\.nats_client\s+import\s+NATSClient': 
        'from ..messaging.hybrid_queue_manager import HybridQueueManager',
    
    r'from\s+\.\.messaging\.jetstream_client\s+import\s+JetStreamClient': 
        'from ..messaging.hybrid_queue_manager import HybridQueueManager',
    
    r'from\s+\.\.messaging\.message_codec\s+import\s+MessageCodec': 
        'from ..messaging.message_types import MessageEnvelope',
        
    r'from\s+\.\.messaging\.protobuf_codec\s+import\s+ProtobufCodec': 
        'from ..messaging.message_types import MessageEnvelope',

    # 相对导入
    r'from\s+\.nats_manager\s+import\s+NATSManager': 
        'from .hybrid_queue_manager import HybridQueueManager',
    
    r'from\s+\.nats_client\s+import\s+NATSClient': 
        'from .hybrid_queue_manager import HybridQueueManager',
    
    r'from\s+\.jetstream_client\s+import\s+JetStreamClient': 
        'from .hybrid_queue_manager import HybridQueueManager',

    # 绝对导入
    r'from\s+src\.messaging\.nats_manager\s+import\s+NATSManager': 
        'from src.messaging.hybrid_queue_manager import HybridQueueManager',
    
    r'from\s+src\.messaging\.nats_client\s+import\s+NATSClient': 
        'from src.messaging.hybrid_queue_manager import HybridQueueManager',
    
    r'from\s+src\.messaging\.jetstream_client\s+import\s+JetStreamClient': 
        'from src.messaging.hybrid_queue_manager import HybridQueueManager',
}

CLASS_REPLACEMENTS = {
    # 类实例化替换
    r'NATSManager\s*\(': 'HybridQueueManager(',
    r'NATSClient\s*\(': 'HybridQueueManager(',
    r'JetStreamClient\s*\(': 'HybridQueueManager(',
    r'MessageCodec\s*\(': 'MessageEnvelope(',
    r'ProtobufCodec\s*\(': 'MessageEnvelope(',
}

VARIABLE_REPLACEMENTS = {
    # 变量名替换
    r'nats_manager': 'queue_manager',
    r'jetstream_client': 'queue_manager', 
    r'nats_client': 'queue_manager',
    r'message_codec': 'message_envelope',
    r'protobuf_codec': 'message_envelope',
}

METHOD_REPLACEMENTS = {
    # 方法调用替换
    r'\.publish_with_priority\s*\(': '.publish(',
    r'\.subscribe\s*\(': '.subscribe(',
    r'\.request\s*\(': '.request(',
    r'\.encode\s*\(': '.payload =',
    r'\.decode\s*\(': '.payload',
}

def find_python_files(directory: Path, exclude_dirs: List[str] = None) -> List[Path]:
    """查找所有Python文件"""
    if exclude_dirs is None:
        exclude_dirs = ['__pycache__', '.git', 'venv', '.pytest_cache', 'node_modules', '.REMOVED']
    
    python_files = []
    for root, dirs, files in os.walk(directory):
        # 排除指定目录
        dirs[:] = [d for d in dirs if d not in exclude_dirs]
        
        for file in files:
            if file.endswith('.py'):
                file_path = Path(root) / file
                python_files.append(file_path)
    
    return python_files

def backup_file(file_path: Path) -> Path:
    """备份文件"""
    backup_path = file_path.with_suffix(f'{file_path.suffix}.backup')
    shutil.copy2(file_path, backup_path)
    return backup_path

def migrate_file(file_path: Path, dry_run: bool = False) -> Tuple[bool, List[str]]:
    """迁移单个文件"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        original_content = content
        changes = []
        
        # 1. 替换导入语句
        for pattern, replacement in IMPORT_REPLACEMENTS.items():
            if re.search(pattern, content):
                content = re.sub(pattern, replacement, content)
                changes.append(f"Import: {pattern} -> {replacement}")
        
        # 2. 替换类实例化
        for pattern, replacement in CLASS_REPLACEMENTS.items():
            if re.search(pattern, content):
                content = re.sub(pattern, replacement, content)
                changes.append(f"Class: {pattern} -> {replacement}")
        
        # 3. 替换变量名
        for pattern, replacement in VARIABLE_REPLACEMENTS.items():
            if re.search(r'\b' + pattern + r'\b', content):
                content = re.sub(r'\b' + pattern + r'\b', replacement, content)
                changes.append(f"Variable: {pattern} -> {replacement}")
        
        # 4. 替换方法调用
        for pattern, replacement in METHOD_REPLACEMENTS.items():
            if re.search(pattern, content):
                content = re.sub(pattern, replacement, content)
                changes.append(f"Method: {pattern} -> {replacement}")
        
        # 检查是否有变化
        has_changes = content != original_content
        
        if has_changes and not dry_run:
            # 备份原文件
            backup_file(file_path)
            
            # 写入新内容
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
        
        return has_changes, changes
        
    except Exception as e:
        return False, [f"Error: {e}"]

def add_migration_comment(file_path: Path):
    """添加迁移注释"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        migration_comment = '''# 🚨 AUTO-MIGRATED: Old messaging components replaced with HybridQueueManager
# See MIGRATION_GUIDE.md for details
'''
        
        # 在第一行后添加注释
        lines = content.split('\n')
        if lines and (lines[0].startswith('#!/') or lines[0].startswith('#')):
            lines.insert(1, migration_comment)
        else:
            lines.insert(0, migration_comment)
        
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write('\n'.join(lines))
            
    except Exception as e:
        print(f"Warning: Could not add migration comment to {file_path}: {e}")

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="自动迁移消息队列组件")
    parser.add_argument('--dry-run', action='store_true', help='只显示会进行的更改，不实际修改文件')
    parser.add_argument('--target-dir', default='src', help='目标目录 (默认: src)')
    parser.add_argument('--include-tests', action='store_true', help='也迁移测试文件')
    parser.add_argument('--include-scripts', action='store_true', help='也迁移脚本文件')
    
    args = parser.parse_args()
    
    print("🚀 开始消息队列组件硬迁移")
    print(f"📁 目标目录: {args.target_dir}")
    print(f"🔍 模式: {'预览模式' if args.dry_run else '实际迁移'}")
    print("-" * 60)
    
    # 确定要处理的目录
    directories = [PROJECT_ROOT / args.target_dir]
    
    if args.include_tests:
        directories.append(PROJECT_ROOT / 'tests')
    
    if args.include_scripts:
        directories.append(PROJECT_ROOT / 'scripts')
    
    total_files = 0
    migrated_files = 0
    total_changes = 0
    
    for directory in directories:
        if not directory.exists():
            print(f"⚠️  目录不存在: {directory}")
            continue
            
        print(f"\n📂 处理目录: {directory}")
        
        python_files = find_python_files(directory)
        
        for file_path in python_files:
            total_files += 1
            
            # 跳过已经移除的文件
            if '.REMOVED' in str(file_path) or 'DEPRECATED' in str(file_path):
                continue
            
            has_changes, changes = migrate_file(file_path, dry_run=args.dry_run)
            
            if has_changes:
                migrated_files += 1
                total_changes += len(changes)
                
                relative_path = file_path.relative_to(PROJECT_ROOT)
                print(f"\n✅ {relative_path}")
                
                for change in changes:
                    print(f"   - {change}")
                
                if not args.dry_run:
                    add_migration_comment(file_path)
    
    print("\n" + "="*60)
    print(f"📊 迁移统计:")
    print(f"   - 扫描文件总数: {total_files}")
    print(f"   - 需要迁移的文件: {migrated_files}")
    print(f"   - 总变更数量: {total_changes}")
    
    if args.dry_run:
        print(f"\n🔍 这是预览模式。要执行实际迁移，请运行:")
        print(f"   python {__file__} --target-dir {args.target_dir}")
    else:
        print(f"\n✅ 迁移完成！")
        print(f"📋 请检查 MIGRATION_GUIDE.md 了解更多信息")
        print(f"🔄 备份文件保存为 .backup 扩展名")

if __name__ == "__main__":
    main()