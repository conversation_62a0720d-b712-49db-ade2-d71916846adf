#!/usr/bin/env python3
"""
动态关系管理系统
支持运行时创建、修改、删除跟单关系，完全替代固定的账户角色配置
"""

import os
import uuid
import json
import yaml
import asyncio
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Set, Tuple
from dataclasses import dataclass, field, asdict
from pathlib import Path
from enum import Enum

from ..utils.logger import get_logger

logger = get_logger(__name__)


class RelationshipType(Enum):
    """关系类型"""
    FORWARD = "forward"          # 正向跟单：A做多，B做多
    REVERSE = "reverse"          # 反向跟单：A做多，B做空
    BIDIRECTIONAL = "bidirectional"  # 双向跟单：A和B互相跟单
    HEDGE = "hedge"              # 对冲关系：A做多，B做空（风险管理）


class RelationshipStatus(Enum):
    """关系状态"""
    ACTIVE = "active"            # 激活状态
    PAUSED = "paused"            # 暂停状态
    DISABLED = "disabled"        # 禁用状态
    SCHEDULED = "scheduled"      # 计划状态（未到执行时间）
    EXPIRED = "expired"          # 已过期


@dataclass
class TimeWindow:
    """时间窗口配置"""
    start_time: str = "00:00"    # 开始时间 HH:MM
    end_time: str = "23:59"      # 结束时间 HH:MM
    timezone: str = "UTC"        # 时区
    days_of_week: List[int] = field(default_factory=lambda: [0, 1, 2, 3, 4, 5, 6])  # 0=周一，默认全周
    exclude_holidays: bool = True


@dataclass
class SymbolFilter:
    """品种过滤器"""
    mode: str = "all"            # all, include, exclude
    symbols: List[str] = field(default_factory=list)
    symbol_patterns: List[str] = field(default_factory=list)  # 支持通配符
    
    def matches(self, symbol: str) -> bool:
        """检查品种是否匹配过滤器"""
        if self.mode == "all":
            return True
        elif self.mode == "include":
            return symbol in self.symbols or any(
                self._match_pattern(symbol, pattern) for pattern in self.symbol_patterns
            )
        elif self.mode == "exclude":
            return symbol not in self.symbols and not any(
                self._match_pattern(symbol, pattern) for pattern in self.symbol_patterns
            )
        return False
    
    def _match_pattern(self, symbol: str, pattern: str) -> bool:
        """通配符匹配"""
        import fnmatch
        return fnmatch.fnmatch(symbol, pattern)


@dataclass
class VolumeMapping:
    """手数映射规则"""
    ratio: float = 1.0           # 基础比例
    min_volume: float = 0.01     # 最小手数
    max_volume: float = 10.0     # 最大手数
    round_to: float = 0.01       # 手数取整到
    
    # 动态比例调整（基于账户余额、风险等）
    use_dynamic_ratio: bool = False
    balance_ratio_mode: str = "none"  # none, proportional, fixed_amount
    
    def calculate_volume(self, source_volume: float, 
                        source_balance: float = None, 
                        target_balance: float = None) -> float:
        """计算目标手数"""
        if self.use_dynamic_ratio and self.balance_ratio_mode == "proportional":
            if source_balance and target_balance and source_balance > 0:
                dynamic_ratio = target_balance / source_balance
                volume = source_volume * dynamic_ratio
            else:
                volume = source_volume * self.ratio
        else:
            volume = source_volume * self.ratio
        
        # 应用限制
        volume = max(self.min_volume, min(self.max_volume, volume))
        
        # 取整
        volume = round(volume / self.round_to) * self.round_to
        
        return volume




@dataclass
class TradingRelationship:
    """交易关系定义"""
    # 基础信息
    relationship_id: str = field(default_factory=lambda: str(uuid.uuid4()))
    name: str = ""
    description: str = ""
    
    # 关系参与者
    source_account: str = ""      # 信号源账户
    target_account: str = ""      # 目标账户
    
    # 关系类型和状态
    relationship_type: RelationshipType = RelationshipType.FORWARD
    status: RelationshipStatus = RelationshipStatus.ACTIVE
    
    # 配置
    volume_mapping: VolumeMapping = field(default_factory=VolumeMapping)
    symbol_filter: SymbolFilter = field(default_factory=SymbolFilter)
    time_window: TimeWindow = field(default_factory=TimeWindow)
    
    # 时间管理
    created_at: datetime = field(default_factory=datetime.now)
    updated_at: datetime = field(default_factory=datetime.now)
    starts_at: Optional[datetime] = None
    expires_at: Optional[datetime] = None
    
    # 统计信息
    total_signals: int = 0
    successful_copies: int = 0
    failed_copies: int = 0
    total_volume: float = 0.0
    total_pnl: float = 0.0
    
    # 元数据
    tags: List[str] = field(default_factory=list)
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def is_active(self, current_time: datetime = None) -> bool:
        """检查关系是否当前激活"""
        if current_time is None:
            current_time = datetime.now()
        
        # 检查状态
        if self.status != RelationshipStatus.ACTIVE:
            return False
        
        # 检查时间范围
        if self.starts_at and current_time < self.starts_at:
            return False
        if self.expires_at and current_time > self.expires_at:
            return False
        
        # 检查时间窗口（默认总是在窗口内，除非特别配置）
        return self._is_in_time_window(current_time)
    
    def _is_in_time_window(self, current_time: datetime) -> bool:
        """检查是否在允许的时间窗口内"""
        try:
            # 检查工作日
            weekday = current_time.weekday()
            if weekday not in self.time_window.days_of_week:
                return False
            
            # 检查时间范围
            current_time_str = current_time.strftime("%H:%M")
            if self.time_window.start_time <= current_time_str <= self.time_window.end_time:
                return True
            
            return False
        except Exception:
            # 如果时间窗口配置有问题，默认返回True
            return True
    
    def can_copy_symbol(self, symbol: str) -> bool:
        """检查是否可以复制指定品种"""
        return self.symbol_filter.matches(symbol)
    
    def calculate_target_volume(self, source_volume: float, 
                               source_balance: float = None,
                               target_balance: float = None) -> float:
        """计算目标手数"""
        return self.volume_mapping.calculate_volume(
            source_volume, source_balance, target_balance
        )


class RelationshipManager:
    """关系管理器"""
    
    def __init__(self, storage_path: str = None, auto_save: bool = True):
        self.storage_path = Path(storage_path or "data/relationships")
        self.auto_save = auto_save
        
        # 关系存储
        self.relationships: Dict[str, TradingRelationship] = {}
        self.relationships_by_source: Dict[str, List[str]] = {}  # source_account -> [relationship_ids]
        self.relationships_by_target: Dict[str, List[str]] = {}  # target_account -> [relationship_ids]
        
        # 统计信息
        self.stats = {
            "total_relationships": 0,
            "active_relationships": 0,
            "total_signals_processed": 0,
            "last_updated": datetime.now()
        }
        
        # 回调函数
        self.on_relationship_created = []
        self.on_relationship_updated = []
        self.on_relationship_deleted = []
        
        # 确保存储目录存在
        self.storage_path.mkdir(parents=True, exist_ok=True)
        
        # 加载现有关系
        self._load_relationships()
        
        logger.info(f"关系管理器初始化完成，存储路径: {self.storage_path}")
    
    def create_relationship(self, 
                           source_account: str,
                           target_account: str,
                           relationship_type: RelationshipType = RelationshipType.FORWARD,
                           **kwargs) -> TradingRelationship:
        """创建新的交易关系"""
        
        # 验证账户
        if source_account == target_account:
            raise ValueError("源账户和目标账户不能相同")
        
        # 创建关系对象
        relationship = TradingRelationship(
            source_account=source_account,
            target_account=target_account,
            relationship_type=relationship_type,
            **kwargs
        )
        
        # 检查冲突
        conflicts = self._check_conflicts(relationship)
        if conflicts:
            raise ValueError(f"关系冲突: {conflicts}")
        
        # 添加到存储
        self.relationships[relationship.relationship_id] = relationship
        self._add_to_indexes(relationship)
        
        # 更新统计
        self.stats["total_relationships"] += 1
        self.stats["last_updated"] = datetime.now()
        
        # 保存
        if self.auto_save:
            self._save_relationship(relationship)
        
        # 触发回调
        self._trigger_callbacks(self.on_relationship_created, relationship)
        
        logger.info(f"创建交易关系: {source_account} -> {target_account} ({relationship_type.value})")
        return relationship
    
    def update_relationship(self, relationship_id: str, **updates) -> TradingRelationship:
        """更新交易关系"""
        if relationship_id not in self.relationships:
            raise ValueError(f"关系不存在: {relationship_id}")
        
        relationship = self.relationships[relationship_id]
        old_source = relationship.source_account
        old_target = relationship.target_account
        
        # 更新字段
        for key, value in updates.items():
            if hasattr(relationship, key):
                setattr(relationship, key, value)
        
        relationship.updated_at = datetime.now()
        
        # 如果账户发生变化，更新索引
        if (relationship.source_account != old_source or 
            relationship.target_account != old_target):
            self._remove_from_indexes(relationship_id, old_source, old_target)
            self._add_to_indexes(relationship)
        
        # 检查更新后的冲突
        conflicts = self._check_conflicts(relationship)
        if conflicts:
            raise ValueError(f"更新后发生冲突: {conflicts}")
        
        # 保存
        if self.auto_save:
            self._save_relationship(relationship)
        
        # 触发回调
        self._trigger_callbacks(self.on_relationship_updated, relationship)
        
        logger.info(f"更新交易关系: {relationship_id}")
        return relationship
    
    def delete_relationship(self, relationship_id: str) -> bool:
        """删除交易关系"""
        if relationship_id not in self.relationships:
            return False
        
        relationship = self.relationships[relationship_id]
        
        # 从索引中移除
        self._remove_from_indexes(relationship_id, 
                                 relationship.source_account, 
                                 relationship.target_account)
        
        # 从存储中移除
        del self.relationships[relationship_id]
        
        # 删除文件
        if self.auto_save:
            self._delete_relationship_file(relationship_id)
        
        # 更新统计
        self.stats["total_relationships"] -= 1
        self.stats["last_updated"] = datetime.now()
        
        # 触发回调
        self._trigger_callbacks(self.on_relationship_deleted, relationship)
        
        logger.info(f"删除交易关系: {relationship_id}")
        return True
    
    def get_relationship(self, relationship_id: str) -> Optional[TradingRelationship]:
        """获取指定关系"""
        return self.relationships.get(relationship_id)
    
    def get_relationships_by_source(self, source_account: str) -> List[TradingRelationship]:
        """获取指定源账户的所有关系"""
        relationship_ids = self.relationships_by_source.get(source_account, [])
        return [self.relationships[rid] for rid in relationship_ids if rid in self.relationships]
    
    def get_relationships_by_target(self, target_account: str) -> List[TradingRelationship]:
        """获取指定目标账户的所有关系"""
        relationship_ids = self.relationships_by_target.get(target_account, [])
        return [self.relationships[rid] for rid in relationship_ids if rid in self.relationships]
    
    def get_active_relationships(self, current_time: datetime = None) -> List[TradingRelationship]:
        """获取当前激活的所有关系"""
        if current_time is None:
            current_time = datetime.now()
        
        active = []
        for relationship in self.relationships.values():
            if relationship.is_active(current_time):
                active.append(relationship)
        
        return active
    
    def get_relationships_for_symbol(self, symbol: str, 
                                   current_time: datetime = None) -> List[TradingRelationship]:
        """获取支持指定品种的激活关系"""
        active_relationships = self.get_active_relationships(current_time)
        return [r for r in active_relationships if r.can_copy_symbol(symbol)]
    
    def pause_relationship(self, relationship_id: str) -> bool:
        """暂停关系"""
        if relationship_id not in self.relationships:
            return False
        
        self.relationships[relationship_id].status = RelationshipStatus.PAUSED
        self.relationships[relationship_id].updated_at = datetime.now()
        
        if self.auto_save:
            self._save_relationship(self.relationships[relationship_id])
        
        logger.info(f"暂停交易关系: {relationship_id}")
        return True
    
    def resume_relationship(self, relationship_id: str) -> bool:
        """恢复关系"""
        if relationship_id not in self.relationships:
            return False
        
        self.relationships[relationship_id].status = RelationshipStatus.ACTIVE
        self.relationships[relationship_id].updated_at = datetime.now()
        
        if self.auto_save:
            self._save_relationship(self.relationships[relationship_id])
        
        logger.info(f"恢复交易关系: {relationship_id}")
        return True
    
    def bulk_create_relationships(self, relationships_config: List[Dict]) -> List[TradingRelationship]:
        """批量创建关系"""
        created_relationships = []
        
        for config in relationships_config:
            try:
                relationship = self.create_relationship(**config)
                created_relationships.append(relationship)
            except Exception as e:
                logger.error(f"批量创建关系失败: {config}, 错误: {e}")
        
        logger.info(f"批量创建关系完成: {len(created_relationships)}/{len(relationships_config)}")
        return created_relationships
    
    def export_relationships(self, format: str = "yaml") -> str:
        """导出所有关系配置"""
        data = {
            "export_info": {
                "timestamp": datetime.now().isoformat(),
                "total_relationships": len(self.relationships),
                "format_version": "1.0"
            },
            "relationships": []
        }
        
        for relationship in self.relationships.values():
            # 转换为可序列化的字典
            relationship_dict = asdict(relationship)
            # 处理日期时间和枚举类型
            relationship_dict = self._serialize_for_export(relationship_dict)
            data["relationships"].append(relationship_dict)
        
        if format.lower() == "yaml":
            return yaml.dump(data, default_flow_style=False, allow_unicode=True)
        elif format.lower() == "json":
            return json.dumps(data, indent=2, ensure_ascii=False)
        else:
            raise ValueError(f"不支持的导出格式: {format}")
    
    def import_relationships(self, data: str, format: str = "yaml", 
                           overwrite: bool = False) -> int:
        """导入关系配置"""
        if format.lower() == "yaml":
            imported_data = yaml.safe_load(data)
        elif format.lower() == "json":
            imported_data = json.loads(data)
        else:
            raise ValueError(f"不支持的导入格式: {format}")
        
        relationships_data = imported_data.get("relationships", [])
        imported_count = 0
        
        for rel_data in relationships_data:
            try:
                # 反序列化
                rel_data = self._deserialize_from_import(rel_data)
                
                # 检查是否已存在
                relationship_id = rel_data.get("relationship_id")
                if relationship_id in self.relationships and not overwrite:
                    logger.warning(f"关系已存在，跳过: {relationship_id}")
                    continue
                
                # 创建关系对象
                relationship = TradingRelationship(**rel_data)
                
                # 添加到存储
                self.relationships[relationship.relationship_id] = relationship
                self._add_to_indexes(relationship)
                
                if self.auto_save:
                    self._save_relationship(relationship)
                
                imported_count += 1
                
            except Exception as e:
                logger.error(f"导入关系失败: {rel_data.get('relationship_id', 'unknown')}, 错误: {e}")
        
        # 更新统计
        self.stats["total_relationships"] = len(self.relationships)
        self.stats["last_updated"] = datetime.now()
        
        logger.info(f"导入关系完成: {imported_count}/{len(relationships_data)}")
        return imported_count
    
    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        current_time = datetime.now()
        active_count = len(self.get_active_relationships(current_time))
        
        self.stats.update({
            "total_relationships": len(self.relationships),
            "active_relationships": active_count,
            "last_updated": datetime.now()
        })
        
        return self.stats.copy()
    
    def cleanup_expired_relationships(self) -> int:
        """清理过期的关系"""
        current_time = datetime.now()
        expired_ids = []
        
        for relationship_id, relationship in self.relationships.items():
            if (relationship.expires_at and 
                current_time > relationship.expires_at and
                relationship.status != RelationshipStatus.EXPIRED):
                
                relationship.status = RelationshipStatus.EXPIRED
                relationship.updated_at = current_time
                expired_ids.append(relationship_id)
        
        logger.info(f"标记过期关系: {len(expired_ids)}")
        return len(expired_ids)
    
    # ============ 私有方法 ============
    
    def _add_to_indexes(self, relationship: TradingRelationship):
        """添加到索引"""
        rid = relationship.relationship_id
        
        # 源账户索引
        if relationship.source_account not in self.relationships_by_source:
            self.relationships_by_source[relationship.source_account] = []
        self.relationships_by_source[relationship.source_account].append(rid)
        
        # 目标账户索引
        if relationship.target_account not in self.relationships_by_target:
            self.relationships_by_target[relationship.target_account] = []
        self.relationships_by_target[relationship.target_account].append(rid)
    
    def _remove_from_indexes(self, relationship_id: str, source_account: str, target_account: str):
        """从索引中移除"""
        # 从源账户索引移除
        if source_account in self.relationships_by_source:
            if relationship_id in self.relationships_by_source[source_account]:
                self.relationships_by_source[source_account].remove(relationship_id)
            if not self.relationships_by_source[source_account]:
                del self.relationships_by_source[source_account]
        
        # 从目标账户索引移除
        if target_account in self.relationships_by_target:
            if relationship_id in self.relationships_by_target[target_account]:
                self.relationships_by_target[target_account].remove(relationship_id)
            if not self.relationships_by_target[target_account]:
                del self.relationships_by_target[target_account]
    
    def _check_conflicts(self, relationship: TradingRelationship) -> List[str]:
        """检查关系冲突"""
        conflicts = []
        
        # 检查是否已存在相同的关系
        for existing in self.relationships.values():
            if (existing.source_account == relationship.source_account and
                existing.target_account == relationship.target_account and
                existing.relationship_id != relationship.relationship_id):
                conflicts.append(f"已存在相同的关系: {existing.relationship_id}")
        
        # 可以添加更多冲突检查逻辑...
        
        return conflicts
    
    def _load_relationships(self):
        """加载现有关系"""
        try:
            relationships_file = self.storage_path / "relationships.yaml"
            if relationships_file.exists():
                with open(relationships_file, 'r', encoding='utf-8') as f:
                    data = yaml.safe_load(f) or {}
                
                relationships_data = data.get("relationships", [])
                for rel_data in relationships_data:
                    try:
                        rel_data = self._deserialize_from_import(rel_data)
                        relationship = TradingRelationship(**rel_data)
                        self.relationships[relationship.relationship_id] = relationship
                        self._add_to_indexes(relationship)
                    except Exception as e:
                        logger.error(f"加载关系失败: {e}")
                
                logger.info(f"加载关系: {len(self.relationships)} 个")
            
        except Exception as e:
            logger.error(f"加载关系文件失败: {e}")
    
    def _save_relationship(self, relationship: TradingRelationship):
        """保存单个关系"""
        try:
            # 保存到主文件
            self._save_all_relationships()
        except Exception as e:
            logger.error(f"保存关系失败: {e}")
    
    def _save_all_relationships(self):
        """保存所有关系到文件"""
        try:
            data = {
                "metadata": {
                    "timestamp": datetime.now().isoformat(),
                    "total_relationships": len(self.relationships),
                    "version": "1.0"
                },
                "relationships": []
            }
            
            for relationship in self.relationships.values():
                rel_data = asdict(relationship)
                rel_data = self._serialize_for_export(rel_data)
                data["relationships"].append(rel_data)
            
            relationships_file = self.storage_path / "relationships.yaml"
            with open(relationships_file, 'w', encoding='utf-8') as f:
                yaml.dump(data, f, default_flow_style=False, allow_unicode=True)
            
        except Exception as e:
            logger.error(f"保存关系文件失败: {e}")
    
    def _delete_relationship_file(self, relationship_id: str):
        """删除关系文件"""
        # 由于我们使用单一文件存储，这里重新保存整个文件
        self._save_all_relationships()
    
    def _serialize_for_export(self, data: Dict) -> Dict:
        """序列化数据用于导出"""
        # 处理日期时间
        for key, value in data.items():
            if isinstance(value, datetime):
                data[key] = value.isoformat()
            elif isinstance(value, Enum):
                data[key] = value.value
            elif isinstance(value, dict):
                data[key] = self._serialize_for_export(value)
        
        return data
    
    def _deserialize_from_import(self, data: Dict) -> Dict:
        """从导入数据反序列化"""
        # 处理日期时间
        datetime_fields = ['created_at', 'updated_at', 'starts_at', 'expires_at']
        for field in datetime_fields:
            if field in data and data[field]:
                try:
                    data[field] = datetime.fromisoformat(data[field])
                except:
                    data[field] = None
        
        # 处理枚举
        if 'relationship_type' in data:
            data['relationship_type'] = RelationshipType(data['relationship_type'])
        if 'status' in data:
            data['status'] = RelationshipStatus(data['status'])
        
        # 处理嵌套对象
        if 'volume_mapping' in data:
            data['volume_mapping'] = VolumeMapping(**data['volume_mapping'])
        if 'symbol_filter' in data:
            data['symbol_filter'] = SymbolFilter(**data['symbol_filter'])
        if 'time_window' in data:
            data['time_window'] = TimeWindow(**data['time_window'])
        if 'risk_constraints' in data:
            data['risk_constraints'] = RiskConstraints(**data['risk_constraints'])
        
        return data
    
    def _trigger_callbacks(self, callback_list: List, relationship: TradingRelationship):
        """触发回调函数"""
        for callback in callback_list:
            try:
                if asyncio.iscoroutinefunction(callback):
                    asyncio.create_task(callback(relationship))
                else:
                    callback(relationship)
            except Exception as e:
                logger.error(f"回调函数执行失败: {e}")


# 全局关系管理器实例
_relationship_manager = None


def get_relationship_manager(storage_path: str = None) -> RelationshipManager:
    """获取全局关系管理器实例"""
    global _relationship_manager
    
    if _relationship_manager is None:
        if storage_path is None:
            storage_path = os.getenv('MT5_RELATIONSHIPS_STORAGE', 'data/relationships')
        _relationship_manager = RelationshipManager(storage_path)
    
    return _relationship_manager