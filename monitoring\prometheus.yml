alerting:
  alertmanagers:
  - static_configs:
    - targets:
      - localhost:9093
global:
  evaluation_interval: 15s
  scrape_interval: 15s
rule_files:
- mt5_alerts.yml
scrape_configs:
- job_name: mt5-copy-trading
  metrics_path: /metrics
  scrape_interval: 5s
  static_configs:
  - targets:
    - localhost:8080
- job_name: mt5-signal-bridge
  scrape_interval: 5s
  static_configs:
  - targets:
    - localhost:8081
- job_name: mt5-account-processes
  scrape_interval: 10s
  static_configs:
  - targets:
    - localhost:8082
    - localhost:8083
    - localhost:8084
