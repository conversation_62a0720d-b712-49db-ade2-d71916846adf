# Redis Sentinel配置 - MT5分布式交易系统
# 提供Redis高可用性，支持自动故障转移

# 基本配置
port 26379
bind 0.0.0.0
protected-mode no

# 监控主Redis实例
sentinel monitor mt5-redis redis-master 6379 2

# 故障检测配置
sentinel down-after-milliseconds mt5-redis 5000
sentinel failover-timeout mt5-redis 60000
sentinel parallel-syncs mt5-redis 1

# 认证配置 (如果Redis启用了密码)
# sentinel auth-pass mt5-redis your_redis_password

# 日志配置
logfile "/data/sentinel.log"
loglevel notice

# 客户端连接
sentinel deny-scripts-reconfig yes

# 故障转移脚本 (可选)
# sentinel notification-script mt5-redis /scripts/notify.sh
# sentinel client-reconfig-script mt5-redis /scripts/reconfig.sh

# Sentinel实例标识
sentinel myid $(cat /proc/sys/kernel/random/uuid | tr -d '-')

# 发布订阅配置
sentinel announce-ip 127.0.0.1
sentinel announce-port 26379
