#!/usr/bin/env python3
"""
优化的NATS管理器 - 替代分布式协调器
专门管理优化的NATS架构：分层流管理 + 混合消费者策略 + 优先级队列集成
"""

import asyncio
import time
from typing import Dict, Any, Optional, List, Callable, Set
from dataclasses import dataclass
from datetime import datetime

from .jetstream_client import JetStreamClient
from .priority_queue import get_priority_queue, MessagePriority, PriorityAnalyzer
from ..utils.logger import get_logger

logger = get_logger(__name__)


@dataclass
class StreamLayerConfig:
    """分层流配置"""
    name: str
    subjects: List[str]
    retention: str
    max_age: int
    max_msgs: int
    storage: str
    replicas: int
    description: str


@dataclass  
class ConsumerStrategyConfig:
    """消费者策略配置"""
    name: str
    stream: str
    subject_filter: str
    priority: MessagePriority
    mode: str  # "broadcast" or "queue_group"
    queue_group: Optional[str] = None
    max_deliver: int = 3
    ack_wait: int = 30
    enabled: bool = True


class NATSManager:
    """
    优化的NATS管理器 - 替代src/distributed/coordinator.py
    
    核心功能：
    1. 分层流架构管理（4层流）
    2. 混合消费者策略（广播+队列组）
    3. 与priority_queue.py深度集成
    4. MT5进程限制处理
    5. 智能路由（本地直达，跨主机NATS）
    """
    
    def __init__(self, host_id: str, jetstream_client: JetStreamClient, config: Dict[str, Any] = None):
        self.host_id = host_id
        self.jetstream = jetstream_client
        self.config = config or {}
        self.running = False
        
        # 优先级队列集成
        self.priority_queue = get_priority_queue()
        
        # 分层流配置
        self.layer_configs = self._initialize_layer_configs()
        
        # 消费者策略配置  
        self.consumer_strategies = self._initialize_consumer_strategies()
        
        # 运行时状态
        self.active_streams: Set[str] = set()
        self.active_consumers: Dict[str, Any] = {}
        self.message_handlers: Dict[str, Callable] = {}
        
        # 统计信息
        self.stats = {
            'messages_processed': 0,
            'messages_published': 0,
            'routing_errors': 0,
            'local_route_count': 0,
            'cross_host_route_count': 0,
            'priority_escalations': 0,
            'last_activity': None,
            'uptime_start': None
        }
        
        logger.info(f"✅ 优化NATS管理器初始化完成 - 主机: {host_id}")
    
    def _initialize_layer_configs(self) -> Dict[str, StreamLayerConfig]:
        """初始化分层流配置"""
        return {
            "local": StreamLayerConfig(
                name=f"MT5_LOCAL_{self.host_id}",
                subjects=[f"MT5.LOCAL.{self.host_id}.*"],
                retention="workqueue",
                max_age=300,
                max_msgs=10000,
                storage="memory",
                replicas=1,
                description="本地主机内部通信流"
            ),
            "signals": StreamLayerConfig(
                name="MT5_SIGNALS",
                subjects=["MT5.SIGNALS.*", "MT5.COPY.*"],
                retention="limits",
                max_age=3600 * 24,  # 24 hours
                max_msgs=1000000,
                storage="file",
                replicas=3,
                description="全局交易信号流"
            ),
            "rpc": StreamLayerConfig(
                name="MT5_RPC",
                subjects=["MT5.RPC.*"],
                retention="workqueue",
                max_age=60,
                max_msgs=50000,
                storage="memory",
                replicas=1,
                description="RPC通信流"
            ),
            "control": StreamLayerConfig(
                name="MT5_CONTROL",
                subjects=["MT5.CONTROL.*", "MT5.HEARTBEAT.*"],
                retention="limits",
                max_age=3600,
                max_msgs=200000,
                storage="file",
                replicas=3,
                description="系统控制和心跳流"
            )
        }
    
    def _initialize_consumer_strategies(self) -> List[ConsumerStrategyConfig]:
        """初始化消费者策略配置"""
        return [
            # 关键信号 - 广播
            ConsumerStrategyConfig(
                name="critical_signal_consumer",
                stream="MT5_SIGNALS",
                subject_filter="MT5.SIGNALS.CRITICAL.*",
                priority=MessagePriority.CRITICAL,
                mode="broadcast"
            ),
            # 高优先级信号 - 队列组
            ConsumerStrategyConfig(
                name="high_priority_signal_consumer",
                stream="MT5_SIGNALS",
                subject_filter="MT5.SIGNALS.HIGH.*",
                priority=MessagePriority.HIGH,
                mode="queue_group",
                queue_group="high_priority_processors"
            ),
            # 普通信号 - 队列组
            ConsumerStrategyConfig(
                name="normal_signal_consumer",
                stream="MT5_SIGNALS",
                subject_filter="MT5.SIGNALS.NORMAL.*",
                priority=MessagePriority.NORMAL,
                mode="queue_group",
                queue_group="normal_priority_processors"
            ),
            # 本地控制命令
            ConsumerStrategyConfig(
                name=f"local_control_consumer_{self.host_id}",
                stream=f"MT5_LOCAL_{self.host_id}",
                subject_filter=f"MT5.LOCAL.{self.host_id}.CONTROL.*",
                priority=MessagePriority.HIGH,
                mode="broadcast"
            )
        ]
    
    async def initialize(self) -> bool:
        """初始化NATS管理器 - 简化版"""
        try:
            logger.info(f"🚀 开始初始化NATS管理器 - 主机: {self.host_id}")

            # 1. 确保JetStream连接
            if not self.jetstream.is_connected():
                logger.info("JetStream未连接，尝试连接...")
                if not await self.jetstream.connect():
                    logger.error("JetStream连接失败")
                    return False
            
            # 2. 初始化流和消费者（委托给JetStreamClient）
            await self.jetstream.initialize(self.host_id)

            # 3. 启动优先级队列工作器
            await self._start_priority_queue_workers()

            # 4. 注册消息处理器
            self._register_message_handlers()

            self.running = True
            self.stats['uptime_start'] = time.time()

            logger.info("✅ NATS管理器初始化完成")
            return True

        except Exception as e:
            logger.error(f"初始化NATS管理器失败: {e}")
            return False
    
    
    
    # 旧的硬编码消费者创建方法已删除，统一由JetStreamClient处理
    
    def _get_message_handler(self, priority: MessagePriority) -> Callable:
        """根据优先级获取消息处理器"""
        if priority not in self.message_handlers:
            # 创建优先级特定的处理器
            self.message_handlers[priority] = self._create_priority_handler(priority)
        
        return self.message_handlers[priority]
    
    def _create_priority_handler(self, priority: MessagePriority) -> Callable:
        """创建优先级特定的消息处理器"""
        async def priority_message_handler(message):
            try:
                start_time = time.perf_counter()
                
                # 解析消息数据
                data = message.data
                subject = message.subject
                
                logger.debug(f"处理{priority.name}优先级消息: {subject}")
                
                # 根据优先级决定处理策略
                if priority == MessagePriority.CRITICAL:
                    # 关键消息直接处理，绕过队列
                    result = await self._handle_critical_message(data, subject)
                elif priority == MessagePriority.HIGH:
                    # 高优先级消息优先入队
                    result = await self.priority_queue.enqueue(priority, data)
                else:
                    # 普通和低优先级消息正常入队
                    result = await self.priority_queue.enqueue(priority, data)
                
                # 确认消息
                if result:
                    await message.ack()
                    self.stats['messages_processed'] += 1
                else:
                    await message.nak()
                    self.stats['routing_errors'] += 1
                
                # 更新统计
                process_time = (time.perf_counter() - start_time) * 1000
                self.stats['last_activity'] = time.time()
                
                logger.debug(f"{priority.name}消息处理完成 - 耗时: {process_time:.1f}ms")
                
            except Exception as e:
                logger.error(f"{priority.name}消息处理失败: {e}")
                try:
                    await message.nak()
                except:
                    pass
                self.stats['routing_errors'] += 1
        
        return priority_message_handler
    
    async def _handle_critical_message(self, data: Dict[str, Any], subject: str) -> bool:
        """处理关键消息 - 直接执行，最高优先级"""
        try:
            # 关键消息直接执行，不经过队列
            logger.info(f"🚨 处理关键消息: {subject}")
            
            # 这里可以直接调用MT5执行器或其他关键处理逻辑
            # 暂时返回True表示处理成功
            return True
            
        except Exception as e:
            logger.error(f"关键消息处理失败: {e}")
            return False
    
    async def _start_priority_queue_workers(self):
        """启动优先级队列工作器"""
        try:
            # 启动多个工作器处理不同优先级
            worker_count = self.config.get('priority_workers', 6)
            await self.priority_queue.start_workers(worker_count=worker_count)
            
            logger.info(f"✅ 优先级队列工作器启动完成: {worker_count}个")
            
        except Exception as e:
            logger.error(f"启动优先级队列工作器失败: {e}")
    
    def _register_message_handlers(self):
        """注册消息处理器"""
        logger.info("注册消息处理器...")
        # 消息处理器已在 _get_message_handler 中动态创建
        logger.info("✅ 消息处理器注册完成")
    
    async def route_signal(self, signal_data: Dict[str, Any]) -> bool:
        """智能路由信号 - 本地直达，跨主机NATS"""
        try:
            target_host = signal_data.get('target_host_id')
            
            if target_host == self.host_id or not target_host:
                # 本地路由 - 直接使用优先级队列，最低延迟
                return await self._route_local_signal(signal_data)
            else:
                # 跨主机路由 - 使用NATS分层流
                return await self._route_cross_host_signal(signal_data)
                
        except Exception as e:
            logger.error(f"信号路由失败: {e}")
            self.stats['routing_errors'] += 1
            return False
    
    async def _route_local_signal(self, signal_data: Dict[str, Any]) -> bool:
        """本地信号路由 - 直接使用优先级队列"""
        try:
            # 分析优先级
            priority = PriorityAnalyzer.get_command_priority(
                method=signal_data.get('command_type', 'unknown'),
                params=signal_data
            )
            
            # 直接入队，无需网络传输
            success = await self.priority_queue.enqueue(priority, signal_data)
            
            if success:
                self.stats['local_route_count'] += 1
                logger.debug(f"本地信号路由成功: {priority.name}")
            
            return success
            
        except Exception as e:
            logger.error(f"本地信号路由失败: {e}")
            return False
    
    async def _route_cross_host_signal(self, signal_data: Dict[str, Any]) -> bool:
        """跨主机信号路由 - 使用NATS分层流"""
        try:
            # 使用JetStream的优先级发布
            success = await self.jetstream.publish_with_priority(signal_data)
            
            if success:
                self.stats['cross_host_route_count'] += 1
                self.stats['messages_published'] += 1
                logger.debug("跨主机信号路由成功")
            
            return success
            
        except Exception as e:
            logger.error(f"跨主机信号路由失败: {e}")
            return False
    
    async def publish_with_priority(self, data: Dict[str, Any], priority: MessagePriority = None) -> bool:
        """发布优先级消息 - 统一入口"""
        return await self.jetstream.publish_with_priority(data, priority)
    
    async def shutdown(self):
        """关闭优化管理器"""
        if not self.running:
            return
        
        try:
            logger.info("🛑 开始关闭优化NATS管理器...")
            
            # 1. 停止接收新消息
            self.running = False
            
            # 2. 停止优先级队列工作器
            if hasattr(self.priority_queue, 'stop_workers'):
                await self.priority_queue.stop_workers()
            
            # 3. 取消所有消费者
            for consumer_name in list(self.active_consumers.keys()):
                try:
                    await self.jetstream.unsubscribe(consumer_name)
                except Exception as e:
                    logger.warning(f"取消消费者失败 {consumer_name}: {e}")
            
            # 4. 清理状态
            self.active_consumers.clear()
            self.active_streams.clear()
            self.message_handlers.clear()
            
            logger.info("✅ 优化NATS管理器已关闭")
            
        except Exception as e:
            logger.error(f"关闭优化管理器失败: {e}")
    
    def get_status(self) -> Dict[str, Any]:
        """获取管理器状态"""
        uptime = time.time() - self.stats['uptime_start'] if self.stats['uptime_start'] else 0
        
        return {
            'running': self.running,
            'host_id': self.host_id,
            'uptime_seconds': uptime,
            'active_streams': list(self.active_streams),
            'active_consumers': list(self.active_consumers.keys()),
            'layered_streams': {
                layer: config.name for layer, config in self.layer_configs.items()
            },
            'consumer_strategies': [
                {
                    'name': s.name,
                    'mode': s.mode,
                    'priority': s.priority.name,
                    'enabled': s.enabled
                }
                for s in self.consumer_strategies
            ],
            'statistics': dict(self.stats),
            'jetstream_connected': self.jetstream.is_connected()
        }
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """获取性能统计"""
        uptime = time.time() - self.stats['uptime_start'] if self.stats['uptime_start'] else 1
        
        return {
            'messages_per_second': self.stats['messages_processed'] / uptime,
            'publish_per_second': self.stats['messages_published'] / uptime,
            'error_rate': self.stats['routing_errors'] / max(self.stats['messages_processed'], 1),
            'local_route_ratio': self.stats['local_route_count'] / max(
                self.stats['local_route_count'] + self.stats['cross_host_route_count'], 1
            ),
            'total_processed': self.stats['messages_processed'],
            'total_published': self.stats['messages_published'],
            'total_errors': self.stats['routing_errors'],
            'uptime_hours': uptime / 3600
        }


class MT5ProcessIsolationStrategy:
    """MT5进程隔离策略 - 处理MT5 API限制"""
    
    def __init__(self):
        self.process_limits = {
            'max_api_calls_per_second': 80,  # 留20%缓冲
            'max_concurrent_connections': 3,  # 留2个缓冲
            'memory_threshold_mb': 1500,     # 1.5GB阈值
            'restart_interval_hours': 24     # 24小时重启
        }
        
        self.process_stats: Dict[str, Dict] = {}
        
    async def handle_api_rate_limit(self, account_id: str, signal_data: Dict[str, Any]) -> bool:
        """处理API速率限制"""
        try:
            # 检查进程状态
            stats = self.process_stats.get(account_id, {})
            current_rate = stats.get('api_calls_per_second', 0)
            
            if current_rate > self.process_limits['max_api_calls_per_second']:
                logger.warning(f"账户 {account_id} API调用频率过高: {current_rate}/s")
                
                # 使用优先级队列缓冲请求
                priority_queue = get_priority_queue()
                
                # 根据信号类型决定优先级
                priority = PriorityAnalyzer.get_command_priority(
                    method=signal_data.get('command_type', 'unknown'),
                    params=signal_data
                )
                
                # 关键信号直接处理，其他排队
                if priority == MessagePriority.CRITICAL:
                    return await self._execute_immediately(account_id, signal_data)
                else:
                    return await priority_queue.enqueue(priority, signal_data)
            else:
                # 正常执行
                return await self._execute_immediately(account_id, signal_data)
                
        except Exception as e:
            logger.error(f"处理API速率限制失败: {e}")
            return False
    
    async def _execute_immediately(self, account_id: str, signal_data: Dict[str, Any]) -> bool:
        """立即执行信号"""
        # 这里应该调用实际的MT5执行逻辑
        logger.debug(f"立即执行信号: {account_id} - {signal_data.get('command_type')}")
        return True
    
    def update_process_stats(self, account_id: str, stats: Dict[str, Any]):
        """更新进程统计信息"""
        self.process_stats[account_id] = {
            **stats,
            'last_update': time.time()
        }