#!/usr/bin/env python3
"""
优化的NATS管理器
封装分层流架构，提供简化API，处理MT5 API限制
"""
import asyncio
import time
from typing import Dict, Any, Optional, Callable, List
from dataclasses import dataclass
from collections import defaultdict, deque

from .jetstream_client import JetStreamClient, JetStreamConfig
from .priority_queue import get_priority_queue, MessagePriority, PriorityAnalyzer
from ..utils.logger import get_logger
from ..utils.metrics import get_metrics_collector

logger = get_logger(__name__)
metrics = get_metrics_collector()


@dataclass
class MT5RateLimit:
    """MT5 API速率限制配置"""
    max_calls_per_second: int = 80  # 每秒最大调用次数
    burst_capacity: int = 120       # 突发容量
    window_size_seconds: float = 1.0  # 时间窗口大小
    cooldown_seconds: float = 0.1   # 冷却时间


class RateLimiter:
    """MT5 API速率限制器 - 令牌桶算法"""
    
    def __init__(self, rate_limit: MT5RateLimit):
        self.max_tokens = rate_limit.burst_capacity
        self.refill_rate = rate_limit.max_calls_per_second
        self.tokens = self.max_tokens
        self.last_refill = time.time()
        self.call_times = deque()
        self.cooldown = rate_limit.cooldown_seconds
        self._lock = asyncio.Lock()
        
        logger.info(f"速率限制器初始化: {self.refill_rate}/s, 突发: {self.max_tokens}")
    
    async def acquire(self, tokens_needed: int = 1) -> bool:
        """获取令牌"""
        async with self._lock:
            current_time = time.time()
            
            # 令牌桶补充
            time_passed = current_time - self.last_refill
            self.tokens = min(
                self.max_tokens,
                self.tokens + time_passed * self.refill_rate
            )
            self.last_refill = current_time
            
            # 清理过期的调用记录
            while self.call_times and current_time - self.call_times[0] > 1.0:
                self.call_times.popleft()
            
            # 检查是否可以获取令牌
            if self.tokens >= tokens_needed and len(self.call_times) < self.refill_rate:
                self.tokens -= tokens_needed
                self.call_times.append(current_time)
                return True
            
            # 记录速率限制事件
            metrics.increment("mt5_rate_limit_hits_total")
            return False
    
    async def wait_for_capacity(self, timeout: float = 5.0) -> bool:
        """等待可用容量"""
        start_time = time.time()
        
        while time.time() - start_time < timeout:
            if await self.acquire():
                return True
            await asyncio.sleep(self.cooldown)
        
        return False
    
    def get_status(self) -> Dict[str, Any]:
        """获取速率限制器状态"""
        current_time = time.time()
        recent_calls = sum(1 for call_time in self.call_times 
                          if current_time - call_time <= 1.0)
        
        return {
            'tokens_available': int(self.tokens),
            'max_tokens': self.max_tokens,
            'calls_in_last_second': recent_calls,
            'max_calls_per_second': self.refill_rate,
            'utilization_percent': (recent_calls / self.refill_rate) * 100
        }


class NATSManager:
    """优化的NATS管理器 - 替代分布式coordinator功能"""
    
    def __init__(self, host_id: str, nats_config: Dict[str, Any]):
        self.host_id = host_id
        self.nats_config = nats_config
        
        # 核心组件
        self.jetstream_client: Optional[JetStreamClient] = None
        self.priority_queue = get_priority_queue()
        self.rate_limiter = RateLimiter(MT5RateLimit())
        
        # 状态管理
        self.is_initialized = False
        self.is_running = False
        self._shutdown_event = asyncio.Event()
        
        # 路由表
        self.local_accounts = set()  # 本主机账户
        self.remote_accounts = {}    # 远程账户映射 {account_id: host_id}
        
        # 统计信息
        self.stats = {
            'messages_sent': 0,
            'messages_received': 0,
            'local_routes': 0,
            'remote_routes': 0,
            'rate_limited': 0,
            'errors': 0
        }
        
        # 回调注册
        self.signal_callbacks = {}    # {priority: callback}
        self.rpc_callbacks = {}       # {method: callback}
        self.local_callbacks = {}     # {command: callback}
        
        logger.info(f"优化NATS管理器初始化 - 主机: {host_id}")
    
    async def initialize(self) -> bool:
        """初始化优化架构"""
        if self.is_initialized:
            logger.warning("NATS管理器已初始化")
            return True
        
        try:
            logger.info(f"初始化优化NATS架构 - 主机: {self.host_id}")
            
            # 1. 创建JetStream客户端
            self.jetstream_client = JetStreamClient(self.nats_config)
            
            # 2. 连接NATS服务器
            if not await self.jetstream_client.connect():
                logger.error("NATS连接失败")
                return False
            
            # 3. 初始化优化架构
            if not await self.jetstream_client.initialize_optimized_architecture(self.host_id):
                logger.error("优化架构初始化失败")
                return False
            
            # 4. 启动优先级队列工作器
            await self.priority_queue.start_workers(worker_count=6)
            
            # 5. 注册回调处理器
            await self._register_callbacks()
            
            self.is_initialized = True
            logger.info(f"优化NATS架构初始化成功 - 主机: {self.host_id}")
            return True
            
        except Exception as e:
            logger.error(f"优化NATS架构初始化失败: {e}")
            return False
    
    async def _register_callbacks(self):
        """注册消息处理回调"""
        
        # 注册优先级队列处理器
        for priority in MessagePriority:
            self.priority_queue.register_processor(
                priority, 
                self._create_priority_processor(priority)
            )
        
        logger.info("消息处理器注册完成")
    
    def _create_priority_processor(self, priority: MessagePriority) -> Callable:
        """创建优先级处理器"""
        async def processor(data: Dict[str, Any]):
            try:
                message_type = data.get('message_type', 'unknown')
                
                if message_type == 'trade_signal':
                    await self._process_trade_signal(data, priority)
                elif message_type == 'rpc_request':
                    await self._process_rpc_request(data, priority)
                elif message_type == 'local_command':
                    await self._process_local_command(data, priority)
                else:
                    logger.warning(f"未知消息类型: {message_type}")
                    
            except Exception as e:
                logger.error(f"{priority.name}优先级处理器异常: {e}")
                self.stats['errors'] += 1
        
        return processor
    
    async def send_trade_signal(self, signal_data: Dict[str, Any], 
                               target_account: str = None, 
                               target_host: str = None) -> bool:
        """发送交易信号"""
        try:
            # 速率限制检查
            if not await self.rate_limiter.acquire():
                if not await self.rate_limiter.wait_for_capacity(timeout=2.0):
                    logger.warning("速率限制超时，信号被丢弃")
                    self.stats['rate_limited'] += 1
                    return False
            
            # 增强信号数据
            enhanced_signal = {
                **signal_data,
                'message_type': 'trade_signal',
                'source_host': self.host_id,
                'target_host_id': target_host,
                'target_account': target_account,
                'timestamp': time.time()
            }
            
            # 路由决策
            if target_host == self.host_id or target_host is None:
                # 本地路由
                return await self._route_locally(enhanced_signal)
            else:
                # 跨主机路由
                return await self._route_remotely(enhanced_signal)
                
        except Exception as e:
            logger.error(f"发送交易信号失败: {e}")
            self.stats['errors'] += 1
            return False
    
    async def send_rpc_request(self, method: str, params: Dict[str, Any], 
                              target_host: str = None) -> Optional[Dict[str, Any]]:
        """发送RPC请求"""
        try:
            # 速率限制检查
            if not await self.rate_limiter.acquire():
                logger.warning("RPC请求受速率限制")
                return None
            
            rpc_data = {
                'message_type': 'rpc_request',
                'method': method,
                'params': params,
                'source_host': self.host_id,
                'target_host': target_host,
                'request_id': f"rpc_{int(time.time() * 1000000)}",
                'timestamp': time.time()
            }
            
            if target_host == self.host_id or target_host is None:
                # 本地RPC
                return await self._execute_local_rpc(rpc_data)
            else:
                # 跨主机RPC
                return await self._execute_remote_rpc(rpc_data)
                
        except Exception as e:
            logger.error(f"RPC请求失败: {e}")
            self.stats['errors'] += 1
            return None
    
    async def _route_locally(self, signal_data: Dict[str, Any]) -> bool:
        """本地路由处理"""
        try:
            # 使用本地流，最低延迟
            subject = f"MT5.LOCAL.{self.host_id}.EXECUTION.{signal_data.get('target_account', 'default')}"
            
            success = await self.jetstream_client.publish(subject, signal_data)
            if success:
                self.stats['local_routes'] += 1
                self.stats['messages_sent'] += 1
            
            return success
            
        except Exception as e:
            logger.error(f"本地路由失败: {e}")
            # 降级到优先级队列
            priority = PriorityAnalyzer.get_command_priority('send_order', signal_data)
            return await self.priority_queue.enqueue(priority, signal_data)
    
    async def _route_remotely(self, signal_data: Dict[str, Any]) -> bool:
        """跨主机路由处理"""
        try:
            # 使用NATS智能发布
            success = await self.jetstream_client.publish_with_priority(
                signal_data, method='send_order'
            )
            
            if success:
                self.stats['remote_routes'] += 1
                self.stats['messages_sent'] += 1
            
            return success
            
        except Exception as e:
            logger.error(f"跨主机路由失败: {e}")
            return False
    
    async def _execute_local_rpc(self, rpc_data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """执行本地RPC"""
        method = rpc_data.get('method')
        
        if method in self.rpc_callbacks:
            try:
                callback = self.rpc_callbacks[method]
                result = await callback(rpc_data.get('params', {}))
                return {'success': True, 'result': result}
            except Exception as e:
                logger.error(f"本地RPC执行失败 {method}: {e}")
                return {'success': False, 'error': str(e)}
        else:
            logger.warning(f"未注册的RPC方法: {method}")
            return {'success': False, 'error': f'Method not found: {method}'}
    
    async def _execute_remote_rpc(self, rpc_data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """执行跨主机RPC"""
        try:
            target_host = rpc_data.get('target_host')
            subject = f"MT5.RPC.EXECUTE.{target_host}"
            
            # 使用request/response模式
            response = await self.jetstream_client.request(subject, rpc_data, timeout=10.0)
            return response
            
        except Exception as e:
            logger.error(f"跨主机RPC执行失败: {e}")
            return {'success': False, 'error': str(e)}
    
    async def _process_trade_signal(self, data: Dict[str, Any], priority: MessagePriority):
        """处理交易信号"""
        try:
            signal_id = data.get('signal_id', 'unknown')
            account_id = data.get('target_account', 'unknown')
            
            logger.debug(f"处理交易信号: {signal_id} -> {account_id} ({priority.name})")
            
            # 调用注册的信号处理器
            if priority in self.signal_callbacks:
                callback = self.signal_callbacks[priority]
                await callback(data)
            else:
                logger.warning(f"未注册{priority.name}优先级信号处理器")
            
            self.stats['messages_received'] += 1
            
        except Exception as e:
            logger.error(f"交易信号处理失败: {e}")
            self.stats['errors'] += 1
    
    async def _process_rpc_request(self, data: Dict[str, Any], priority: MessagePriority):
        """处理RPC请求"""
        try:
            method = data.get('method', 'unknown')
            request_id = data.get('request_id', 'unknown')
            
            logger.debug(f"处理RPC请求: {method} ({request_id})")
            
            if method in self.rpc_callbacks:
                callback = self.rpc_callbacks[method]
                result = await callback(data.get('params', {}))
                
                # 如果有回复地址，发送响应
                reply_subject = data.get('reply_subject')
                if reply_subject:
                    response = {'success': True, 'result': result}
                    await self.jetstream_client.publish(reply_subject, response)
            
            self.stats['messages_received'] += 1
            
        except Exception as e:
            logger.error(f"RPC请求处理失败: {e}")
            self.stats['errors'] += 1
    
    async def _process_local_command(self, data: Dict[str, Any], priority: MessagePriority):
        """处理本地命令"""
        try:
            command = data.get('command', 'unknown')
            
            logger.debug(f"处理本地命令: {command}")
            
            if command in self.local_callbacks:
                callback = self.local_callbacks[command]
                await callback(data)
            
            self.stats['messages_received'] += 1
            
        except Exception as e:
            logger.error(f"本地命令处理失败: {e}")
            self.stats['errors'] += 1
    
    def register_signal_handler(self, priority: MessagePriority, callback: Callable):
        """注册信号处理器"""
        self.signal_callbacks[priority] = callback
        logger.info(f"注册{priority.name}优先级信号处理器")
    
    def register_rpc_handler(self, method: str, callback: Callable):
        """注册RPC处理器"""
        self.rpc_callbacks[method] = callback
        logger.info(f"注册RPC处理器: {method}")
    
    def register_local_handler(self, command: str, callback: Callable):
        """注册本地命令处理器"""
        self.local_callbacks[command] = callback
        logger.info(f"注册本地命令处理器: {command}")
    
    def register_account(self, account_id: str, host_id: str = None):
        """注册账户路由"""
        if host_id is None or host_id == self.host_id:
            self.local_accounts.add(account_id)
            logger.info(f"注册本地账户: {account_id}")
        else:
            self.remote_accounts[account_id] = host_id
            logger.info(f"注册远程账户: {account_id} -> {host_id}")
    
    async def start(self) -> bool:
        """启动NATS管理器"""
        if not self.is_initialized:
            if not await self.initialize():
                return False
        
        if self.is_running:
            logger.warning("NATS管理器已运行")
            return True
        
        try:
            self.is_running = True
            self._shutdown_event.clear()
            
            # 启动监控任务
            asyncio.create_task(self._monitoring_loop())
            
            logger.info(f"优化NATS管理器启动成功 - 主机: {self.host_id}")
            return True
            
        except Exception as e:
            logger.error(f"NATS管理器启动失败: {e}")
            self.is_running = False
            return False
    
    async def stop(self):
        """停止NATS管理器"""
        if not self.is_running:
            return
        
        logger.info(f"停止优化NATS管理器 - 主机: {self.host_id}")
        
        try:
            self.is_running = False
            self._shutdown_event.set()
            
            # 停止优先级队列
            await self.priority_queue.stop_workers()
            
            # 断开NATS连接
            if self.jetstream_client:
                await self.jetstream_client.disconnect()
            
            logger.info(f"优化NATS管理器已停止 - 主机: {self.host_id}")
            
        except Exception as e:
            logger.error(f"NATS管理器停止失败: {e}")
    
    async def _monitoring_loop(self):
        """监控循环"""
        while self.is_running:
            try:
                await asyncio.sleep(30)  # 每30秒监控一次
                
                # 记录统计信息
                rate_status = self.rate_limiter.get_status()
                
                metrics.set_gauge("nats_messages_sent_total", self.stats['messages_sent'])
                metrics.set_gauge("nats_messages_received_total", self.stats['messages_received'])
                metrics.set_gauge("nats_local_routes_total", self.stats['local_routes'])
                metrics.set_gauge("nats_remote_routes_total", self.stats['remote_routes'])
                metrics.set_gauge("nats_rate_limited_total", self.stats['rate_limited'])
                metrics.set_gauge("nats_errors_total", self.stats['errors'])
                
                metrics.set_gauge("mt5_rate_limit_utilization", rate_status['utilization_percent'])
                metrics.set_gauge("mt5_tokens_available", rate_status['tokens_available'])
                
                logger.debug(f"NATS管理器状态: {self.get_status()}")
                
            except Exception as e:
                logger.error(f"监控循环异常: {e}")
                await asyncio.sleep(5)
    
    def get_status(self) -> Dict[str, Any]:
        """获取管理器状态"""
        jetstream_status = None
        if self.jetstream_client:
            jetstream_status = self.jetstream_client.get_optimization_status()
        
        return {
            'host_id': self.host_id,
            'is_initialized': self.is_initialized,
            'is_running': self.is_running,
            'local_accounts': list(self.local_accounts),
            'remote_accounts': dict(self.remote_accounts),
            'stats': dict(self.stats),
            'rate_limiter': self.rate_limiter.get_status(),
            'jetstream': jetstream_status,
            'callbacks_registered': {
                'signals': list(self.signal_callbacks.keys()),
                'rpc': list(self.rpc_callbacks.keys()),
                'local': list(self.local_callbacks.keys())
            }
        }
    
    def get_health_status(self) -> Dict[str, Any]:
        """获取健康状态"""
        is_healthy = (
            self.is_initialized and 
            self.is_running and 
            self.jetstream_client and 
            self.jetstream_client.is_connected()
        )
        
        return {
            'healthy': is_healthy,
            'components': {
                'initialized': self.is_initialized,
                'running': self.is_running,
                'jetstream_connected': self.jetstream_client.is_connected() if self.jetstream_client else False,
                'priority_queue_running': self.priority_queue._running if self.priority_queue else False
            }
        }