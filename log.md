PS D:\github\mt5-python> python main.py --config config/core/system.yaml --host-id uk-001
{"timestamp": "2025-07-22T03:52:33.190346", "level": "INFO", "logger": "__main__", "message": "============================================================", "module": "", "function": null, "line": 0}
{"timestamp": "2025-07-22T03:52:33.190346", "level": "INFO", "logger": "__main__", "message": "MT5 分布式交易系统", "module": "", "function": null, "line": 0}
{"timestamp": "2025-07-22T03:52:33.190346", "level": "INFO", "logger": "__main__", "message": "============================================================", "module": "", "function": null, "line": 0}
{"timestamp": "2025-07-22T03:52:33.190346", "level": "INFO", "logger": "__main__", "message": "主机ID: uk-001", "module": "", "function": null, "line": 0}
{"timestamp": "2025-07-22T03:52:33.191346", "level": "INFO", "logger": "__main__", "message": "配置文件: config/core/system.yaml", "module": "", "function": null, "line": 0}
{"timestamp": "2025-07-22T03:52:33.191346", "level": "INFO", "logger": "__main__", "message": "运行模式: 增强模式", "module": "", "function": null, "line": 0}
{"timestamp": "2025-07-22T03:52:33.191346", "level": "INFO", "logger": "__main__", "message": "日志级别: INFO", "module": "", "function": null, "line": 0}
{"timestamp": "2025-07-22T03:52:33.191346", "level": "INFO", "logger": "__main__", "message": "============================================================", "module": "", "function": null, "line": 0}
{"timestamp": "2025-07-22T03:52:33.191346", "level": "INFO", "logger": "__main__", "message": "启动MT5跟单交易系统 - 主机: uk-001", "module": "", "function": null, "line": 0}
{"timestamp": "2025-07-22T03:52:33.200346", "level": "INFO", "logger": "src.core.config_manager", "message": "Loaded default values from config file: config\\core\\system_defaults.yaml", "module": "config_manager", "function": "_load_from_config", "line": 58}
{"timestamp": "2025-07-22T03:52:33.200346", "level": "INFO", "logger": "src.core.config_manager", "message": "统一配置管理器初始化完成 (development环境)", "module": "config_manager", "function": "_load_all_configs", "line": 101}
{"timestamp": "2025-07-22T03:52:33.200346", "level": "INFO", "logger": "__main__", "message": "已加载系统配置: config/core/system.yaml", "module": "", "function": null, "line": 0}
{"timestamp": "2025-07-22T03:52:33.200346", "level": "INFO", "logger": "__main__", "message": "账户配置将由协调器自动发现并加载", "module": "", "function": null, "line": 0}
{"timestamp": "2025-07-22T03:52:33.209345", "level": "INFO", "logger": "src.core.config_manager", "message": "Loaded default values from config file: config\\core\\system_defaults.yaml", "module": "config_manager", "function": "_load_from_config", "line": 58}
{"timestamp": "2025-07-22T03:52:33.292398", "level": "ERROR", "logger": "src.core.config_manager", "message": "配置加载失败: dictionary changed size during iteration", "module": "config_manager", "function": "_load_all_configs", "line": 104}
{"timestamp": "2025-07-22T03:52:33.296223", "level": "INFO", "logger": "TradeCopier.MT5ProcessManager", "message": "日志管理器初始化完成", "module": "mt5_process_manager", "function": "_init_log_manager", "line": 1457}
{"timestamp": "2025-07-22T03:52:33.296223", "level": "INFO", "logger": "TradeCopier.MT5ProcessManager", "message": "MT5ProcessManager初始化完成，数据目录: mt5_terminals", "module": "mt5_process_manager", "function": "__init__", "line": 905}
{"timestamp": "2025-07-22T03:52:33.296223", "level": "INFO", "logger": "TradeCopier.MT5ProcessManager", "message": "资源限制: CPU=80%, 内存=1024MB", "module": "mt5_process_manager", "function": "__init__", "line": 906}
{"timestamp": "2025-07-22T03:52:33.296223", "level": "INFO", "logger": "src.core.mt5_configuration", "message": "============================================================", "module": "mt5_configuration", "function": "reload_all_configs", "line": 279}
{"timestamp": "2025-07-22T03:52:33.296223", "level": "INFO", "logger": "src.core.mt5_configuration", "message": "开始重新加载账户配置...", "module": "mt5_configuration", "function": "reload_all_configs", "line": 280}
{"timestamp": "2025-07-22T03:52:33.315223", "level": "INFO", "logger": "src.core.mt5_configuration", "message": "加载账户 ACC001 (主机: uk-001)", "module": "mt5_configuration", "function": "reload_all_configs", "line": 322}
{"timestamp": "2025-07-22T03:52:33.333302", "level": "INFO", "logger": "src.core.mt5_configuration", "message": "加载账户 ACC002 (主机: uk-001)", "module": "mt5_configuration", "function": "reload_all_configs", "line": 322}
{"timestamp": "2025-07-22T03:52:33.344301", "level": "INFO", "logger": "src.core.mt5_configuration", "message": "配置加载完成: 2 个账户", "module": "mt5_configuration", "function": "reload_all_configs", "line": 333}
{"timestamp": "2025-07-22T03:52:33.344301", "level": "INFO", "logger": "src.core.mt5_configuration", "message": "验证错误: 0 个", "module": "mt5_configuration", "function": "reload_all_configs", "line": 334}
{"timestamp": "2025-07-22T03:52:33.344301", "level": "INFO", "logger": "src.core.mt5_configuration", "message": "============================================================", "module": "mt5_configuration", "function": "reload_all_configs", "line": 337}
{"timestamp": "2025-07-22T03:52:33.345300", "level": "INFO", "logger": "src.core.mt5_configuration", "message": "配置文件热重载已启动", "module": "mt5_configuration", "function": "_start_hot_reload", "line": 519}
{"timestamp": "2025-07-22T03:52:33.345300", "level": "INFO", "logger": "src.core.mt5_coordinator", "message": "DistributedMT5Coordinator初始化完成，主机: uk-001", "module": "mt5_coordinator", "function": "__init__", "line": 60}
{"timestamp": "2025-07-22T03:52:33.345300", "level": "INFO", "logger": "src.core.mt5_coordinator", "message": "启动分布式MT5协调器 - 主机: uk-001", "module": "mt5_coordinator", "function": "start", "line": 81}
{"timestamp": "2025-07-22T03:52:33.402473", "level": "WARNING", "logger": "src.core.mt5_coordinator", "message": "分布式组件初始化失败: No module named 'src.distributed.config_driven_coordinator'", "module": "mt5_coordinator", "function": "_init_distributed_components", "line": 142}
{"timestamp": "2025-07-22T03:52:33.402473", "level": "INFO", "logger": "src.core.mt5_coordinator", "message": "系统将在本地模式下运行", "module": "mt5_coordinator", "function": "_init_distributed_components", "line": 143}
{"timestamp": "2025-07-22T03:52:33.402473", "level": "INFO", "logger": "src.core.message_routing_coordinator", "message": " 本地消息路由器初始化完成", "module": "", "function": null, "line": 0}
{"timestamp": "2025-07-22T03:52:33.402473", "level": "INFO", "logger": "src.core.message_routing_coordinator", "message": "本地消息路由器已启动", "module": "", "function": null, "line": 0}
{"timestamp": "2025-07-22T03:52:33.402473", "level": "INFO", "logger": "src.core.mt5_coordinator", "message": "本地消息路由器启动成功", "module": "mt5_coordinator", "function": "_init_distributed_components", "line": 147}
{"timestamp": "2025-07-22T03:52:33.403474", "level": "INFO", "logger": "src.core.mt5_coordinator", "message": "发现本地账户: ACC001 (主机: uk-001)", "module": "mt5_coordinator", "function": "_discover_local_accounts", "line": 176}
{"timestamp": "2025-07-22T03:52:33.403474", "level": "INFO", "logger": "src.core.mt5_coordinator", "message": "发现本地账户: ACC002 (主机: uk-001)", "module": "mt5_coordinator", "function": "_discover_local_accounts", "line": 176}
{"timestamp": "2025-07-22T03:52:33.403474", "level": "INFO", "logger": "src.core.mt5_coordinator", "message": "本主机负责 2 个账户", "module": "mt5_coordinator", "function": "_discover_local_accounts", "line": 178}
{"timestamp": "2025-07-22T03:52:33.403474", "level": "INFO", "logger": "src.core.mt5_coordinator", "message": "启动账户: ACC001", "module": "mt5_coordinator", "function": "_start_account_processes_enhanced", "line": 222}
{"timestamp": "2025-07-22T03:52:33.403474", "level": "INFO", "logger": "TradeCopier.MT5ProcessManager", "message": "启动进程健康监控", "module": "mt5_process_manager", "function": "_monitor_process_health", "line": 986}
{"timestamp": "2025-07-22T03:52:33.403474", "level": "INFO", "logger": "TradeCopier.MT5ProcessManager", "message": "MT5进程管理器已启动", "module": "mt5_process_manager", "function": "start_manager", "line": 931}
{"timestamp": "2025-07-22T03:52:33.956216", "level": "INFO", "logger": "TradeCopier.MT5ProcessManager", "message": "系统消息 - [ACC001] - process_started: success", "module": "mt5_process_manager", "function": "_handle_system_results", "line": 1126}
{"timestamp": "2025-07-22T03:52:34.420372", "level": "INFO", "logger": "TradeCopier.MT5ProcessManager", "message": "成功启动账户 [ACC001] 的MT5进程, PID: 12276", "module": "mt5_process_manager", "function": "add_account", "line": 1238}
{"timestamp": "2025-07-22T03:52:34.423376", "level": "INFO", "logger": "src.core.mt5_coordinator", "message": "终端进程启动成功: ACC001", "module": "mt5_coordinator", "function": "_start_account_processes_enhanced", "line": 233}
{"timestamp": "2025-07-22T03:52:35.429017", "level": "INFO", "logger": "src.core.mt5_coordinator", "message": "终端状态: {'status': 'running', 'pid': 12276, 'connected': False, 'uptime_seconds': 0.0, 'error_count': 0, 'last_heartbeat': '2025-07-22T03:52:34.420371', 'account_info': None}", "module": "mt5_coordinator", "function": "_start_account_processes_enhanced", "line": 242}
{"timestamp": "2025-07-22T03:52:35.429017", "level": "INFO", "logger": "src.core.mt5_coordinator", "message": "开始创建分离进程: ACC001", "module": "mt5_coordinator", "function": "_start_account_processes_enhanced", "line": 247}
{"timestamp": "2025-07-22T03:52:35.429017", "level": "INFO", "logger": "src.core.mt5_coordinator", "message": "账户 ACC001 配置传递:", "module": "mt5_coordinator", "function": "_create_separated_account_processes", "line": 290}
{"timestamp": "2025-07-22T03:52:35.429017", "level": "INFO", "logger": "src.core.mt5_coordinator", "message": "  login: ********", "module": "mt5_coordinator", "function": "_create_separated_account_processes", "line": 291}
{"timestamp": "2025-07-22T03:52:35.429017", "level": "INFO", "logger": "src.core.mt5_coordinator", "message": "  password: ***", "module": "mt5_coordinator", "function": "_create_separated_account_processes", "line": 292}
{"timestamp": "2025-07-22T03:52:35.429017", "level": "INFO", "logger": "src.core.mt5_coordinator", "message": "  server: TradeMaxGlobal-Demo", "module": "mt5_coordinator", "function": "_create_separated_account_processes", "line": 293}
{"timestamp": "2025-07-22T03:52:35.429017", "level": "INFO", "logger": "src.core.mt5_coordinator", "message": "  terminal_path: D:/MetaTrader5/v1-mt5/terminal64.exe", "module": "mt5_coordinator", "function": "_create_separated_account_processes", "line": 294}
{"timestamp": "2025-07-22T03:52:35.429017", "level": "INFO", "logger": "src.core.mt5_coordinator", "message": "协调器NATS配置: {'servers': ['nats://localhost:4222', 'nats://127.0.0.1:4222'], 'connection': {'timeout': 10, 'ping_interval': 30, 'max_pings_out': 3, 'reconnect_wait': 2, 'max_reconnect': 60}, 'jetstream': {'enabled': True, 'stream_name': 'MT5_TRADING_STREAM', 'storage': 'file', 'retention': 'limits', 'max_msgs': ********, 'max_bytes': ***********, 'max_age': 86400, 'max_msg_size': 1048576, 'duplicate_window': 120, 'replicas': 1, 'subjects': {'trading_signals': 'MT5.SIGNALS.*', 'trade_execution': 'MT5.EXECUTION.*', 'account_monitoring': 'MT5.MONITOR.*', 'system_control': 'MT5.CONTROL.*', 'metrics': 'MT5.METRICS.*', 'execution_results': 'MT5.RESULT.*', 'execute_commands': 'MT5.EXECUTE.*'}}}", "module": "mt5_coordinator", "function": "_create_separated_account_processes", "line": 297}
{"timestamp": "2025-07-22T03:52:35.429017", "level": "INFO", "logger": "src.core.mt5_coordinator", "message": "传递给进程的NATS配置: {'servers': ['nats://localhost:4222', 'nats://127.0.0.1:4222'], 'connection': {'timeout': 10, 'ping_interval': 30, 'max_pings_out': 3, 'reconnect_wait': 2, 'max_reconnect': 60}, 'jetstream': {'enabled': True, 'stream_name': 'MT5_TRADING_STREAM', 'storage': 'file', 'retention': 'limits', 'max_msgs': ********, 'max_bytes': ***********, 'max_age': 86400, 'max_msg_size': 1048576, 'duplicate_window': 120, 'replicas': 1, 'subjects': {'trading_signals': 'MT5.SIGNALS.*', 'trade_execution': 'MT5.EXECUTION.*', 'account_monitoring': 'MT5.MONITOR.*', 'system_control': 'MT5.CONTROL.*', 'metrics': 'MT5.METRICS.*', 'execution_results': 'MT5.RESULT.*', 'execute_commands': 'MT5.EXECUTE.*'}}}", "module": "mt5_coordinator", "function": "_create_separated_account_processes", "line": 298}
{"timestamp": "2025-07-22T03:52:35.439018", "level": "INFO", "logger": "src.core.mt5_coordinator", "message": "监控器进程启动: ACC001 (PID: 16364)", "module": "mt5_coordinator", "function": "_create_monitor_process", "line": 343}
{"timestamp": "2025-07-22T03:52:35.951815", "level": "INFO", "logger": "src.core.mt5_coordinator", "message": "执行器进程启动: ACC001 (PID: 34748)", "module": "mt5_coordinator", "function": "_create_executor_process", "line": 366}
{"timestamp": "2025-07-22T03:52:35.978815", "level": "INFO", "logger": "src.core.separated_process_runners", "message": "启动账户监控器进程: ACC001", "module": "", "function": null, "line": 0}
{"timestamp": "2025-07-22T03:52:35.979814", "level": "INFO", "logger": "src.core.separated_process_runners", "message": "监控器收到的NATS配置: {'servers': ['nats://localhost:4222', 'nats://127.0.0.1:4222'], 'connection': {'timeout': 10, 'ping_interval': 30, 'max_pings_out': 3, 'reconnect_wait': 2, 'max_reconnect': 60}, 'jetstream': {'enabled': True, 'stream_name': 'MT5_TRADING_STREAM', 'storage': 'file', 'retention': 'limits', 'max_msgs': ********, 'max_bytes': ***********, 'max_age': 86400, 'max_msg_size': 1048576, 'duplicate_window': 120, 'replicas': 1, 'subjects': {'trading_signals': 'MT5.SIGNALS.*', 'trade_execution': 'MT5.EXECUTION.*', 'account_monitoring': 'MT5.MONITOR.*', 'system_control': 'MT5.CONTROL.*', 'metrics': 'MT5.METRICS.*', 'execution_results': 'MT5.RESULT.*', 'execute_commands': 'MT5.EXECUTE.*'}}}", "module": "", "function": null, "line": 0}
{"timestamp": "2025-07-22T03:52:36.457959", "level": "ERROR", "logger": "src.core.mt5_coordinator", "message": "创建RPC服务失败 ACC001: 'DistributedMT5Coordinator' object has no attribute 'rpc_handler'", "module": "mt5_coordinator", "function": "_create_rpc_service", "line": 400}
{"timestamp": "2025-07-22T03:52:36.457959", "level": "ERROR", "logger": "src.core.mt5_coordinator", "message": "创建分离进程失败 ACC001: 'DistributedMT5Coordinator' object has no attribute 'rpc_handler'", "module": "mt5_coordinator", "function": "_create_separated_account_processes", "line": 325}
{"timestamp": "2025-07-22T03:52:36.457959", "level": "ERROR", "logger": "src.core.mt5_coordinator", "message": "创建分离进程失败 ACC001: 'DistributedMT5Coordinator' object has no attribute 'rpc_handler'", "module": "mt5_coordinator", "function": "_start_account_processes_enhanced", "line": 259}
{"timestamp": "2025-07-22T03:52:36.458959", "level": "ERROR", "logger": "src.core.mt5_coordinator", "message": "详细错误信息: Traceback (most recent call last):\n  File \"D:\\github\\mt5-python\\src\\core\\mt5_coordinator.py\", line 249, in _start_account_processes_enhanced\n    await asyncio.wait_for(\n  File \"C:\\Program Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.10_3.10.3056.0_x64__qbz5n2kfra8p0\\lib\\asyncio\\tasks.py\", line 445, in wait_for\n    return fut.result()\n  File \"D:\\github\\mt5-python\\src\\core\\mt5_coordinator.py\", line 303, in _create_separated_account_processes\n    await self._create_rpc_service(account_id, account_config)\n  File \"D:\\github\\mt5-python\\src\\core\\mt5_coordinator.py\", line 386, in _create_rpc_service\n    success = await self.rpc_handler.add_account_service(\nAttributeError: 'DistributedMT5Coordinator' object has no attribute 'rpc_handler'\n", "module": "mt5_coordinator", "function": "_start_account_processes_enhanced", "line": 262}
{"timestamp": "2025-07-22T03:52:36.458959", "level": "INFO", "logger": "src.core.mt5_coordinator", "message": "启动账户: ACC002", "module": "mt5_coordinator", "function": "_start_account_processes_enhanced", "line": 222}
{"timestamp": "2025-07-22T03:52:36.488959", "level": "INFO", "logger": "src.core.separated_process_runners", "message": "启动账户执行器进程: ACC001", "module": "", "function": null, "line": 0}
{"timestamp": "2025-07-22T03:52:36.488959", "level": "INFO", "logger": "src.core.separated_process_runners", "message": "执行器收到的NATS配置: {'servers': ['nats://localhost:4222', 'nats://127.0.0.1:4222'], 'connection': {'timeout': 10, 'ping_interval': 30, 'max_pings_out': 3, 'reconnect_wait': 2, 'max_reconnect': 60}, 'jetstream': {'enabled': True, 'stream_name': 'MT5_TRADING_STREAM', 'storage': 'file', 'retention': 'limits', 'max_msgs': ********, 'max_bytes': ***********, 'max_age': 86400, 'max_msg_size': 1048576, 'duplicate_window': 120, 'replicas': 1, 'subjects': {'trading_signals': 'MT5.SIGNALS.*', 'trade_execution': 'MT5.EXECUTION.*', 'account_monitoring': 'MT5.MONITOR.*', 'system_control': 'MT5.CONTROL.*', 'metrics': 'MT5.METRICS.*', 'execution_results': 'MT5.RESULT.*', 'execute_commands': 'MT5.EXECUTE.*'}}}", "module": "", "function": null, "line": 0}
{"timestamp": "2025-07-22T03:52:36.995661", "level": "INFO", "logger": "TradeCopier.MT5ProcessManager", "message": "系统消息 - [ACC002] - process_started: success", "module": "mt5_process_manager", "function": "_handle_system_results", "line": 1126}
{"timestamp": "2025-07-22T03:52:37.480198", "level": "INFO", "logger": "TradeCopier.MT5ProcessManager", "message": "成功启动账户 [ACC002] 的MT5进程, PID: 14540", "module": "mt5_process_manager", "function": "add_account", "line": 1238}
{"timestamp": "2025-07-22T03:52:37.480198", "level": "INFO", "logger": "src.core.mt5_coordinator", "message": "终端进程启动成功: ACC002", "module": "mt5_coordinator", "function": "_start_account_processes_enhanced", "line": 233}
{"timestamp": "2025-07-22T03:52:38.011249", "level": "ERROR", "logger": "src.messaging.jetstream_client", "message": "JetStream错误: [WinError 1225] 远程计算机拒绝网络连接。", "module": "", "function": null, "line": 0}
{"timestamp": "2025-07-22T03:52:38.493259", "level": "INFO", "logger": "src.core.mt5_coordinator", "message": "终端状态: {'status': 'running', 'pid': 14540, 'connected': False, 'uptime_seconds': 0.0, 'error_count': 0, 'last_heartbeat': '2025-07-22T03:52:37.480197', 'account_info': None}", "module": "mt5_coordinator", "function": "_start_account_processes_enhanced", "line": 242}
{"timestamp": "2025-07-22T03:52:38.493259", "level": "INFO", "logger": "src.core.mt5_coordinator", "message": "开始创建分离进程: ACC002", "module": "mt5_coordinator", "function": "_start_account_processes_enhanced", "line": 247}
{"timestamp": "2025-07-22T03:52:38.493259", "level": "INFO", "logger": "src.core.mt5_coordinator", "message": "账户 ACC002 配置传递:", "module": "mt5_coordinator", "function": "_create_separated_account_processes", "line": 290}
{"timestamp": "2025-07-22T03:52:38.493259", "level": "INFO", "logger": "src.core.mt5_coordinator", "message": "  login: ********", "module": "mt5_coordinator", "function": "_create_separated_account_processes", "line": 291}
{"timestamp": "2025-07-22T03:52:38.493259", "level": "INFO", "logger": "src.core.mt5_coordinator", "message": "  password: ***", "module": "mt5_coordinator", "function": "_create_separated_account_processes", "line": 292}
{"timestamp": "2025-07-22T03:52:38.493259", "level": "INFO", "logger": "src.core.mt5_coordinator", "message": "  server: TradeMaxGlobal-Demo", "module": "mt5_coordinator", "function": "_create_separated_account_processes", "line": 293}
{"timestamp": "2025-07-22T03:52:38.493259", "level": "INFO", "logger": "src.core.mt5_coordinator", "message": "  terminal_path: D:/MetaTrader5/v2-mt5/terminal64.exe", "module": "mt5_coordinator", "function": "_create_separated_account_processes", "line": 294}
{"timestamp": "2025-07-22T03:52:38.493259", "level": "INFO", "logger": "src.core.mt5_coordinator", "message": "协调器NATS配置: {'servers': ['nats://localhost:4222', 'nats://127.0.0.1:4222'], 'connection': {'timeout': 10, 'ping_interval': 30, 'max_pings_out': 3, 'reconnect_wait': 2, 'max_reconnect': 60}, 'jetstream': {'enabled': True, 'stream_name': 'MT5_TRADING_STREAM', 'storage': 'file', 'retention': 'limits', 'max_msgs': ********, 'max_bytes': ***********, 'max_age': 86400, 'max_msg_size': 1048576, 'duplicate_window': 120, 'replicas': 1, 'subjects': {'trading_signals': 'MT5.SIGNALS.*', 'trade_execution': 'MT5.EXECUTION.*', 'account_monitoring': 'MT5.MONITOR.*', 'system_control': 'MT5.CONTROL.*', 'metrics': 'MT5.METRICS.*', 'execution_results': 'MT5.RESULT.*', 'execute_commands': 'MT5.EXECUTE.*'}}}", "module": "mt5_coordinator", "function": "_create_separated_account_processes", "line": 297}
{"timestamp": "2025-07-22T03:52:38.493259", "level": "INFO", "logger": "src.core.mt5_coordinator", "message": "传递给进程的NATS配置: {'servers': ['nats://localhost:4222', 'nats://127.0.0.1:4222'], 'connection': {'timeout': 10, 'ping_interval': 30, 'max_pings_out': 3, 'reconnect_wait': 2, 'max_reconnect': 60}, 'jetstream': {'enabled': True, 'stream_name': 'MT5_TRADING_STREAM', 'storage': 'file', 'retention': 'limits', 'max_msgs': ********, 'max_bytes': ***********, 'max_age': 86400, 'max_msg_size': 1048576, 'duplicate_window': 120, 'replicas': 1, 'subjects': {'trading_signals': 'MT5.SIGNALS.*', 'trade_execution': 'MT5.EXECUTION.*', 'account_monitoring': 'MT5.MONITOR.*', 'system_control': 'MT5.CONTROL.*', 'metrics': 'MT5.METRICS.*', 'execution_results': 'MT5.RESULT.*', 'execute_commands': 'MT5.EXECUTE.*'}}}", "module": "mt5_coordinator", "function": "_create_separated_account_processes", "line": 298}
{"timestamp": "2025-07-22T03:52:38.503261", "level": "INFO", "logger": "src.core.mt5_coordinator", "message": "监控器进程启动: ACC002 (PID: 25752)", "module": "mt5_coordinator", "function": "_create_monitor_process", "line": 343}
{"timestamp": "2025-07-22T03:52:39.013058", "level": "INFO", "logger": "src.core.mt5_coordinator", "message": "执行器进程启动: ACC002 (PID: 32176)", "module": "mt5_coordinator", "function": "_create_executor_process", "line": 366}
{"timestamp": "2025-07-22T03:52:39.035055", "level": "INFO", "logger": "src.core.separated_process_runners", "message": "启动账户监控器进程: ACC002", "module": "", "function": null, "line": 0}
{"timestamp": "2025-07-22T03:52:39.036056", "level": "INFO", "logger": "src.core.separated_process_runners", "message": "监控器收到的NATS配置: {'servers': ['nats://localhost:4222', 'nats://127.0.0.1:4222'], 'connection': {'timeout': 10, 'ping_interval': 30, 'max_pings_out': 3, 'reconnect_wait': 2, 'max_reconnect': 60}, 'jetstream': {'enabled': True, 'stream_name': 'MT5_TRADING_STREAM', 'storage': 'file', 'retention': 'limits', 'max_msgs': ********, 'max_bytes': ***********, 'max_age': 86400, 'max_msg_size': 1048576, 'duplicate_window': 120, 'replicas': 1, 'subjects': {'trading_signals': 'MT5.SIGNALS.*', 'trade_execution': 'MT5.EXECUTION.*', 'account_monitoring': 'MT5.MONITOR.*', 'system_control': 'MT5.CONTROL.*', 'metrics': 'MT5.METRICS.*', 'execution_results': 'MT5.RESULT.*', 'execute_commands': 'MT5.EXECUTE.*'}}}", "module": "", "function": null, "line": 0}
{"timestamp": "2025-07-22T03:52:39.519060", "level": "ERROR", "logger": "src.core.mt5_coordinator", "message": "创建RPC服务失败 ACC002: 'DistributedMT5Coordinator' object has no attribute 'rpc_handler'", "module": "mt5_coordinator", "function": "_create_rpc_service", "line": 400}
{"timestamp": "2025-07-22T03:52:39.519060", "level": "ERROR", "logger": "src.core.mt5_coordinator", "message": "创建分离进程失败 ACC002: 'DistributedMT5Coordinator' object has no attribute 'rpc_handler'", "module": "mt5_coordinator", "function": "_create_separated_account_processes", "line": 325}
{"timestamp": "2025-07-22T03:52:39.519060", "level": "ERROR", "logger": "src.core.mt5_coordinator", "message": "创建分离进程失败 ACC002: 'DistributedMT5Coordinator' object has no attribute 'rpc_handler'", "module": "mt5_coordinator", "function": "_start_account_processes_enhanced", "line": 259}
{"timestamp": "2025-07-22T03:52:39.519060", "level": "ERROR", "logger": "src.core.mt5_coordinator", "message": "详细错误信息: Traceback (most recent call last):\n  File \"D:\\github\\mt5-python\\src\\core\\mt5_coordinator.py\", line 249, in _start_account_processes_enhanced\n    await asyncio.wait_for(\n  File \"C:\\Program Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.10_3.10.3056.0_x64__qbz5n2kfra8p0\\lib\\asyncio\\tasks.py\", line 445, in wait_for\n    return fut.result()\n  File \"D:\\github\\mt5-python\\src\\core\\mt5_coordinator.py\", line 303, in _create_separated_account_processes\n    await self._create_rpc_service(account_id, account_config)\n  File \"D:\\github\\mt5-python\\src\\core\\mt5_coordinator.py\", line 386, in _create_rpc_service\n    success = await self.rpc_handler.add_account_service(\nAttributeError: 'DistributedMT5Coordinator' object has no attribute 'rpc_handler'\n", "module": "mt5_coordinator", "function": "_start_account_processes_enhanced", "line": 262}
{"timestamp": "2025-07-22T03:52:39.519060", "level": "INFO", "logger": "src.core.mt5_coordinator", "message": "账户进程启动完成，成功启动 2 个监控器和 2 个执行器", "module": "mt5_coordinator", "function": "_start_account_processes_enhanced", "line": 271}
{"timestamp": "2025-07-22T03:52:39.519060", "level": "INFO", "logger": "src.core.mt5_coordinator", "message": "分布式MT5协调器启动完成", "module": "mt5_coordinator", "function": "start", "line": 91}
{"timestamp": "2025-07-22T03:52:39.519060", "level": "INFO", "logger": "__main__", "message": "SUCCESS: 协调器启动成功", "module": "", "function": null, "line": 0}
{"timestamp": "2025-07-22T03:52:39.519060", "level": "INFO", "logger": "__main__", "message": "INFO: 协调器将自动发现并启动本地账户进程", "module": "", "function": null, "line": 0}
{"timestamp": "2025-07-22T03:52:39.520061", "level": "INFO", "logger": "__main__", "message": "INFO: 跟单关系已在配置文件中定义: ACC001 (主) -> ACC002 (从)", "module": "", "function": null, "line": 0}
{"timestamp": "2025-07-22T03:52:39.520061", "level": "INFO", "logger": "__main__", "message": "SUCCESS: 跟单规则将从配置文件自动加载", "module": "", "function": null, "line": 0}
{"timestamp": "2025-07-22T03:52:39.520061", "level": "INFO", "logger": "__main__", "message": "SUCCESS: 系统启动成功，等待停止信号...", "module": "", "function": null, "line": 0}
{"timestamp": "2025-07-22T03:52:39.520061", "level": "INFO", "logger": "__main__", "message": "INFO: ACC001 (主账户) 将生成交易信号", "module": "", "function": null, "line": 0}
{"timestamp": "2025-07-22T03:52:39.520061", "level": "INFO", "logger": "__main__", "message": "INFO: ACC002 (从账户) 将跟随 ACC001 的交易", "module": "", "function": null, "line": 0}
{"timestamp": "2025-07-22T03:52:39.520061", "level": "INFO", "logger": "src.core.mt5_coordinator", "message": "启动分离进程监控", "module": "mt5_coordinator", "function": "_monitor_processes", "line": 405}
{"timestamp": "2025-07-22T03:52:39.520061", "level": "INFO", "logger": "src.core.mt5_coordinator", "message": "监控循环: 监控器进程=2, 执行器进程=2", "module": "mt5_coordinator", "function": "_monitor_processes", "line": 422}
{"timestamp": "2025-07-22T03:52:39.520061", "level": "INFO", "logger": "src.core.mt5_coordinator", "message": "进程状态: 监控器 2/2, 执行器 2/2", "module": "mt5_coordinator", "function": "_monitor_processes", "line": 446}
{"timestamp": "2025-07-22T03:52:39.520061", "level": "INFO", "logger": "src.core.mt5_coordinator", "message": "系统心跳: 主机=uk-001, 账户=2, 监控器=2, 执行器=2", "module": "mt5_coordinator", "function": "_heartbeat_task", "line": 478}
{"timestamp": "2025-07-22T03:52:39.531060", "level": "INFO", "logger": "src.core.separated_process_runners", "message": "启动账户执行器进程: ACC002", "module": "", "function": null, "line": 0}
{"timestamp": "2025-07-22T03:52:39.531060", "level": "INFO", "logger": "src.core.separated_process_runners", "message": "执行器收到的NATS配置: {'servers': ['nats://localhost:4222', 'nats://127.0.0.1:4222'], 'connection': {'timeout': 10, 'ping_interval': 30, 'max_pings_out': 3, 'reconnect_wait': 2, 'max_reconnect': 60}, 'jetstream': {'enabled': True, 'stream_name': 'MT5_TRADING_STREAM', 'storage': 'file', 'retention': 'limits', 'max_msgs': ********, 'max_bytes': ***********, 'max_age': 86400, 'max_msg_size': 1048576, 'duplicate_window': 120, 'replicas': 1, 'subjects': {'trading_signals': 'MT5.SIGNALS.*', 'trade_execution': 'MT5.EXECUTION.*', 'account_monitoring': 'MT5.MONITOR.*', 'system_control': 'MT5.CONTROL.*', 'metrics': 'MT5.METRICS.*', 'execution_results': 'MT5.RESULT.*', 'execute_commands': 'MT5.EXECUTE.*'}}}", "module": "", "function": null, "line": 0}