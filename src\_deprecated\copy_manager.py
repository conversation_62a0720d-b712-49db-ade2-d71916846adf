# src/core/copy_manager_enhanced.py
"""
跟单关系管理器
负责动态跟单关系的管理和配置
"""
import asyncio
import yaml
import logging
from typing import Dict, List, Optional, Set, Any
from dataclasses import dataclass
from pathlib import Path
from datetime import datetime

# 导入统一的跟单工具
from .copy_utils import normalize_copy_mode, get_relationship_key, CopyConfigValidator

logger = logging.getLogger(__name__)


@dataclass
class TradingRelationship:
    """统一的交易关系数据结构"""
    relationship_id: str
    master_account: str
    slave_account: str
    enabled: bool = True
    priority: int = 1

    copy_mode: str = "forward"  # 暂时保持字符串，稍后统一替换
    copy_ratio: float = 1.0

    limits: dict = None
    risk_management: dict = None
    time_restrictions: dict = None
    symbol_filter: dict = None
    execution: dict = None
    monitoring: dict = None
    created_at: datetime = None
    last_copy_at: datetime = None
    total_copies: int = 0
    successful_copies: int = 0

    def __post_init__(self):
        if self.created_at is None:
            self.created_at = datetime.now()
        if self.limits is None:
            self.limits = {
                "max_volume_per_trade": 10.0,
                "min_volume_per_trade": 0.01,
                "max_positions": 10,
                "max_daily_volume": 100.0,
                "max_daily_trades": 50
            }
        if self.risk_management is None:
            self.risk_management = {
                "risk_multiplier": 1.0,
                "max_drawdown": 0.15,
                "stop_loss_multiplier": 1.0,
                "take_profit_multiplier": 1.0,
                "reverse_sl_tp": False
            }
        if self.time_restrictions is None:
            self.time_restrictions = {
                "active_hours": {"start": "00:00", "end": "24:00", "timezone": "UTC"},
                "active_days": ["monday", "tuesday", "wednesday", "thursday", "friday"],
                "exclude_news_events": False
            }
        if self.symbol_filter is None:
            self.symbol_filter = {"mode": "all", "symbols": []}
        if self.execution is None:
            self.execution = {
                "delay_ms": 100,
                "timeout_ms": 5000,
                "retry_attempts": 3,
                "retry_delay_ms": 1000
            }
        if self.monitoring is None:
            self.monitoring = {
                "track_performance": True,
                "alert_on_failure": True,
                "log_all_trades": True
            }
    
    def should_copy_symbol(self, symbol: str) -> bool:
        """判断是否应该跟单该品种"""
        if not self.enabled:
            return False
        if not self.symbol_filter:   
            return True
        return symbol in self.symbol_filter
    
    def is_active_time(self) -> bool:
        """检查当前时间是否在活跃时间内"""
        current_hour = datetime.now().hour
        return any(start <= current_hour < end for start, end in self.active_hours)
    
    def can_copy_volume(self, volume: float) -> bool:
        """检查是否可以跟单该成交量"""
        return volume * self.copy_ratio <= self.max_daily_volume
    
    def update_copy_stats(self, success: bool):
        """更新跟单统计"""
        self.total_copies += 1
        if success:
            self.successful_copies += 1
        self.last_copy_at = datetime.now()
    
    def get_success_rate(self) -> float:
        """获取成功率"""
        if self.total_copies == 0:
            return 0.0
        return self.successful_copies / self.total_copies


class CopyRelationshipManager:
    """统一的跟单关系管理器 - 支持新格式"""

    def __init__(self, config_file: str = "config/relationships/copy_trading.yaml"):
        self.config_file = Path(config_file)
        self.relationships: Dict[str, Dict[str, Any]] = {}  # relationship_id -> relationship_config
        self.relationships_by_master: Dict[str, List[str]] = {}  # master_account -> [relationship_ids]
        self.relationships_by_slave: Dict[str, List[str]] = {}   # slave_account -> [relationship_ids]

        self.last_reload = None
        self.reload_lock = asyncio.Lock()

        self.stats = {
            'total_relationships': 0,
            'active_relationships': 0,
            'last_reload_time': None,
            'reload_count': 0
        }

        self._load_relationships()
        self._start_hot_reload()
        
    def _load_relationships(self):
        """加载跟单关系"""
        try:
            if not self.config_file.exists():
                logger.warning(f"跟单关系配置文件不存在: {self.config_file}")
                return
            
            with open(self.config_file, 'r', encoding='utf-8') as f:
                data = yaml.safe_load(f)
            
            self.relationships.clear()
            self.relationships_by_master.clear()
            self.relationships_by_slave.clear()

            for rel_data in data.get('relationships', []):
                # 直接使用配置字典，标准化跟单模式
                relationship_id = rel_data.get('relationship_id', f"{rel_data['master_account']}_{rel_data['slave_account']}")
                relationship = {
                    'relationship_id': relationship_id,
                    'master_account': rel_data['master_account'],
                    'slave_account': rel_data['slave_account'],
                    'enabled': rel_data.get('enabled', True),
                    'priority': rel_data.get('priority', 1),
                    'copy_mode': normalize_copy_mode(rel_data.get('copy_mode', 'forward')),
                    'copy_ratio': rel_data.get('copy_ratio', 1.0),
                    'limits': rel_data.get('limits', {}),
                    'risk_management': rel_data.get('risk_management', {}),
                    'time_restrictions': rel_data.get('time_restrictions', {}),
                    'symbol_filter': rel_data.get('symbol_filter', {}),
                    'execution': rel_data.get('execution', {}),
                    'monitoring': rel_data.get('monitoring', {})
                }

                self.relationships[relationship_id] = relationship
                master = relationship['master_account']
                slave = relationship['slave_account']

                if master not in self.relationships_by_master:
                    self.relationships_by_master[master] = []
                self.relationships_by_master[master].append(relationship_id)

                if slave not in self.relationships_by_slave:
                    self.relationships_by_slave[slave] = []
                self.relationships_by_slave[slave].append(relationship_id)
            
            total_rels = len(self.relationships)
            active_rels = len([r for r in self.relationships.values() if r.get('enabled', True)])
            
            self.stats.update({
                'total_relationships': total_rels,
                'active_relationships': active_rels,
                'last_reload_time': datetime.now(),
                'reload_count': self.stats['reload_count'] + 1
            })
            
            logger.info(f"加载跟单关系: {total_rels} 个总关系, {active_rels} 个活跃关系")
            
        except Exception as e:
            logger.error(f"加载跟单关系失败: {e}")
    
    def _start_hot_reload(self):
        """启动热重载"""
        try:
            loop = asyncio.get_event_loop()
            if loop.is_running():
                asyncio.create_task(self._hot_reload_task())
        except RuntimeError:
            pass
    
    async def _hot_reload_task(self):
        """热重载任务"""
        last_mtime = 0
        
        while True:
            try:
                if self.config_file.exists():
                    current_mtime = self.config_file.stat().st_mtime
                    if current_mtime > last_mtime:
                        async with self.reload_lock:
                            logger.info("检测到配置文件变化，重新加载跟单关系")
                            self._load_relationships()
                            last_mtime = current_mtime
                
                await asyncio.sleep(5)  
                
            except Exception as e:
                logger.error(f"热重载任务异常: {e}")
                await asyncio.sleep(10)
    
    def get_slaves_for_master(self, master_account: str) -> List[TradingRelationship]:
        """获取主账户的所有从账户关系"""
        relationship_ids = self.relationships_by_master.get(master_account, [])
        return [self.relationships[rid] for rid in relationship_ids]

    def get_masters_for_slave(self, slave_account: str) -> List[TradingRelationship]:
        """获取从账户的所有主账户关系"""
        relationship_ids = self.relationships_by_slave.get(slave_account, [])
        return [self.relationships[rid] for rid in relationship_ids]

    def get_all_relationships(self) -> Dict:
        """获取所有跟单关系 - 返回新格式"""
        return {
            'relationships': list(self.relationships.values()),
            'by_master': {
                master: [self.relationships[rid] for rid in rids]
                for master, rids in self.relationships_by_master.items()
            },
            'by_slave': {
                slave: [self.relationships[rid] for rid in rids]
                for slave, rids in self.relationships_by_slave.items()
            }
        }
    
    def is_master_account(self, account_id: str) -> bool:
        """检查是否为主账户"""
        return account_id in self.relationships['by_master']
    
    def is_slave_account(self, account_id: str) -> bool:
        """检查是否为从账户"""
        return account_id in self.relationships['by_slave']
    
    def get_account_role(self, account_id: str) -> str:
        """获取账户角色"""
        is_master = self.is_master_account(account_id)
        is_slave = self.is_slave_account(account_id)
        
        if is_master and is_slave:
            return "both"
        elif is_master:
            return "master"
        elif is_slave:
            return "slave"
        else:
            return "standalone"
    
    def add_relationship(self, master: str, slave: str, **kwargs) -> bool:
        """动态添加跟单关系 - 新格式"""
        try:
            relationship_id = kwargs.get('relationship_id', f"{master}_{slave}")

            relationship = TradingRelationship(
                relationship_id=relationship_id,
                master_account=master,
                slave_account=slave,
                copy_mode=kwargs.get('copy_mode', 'forward'),
                copy_ratio=kwargs.get('copy_ratio', 1.0),
                enabled=kwargs.get('enabled', True),
                priority=kwargs.get('priority', 1),
                limits=kwargs.get('limits'),
                risk_management=kwargs.get('risk_management'),
                time_restrictions=kwargs.get('time_restrictions'),
                symbol_filter=kwargs.get('symbol_filter'),
                execution=kwargs.get('execution'),
                monitoring=kwargs.get('monitoring')
            )

            self.relationships[relationship_id] = relationship

            if master not in self.relationships_by_master:
                self.relationships_by_master[master] = []
            self.relationships_by_master[master].append(relationship_id)

            if slave not in self.relationships_by_slave:
                self.relationships_by_slave[slave] = []
            self.relationships_by_slave[slave].append(relationship_id)

            logger.info(f"添加跟单关系: {master} -> {slave} (ID: {relationship_id})")
            return True

        except Exception as e:
            logger.error(f"添加跟单关系失败: {e}")
            return False
    
    def remove_relationship(self, master: str, slave: str) -> bool:
        """移除跟单关系"""
        try:
            relationship_ids_to_remove = []

            for rid, rel in self.relationships.items():
                if rel.master_account == master and rel.slave_account == slave:
                    relationship_ids_to_remove.append(rid)

            for rid in relationship_ids_to_remove:
                del self.relationships[rid]

                if master in self.relationships_by_master:
                    self.relationships_by_master[master] = [
                        r for r in self.relationships_by_master[master] if r != rid
                    ]

                if slave in self.relationships_by_slave:
                    self.relationships_by_slave[slave] = [
                        r for r in self.relationships_by_slave[slave] if r != rid
                    ]

            logger.info(f"移除跟单关系: {master} -> {slave} ({len(relationship_ids_to_remove)} 个)")
            return True

        except Exception as e:
            logger.error(f"移除跟单关系失败: {e}")
            return False
    
    def get_relationship_stats(self) -> Dict:
        """获取关系统计 - 新格式"""
        return {
            **self.stats,
            'masters_count': len(self.relationships_by_master),
            'slaves_count': len(self.relationships_by_slave),
            'relationship_details': {
                master: len(rids) for master, rids in self.relationships_by_master.items()
            }
        }
    
    async def validate_relationships(self) -> List[str]:
        """验证所有关系的有效性"""
        errors = []
        
        for master, rels in self.relationships['by_master'].items():
            for rel in rels:
                if rel.copy_ratio <= 0:
                    errors.append(f"无效的跟单比例: {master} -> {rel.slave_account}")
                
                if rel.master_account == rel.slave_account:
                    errors.append(f"自跟单关系: {master}")
        
        return errors
