# 性能优化配置
# 用于调优系统性能，提升交易执行效率

# 轮询优化
polling:
  # 自适应轮询策略
  adaptive:
    enabled: true
    algorithm: "ML_DRIVEN"  # SIMPLE, EXPONENTIAL, ML_DRIVEN
    
    # 动态调整参数
    adjustment:
      load_threshold: 0.8       # CPU负载阈值
      latency_threshold: 5.0    # 延迟阈值(ms)
      error_threshold: 0.01     # 错误率阈值
      
    # 调整策略
    strategy:
      increase_factor: 1.5      # 降频因子
      decrease_factor: 0.8      # 加速因子
      max_adjustment: 10.0      # 最大调整倍数
      min_adjustment: 0.1       # 最小调整倍数
      
  # 批处理优化
  batching:
    enabled: true
    
    # 智能批处理
    smart_batching:
      enabled: true
      min_batch_size: 1
      max_batch_size: 100
      timeout_ms: 5.0
      
    # 批处理策略
    strategies:
      size_based:
        enabled: true
        target_size: 50
        max_wait_time: 2.0
        
      time_based:
        enabled: true
        interval_ms: 1.0
        max_batch_size: 200
        
      load_based:
        enabled: true
        cpu_threshold: 0.7
        memory_threshold: 0.8

# 并发优化
concurrency:
  # 线程池配置
  thread_pool:
    core_size: 4              # 核心线程数
    max_size: 16              # 最大线程数
    keep_alive: 60            # 线程存活时间(秒)
    queue_size: 1000          # 队列大小
    
  # 进程池配置
  process_pool:
    core_size: 2              # 核心进程数
    max_size: 8               # 最大进程数
    
  # 异步配置
  async_config:
    event_loop: "uvloop"      # asyncio, uvloop
    max_workers: 32           # 异步工作器数量
    timeout: 30.0             # 异步操作超时
    
  # 锁优化
  locking:
    strategy: "RLock"         # Lock, RLock, ReadWriteLock
    timeout: 5.0              # 锁超时时间
    retry_count: 3            # 重试次数

# 内存优化
memory:
  # 对象池
  object_pooling:
    enabled: true
    pool_sizes:
      signals: 1000           # 信号对象池大小
      orders: 500             # 订单对象池大小
      accounts: 100           # 账户对象池大小
      
  # 缓存配置
  caching:
    enabled: true
    
    # 内存缓存
    memory_cache:
      max_size: 10000         # 最大缓存项
      ttl: 300                # 生存时间(秒)
      
    # LRU缓存
    lru_cache:
      max_size: 5000
      statistics: true
      
  # 垃圾回收优化
  gc_optimization:
    enabled: true
    generation_0: 700         # 第0代回收阈值
    generation_1: 10          # 第1代回收阈值
    generation_2: 10          # 第2代回收阈值

# 网络优化
network:
  # 连接池优化
  connection_pooling:
    enabled: true
    
    # HTTP连接池
    http_pool:
      max_connections: 100
      max_idle_connections: 20
      idle_timeout: 30.0
      
    # WebSocket连接池
    websocket_pool:
      max_connections: 50
      ping_interval: 30.0
      ping_timeout: 10.0
      
  # 网络缓冲区
  buffers:
    send_buffer: 65536        # 发送缓冲区(bytes)
    receive_buffer: 65536     # 接收缓冲区(bytes)
    
  # 压缩配置
  compression:
    enabled: true
    algorithm: "gzip"         # gzip, deflate, br
    level: 6                  # 压缩级别(1-9)

# I/O优化
io_optimization:
  # 文件I/O
  file_io:
    async_enabled: true
    buffer_size: 8192         # 文件缓冲区大小
    use_direct_io: false      # 直接I/O
    
  # 数据库I/O
  database_io:
    connection_pool_size: 20
    max_overflow: 30
    pool_timeout: 30.0
    pool_recycle: 3600
    
  # 消息队列I/O
  message_queue_io:
    batch_size: 100
    flush_interval: 1.0
    compression: true

# CPU优化
cpu_optimization:
  # CPU亲和性
  affinity:
    enabled: false            # CPU绑定
    core_list: [0, 1, 2, 3]  # 绑定的CPU核心
    
  # NUMA优化
  numa:
    enabled: false
    node_list: [0]            # NUMA节点
    
  # 优先级设置
  priority:
    process_priority: "high"  # low, normal, high, realtime
    thread_priority: "normal" # low, normal, high

# 监控和调优
monitoring:
  # 性能监控
  performance_monitoring:
    enabled: true
    interval: 1.0             # 监控间隔(秒)
    
    # 监控指标
    metrics:
      cpu_usage: true
      memory_usage: true
      network_io: true
      disk_io: true
      gc_stats: true
      
  # 自动调优
  auto_tuning:
    enabled: true
    
    # 调优策略
    strategies:
      adaptive_polling: true
      dynamic_batching: true
      memory_optimization: true
      
    # 调优参数
    tuning_interval: 300      # 调优间隔(秒)
    history_window: 3600      # 历史窗口(秒)
    
# 特定场景优化
scenarios:
  # 高频交易优化
  high_frequency:
    enabled: true
    polling_interval: 0.0001  # 0.1ms
    batch_size: 1             # 不批处理
    threading: "single"       # 单线程
    gc_disabled: true         # 禁用GC
    
  # 大量账户优化
  high_volume:
    enabled: false
    polling_interval: 0.001   # 1ms
    batch_size: 100           # 大批处理
    threading: "multi"        # 多线程
    connection_pooling: true  # 连接池
    
  # 低延迟优化
  low_latency:
    enabled: false
    polling_interval: 0.0005  # 0.5ms
    batch_size: 10            # 小批处理
    threading: "optimized"    # 优化线程
    memory_preallocation: true # 内存预分配

# 环境特定优化
environments:
  development:
    polling:
      adaptive:
        enabled: false
    monitoring:
      performance_monitoring:
        interval: 5.0
        
  testing:
    cpu_optimization:
      priority:
        process_priority: "normal"
    monitoring:
      auto_tuning:
        enabled: false
        
  production:
    scenarios:
      high_frequency:
        enabled: true
    cpu_optimization:
      priority:
        process_priority: "high"