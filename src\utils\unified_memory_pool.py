#!/usr/bin/env python3
"""
统一共享内存池 - 架构唯一权威实现
减少对象创建开销，提供预分配内存管理
实现微秒级内存分配，消除GC压力
硬迁移：零向后兼容，强制统一，SSOT
"""
import asyncio
import threading
import time
import logging
from typing import Dict, Any, Optional, List, Union, Tuple, Generic, TypeVar
from dataclasses import dataclass
from enum import Enum
import weakref
import mmap
import os
import tempfile
from concurrent.futures import ThreadPoolExecutor
import ctypes

logger = logging.getLogger(__name__)

T = TypeVar('T')

class MemoryBlockSize(Enum):
    """内存块大小"""
    TINY = 64           # 64 bytes - 小消息头部
    SMALL = 256         # 256 bytes - 交易信号
    MEDIUM = 1024       # 1K - 账户信息
    LARGE = 4096        # 4K - 持仓列表
    HUGE = 16384        # 16K - 历史数据
    MASSIVE = 65536     # 64K - 大批量数据

@dataclass
class MemoryBlock:
    """内存块"""
    block_id: int
    size: int
    offset: int
    data: memoryview
    allocated: bool = False
    allocated_time: float = 0.0
    last_used_time: float = 0.0
    use_count: int = 0
    owner: Optional[str] = None

@dataclass
class PoolStats:
    """内存池统计"""
    total_blocks: int = 0
    allocated_blocks: int = 0
    free_blocks: int = 0
    total_memory: int = 0
    allocated_memory: int = 0
    free_memory: int = 0
    allocation_count: int = 0
    deallocation_count: int = 0
    reuse_count: int = 0
    gc_avoided_count: int = 0

class MemoryRegion:
    """内存区域"""
    
    def __init__(self, name: str, size: int, block_size: int):
        self.name = name
        self.size = size
        self.block_size = block_size
        self.block_count = size // block_size
        
        # 创建共享内存映射
        self.temp_file = tempfile.NamedTemporaryFile(delete=False)
        self.temp_file.write(b'\x00' * size)
        self.temp_file.flush()
        
        self.memory_map = mmap.mmap(
            self.temp_file.fileno(),
            size,
            access=mmap.ACCESS_WRITE
        )
        
        # 创建内存块
        self.blocks: List[MemoryBlock] = []
        self.free_blocks: List[int] = []  # 空闲块索引
        self.allocated_blocks: Dict[int, MemoryBlock] = {}
        
        self._init_blocks()
        
        # 线程安全
        self._lock = threading.RLock()
        
        logger.info(f"✅ 内存区域已创建: {name} ({size} bytes, {self.block_count} blocks)")
    
    def _init_blocks(self):
        """初始化内存块"""
        for i in range(self.block_count):
            offset = i * self.block_size
            
            # 创建内存视图（零拷贝）
            block_data = memoryview(self.memory_map)[offset:offset + self.block_size]
            
            block = MemoryBlock(
                block_id=i,
                size=self.block_size,
                offset=offset,
                data=block_data
            )
            
            self.blocks.append(block)
            self.free_blocks.append(i)
    
    def allocate(self, owner: Optional[str] = None) -> Optional[MemoryBlock]:
        """分配内存块"""
        with self._lock:
            if not self.free_blocks:
                return None
            
            # 获取空闲块
            block_index = self.free_blocks.pop(0)
            block = self.blocks[block_index]
            
            # 标记为已分配
            block.allocated = True
            block.allocated_time = time.time()
            block.last_used_time = time.time()
            block.use_count += 1
            block.owner = owner
            
            self.allocated_blocks[block_index] = block
            
            return block
    
    def deallocate(self, block: MemoryBlock) -> bool:
        """释放内存块"""
        with self._lock:
            if not block.allocated or block.block_id not in self.allocated_blocks:
                return False
            
            # 清零内存（可选）
            # block.data[:] = b'\x00' * len(block.data)
            
            # 标记为未分配
            block.allocated = False
            block.owner = None
            
            # 移回空闲列表
            self.allocated_blocks.pop(block.block_id)
            self.free_blocks.append(block.block_id)
            
            return True
    
    def get_stats(self) -> Dict[str, Any]:
        """获取区域统计"""
        with self._lock:
            allocated_count = len(self.allocated_blocks)
            free_count = len(self.free_blocks)
            
            return {
                'name': self.name,
                'total_blocks': self.block_count,
                'allocated_blocks': allocated_count,
                'free_blocks': free_count,
                'utilization': allocated_count / self.block_count * 100,
                'total_memory': self.size,
                'allocated_memory': allocated_count * self.block_size,
                'free_memory': free_count * self.block_size
            }
    
    def close(self):
        """关闭内存区域"""
        try:
            if self.memory_map:
                self.memory_map.close()
                self.memory_map = None
            
            if self.temp_file:
                self.temp_file.close()
                os.unlink(self.temp_file.name)
                self.temp_file = None
            
            logger.debug(f"🗑️ 内存区域已关闭: {self.name}")
            
        except Exception as e:
            logger.error(f"❌ 关闭内存区域失败: {e}")

class ObjectPool(Generic[T]):
    """对象池"""
    
    def __init__(self, name: str, factory: callable, reset_func: callable = None, max_size: int = 1000):
        self.name = name
        self.factory = factory
        self.reset_func = reset_func
        self.max_size = max_size
        
        self._pool: List[T] = []
        self._active_objects: weakref.WeakSet = weakref.WeakSet()
        self._lock = threading.RLock()
        
        # 统计
        self._stats = {
            'created': 0,
            'reused': 0,
            'returned': 0,
            'destroyed': 0
        }
        
        logger.info(f"📦 对象池已创建: {name} (max_size: {max_size})")
    
    def acquire(self) -> T:
        """获取对象"""
        with self._lock:
            if self._pool:
                obj = self._pool.pop()
                self._stats['reused'] += 1
            else:
                obj = self.factory()
                self._stats['created'] += 1
            
            self._active_objects.add(obj)
            return obj
    
    def release(self, obj: T):
        """释放对象"""
        with self._lock:
            if obj not in self._active_objects:
                return
            
            self._active_objects.discard(obj)
            
            if len(self._pool) < self.max_size:
                # 重置对象状态
                if self.reset_func:
                    try:
                        self.reset_func(obj)
                    except Exception as e:
                        logger.error(f"❌ 对象重置失败: {e}")
                        return
                
                self._pool.append(obj)
                self._stats['returned'] += 1
            else:
                self._stats['destroyed'] += 1
    
    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        with self._lock:
            return {
                'name': self.name,
                'pool_size': len(self._pool),
                'active_objects': len(self._active_objects),
                'max_size': self.max_size,
                'utilization': len(self._active_objects) / self.max_size * 100,
                **self._stats
            }
    
    def clear(self):
        """清空对象池"""
        with self._lock:
            self._pool.clear()
            self._active_objects.clear()

class UnifiedMemoryPool:
    """
    统一内存池 - 架构唯一权威实现
    提供预分配内存管理，减少GC压力和对象创建开销
    """
    
    def __init__(self, pool_name: str = "unified_memory_pool"):
        self.pool_name = pool_name
        
        # 内存区域映射
        self.regions: Dict[MemoryBlockSize, MemoryRegion] = {}
        
        # 对象池映射
        self.object_pools: Dict[str, ObjectPool] = {}
        
        # 全局统计
        self.stats = PoolStats()
        
        # 性能监控
        self._allocation_times: List[float] = []
        self._gc_pressure_reduced = 0
        
        # 线程安全
        self._global_lock = threading.RLock()
        
        # 清理任务
        self._cleanup_task: Optional[asyncio.Task] = None
        self.running = False
        
        # 初始化默认内存区域
        self._init_default_regions()
        
        logger.info(f"🎯 统一内存池已创建: {pool_name}")
    
    def _init_default_regions(self):
        """初始化默认内存区域"""
        try:
            # 为每种大小创建内存区域
            region_configs = {
                MemoryBlockSize.TINY: (64, 10000),      # 64B x 10K = 640KB
                MemoryBlockSize.SMALL: (256, 5000),     # 256B x 5K = 1.25MB
                MemoryBlockSize.MEDIUM: (1024, 2000),   # 1KB x 2K = 2MB
                MemoryBlockSize.LARGE: (4096, 500),     # 4KB x 500 = 2MB
                MemoryBlockSize.HUGE: (16384, 100),     # 16KB x 100 = 1.6MB
                MemoryBlockSize.MASSIVE: (65536, 20),   # 64KB x 20 = 1.28MB
            }
            
            for block_size, (size, count) in region_configs.items():
                total_size = size * count
                region = MemoryRegion(
                    name=f"{self.pool_name}_{block_size.name.lower()}",
                    size=total_size,
                    block_size=size
                )
                
                self.regions[block_size] = region
                
                # 更新统计
                self.stats.total_blocks += region.block_count
                self.stats.free_blocks += region.block_count
                self.stats.total_memory += total_size
                self.stats.free_memory += total_size
            
            logger.info("✅ 默认内存区域初始化完成")
            
        except Exception as e:
            logger.error(f"❌ 初始化默认内存区域失败: {e}")
            raise
    
    async def start(self):
        """启动内存池"""
        if self.running:
            logger.warning("内存池已在运行")
            return
        
        self.running = True
        
        # 启动清理任务
        self._cleanup_task = asyncio.create_task(self._cleanup_worker())
        
        logger.info("🚀 统一内存池已启动")
    
    async def stop(self):
        """停止内存池"""
        if not self.running:
            return
        
        self.running = False
        
        # 停止清理任务
        if self._cleanup_task:
            self._cleanup_task.cancel()
            try:
                await self._cleanup_task
            except asyncio.CancelledError:
                pass
        
        # 关闭所有内存区域
        for region in self.regions.values():
            region.close()
        
        # 清空对象池
        for pool in self.object_pools.values():
            pool.clear()
        
        logger.info("🛑 统一内存池已停止")
    
    def allocate_memory(self, size: int, owner: Optional[str] = None) -> Optional[MemoryBlock]:
        """
        分配内存块
        
        Args:
            size: 所需内存大小
            owner: 所有者标识
        
        Returns:
            内存块或None
        """
        start_time = time.time()
        
        try:
            # 选择合适的内存区域
            block_size = self._select_block_size(size)
            region = self.regions.get(block_size)
            
            if not region:
                logger.error(f"❌ 没有找到合适的内存区域: {size} bytes")
                return None
            
            # 分配内存块
            block = region.allocate(owner)
            
            if block:
                # 更新全局统计
                with self._global_lock:
                    self.stats.allocated_blocks += 1
                    self.stats.free_blocks -= 1
                    self.stats.allocated_memory += block.size
                    self.stats.free_memory -= block.size
                    self.stats.allocation_count += 1
                    self.stats.gc_avoided_count += 1
                
                # 记录分配时间
                allocation_time = time.time() - start_time
                self._allocation_times.append(allocation_time)
                
                # 保持最近1000次分配时间
                if len(self._allocation_times) > 1000:
                    self._allocation_times.pop(0)
                
                logger.debug(f"📦 内存分配成功: {size} bytes -> {block.size} bytes ({allocation_time*1000:.3f}ms)")
                
                return block
            else:
                logger.warning(f"⚠️ 内存分配失败: {size} bytes - 区域已满")
                return None
                
        except Exception as e:
            logger.error(f"❌ 内存分配异常: {e}")
            return None
    
    def deallocate_memory(self, block: MemoryBlock) -> bool:
        """释放内存块"""
        try:
            if not block or not block.allocated:
                return False
            
            # 找到对应的区域
            block_size = self._size_to_block_size(block.size)
            region = self.regions.get(block_size)
            
            if not region:
                logger.error(f"❌ 找不到对应的内存区域: {block.size}")
                return False
            
            # 释放内存块
            success = region.deallocate(block)
            
            if success:
                # 更新全局统计
                with self._global_lock:
                    self.stats.allocated_blocks -= 1
                    self.stats.free_blocks += 1
                    self.stats.allocated_memory -= block.size
                    self.stats.free_memory += block.size
                    self.stats.deallocation_count += 1
                
                logger.debug(f"📤 内存释放成功: {block.size} bytes")
                
                return True
            else:
                logger.error(f"❌ 内存释放失败: {block.block_id}")
                return False
                
        except Exception as e:
            logger.error(f"❌ 内存释放异常: {e}")
            return False
    
    def _select_block_size(self, size: int) -> MemoryBlockSize:
        """选择合适的内存块大小"""
        if size <= 64:
            return MemoryBlockSize.TINY
        elif size <= 256:
            return MemoryBlockSize.SMALL
        elif size <= 1024:
            return MemoryBlockSize.MEDIUM
        elif size <= 4096:
            return MemoryBlockSize.LARGE
        elif size <= 16384:
            return MemoryBlockSize.HUGE
        else:
            return MemoryBlockSize.MASSIVE
    
    def _size_to_block_size(self, size: int) -> MemoryBlockSize:
        """根据实际大小确定块大小枚举"""
        for block_size in MemoryBlockSize:
            if block_size.value == size:
                return block_size
        
        # 如果没有精确匹配，选择最接近的
        return self._select_block_size(size)
    
    def create_object_pool(self, name: str, factory: callable, reset_func: callable = None, max_size: int = 1000) -> ObjectPool:
        """创建对象池"""
        if name in self.object_pools:
            logger.warning(f"⚠️ 对象池已存在: {name}")
            return self.object_pools[name]
        
        pool = ObjectPool(name, factory, reset_func, max_size)
        self.object_pools[name] = pool
        
        logger.info(f"✅ 对象池已创建: {name}")
        return pool
    
    def get_object_pool(self, name: str) -> Optional[ObjectPool]:
        """获取对象池"""
        return self.object_pools.get(name)
    
    def allocate_zero_copy_buffer(self, size: int) -> Optional[memoryview]:
        """分配零拷贝缓冲区"""
        block = self.allocate_memory(size, "zero_copy_buffer")
        if block:
            return block.data[:size]  # 返回指定大小的视图
        return None
    
    def get_memory_view(self, block: MemoryBlock, offset: int = 0, length: int = None) -> Optional[memoryview]:
        """获取内存块的视图（零拷贝）"""
        try:
            if not block or not block.allocated:
                return None
            
            if length is None:
                length = block.size - offset
            
            if offset + length > block.size:
                logger.error("❌ 内存视图超出边界")
                return None
            
            return block.data[offset:offset + length]
            
        except Exception as e:
            logger.error(f"❌ 获取内存视图失败: {e}")
            return None
    
    async def _cleanup_worker(self):
        """清理工作线程"""
        logger.info("🔄 启动内存池清理工作线程")
        
        while self.running:
            try:
                await asyncio.sleep(30)  # 每30秒清理一次
                
                # 清理长时间未使用的内存块
                await self._cleanup_unused_blocks()
                
                # 整理内存碎片
                await self._defragment_memory()
                
                # 记录统计信息
                self._log_performance_stats()
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"❌ 内存池清理异常: {e}")
                await asyncio.sleep(5)
        
        logger.info("🛑 内存池清理工作线程停止")
    
    async def _cleanup_unused_blocks(self):
        """清理未使用的内存块"""
        try:
            current_time = time.time()
            cleanup_threshold = 300  # 5分钟未使用
            
            for region in self.regions.values():
                with region._lock:
                    for block in region.allocated_blocks.values():
                        if current_time - block.last_used_time > cleanup_threshold:
                            # 可以考虑释放长时间未使用的块
                            # 这里暂时只记录日志
                            logger.debug(f"🕐 发现长时间未使用的内存块: {block.block_id}")
            
        except Exception as e:
            logger.error(f"❌ 清理未使用内存块失败: {e}")
    
    async def _defragment_memory(self):
        """整理内存碎片"""
        try:
            # 这里可以实现内存碎片整理逻辑
            # 由于使用固定大小的内存块，碎片问题较少
            pass
            
        except Exception as e:
            logger.error(f"❌ 内存碎片整理失败: {e}")
    
    def _log_performance_stats(self):
        """记录性能统计"""
        try:
            # 计算平均分配时间
            if self._allocation_times:
                avg_allocation_time = sum(self._allocation_times) / len(self._allocation_times)
                max_allocation_time = max(self._allocation_times)
                min_allocation_time = min(self._allocation_times)
                
                logger.info(f"📊 内存池性能: 平均分配时间={avg_allocation_time*1000:.3f}ms, "
                          f"最大={max_allocation_time*1000:.3f}ms, "
                          f"最小={min_allocation_time*1000:.3f}ms")
            
            # 记录内存使用情况
            utilization = self.stats.allocated_memory / self.stats.total_memory * 100
            logger.info(f"📊 内存使用率: {utilization:.1f}% "
                      f"({self.stats.allocated_memory}/{self.stats.total_memory} bytes)")
            
        except Exception as e:
            logger.error(f"❌ 记录性能统计失败: {e}")
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """获取性能统计"""
        with self._global_lock:
            # 计算分配时间统计
            allocation_stats = {}
            if self._allocation_times:
                allocation_stats = {
                    'avg_allocation_time_ms': sum(self._allocation_times) / len(self._allocation_times) * 1000,
                    'max_allocation_time_ms': max(self._allocation_times) * 1000,
                    'min_allocation_time_ms': min(self._allocation_times) * 1000,
                    'recent_allocations': len(self._allocation_times)
                }
            
            # 区域统计
            region_stats = {}
            for block_size, region in self.regions.items():
                region_stats[block_size.name] = region.get_stats()
            
            # 对象池统计
            pool_stats = {}
            for name, pool in self.object_pools.items():
                pool_stats[name] = pool.get_stats()
            
            return {
                'pool_name': self.pool_name,
                'global_stats': {
                    'total_blocks': self.stats.total_blocks,
                    'allocated_blocks': self.stats.allocated_blocks,
                    'free_blocks': self.stats.free_blocks,
                    'total_memory': self.stats.total_memory,
                    'allocated_memory': self.stats.allocated_memory,
                    'free_memory': self.stats.free_memory,
                    'memory_utilization': self.stats.allocated_memory / self.stats.total_memory * 100,
                    'allocation_count': self.stats.allocation_count,
                    'deallocation_count': self.stats.deallocation_count,
                    'gc_avoided_count': self.stats.gc_avoided_count
                },
                'allocation_stats': allocation_stats,
                'region_stats': region_stats,
                'object_pool_stats': pool_stats
            }

# 全局内存池实例
_global_memory_pool: Optional[UnifiedMemoryPool] = None

def get_unified_memory_pool() -> UnifiedMemoryPool:
    """获取全局统一内存池实例"""
    global _global_memory_pool
    if _global_memory_pool is None:
        _global_memory_pool = UnifiedMemoryPool()
    return _global_memory_pool

def reset_unified_memory_pool():
    """重置统一内存池（主要用于测试）"""
    global _global_memory_pool
    _global_memory_pool = None

# 便捷函数
def allocate_message_buffer(size: int) -> Optional[memoryview]:
    """分配消息缓冲区"""
    return get_unified_memory_pool().allocate_zero_copy_buffer(size)

def create_message_object_pool(factory: callable, max_size: int = 1000) -> ObjectPool:
    """创建消息对象池"""
    return get_unified_memory_pool().create_object_pool(
        "message_objects",
        factory,
        max_size=max_size
    )