"""
终端连接池

管理MT5连接的池化和复用。
"""

import asyncio
from dataclasses import dataclass
from datetime import datetime, timedelta
from typing import Dict, Optional, Any, List
from collections import deque
import logging
import time


logger = logging.getLogger(__name__)


@dataclass
class PoolConfig:
    """连接池配置"""
    min_connections: int = 5
    max_connections: int = 100
    max_idle_time: int = 300  # 秒
    validation_interval: int = 60  # 秒
    acquire_timeout: float = 5.0  # 秒
    
    # 连接复用
    max_connection_reuse: int = 1000  # 最大复用次数
    connection_lifetime: int = 3600  # 连接生命周期（秒）


@dataclass
class ConnectionStats:
    """连接统计"""
    created_at: datetime
    last_used: datetime
    use_count: int = 0
    errors: int = 0
    total_time_ms: float = 0.0
    
    @property
    def avg_time_ms(self) -> float:
        """平均使用时间"""
        return self.total_time_ms / self.use_count if self.use_count > 0 else 0.0
    
    @property
    def idle_time(self) -> timedelta:
        """空闲时间"""
        return datetime.now() - self.last_used


class PooledConnection:
    """池化连接包装器"""
    
    def __init__(self, connection_id: str, connection: Any):
        self.id = connection_id
        self.connection = connection
        self.stats = ConnectionStats(
            created_at=datetime.now(),
            last_used=datetime.now()
        )
        self.in_use = False
        self.valid = True
    
    async def validate(self) -> bool:
        """验证连接是否有效"""
        try:
            # 检查连接是否还活着
            if hasattr(self.connection, 'ping'):
                return await self.connection.ping()
            elif hasattr(self.connection, 'is_connected'):
                return self.connection.is_connected()
            else:
                # 默认认为有效
                return True
        except Exception as e:
            logger.error(f"Connection validation failed: {e}")
            return False
    
    def acquire(self):
        """获取连接"""
        self.in_use = True
        self.stats.use_count += 1
        self.stats.last_used = datetime.now()
    
    def release(self):
        """释放连接"""
        self.in_use = False
        self.stats.last_used = datetime.now()
    
    def should_retire(self, config: PoolConfig) -> bool:
        """检查是否应该退休"""
        # 检查使用次数
        if self.stats.use_count >= config.max_connection_reuse:
            return True
        
        # 检查生命周期
        age = (datetime.now() - self.stats.created_at).total_seconds()
        if age >= config.connection_lifetime:
            return True
        
        # 检查错误率
        if self.stats.errors > 10:
            return True
        
        return False


class TerminalPool:
    """终端连接池"""
    
    def __init__(self, size: int = 10, config: Optional[PoolConfig] = None):
        self.size = size
        self.config = config or PoolConfig()
        
        # 连接存储
        self.connections: Dict[str, PooledConnection] = {}
        self.available: Dict[str, deque] = {}  # terminal_id -> deque of connections
        self.waiters: Dict[str, deque] = {}  # terminal_id -> deque of waiters
        
        # 统计
        self.stats = {
            'total_connections': 0,
            'active_connections': 0,
            'idle_connections': 0,
            'total_acquires': 0,
            'total_releases': 0,
            'total_creates': 0,
            'total_destroys': 0,
            'wait_time_ms': 0.0
        }
        
        # 验证任务
        self.validation_task: Optional[asyncio.Task] = None
        
        # 锁
        self._lock = asyncio.Lock()
    
    async def start(self):
        """启动连接池"""
        # 启动验证任务
        self.validation_task = asyncio.create_task(self._validation_task())
        logger.info("Terminal pool started")
    
    async def close(self):
        """关闭连接池"""
        # 停止验证任务
        if self.validation_task:
            self.validation_task.cancel()
            try:
                await self.validation_task
            except asyncio.CancelledError:
                pass
        
        # 关闭所有连接
        for conn_id in list(self.connections.keys()):
            await self._destroy_connection(conn_id)
        
        # 取消所有等待者
        for waiters in self.waiters.values():
            while waiters:
                waiter = waiters.popleft()
                if not waiter.done():
                    waiter.set_exception(Exception("Pool closed"))
        
        logger.info("Terminal pool closed")
    
    async def add_connection(self, connection_id: str, connection: Any) -> bool:
        """添加连接到池"""
        async with self._lock:
            if connection_id in self.connections:
                logger.warning(f"Connection {connection_id} already exists")
                return False
            
            # 创建池化连接
            pooled = PooledConnection(connection_id, connection)
            self.connections[connection_id] = pooled
            
            # 提取终端ID
            terminal_id = connection_id.split(':')[0]
            
            # 添加到可用队列
            if terminal_id not in self.available:
                self.available[terminal_id] = deque()
            
            self.available[terminal_id].append(pooled)
            
            # 更新统计
            self.stats['total_connections'] += 1
            self.stats['idle_connections'] += 1
            self.stats['total_creates'] += 1
            
            # 通知等待者
            await self._notify_waiters(terminal_id)
            
            logger.debug(f"Added connection {connection_id} to pool")
            return True
    
    async def get_connection(self, connection_id: str,
                           timeout: Optional[float] = None):
        """获取连接"""
        timeout = timeout or self.config.acquire_timeout
        start_time = datetime.now()
        
        # 直接获取特定连接
        if ':' in connection_id:
            async with self._lock:
                pooled = self.connections.get(connection_id)
                if pooled and not pooled.in_use and pooled.valid:
                    pooled.acquire()
                    self.stats['active_connections'] += 1
                    self.stats['idle_connections'] -= 1
                    self.stats['total_acquires'] += 1
                    return pooled.connection
                return None
        
        # 获取终端的任意可用连接
        terminal_id = connection_id
        
        # 尝试获取可用连接
        conn = await self._try_acquire(terminal_id)
        if conn:
            return conn
        
        # 等待可用连接
        waiter = asyncio.Future()
        
        async with self._lock:
            if terminal_id not in self.waiters:
                self.waiters[terminal_id] = deque()
            self.waiters[terminal_id].append(waiter)
        
        try:
            conn = await asyncio.wait_for(waiter, timeout=timeout)
            
            # 更新等待时间统计
            wait_time = (datetime.now() - start_time).total_seconds() * 1000
            self._update_wait_time(wait_time)
            
            return conn
            
        except asyncio.TimeoutError:
            # 从等待队列移除
            async with self._lock:
                if terminal_id in self.waiters:
                    try:
                        self.waiters[terminal_id].remove(waiter)
                    except ValueError:
                        pass
            
            logger.warning(f"Timeout acquiring connection for {terminal_id}")
            return None
    
    async def release_connection(self, connection_id: str):
        """释放连接"""
        async with self._lock:
            pooled = self.connections.get(connection_id)
            if not pooled:
                logger.warning(f"Connection {connection_id} not found")
                return
            
            if not pooled.in_use:
                logger.warning(f"Connection {connection_id} not in use")
                return
            
            pooled.release()
            
            # 更新统计
            self.stats['active_connections'] -= 1
            self.stats['total_releases'] += 1
            
            # 检查是否应该退休
            if pooled.should_retire(self.config):
                await self._destroy_connection(connection_id)
            else:
                # 放回可用队列
                terminal_id = connection_id.split(':')[0]
                if terminal_id not in self.available:
                    self.available[terminal_id] = deque()
                
                self.available[terminal_id].append(pooled)
                self.stats['idle_connections'] += 1
                
                # 通知等待者
                await self._notify_waiters(terminal_id)
    
    async def remove_connection(self, connection_id: str):
        """从池中移除连接"""
        async with self._lock:
            await self._destroy_connection(connection_id)
    
    async def _try_acquire(self, terminal_id: str) -> Optional[Any]:
        """尝试获取可用连接"""
        async with self._lock:
            if terminal_id not in self.available:
                return None
            
            available_conns = self.available[terminal_id]
            
            while available_conns:
                pooled = available_conns.popleft()
                
                if pooled.in_use:
                    continue
                
                # 检查连接是否有效
                if pooled.valid:
                    pooled.acquire()
                    self.stats['active_connections'] += 1
                    self.stats['idle_connections'] -= 1
                    self.stats['total_acquires'] += 1
                    return pooled.connection
                else:
                    # 销毁无效连接
                    await self._destroy_connection(pooled.id)
            
            return None
    
    async def _destroy_connection(self, connection_id: str):
        """销毁连接"""
        pooled = self.connections.get(connection_id)
        if not pooled:
            return
        
        try:
            # 关闭连接
            if hasattr(pooled.connection, 'close'):
                await pooled.connection.close()
            elif hasattr(pooled.connection, 'disconnect'):
                await pooled.connection.disconnect()
        except Exception as e:
            logger.error(f"Error closing connection {connection_id}: {e}")
        
        # 从池中移除
        del self.connections[connection_id]
        
        # 从可用队列移除
        terminal_id = connection_id.split(':')[0]
        if terminal_id in self.available:
            try:
                self.available[terminal_id].remove(pooled)
            except ValueError:
                pass
        
        # 更新统计
        self.stats['total_connections'] -= 1
        if pooled.in_use:
            self.stats['active_connections'] -= 1
        else:
            self.stats['idle_connections'] -= 1
        self.stats['total_destroys'] += 1
        
        logger.debug(f"Destroyed connection {connection_id}")
    
    async def _notify_waiters(self, terminal_id: str):
        """通知等待者"""
        if terminal_id not in self.waiters:
            return
        
        waiters = self.waiters[terminal_id]
        available_conns = self.available.get(terminal_id, deque())
        
        while waiters and available_conns:
            waiter = waiters.popleft()
            
            if waiter.done():
                continue
            
            # 尝试分配连接
            conn = await self._try_acquire(terminal_id)
            if conn:
                waiter.set_result(conn)
            else:
                # 放回等待队列
                waiters.appendleft(waiter)
                break
    
    async def _validation_task(self):
        """连接验证任务"""
        while True:
            try:
                await asyncio.sleep(self.config.validation_interval)
                
                # 验证所有空闲连接
                invalid_connections = []
                
                async with self._lock:
                    for conn_id, pooled in self.connections.items():
                        if not pooled.in_use:
                            # 检查空闲时间
                            if pooled.stats.idle_time.total_seconds() > self.config.max_idle_time:
                                invalid_connections.append(conn_id)
                                continue
                            
                            # 验证连接
                            if not await pooled.validate():
                                pooled.valid = False
                                invalid_connections.append(conn_id)
                
                # 销毁无效连接
                for conn_id in invalid_connections:
                    await self.remove_connection(conn_id)
                
                if invalid_connections:
                    logger.info(f"Removed {len(invalid_connections)} invalid connections")
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Validation task error: {e}")
    
    def _update_wait_time(self, wait_time_ms: float):
        """更新等待时间统计"""
        total_waits = self.stats['total_acquires']
        if total_waits > 0:
            current_avg = self.stats['wait_time_ms']
            self.stats['wait_time_ms'] = (
                (current_avg * (total_waits - 1) + wait_time_ms) / total_waits
            )
    
    async def get_stats(self) -> Dict[str, Any]:
        """获取连接池统计"""
        async with self._lock:
            terminal_stats = {}
            
            for terminal_id, conns in self.available.items():
                terminal_stats[terminal_id] = {
                    'available': len(conns),
                    'waiters': len(self.waiters.get(terminal_id, []))
                }
            
            # 连接详情
            connection_details = {}
            for conn_id, pooled in self.connections.items():
                connection_details[conn_id] = {
                    'in_use': pooled.in_use,
                    'valid': pooled.valid,
                    'use_count': pooled.stats.use_count,
                    'errors': pooled.stats.errors,
                    'avg_time_ms': pooled.stats.avg_time_ms,
                    'idle_seconds': pooled.stats.idle_time.total_seconds()
                }
            
            return {
                **self.stats,
                'terminals': terminal_stats,
                'connections': connection_details
            }
    
    async def health_check(self) -> Dict[str, Any]:
        """健康检查"""
        stats = await self.get_stats()
        
        # 计算健康指标
        total_conns = stats['total_connections']
        active_conns = stats['active_connections']
        
        health = {
            'healthy': True,
            'total_connections': total_conns,
            'active_connections': active_conns,
            'connection_usage': active_conns / total_conns * 100 if total_conns > 0 else 0,
            'avg_wait_time_ms': stats['wait_time_ms']
        }
        
        # 检查连接使用率
        if health['connection_usage'] > 90:
            health['healthy'] = False
            health['reason'] = 'High connection usage'
        
        # 检查等待时间
        if health['avg_wait_time_ms'] > 1000:
            health['healthy'] = False
            health['reason'] = 'High wait time'
        
        return health


if __name__ == "__main__":
    # 简单测试
    import asyncio

    async def test_pool():
        print("🧪 测试终端连接池...")

        # 创建连接池
        config = PoolConfig(min_connections=2, max_connections=10)
        pool = TerminalPool(config=config)

        print(f"✅ 连接池创建成功")
        print(f"   最小连接数: {config.min_connections}")
        print(f"   最大连接数: {config.max_connections}")

        # 启动连接池
        await pool.start()
        print("✅ 连接池已启动")

        # 添加模拟连接
        mock_connection = {"id": "test_connection", "status": "active"}
        success = await pool.add_connection("terminal_001:account_001", mock_connection)
        print(f"✅ 添加连接: {success}")

        # 获取统计
        stats = await pool.get_stats()
        print(f"✅ 连接池统计:")
        print(f"   总连接数: {stats['total_connections']}")
        print(f"   空闲连接数: {stats['idle_connections']}")

        # 健康检查
        health = await pool.health_check()
        print(f"✅ 健康检查: {'健康' if health['healthy'] else '不健康'}")

        # 关闭连接池
        await pool.close()
        print("✅ 连接池已关闭")

        print("🎉 连接池测试通过!")

    # 运行测试
    asyncio.run(test_pool())