#!/usr/bin/env python3
"""
Scripts目录结构优化工具
将scripts目录中的文件按功能分类到子目录中
"""
import os
import shutil
from pathlib import Path
from typing import Dict, List

class ScriptsOrganizer:
    """Scripts目录组织器"""
    
    def __init__(self):
        self.scripts_dir = Path(__file__).parent
        
        # 定义目录结构和文件分类
        self.directory_structure = {
            "deployment": {
                "description": "部署相关脚本",
                "files": [
                    "deploy.sh",
                    "deploy_monitoring.py", 
                    "setup_remote_host.py",
                    "setup_security.sh",
                    "windows_quick_start.bat"
                ]
            },
            "launchers": {
                "description": "启动器脚本",
                "files": [
                    "distributed_launcher.py",
                    "mt5_launcher.py",
                    "start_copy_trading.py",
                    "start_distributed.bat",
                    "start_distributed.sh"
                ]
            },
            "utilities": {
                "description": "工具脚本",
                "files": [
                    "config_manager.py",
                    "health_check.py",
                    "role_manager.py",
                    "generate_api_docs.py",
                    "verify-optimized-architecture.py"
                ]
            },
            "maintenance": {
                "description": "维护脚本",
                "files": [
                    "cleanup_config.py",
                    "cleanup_scripts_directory.py",
                    "collect_logs.py",
                    "migrate_config.py",
                    "fix_system_issues.py",
                    "optimize_configuration.py"
                ]
            },
            "security": {
                "description": "安全相关脚本",
                "files": [
                    "generate_security_keys.py",
                    "generate_tls_certs.sh",
                    "security_check.sh",
                    "validate_security.py"
                ]
            },
            "validation": {
                "description": "验证脚本",
                "files": [
                    "validate_distributed_config.py"
                ]
            },
            "infrastructure": {
                "description": "基础设施管理",
                "files": [
                    "manage_infrastructure.py"
                ]
            }
        }
    
    def create_directory_structure(self) -> bool:
        """创建目录结构"""
        print("📁 创建目录结构...")
        
        try:
            for dir_name, config in self.directory_structure.items():
                dir_path = self.scripts_dir / dir_name
                dir_path.mkdir(exist_ok=True)
                
                # 创建README文件
                readme_path = dir_path / "README.md"
                if not readme_path.exists():
                    self._create_readme(readme_path, dir_name, config["description"])
                
                print(f"✅ 创建目录: {dir_name}/ - {config['description']}")
            
            return True
            
        except Exception as e:
            print(f"❌ 创建目录结构失败: {e}")
            return False
    
    def _create_readme(self, readme_path: Path, dir_name: str, description: str):
        """创建README文件"""
        content = f"""# {dir_name.title()} Scripts

{description}

## 文件说明

"""
        
        # 添加文件列表
        for file in self.directory_structure[dir_name]["files"]:
            content += f"- `{file}`: \n"
        
        content += f"""
## 使用方法

请参考各个脚本文件中的文档说明。

## 注意事项

- 执行脚本前请确保已正确配置环境
- 部分脚本需要管理员权限
- 建议在测试环境中先验证脚本功能
"""
        
        with open(readme_path, 'w', encoding='utf-8') as f:
            f.write(content)
    
    def move_files_to_subdirectories(self) -> bool:
        """移动文件到子目录"""
        print("📦 移动文件到子目录...")
        
        try:
            moved_files = []
            
            for dir_name, config in self.directory_structure.items():
                target_dir = self.scripts_dir / dir_name
                
                for filename in config["files"]:
                    source_path = self.scripts_dir / filename
                    target_path = target_dir / filename
                    
                    if source_path.exists() and not target_path.exists():
                        shutil.move(str(source_path), str(target_path))
                        moved_files.append(f"{filename} -> {dir_name}/")
                        print(f"📁 移动: {filename} -> {dir_name}/")
                    elif target_path.exists():
                        print(f"⚠️ 文件已存在: {dir_name}/{filename}")
                    else:
                        print(f"⚠️ 源文件不存在: {filename}")
            
            print(f"✅ 成功移动 {len(moved_files)} 个文件")
            return True
            
        except Exception as e:
            print(f"❌ 移动文件失败: {e}")
            return False
    
    def create_main_readme(self):
        """创建主README文件"""
        readme_path = self.scripts_dir / "README.md"
        
        content = """# Scripts Directory

MT5分布式交易系统的脚本工具集合。

## 目录结构

"""
        
        for dir_name, config in self.directory_structure.items():
            content += f"### {dir_name}/\n{config['description']}\n\n"
            for file in config['files'][:3]:  # 只显示前3个文件
                content += f"- `{file}`\n"
            if len(config['files']) > 3:
                content += f"- ... 还有 {len(config['files']) - 3} 个文件\n"
            content += "\n"
        
        content += """## 快速开始

### 部署系统
```bash
# 部署到本地
./deployment/deploy.sh

# 部署到远程主机
python deployment/setup_remote_host.py
```

### 启动系统
```bash
# 启动分布式系统
python launchers/distributed_launcher.py

# 启动复制交易
python launchers/start_copy_trading.py
```

### 系统维护
```bash
# 健康检查
python utilities/health_check.py

# 收集日志
python maintenance/collect_logs.py

# 配置优化
python maintenance/optimize_configuration.py
```

## 注意事项

1. **权限要求**: 部分脚本需要管理员权限
2. **环境依赖**: 确保已安装所需的Python包和系统依赖
3. **配置文件**: 执行前请检查相关配置文件
4. **测试环境**: 建议先在测试环境中验证脚本功能

## 开发指南

### 添加新脚本

1. 根据功能选择合适的子目录
2. 添加脚本文件
3. 更新对应子目录的README.md
4. 在脚本中添加详细的文档说明

### 脚本规范

- 使用Python 3.8+
- 添加类型注解
- 包含详细的docstring
- 实现错误处理和日志记录
- 支持命令行参数

## 故障排除

如果遇到问题，请：

1. 检查日志文件
2. 运行健康检查脚本
3. 验证配置文件
4. 查看相关文档

## 贡献

欢迎提交改进建议和bug报告。
"""
        
        with open(readme_path, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print("📝 创建主README文件")
    
    def generate_organization_report(self):
        """生成组织报告"""
        print("\n" + "="*60)
        print("📊 Scripts目录组织报告")
        print("="*60)
        
        print(f"\n📁 新的目录结构:")
        print("scripts/")
        print("├── README.md")
        
        for dir_name, config in self.directory_structure.items():
            print(f"├── {dir_name}/")
            print(f"│   ├── README.md")
            for file in config["files"][:2]:  # 只显示前2个文件
                print(f"│   ├── {file}")
            if len(config["files"]) > 2:
                print(f"│   └── ... ({len(config['files']) - 2} more files)")
            print("│")
        
        print(f"\n📊 统计信息:")
        total_files = sum(len(config["files"]) for config in self.directory_structure.values())
        print(f"  总目录数: {len(self.directory_structure)}")
        print(f"  总文件数: {total_files}")
        
        print(f"\n✅ 优势:")
        print("  - 按功能清晰分类")
        print("  - 易于查找和维护")
        print("  - 每个目录都有说明文档")
        print("  - 支持快速定位脚本")


def main():
    """主函数"""
    organizer = ScriptsOrganizer()
    
    print("🚀 开始组织scripts目录结构...")
    
    # 创建目录结构
    if not organizer.create_directory_structure():
        return
    
    # 移动文件
    if not organizer.move_files_to_subdirectories():
        return
    
    # 创建主README
    organizer.create_main_readme()
    
    # 生成报告
    organizer.generate_organization_report()
    
    print("\n🎉 Scripts目录组织完成！")
    print("💡 建议: 查看各子目录的README.md了解详细信息")


if __name__ == "__main__":
    main()
