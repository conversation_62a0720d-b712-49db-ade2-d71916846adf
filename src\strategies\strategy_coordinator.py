# src/strategies/hybrid_optimal/coordinator.py
"""混合最优策略协调器"""
import asyncio
from typing import Dict, List, Optional
from datetime import datetime

from strategy_manager import PyramidAggregationStrategy, WaveProgressionStrategy, PairHedgeStrategy
from manager.account_manager import AccountLevel, AccountState
from ..messaging.message_types import TradeSignal
from ..utils.logger import get_logger

logger = get_logger(__name__)


class HybridCoordinator:
    """混合最优策略协调器"""
    
    def __init__(self):
        # 三个子策略
        self.wave_strategy = WaveProgressionStrategy()
        self.hedge_strategy = PairHedgeStrategy()
        self.pyramid_strategy = PyramidAggregationStrategy()
        
        # 策略参数
        self.phase_durations = {
            1: 7,   # 波浪推进 - 7天
            2: 7,   # 配对对冲 - 7天
            3: 7    # 金字塔聚合 - 7天
        }
        
        self.current_phase = 1
        self.phase_start_time = datetime.now()
        
    async def initialize(self, accounts: Dict[str, AccountState]):
        """初始化策略"""
        logger.info("初始化混合最优策略")
        
        await self.wave_strategy.initialize(accounts)
        await self.hedge_strategy.initialize(accounts)
        await self.pyramid_strategy.initialize(accounts)
        
    async def generate_signals(self, phase: int, 
                             accounts: Dict[str, AccountState]) -> List[TradeSignal]:
        """根据阶段生成交易信号"""
        self.current_phase = phase
        
        account_groups = self._create_account_groups(accounts)
        
        if phase == 1:
            logger.debug("执行波浪推进策略")
            return await self.wave_strategy.execute(account_groups, accounts)
            
        elif phase == 2:
            logger.debug("执行改进对冲策略")
            return await self.hedge_strategy.execute(account_groups, accounts)
            
        else:  # phase == 3
            logger.debug("执行金字塔聚合策略")
            return await self.pyramid_strategy.execute(account_groups, accounts)
    
    def _create_account_groups(self, accounts: Dict[str, AccountState]) -> Dict:
        """创建账户分组"""
        # 按等级分组
        level_groups = {}
        for level in AccountLevel:
            level_accounts = [
                acc_id for acc_id, acc in accounts.items()
                if acc.current_level == level
            ]
            if level_accounts:
                level_groups[level] = level_accounts
        
        groups = {
            'aggressive': [],    # 激进组 - 快速升级
            'balanced': [],      # 平衡组 - 稳定发展
            'conservative': [],  # 保守组 - 保护成果
            'rescue': []         # 救援组 - 危险账户
        }
        
        for level, account_ids in level_groups.items():
            if level <= AccountLevel.LEVEL_MINUS_2:
                groups['rescue'].extend(account_ids)
            elif level <= AccountLevel.LEVEL_0:
                groups['aggressive'].extend(account_ids)
            elif level <= AccountLevel.LEVEL_2:
                groups['balanced'].extend(account_ids)
            else:  # LEVEL_3 and above
                groups['conservative'].extend(account_ids)
        
        return groups
    
    def get_strategy_allocation(self, account: AccountState) -> Dict[str, float]:
        """获取账户的策略配置"""
        level = account.current_level
        
        if self.current_phase == 1:  # 波浪推进阶段
            if level <= 0:
                return {'trend': 0.6, 'breakout': 0.3, 'scalping': 0.1}
            else:
                return {'trend': 0.4, 'breakout': 0.4, 'scalping': 0.2}
                
        elif self.current_phase == 2:  # 对冲阶段
            if level <= 0:
                return {'arbitrage': 0.7, 'hedge': 0.2, 'directional': 0.1}
            else:
                return {'arbitrage': 0.5, 'hedge': 0.4, 'directional': 0.1}
                
        else:  # 金字塔阶段
            if level >= 2:
                return {'protection': 0.8, 'growth': 0.2}
            else:
                return {'aggressive': 0.6, 'support': 0.4}