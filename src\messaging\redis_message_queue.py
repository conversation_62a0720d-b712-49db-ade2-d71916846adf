#!/usr/bin/env python3
"""
Redis Streams消息队列实现
使用Redis Streams作为消息队列后端，提供持久化和高性能的消息传递
"""

import asyncio
import json
import time
from typing import Dict, Any, Optional, List, Callable, Union
import redis.asyncio as redis

from .message_queue_interface import MessageQueueInterface, QueueConfig, QueueStatus
from .message_types import MessageEnvelope, MessagePriority
from ..utils.logger import get_logger

logger = get_logger(__name__)


class RedisMessageQueue(MessageQueueInterface):
    """
    Redis Streams消息队列实现
    
    使用Redis Streams提供高性能、持久化的消息队列功能
    支持优先级、消费者组、批量操作等特性
    """
    
    def __init__(self, config: QueueConfig):
        super().__init__(config)
        self.redis_client: Optional[redis.Redis] = None
        self._connected = False
        self._consumer_tasks: Dict[str, asyncio.Task] = {}
        self._stop_consuming = False
        
        # 从配置中提取Redis连接参数
        self.redis_config = config.connection_params.get('redis', {})
        self.host = self.redis_config.get('host', 'localhost')  
        self.port = self.redis_config.get('port', 6379)
        self.db = self.redis_config.get('db', 0)
        self.password = self.redis_config.get('password')
        self.consumer_group = self.redis_config.get('consumer_group', 'mt5-workers')
        self.consumer_name = self.redis_config.get('consumer_name', f'worker-{int(time.time())}')
        
        # 优先级流名称映射
        self._priority_streams = {
            MessagePriority.SYSTEM_CRITICAL: "mt5:streams:critical",
            MessagePriority.RISK_COMMAND: "mt5:streams:risk",
            MessagePriority.SIGNAL_EXECUTION: "mt5:streams:signals",
            MessagePriority.REALTIME_QUERY: "mt5:streams:queries", 
            MessagePriority.BACKGROUND_TASK: "mt5:streams:background"
        }
    
    async def connect(self) -> bool:
        """连接到Redis"""
        try:
            logger.info(f"正在连接Redis: {self.host}:{self.port}")
            
            # 创建Redis客户端
            self.redis_client = redis.Redis(
                host=self.host,
                port=self.port,
                db=self.db,
                password=self.password,
                decode_responses=True,
                socket_keepalive=True,
                socket_keepalive_options={},
                retry_on_timeout=True,
                max_connections=20
            )
            
            # 测试连接
            await self.redis_client.ping()
            
            # 初始化消费者组
            await self._initialize_consumer_groups()
            
            self._connected = True
            self.status = QueueStatus.CONNECTED
            logger.info("Redis MessageQueue连接成功")
            return True
            
        except Exception as e:
            logger.error(f"Redis连接失败: {e}")
            self.status = QueueStatus.FAILED
            return False
    
    async def disconnect(self) -> None:
        """断开Redis连接"""
        try:
            self._stop_consuming = True
            
            # 停止所有消费者任务
            for task in self._consumer_tasks.values():
                task.cancel()
            
            # 等待任务完成
            if self._consumer_tasks:
                await asyncio.gather(*self._consumer_tasks.values(), return_exceptions=True)
            
            self._consumer_tasks.clear()
            
            # 关闭Redis连接
            if self.redis_client:
                await self.redis_client.aclose()
            
            self._connected = False
            self.status = QueueStatus.DISCONNECTED
            logger.info("Redis MessageQueue已断开连接")
            
        except Exception as e:
            logger.error(f"断开Redis连接失败: {e}")
    
    async def publish(
        self, 
        subject: str, 
        message: MessageEnvelope,
        priority: MessagePriority = MessagePriority.REALTIME_QUERY
    ) -> bool:
        """发布消息到Redis Streams"""
        if not self._connected or not self.redis_client:
            logger.error("Redis未连接")
            return False
        
        try:
            start_time = time.perf_counter()
            
            # 选择对应优先级的流
            stream_name = self._get_priority_stream(subject, priority)
            
            # 准备消息数据
            message_data = {
                "id": message.id,
                "subject": subject,
                "payload": json.dumps(message.payload, default=str),
                "headers": json.dumps(message.headers),
                "timestamp": str(message.timestamp),
                "priority": priority.name,
                "ttl": str(message.ttl) if message.ttl else ""
            }
            
            # 发布到Redis Stream
            message_id = await self.redis_client.xadd(stream_name, message_data)
            
            # 如果设置了TTL，添加过期时间
            if message.ttl:
                await self.redis_client.expire(f"{stream_name}:msg:{message_id}", message.ttl)
            
            # 更新指标
            latency_ms = (time.perf_counter() - start_time) * 1000
            self._update_metrics("publish", True, latency_ms)
            
            logger.debug(f"Redis消息发布成功: {stream_name} (ID: {message_id})")
            return True
            
        except Exception as e:
            logger.error(f"Redis发布消息失败: {e}")
            self._update_metrics("publish", False)
            return False
    
    async def publish_batch(
        self, 
        messages: List[tuple[str, MessageEnvelope, MessagePriority]]
    ) -> int:
        """批量发布消息到Redis"""
        if not self._connected or not self.redis_client:
            logger.error("Redis未连接")
            return 0
        
        success_count = 0
        
        try:
            # 使用Redis Pipeline进行批量操作
            pipe = self.redis_client.pipeline()
            
            for subject, message, priority in messages:
                stream_name = self._get_priority_stream(subject, priority)
                
                message_data = {
                    "id": message.id,
                    "subject": subject,
                    "payload": json.dumps(message.payload, default=str),
                    "headers": json.dumps(message.headers),
                    "timestamp": str(message.timestamp),
                    "priority": priority.name,
                    "ttl": str(message.ttl) if message.ttl else ""
                }
                
                pipe.xadd(stream_name, message_data)
            
            # 执行批量操作
            results = await pipe.execute()
            
            # 统计成功数量
            for result in results:
                if result:  # Redis返回消息ID表示成功
                    success_count += 1
            
            logger.debug(f"Redis批量发布完成: {success_count}/{len(messages)}")
            
        except Exception as e:
            logger.error(f"Redis批量发布失败: {e}")
        
        return success_count
    
    async def subscribe(
        self, 
        subject: str, 
        callback: Callable[[MessageEnvelope], None],
        queue_group: Optional[str] = None
    ) -> bool:
        """订阅Redis Streams消息"""
        if not self._connected or not self.redis_client:
            logger.error("Redis未连接")
            return False
        
        try:
            # 为每个优先级创建消费任务
            for priority in MessagePriority:
                stream_name = self._get_priority_stream(subject, priority)
                consumer_group = queue_group or self.consumer_group
                
                # 创建消费者任务
                task_key = f"{subject}_{priority.name}_{consumer_group}"
                if task_key not in self._consumer_tasks:
                    task = asyncio.create_task(
                        self._consume_stream(stream_name, consumer_group, callback, priority)
                    )
                    self._consumer_tasks[task_key] = task
                    logger.debug(f"Redis订阅成功: {stream_name}")
            
            return True
            
        except Exception as e:
            logger.error(f"Redis订阅失败: {e}")
            return False
    
    async def unsubscribe(self, subject: str) -> bool:
        """取消Redis订阅"""
        try:
            # 停止相关的消费者任务
            tasks_to_cancel = []
            for task_key in list(self._consumer_tasks.keys()):
                if task_key.startswith(f"{subject}_"):
                    task = self._consumer_tasks.pop(task_key)
                    tasks_to_cancel.append(task)
            
            # 取消任务
            for task in tasks_to_cancel:
                task.cancel()
            
            if tasks_to_cancel:
                await asyncio.gather(*tasks_to_cancel, return_exceptions=True)
            
            logger.info(f"Redis取消订阅成功: {subject}")
            return True
            
        except Exception as e:
            logger.error(f"Redis取消订阅失败: {e}")
            return False
    
    async def request(
        self, 
        subject: str, 
        message: MessageEnvelope,
        timeout_ms: int = 5000
    ) -> Optional[MessageEnvelope]:
        """Redis请求-响应模式"""
        if not self._connected or not self.redis_client:
            logger.error("Redis未连接")
            return None
        
        try:
            # 创建响应流
            response_subject = f"{subject}.response.{int(time.time() * 1000)}"
            response_stream = self._get_priority_stream(response_subject, MessagePriority.REALTIME_QUERY)
            
            response_received = None
            response_event = asyncio.Event()
            
            # 临时订阅响应
            async def response_callback(response_msg):
                nonlocal response_received
                response_received = response_msg
                response_event.set()
            
            await self.subscribe(response_subject, response_callback)
            
            # 发送请求（包含响应主题）
            message.headers['reply_to'] = response_subject
            await self.publish(subject, message)
            
            # 等待响应
            try:
                await asyncio.wait_for(response_event.wait(), timeout=timeout_ms / 1000)
                return response_received
            except asyncio.TimeoutError:
                logger.warning(f"Redis请求超时: {subject}")
                return None
            finally:
                # 清理临时订阅和流
                await self.unsubscribe(response_subject)
                await self.redis_client.delete(response_stream)
                
        except Exception as e:
            logger.error(f"Redis请求失败: {e}")
            return None
    
    async def get_queue_depth(self, subject: str) -> int:
        """获取队列深度"""
        if not self._connected or not self.redis_client:
            return 0
        
        try:
            total_messages = 0
            
            for priority in MessagePriority:
                stream_name = self._get_priority_stream(subject, priority)
                stream_info = await self.redis_client.xinfo_stream(stream_name)
                total_messages += stream_info.get('length', 0)
            
            self.metrics.queue_depth = total_messages
            return total_messages
            
        except Exception as e:
            logger.error(f"获取Redis队列深度失败: {e}")
            return 0
    
    async def purge_queue(self, subject: str) -> bool:
        """清空队列"""
        if not self._connected or not self.redis_client:
            return False
        
        try:
            success = True
            
            for priority in MessagePriority:
                stream_name = self._get_priority_stream(subject, priority)
                try:
                    await self.redis_client.delete(stream_name)
                except Exception as e:
                    logger.warning(f"清空流失败 {stream_name}: {e}")
                    success = False
            
            if success:
                logger.info(f"Redis队列清空成功: {subject}")
            else:
                logger.warning(f"Redis队列清空部分失败: {subject}")
            
            return success
            
        except Exception as e:
            logger.error(f"清空Redis队列失败: {e}")
            return False
    
    async def _initialize_consumer_groups(self):
        """初始化消费者组"""
        try:
            for stream_name in self._priority_streams.values():
                try:
                    # 创建消费者组
                    await self.redis_client.xgroup_create(
                        stream_name, 
                        self.consumer_group, 
                        id="0", 
                        mkstream=True
                    )
                    logger.debug(f"创建消费者组: {self.consumer_group} @ {stream_name}")
                except redis.ResponseError as e:
                    if "BUSYGROUP" in str(e):
                        # 消费者组已存在
                        pass
                    else:
                        raise
                        
        except Exception as e:
            logger.error(f"初始化消费者组失败: {e}")
            raise
    
    async def _consume_stream(
        self, 
        stream_name: str, 
        consumer_group: str, 
        callback: Callable[[MessageEnvelope], None],
        priority: MessagePriority
    ):
        """消费Redis Stream"""
        while not self._stop_consuming:
            try:
                # 从消费者组读取消息
                messages = await self.redis_client.xreadgroup(
                    consumer_group,
                    self.consumer_name,
                    {stream_name: ">"},
                    count=self.config.batch_size,
                    block=1000  # 1秒超时
                )
                
                for stream, msgs in messages:
                    for msg_id, fields in msgs:
                        try:
                            # 解析消息
                            envelope = MessageEnvelope(
                                id=fields.get('id', ''),
                                subject=fields.get('subject', ''),
                                payload=json.loads(fields.get('payload', '{}')),
                                headers=json.loads(fields.get('headers', '{}')),
                                timestamp=float(fields.get('timestamp', time.time())),
                                ttl=int(fields.get('ttl')) if fields.get('ttl') else None
                            )
                            
                            # 调用回调
                            if asyncio.iscoroutinefunction(callback):
                                await callback(envelope)
                            else:
                                callback(envelope)
                            
                            # 确认消息
                            await self.redis_client.xack(stream_name, consumer_group, msg_id)
                            self._update_metrics("receive", True)
                            
                        except Exception as e:
                            logger.error(f"处理Redis消息失败: {e}")
                            self._update_metrics("receive", False)
                            
            except asyncio.CancelledError:
                break
            except Exception as e:
                if not self._stop_consuming:
                    logger.error(f"Redis消费失败: {e}")
                    await asyncio.sleep(1)  # 错误时短暂等待
    
    def _get_priority_stream(self, base_subject: str, priority: MessagePriority) -> str:
        """根据优先级获取流名称"""
        if priority in self._priority_streams:
            return f"{self._priority_streams[priority]}:{base_subject}"
        else:
            return f"mt5:streams:{priority.name.lower()}:{base_subject}"
    
    async def health_check(self) -> bool:
        """Redis健康检查"""
        if not self._connected or not self.redis_client:
            return False
        
        try:
            # Ping Redis服务器
            pong = await self.redis_client.ping()
            return pong == True
        except Exception:
            return False