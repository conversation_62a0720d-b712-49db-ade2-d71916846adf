# MT5 SystemD 服务配置

<div align="center">

[English](README_EN.md) | 简体中文

</div>

此目录包含MT5跟单系统的systemd服务配置文件。

## 服务列表

- `mt5-api.service` - 主API服务
- `mt5-master.service` - 主账户监控服务
- `mt5-slave.service` - 从账户执行服务

## 安装步骤

1. 创建系统用户：
```bash
sudo useradd -r -s /bin/false mt5
sudo mkdir -p /opt/mt5-copier
sudo chown mt5:mt5 /opt/mt5-copier
```

2. 复制服务文件：
```bash
sudo cp systemd/*.service /etc/systemd/system/
sudo systemctl daemon-reload
```

3. 启用服务：
```bash
sudo systemctl enable mt5-api.service
sudo systemctl enable mt5-master.service
sudo systemctl enable mt5-slave.service
```

4. 启动服务：
```bash
sudo systemctl start mt5-api.service
sudo systemctl start mt5-master.service
sudo systemctl start mt5-slave.service
```

## 管理命令

### 查看服务状态
```bash
sudo systemctl status mt5-api.service
sudo systemctl status mt5-master.service
sudo systemctl status mt5-slave.service
```

### 查看日志
```bash
sudo journalctl -u mt5-api.service -f
sudo journalctl -u mt5-master.service -f
sudo journalctl -u mt5-slave.service -f
```

### 重启服务
```bash
sudo systemctl restart mt5-api.service
sudo systemctl restart mt5-master.service
sudo systemctl restart mt5-slave.service
```

### 停止服务
```bash
sudo systemctl stop mt5-api.service
sudo systemctl stop mt5-master.service
sudo systemctl stop mt5-slave.service
```

## 配置说明

### 环境变量
- `MT5_CONFIG_PATH` - 配置文件路径
- `MT5_LOG_LEVEL` - 日志级别
- `MT5_ACCOUNT_ID` - 账户ID

### 资源限制
- 内存限制：API服务2GB，其他服务1GB
- CPU限制：API服务200%，其他服务100%
- 文件描述符限制：65536
- 进程数限制：4096

### 安全设置
- 使用非特权用户运行
- 保护系统文件
- 限制文件访问权限
- 禁用新权限获取

## 日志管理

服务日志通过systemd journal管理：

```bash
# 查看最新100行日志
sudo journalctl -u mt5-api.service -n 100

# 查看特定时间段的日志
sudo journalctl -u mt5-api.service --since "2024-01-01 00:00:00" --until "2024-01-01 23:59:59"

# 持续监控日志
sudo journalctl -u mt5-api.service -f
```

## 故障排除

### 常见问题

1. **服务启动失败**
   - 检查配置文件路径
   - 确认Python虚拟环境正确
   - 检查文件权限

2. **Redis/NATS连接失败**
   - 确认Redis和NATS服务正在运行
   - 检查网络连接
   - 验证配置文件中的连接信息

3. **权限错误**
   - 确认mt5用户对相关目录有读写权限
   - 检查SELinux设置（如果启用）

### 性能调优

1. **内存使用**
   - 根据实际需求调整MemoryMax设置
   - 监控内存使用情况：`sudo systemctl status mt5-api.service`

2. **CPU使用**
   - 调整CPUQuota设置
   - 监控CPU使用：`top -p $(pgrep -f mt5-api)`

3. **文件描述符**
   - 根据连接数调整LimitNOFILE
   - 监控文件描述符使用：`lsof -p $(pgrep -f mt5-api)`

## 备份和恢复

### 备份配置
```bash
sudo cp /etc/systemd/system/mt5-*.service /backup/location/
```

### 恢复配置
```bash
sudo cp /backup/location/mt5-*.service /etc/systemd/system/
sudo systemctl daemon-reload
sudo systemctl restart mt5-api.service mt5-master.service mt5-slave.service
```