services:
  alertmanager:
    container_name: mt5-alertmanager
    image: prom/alertmanager:latest
    ports:
    - 9093:9093
    volumes:
    - ./monitoring/alertmanager.yml:/etc/alertmanager/alertmanager.yml
  grafana:
    container_name: mt5-grafana
    environment:
    - GF_SECURITY_ADMIN_PASSWORD=admin123
    image: grafana/grafana:latest
    ports:
    - 3000:3000
    volumes:
    - grafana-storage:/var/lib/grafana
    - ./monitoring/grafana_dashboard.json:/var/lib/grafana/dashboards/mt5_dashboard.json
  prometheus:
    command:
    - --config.file=/etc/prometheus/prometheus.yml
    - --storage.tsdb.path=/prometheus
    - --web.console.libraries=/etc/prometheus/console_libraries
    - --web.console.templates=/etc/prometheus/consoles
    - --storage.tsdb.retention.time=200h
    - --web.enable-lifecycle
    container_name: mt5-prometheus
    image: prom/prometheus:latest
    ports:
    - 9090:9090
    volumes:
    - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml
    - ./monitoring/mt5_alerts.yml:/etc/prometheus/mt5_alerts.yml
version: '3.8'
volumes:
  grafana-storage: {}
