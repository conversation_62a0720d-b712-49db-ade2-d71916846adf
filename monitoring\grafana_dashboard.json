{"dashboard": {"id": null, "title": "MT5 Copy Trading System", "tags": ["mt5", "trading", "copy-trading"], "timezone": "browser", "panels": [{"id": 1, "title": "Signal Processing Rate", "type": "graph", "targets": [{"expr": "rate(mt5_signals_processed_total[5m])", "legendFormat": "{{account_id}} - {{status}}"}], "yAxes": [{"label": "Signals/sec"}], "gridPos": {"h": 8, "w": 12, "x": 0, "y": 0}}, {"id": 2, "title": "Copy Execution Success Rate", "type": "singlestat", "targets": [{"expr": "avg(mt5_copy_execution_success_rate)", "legendFormat": "Success Rate"}], "valueName": "current", "format": "percentunit", "gridPos": {"h": 8, "w": 12, "x": 12, "y": 0}}, {"id": 3, "title": "Signal Processing Latency", "type": "graph", "targets": [{"expr": "histogram_quantile(0.95, mt5_signal_processing_duration_bucket)", "legendFormat": "95th percentile"}, {"expr": "histogram_quantile(0.50, mt5_signal_processing_duration_bucket)", "legendFormat": "50th percentile"}], "yAxes": [{"label": "Latency (ms)"}], "gridPos": {"h": 8, "w": 12, "x": 0, "y": 8}}, {"id": 4, "title": "Trade Matching Accuracy", "type": "graph", "targets": [{"expr": "mt5_trade_matching_accuracy_ratio", "legendFormat": "{{algorithm}}"}], "yAxes": [{"label": "Accuracy", "min": 0, "max": 1}], "gridPos": {"h": 8, "w": 12, "x": 12, "y": 8}}, {"id": 5, "title": "Error Rate", "type": "graph", "targets": [{"expr": "rate(mt5_errors_total[5m])", "legendFormat": "{{component}} - {{error_type}}"}], "yAxes": [{"label": "Errors/sec"}], "gridPos": {"h": 8, "w": 24, "x": 0, "y": 16}}, {"id": 6, "title": "NATS Connection Status", "type": "singlestat", "targets": [{"expr": "mt5_nats_connection_status", "legendFormat": "Connection Status"}], "valueName": "current", "valueMaps": [{"value": "1", "text": "Connected"}, {"value": "0", "text": "Disconnected"}], "gridPos": {"h": 4, "w": 6, "x": 0, "y": 24}}, {"id": 7, "title": "Signal Queue Size", "type": "graph", "targets": [{"expr": "mt5_signal_queue_size", "legendFormat": "{{instance_id}}"}], "yAxes": [{"label": "<PERSON><PERSON> Size"}], "gridPos": {"h": 8, "w": 18, "x": 6, "y": 24}}], "time": {"from": "now-1h", "to": "now"}, "refresh": "5s"}}