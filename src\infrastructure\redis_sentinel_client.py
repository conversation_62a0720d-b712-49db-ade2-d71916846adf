"""
Redis Sentinel客户端封装
提供高可用Redis连接
"""
import asyncio
import time
from typing import Optional, Dict, Any, List, Tuple
from dataclasses import dataclass
import redis.asyncio as redis
from redis.asyncio.sentinel import Sentinel
try:
    from redis.asyncio.retry import Retry
    from redis.asyncio.backoff import ExponentialBackoff
    REDIS_RETRY_AVAILABLE = True
except ImportError:
    # 兼容旧版本Redis
    REDIS_RETRY_AVAILABLE = False

from ..utils.logger import get_logger
from ..utils.metrics import get_metrics_collector


logger = get_logger(__name__)
metrics = get_metrics_collector()


@dataclass
class RedisSentinelConfig:
    """Redis Sentinel配置"""
    sentinels: List[Tuple[str, int]]
    service_name: str = "mt5master"
    password: Optional[str] = None
    socket_timeout: float = 5.0
    socket_connect_timeout: float = 5.0
    socket_keepalive: bool = True
    socket_keepalive_options: Dict[str, int] = None
    connection_pool_kwargs: Dict[str, Any] = None

    sentinel_kwargs: Dict[str, Any] = None

    retry_on_error: List[type] = None
    retry_on_timeout: bool = True
    retry_times: int = 3
    
    def __post_init__(self):
        if self.socket_keepalive_options is None:
            self.socket_keepalive_options = {}
        if self.connection_pool_kwargs is None:
            self.connection_pool_kwargs = {}
        if self.sentinel_kwargs is None:
            self.sentinel_kwargs = {}
        if self.retry_on_error is None:
            self.retry_on_error = [
                redis.ConnectionError,
                redis.TimeoutError,
                redis.BusyLoadingError
            ]


class RedisSentinelClient:
    """Redis Sentinel客户端"""
    
    def __init__(self, config: RedisSentinelConfig):
        self.config = config
        self.sentinel: Optional[Sentinel] = None
        self.master_client: Optional[redis.Redis] = None
        self.slave_client: Optional[redis.Redis] = None
        self._connected = False
        self._last_error = None
        self._connection_stats = {
            'master_connections': 0,
            'slave_connections': 0,
            'connection_errors': 0,
            'failovers': 0,
            'last_failover': None
        }
        
    async def connect(self) -> bool:
        """连接到Redis Sentinel集群"""
        try:
            self.sentinel = Sentinel(
                self.config.sentinels,
                socket_timeout=self.config.socket_timeout,
                socket_connect_timeout=self.config.socket_connect_timeout,
                socket_keepalive=self.config.socket_keepalive,
                socket_keepalive_options=self.config.socket_keepalive_options,
                **self.config.sentinel_kwargs
            )
            
            connection_kwargs = self.config.connection_pool_kwargs.copy()
            if REDIS_RETRY_AVAILABLE:
                retry = Retry(
                    ExponentialBackoff(),
                    self.config.retry_times
                )
                connection_kwargs.update({
                    'retry': retry,
                    'retry_on_error': self.config.retry_on_error,
                    'retry_on_timeout': self.config.retry_on_timeout
                })
            
            self.master_client = self.sentinel.master_for(
                self.config.service_name,
                password=self.config.password,
                **connection_kwargs
            )
            
            self.slave_client = self.sentinel.slave_for(
                self.config.service_name,
                password=self.config.password,
                **connection_kwargs
            )
            
            await self.master_client.ping()
            await self.slave_client.ping()
            
            self._connected = True
            self._connection_stats['master_connections'] += 1
            self._connection_stats['slave_connections'] += 1
            
            logger.info(f"Redis Sentinel连接成功: {self.config.service_name}")
            metrics.increment("redis_sentinel_connections_total")
            metrics.set_gauge("redis_sentinel_connection_status", 1)
            
            return True
            
        except Exception as e:
            self._last_error = str(e)
            self._connection_stats['connection_errors'] += 1
            logger.error(f"Redis Sentinel连接失败: {e}")
            metrics.increment("redis_sentinel_connection_errors_total")
            metrics.set_gauge("redis_sentinel_connection_status", 0)
            return False
    
    async def disconnect(self):
        """断开连接"""
        try:
            if self.master_client:
                await self.master_client.aclose()
            if self.slave_client:
                await self.slave_client.aclose()
            if self.sentinel:
                await self.sentinel.aclose()
            
            self._connected = False
            logger.info("Redis Sentinel连接已断开")
            metrics.set_gauge("redis_sentinel_connection_status", 0)
            
        except Exception as e:
            logger.error(f"断开Redis Sentinel连接失败: {e}")
    
    async def get_master_client(self) -> Optional[redis.Redis]:
        """获取主服务器客户端 """
        if not self._connected or not self.master_client:
            if not await self.connect():
                return None
        
        try:
            await self.master_client.ping()
            return self.master_client
        except Exception as e:
            logger.warning(f"主服务器连接异常，尝试重连: {e}")
            await self._handle_failover()
            return self.master_client if self._connected else None
    
    async def get_slave_client(self) -> Optional[redis.Redis]:
        """获取从服务器客户端（用于读操作）"""
        if not self._connected or not self.slave_client:
            if not await self.connect():
                return None
        
        try:
            await self.slave_client.ping()
            return self.slave_client
        except Exception as e:
            logger.warning(f"从服务器连接异常，降级到主服务器: {e}")
            return await self.get_master_client()
    
    async def _handle_failover(self):
        """处理故障转移"""
        try:
            logger.info("开始Redis故障转移...")
            
            connection_kwargs = self.config.connection_pool_kwargs.copy()
            if REDIS_RETRY_AVAILABLE:
                retry = Retry(
                    ExponentialBackoff(),
                    self.config.retry_times
                )
                connection_kwargs.update({
                    'retry': retry,
                    'retry_on_error': self.config.retry_on_error,
                    'retry_on_timeout': self.config.retry_on_timeout
                })
            
            self.master_client = self.sentinel.master_for(
                self.config.service_name,
                password=self.config.password,
                **connection_kwargs
            )
            
            self.slave_client = self.sentinel.slave_for(
                self.config.service_name,
                password=self.config.password,
                **connection_kwargs
            )
            
            await self.master_client.ping()
            await self.slave_client.ping()
            
            self._connection_stats['failovers'] += 1
            self._connection_stats['last_failover'] = time.time()
            
            logger.info("Redis故障转移完成")
            metrics.increment("redis_sentinel_failovers_total")
            
        except Exception as e:
            logger.error(f"Redis故障转移失败: {e}")
            self._connected = False
            raise
    
    async def set(self, key: str, value: Any, ttl: Optional[int] = None) -> bool:
        """设置键值（写操作）"""
        client = await self.get_master_client()
        if not client:
            return False
        
        try:
            start_time = time.perf_counter()
            
            if ttl:
                await client.setex(key, ttl, value)
            else:
                await client.set(key, value)
            
            operation_time = (time.perf_counter() - start_time) * 1000
            metrics.observe("redis_operation_duration_ms", operation_time, {"operation": "set"})
            metrics.increment("redis_operations_total", {"operation": "set", "status": "success"})
            
            return True
            
        except Exception as e:
            logger.error(f"Redis SET操作失败: {e}")
            metrics.increment("redis_operations_total", {"operation": "set", "status": "error"})
            return False
    
    async def get(self, key: str) -> Optional[Any]:
        """获取键值（读操作，优先从从服务器读取）"""
        client = await self.get_slave_client()
        if not client:
            return None
        
        try:
            start_time = time.perf_counter()
            
            result = await client.get(key)
            
            operation_time = (time.perf_counter() - start_time) * 1000
            metrics.observe("redis_operation_duration_ms", operation_time, {"operation": "get"})
            metrics.increment("redis_operations_total", {"operation": "get", "status": "success"})
            
            return result
            
        except Exception as e:
            logger.error(f"Redis GET操作失败: {e}")
            metrics.increment("redis_operations_total", {"operation": "get", "status": "error"})
            return None
    
    async def delete(self, key: str) -> bool:
        """删除键"""
        client = await self.get_master_client()
        if not client:
            return False
        
        try:
            start_time = time.perf_counter()
            
            result = await client.delete(key)
            
            operation_time = (time.perf_counter() - start_time) * 1000
            metrics.observe("redis_operation_duration_ms", operation_time, {"operation": "delete"})
            metrics.increment("redis_operations_total", {"operation": "delete", "status": "success"})
            
            return bool(result)
            
        except Exception as e:
            logger.error(f"Redis DELETE操作失败: {e}")
            metrics.increment("redis_operations_total", {"operation": "delete", "status": "error"})
            return False
    
    async def hset(self, name: str, key: str, value: Any) -> bool:
        """设置哈希字段（写操作）"""
        client = await self.get_master_client()
        if not client:
            return False
        
        try:
            start_time = time.perf_counter()
            
            await client.hset(name, key, value)
            
            operation_time = (time.perf_counter() - start_time) * 1000
            metrics.observe("redis_operation_duration_ms", operation_time, {"operation": "hset"})
            metrics.increment("redis_operations_total", {"operation": "hset", "status": "success"})
            
            return True
            
        except Exception as e:
            logger.error(f"Redis HSET操作失败: {e}")
            metrics.increment("redis_operations_total", {"operation": "hset", "status": "error"})
            return False
    
    async def hget(self, name: str, key: str) -> Optional[Any]:
        """获取哈希字段"""
        client = await self.get_slave_client()
        if not client:
            return None
        
        try:
            start_time = time.perf_counter()
            
            result = await client.hget(name, key)
            
            operation_time = (time.perf_counter() - start_time) * 1000
            metrics.observe("redis_operation_duration_ms", operation_time, {"operation": "hget"})
            metrics.increment("redis_operations_total", {"operation": "hget", "status": "success"})
            
            return result
            
        except Exception as e:
            logger.error(f"Redis HGET操作失败: {e}")
            metrics.increment("redis_operations_total", {"operation": "hget", "status": "error"})
            return None
    
    async def hgetall(self, name: str) -> Dict[str, Any]:
        """获取所有哈希字段"""
        client = await self.get_slave_client()
        if not client:
            return {}
        
        try:
            start_time = time.perf_counter()
            
            result = await client.hgetall(name)
            
            operation_time = (time.perf_counter() - start_time) * 1000
            metrics.observe("redis_operation_duration_ms", operation_time, {"operation": "hgetall"})
            metrics.increment("redis_operations_total", {"operation": "hgetall", "status": "success"})
            
            return result or {}
            
        except Exception as e:
            logger.error(f"Redis HGETALL操作失败: {e}")
            metrics.increment("redis_operations_total", {"operation": "hgetall", "status": "error"})
            return {}
    
    async def get_sentinel_info(self) -> Dict[str, Any]:
        """获取Sentinel信息"""
        if not self.sentinel:
            return {}
        
        try:
            masters = await self.sentinel.sentinel_masters()
            slaves = await self.sentinel.sentinel_slaves(self.config.service_name)
            sentinels = await self.sentinel.sentinel_sentinels(self.config.service_name)
            
            return {
                'masters': masters,
                'slaves': slaves,
                'sentinels': sentinels,
                'service_name': self.config.service_name
            }
        except Exception as e:
            logger.error(f"获取Sentinel信息失败: {e}")
            return {}
    
    def get_connection_stats(self) -> Dict[str, Any]:
        """获取连接统计"""
        return {
            'connected': self._connected,
            'last_error': self._last_error,
            'stats': self._connection_stats.copy()
        }
    
    async def health_check(self) -> Dict[str, Any]:
        """健康检查"""
        health = {
            'healthy': False,
            'master_status': 'unknown',
            'slave_status': 'unknown',
            'sentinel_status': 'unknown',
            'details': {}
        }
        
        try:
            master_client = await self.get_master_client()
            if master_client:
                await master_client.ping()
                health['master_status'] = 'healthy'
            
            slave_client = await self.get_slave_client()
            if slave_client:
                await slave_client.ping()
                health['slave_status'] = 'healthy'
            
            if self.sentinel:
                sentinel_info = await self.get_sentinel_info()
                if sentinel_info:
                    health['sentinel_status'] = 'healthy'
                    health['details'] = sentinel_info
            
            health['healthy'] = (
                health['master_status'] == 'healthy' and
                health['sentinel_status'] == 'healthy'
            )
            
        except Exception as e:
            health['last_error'] = str(e)
            logger.error(f"Redis Sentinel健康检查失败: {e}")
        
        return health


# 别名，用于向后兼容
RedisClient = RedisSentinelClient