#!/usr/bin/env python3
"""
多MT5终端动态角色切换示例
演示如何在单个主机上运行多个MT5终端，并动态切换主从角色
"""
import asyncio
import sys
import time
from pathlib import Path
from typing import List

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent.parent))

from src.multi_terminal.terminal_manager import MultiTerminalManager
from src.multi_terminal.role_manager import DynamicRoleManager, RoleConfiguration
from src.config.settings import ConfigManager, AccountSettings
from src.config.redis_client import RedisClient
from src.messaging.nats_client import NATSClient, NATSConfig
from src.utils.logger import get_logger, setup_logging


logger = get_logger(__name__)


class MultiTerminalDemo:
    """多终端演示"""
    
    def __init__(self, config_path: str):
        self.config_path = config_path
        self.config_manager = ConfigManager(config_path)
        self.terminal_manager: MultiTerminalManager = None
        self.role_manager: DynamicRoleManager = None
        self.redis_client: RedisClient = None
        self.nats_client: NATSClient = None
    
    async def initialize(self):
        """初始化系统"""
        logger.info("初始化多终端系统...")
        
        try:
            # 加载配置
            await self.config_manager.load_config()
            
            # 初始化Redis客户端
            redis_settings = self.config_manager.get_redis_settings()
            self.redis_client = RedisClient(
                url=redis_settings.url,
                db=redis_settings.db,
                password=redis_settings.password
            )
            
            if not await self.redis_client.connect():
                raise Exception("Redis连接失败")
            
            # 初始化NATS客户端
            nats_settings = self.config_manager.get_nats_settings()
            nats_config = NATSConfig(
                servers=nats_settings.servers,
                name="multi-terminal-demo"
            )
            
            self.nats_client = NATSClient(nats_config)
            if not await self.nats_client.connect():
                raise Exception("NATS连接失败")
            
            # 初始化终端管理器
            self.terminal_manager = MultiTerminalManager("demo_host")
            await self.terminal_manager.start()
            
            # 初始化角色管理器
            self.role_manager = DynamicRoleManager(
                self.terminal_manager, self.nats_client, self.redis_client
            )
            await self.role_manager.start()
            
            logger.info("多终端系统初始化完成")
            
        except Exception as e:
            logger.error(f"初始化失败: {e}")
            raise
    
    async def cleanup(self):
        """清理资源"""
        if self.role_manager:
            await self.role_manager.stop()
        if self.terminal_manager:
            await self.terminal_manager.stop()
        if self.nats_client:
            await self.nats_client.disconnect()
        if self.redis_client:
            await self.redis_client.disconnect()
    
    async def register_demo_terminals(self):
        """注册演示终端"""
        logger.info("注册演示终端...")
        
        # 获取账户配置
        master_accounts = self.config_manager.get_master_accounts()
        slave_accounts = self.config_manager.get_slave_accounts()
        
        # 注册主账户终端
        for account in master_accounts[:3]:  # 只注册前3个
            try:
                terminal_id = await self.terminal_manager.register_terminal(account)
                logger.info(f"主账户终端已注册: {terminal_id}")
            except Exception as e:
                logger.error(f"注册主账户终端失败 {account.id}: {e}")
        
        # 注册从账户终端
        for account in slave_accounts[:3]:  # 只注册前3个
            try:
                terminal_id = await self.terminal_manager.register_terminal(account)
                logger.info(f"从账户终端已注册: {terminal_id}")
            except Exception as e:
                logger.error(f"注册从账户终端失败 {account.id}: {e}")
    
    async def start_all_terminals(self):
        """启动所有终端"""
        logger.info("启动所有终端...")
        
        terminals = await self.terminal_manager.get_all_terminals_status()
        
        for terminal in terminals:
            try:
                success = await self.terminal_manager.start_terminal(terminal['terminal_id'])
                if success:
                    logger.info(f"终端启动成功: {terminal['terminal_id']}")
                else:
                    logger.warning(f"终端启动失败: {terminal['terminal_id']}")
            except Exception as e:
                logger.error(f"启动终端失败 {terminal['terminal_id']}: {e}")
        
        # 等待终端稳定
        await asyncio.sleep(10)
    
    async def create_demo_configurations(self):
        """创建演示配置"""
        logger.info("创建演示角色配置...")
        
        terminals = await self.terminal_manager.get_all_terminals_status()
        if len(terminals) < 4:
            logger.error("需要至少4个终端来创建演示配置")
            return
        
        terminal_ids = [t['terminal_id'] for t in terminals]
        
        # 配置1: 早盘配置
        config1 = RoleConfiguration(
            config_id="demo_morning",
            name="演示早盘配置",
            description="1个主账户，2个从账户",
            master_terminals=[terminal_ids[0]],
            slave_terminals=[terminal_ids[1], terminal_ids[2]],
            strategy="proportional",
            lot_multiplier=1.0
        )
        
        await self.role_manager.create_role_configuration(config1)
        logger.info("早盘配置已创建")
        
        # 配置2: 午盘配置
        config2 = RoleConfiguration(
            config_id="demo_afternoon",
            name="演示午盘配置",
            description="2个主账户，1个从账户",
            master_terminals=[terminal_ids[0], terminal_ids[1]],
            slave_terminals=[terminal_ids[2]],
            strategy="proportional",
            lot_multiplier=0.8
        )
        
        await self.role_manager.create_role_configuration(config2)
        logger.info("午盘配置已创建")
        
        # 配置3: 晚盘配置
        if len(terminal_ids) >= 4:
            config3 = RoleConfiguration(
                config_id="demo_evening",
                name="演示晚盘配置",
                description="1个主账户，3个从账户",
                master_terminals=[terminal_ids[1]],
                slave_terminals=[terminal_ids[0], terminal_ids[2], terminal_ids[3]],
                strategy="proportional",
                lot_multiplier=1.2
            )
            
            await self.role_manager.create_role_configuration(config3)
            logger.info("晚盘配置已创建")
    
    async def demonstrate_role_switching(self):
        """演示角色切换"""
        logger.info("开始演示角色切换...")
        
        configs = await self.role_manager.list_configurations()
        if not configs:
            logger.error("没有可用的角色配置")
            return
        
        # 演示切换序列
        switch_sequence = [
            ("demo_morning", "早盘配置", 30),
            ("demo_afternoon", "午盘配置", 30),
            ("demo_evening", "晚盘配置", 30) if len(configs) >= 3 else None
        ]
        
        # 过滤掉None值
        switch_sequence = [item for item in switch_sequence if item is not None]
        
        for config_id, config_name, duration in switch_sequence:
            logger.info(f"切换到 {config_name}...")
            
            # 执行切换
            success = await self.role_manager.switch_to_configuration(config_id)
            
            if success:
                logger.info(f"✅ 成功切换到 {config_name}")
                
                # 显示当前状态
                await self.show_current_status()
                
                # 等待指定时间
                logger.info(f"运行 {duration} 秒...")
                await asyncio.sleep(duration)
                
            else:
                logger.error(f"❌ 切换到 {config_name} 失败")
    
    async def show_current_status(self):
        """显示当前状态"""
        current_config = await self.role_manager.get_current_configuration()
        terminals = await self.terminal_manager.get_all_terminals_status()
        
        print("\n" + "="*60)
        print("📊 当前系统状态")
        print("="*60)
        
        if current_config:
            print(f"当前配置: {current_config.name} ({current_config.config_id})")
            print(f"描述: {current_config.description}")
            print(f"主终端: {current_config.master_terminals}")
            print(f"从终端: {current_config.slave_terminals}")
            print(f"策略: {current_config.strategy}")
            print(f"倍数: {current_config.lot_multiplier}")
        else:
            print("当前配置: 无")
        
        print(f"\n终端状态:")
        for terminal in terminals:
            status_icon = "🟢" if terminal['status'] == 'running' else "🔴"
            role_icon = {
                'master': '👑',
                'slave': '👥',
                'idle': '⚪'
            }.get(terminal['role'], '❓')
            
            print(f"  {status_icon} {role_icon} {terminal['terminal_id']} - {terminal['role']} ({terminal['status']})")
        
        print("="*60 + "\n")
    
    async def monitor_system(self, duration: int = 60):
        """监控系统运行"""
        logger.info(f"监控系统运行 {duration} 秒...")
        
        start_time = time.time()
        
        while time.time() - start_time < duration:
            try:
                # 检查终端状态
                terminals = await self.terminal_manager.get_all_terminals_status()
                running_count = len([t for t in terminals if t['status'] == 'running'])
                
                # 检查角色分布
                master_count = len([t for t in terminals if t['role'] == 'master'])
                slave_count = len([t for t in terminals if t['role'] == 'slave'])
                
                # 获取当前配置
                current_config = await self.role_manager.get_current_configuration()
                config_name = current_config.name if current_config else "无"
                
                logger.info(f"监控: 运行终端 {running_count}/{len(terminals)}, "
                          f"主账户 {master_count}, 从账户 {slave_count}, "
                          f"当前配置: {config_name}")
                
                await asyncio.sleep(10)  # 每10秒检查一次
                
            except Exception as e:
                logger.error(f"监控失败: {e}")
                await asyncio.sleep(5)
    
    async def run_demo(self):
        """运行完整演示"""
        try:
            logger.info("🚀 开始多终端动态角色切换演示")
            
            # 1. 初始化系统
            await self.initialize()
            
            # 2. 注册终端
            await self.register_demo_terminals()
            
            # 3. 启动终端
            await self.start_all_terminals()
            
            # 4. 创建角色配置
            await self.create_demo_configurations()
            
            # 5. 显示初始状态
            await self.show_current_status()
            
            # 6. 演示角色切换
            await self.demonstrate_role_switching()
            
            # 7. 监控系统
            await self.monitor_system(60)
            
            logger.info("✅ 演示完成")
            
        except Exception as e:
            logger.error(f"演示失败: {e}")
            raise
        finally:
            await self.cleanup()


async def main():
    """主函数"""
    # 设置日志
    setup_logging({"level": "INFO"})
    
    # 配置文件路径
    config_path = "config/multi_terminal_config.yaml"
    
    # 创建演示实例
    demo = MultiTerminalDemo(config_path)
    
    try:
        await demo.run_demo()
    except KeyboardInterrupt:
        logger.info("演示被用户中断")
    except Exception as e:
        logger.error(f"演示失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    print("""
🎯 MT5多终端动态角色切换演示

这个演示将展示如何：
1. 在单个主机上管理多个MT5终端
2. 动态创建和切换角色配置
3. 实时监控系统状态

请确保：
- Redis和NATS服务正在运行
- 配置文件中有足够的账户信息
- MT5终端路径配置正确

按 Ctrl+C 可以随时停止演示
""")
    
    input("按回车键开始演示...")
    asyncio.run(main())
