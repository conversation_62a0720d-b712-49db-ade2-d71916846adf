"""
增强的API数据模型，包含完整的OpenAPI文档
"""
from typing import List, Optional, Dict, Any, Union
from pydantic import BaseModel, Field, validator
from datetime import datetime
from enum import Enum
import time


class BaseResponseModel(BaseModel):
    """基础响应模型"""
    success: bool = Field(description="操作是否成功")
    message: str = Field(description="响应消息")
    timestamp: float = Field(description="时间戳", default_factory=time.time)
    
    class Config:
        schema_extra = {
            "example": {
                "success": True,
                "message": "操作成功",
                "timestamp": **********.0
            }
        }


class PaginationModel(BaseModel):
    """分页模型"""
    page: int = Field(description="当前页码", ge=1)
    size: int = Field(description="每页大小", ge=1, le=100)
    total: int = Field(description="总记录数", ge=0)
    pages: int = Field(description="总页数", ge=0)
    
    class Config:
        schema_extra = {
            "example": {
                "page": 1,
                "size": 20,
                "total": 100,
                "pages": 5
            }
        }


class TradingStrategy(str, Enum):
    """交易策略枚举"""
    FULL_COPY = "full_copy"
    PARTIAL_COPY = "partial_copy"
    REVERSE_COPY = "reverse_copy"
    HEDGE_COPY = "hedge_copy"
    SCALE_COPY = "scale_copy"


class AccountType(str, Enum):
    """账户类型枚举"""
    DEMO = "demo"
    LIVE = "live"


class AccountStatus(str, Enum):
    """账户状态枚举"""
    ACTIVE = "active"
    INACTIVE = "inactive"
    SUSPENDED = "suspended"
    ERROR = "error"


class SystemRole(str, Enum):
    """系统角色枚举"""
    MASTER = "master"
    SLAVE = "slave"
    HYBRID = "hybrid"


class MT5AccountModel(BaseModel):
    """MT5账户模型"""
    account_id: str = Field(description="账户ID", min_length=1, max_length=100)
    login: int = Field(description="MT5登录号", ge=1)
    password: str = Field(description="密码", min_length=1, max_length=100)
    server: str = Field(description="服务器名称", min_length=1, max_length=100)
    terminal_path: str = Field(description="终端路径", min_length=1)
    account_type: AccountType = Field(description="账户类型")
    status: AccountStatus = Field(description="账户状态", default=AccountStatus.ACTIVE)
    balance: Optional[float] = Field(description="账户余额", default=None)
    equity: Optional[float] = Field(description="净值", default=None)
    margin: Optional[float] = Field(description="保证金", default=None)
    free_margin: Optional[float] = Field(description="可用保证金", default=None)
    leverage: Optional[int] = Field(description="杠杆", default=None)
    currency: Optional[str] = Field(description="基础货币", default=None)
    company: Optional[str] = Field(description="经纪商", default=None)
    last_update: Optional[datetime] = Field(description="最后更新时间", default=None)
    
    @validator('password')
    def validate_password(cls, v):
        if len(v) < 4:
            raise ValueError('密码至少4个字符')
        return v
    
    class Config:
        schema_extra = {
            "example": {
                "account_id": "MT5_DEMO_001",
                "login": 5000001,
                "password": "password123",
                "server": "ICMarkets-Demo",
                "terminal_path": "/opt/mt5/terminal.exe",
                "account_type": "demo",
                "status": "active",
                "balance": 10000.0,
                "equity": 9850.0,
                "margin": 150.0,
                "free_margin": 9700.0,
                "leverage": 500,
                "currency": "USD",
                "company": "IC Markets"
            }
        }


class RiskManagementModel(BaseModel):
    """风险管理模型"""
    max_lot_size: float = Field(description="最大手数", ge=0.01, le=100.0, default=10.0)
    min_lot_size: float = Field(description="最小手数", ge=0.01, le=10.0, default=0.01)
    lot_multiplier: float = Field(description="手数乘数", ge=0.01, le=100.0, default=1.0)
    max_positions: int = Field(description="最大持仓数", ge=1, le=100, default=10)
    max_drawdown: float = Field(description="最大回撤", ge=0.01, le=1.0, default=0.2)
    daily_loss_limit: float = Field(description="每日亏损限制", ge=0.0, default=1000.0)
    max_risk_per_trade: float = Field(description="单笔交易最大风险", ge=0.01, le=0.5, default=0.02)
    symbols_filter: Optional[List[str]] = Field(description="货币对过滤", default=None)
    copy_sl_tp: bool = Field(description="复制止损止盈", default=True)
    auto_close_on_risk: bool = Field(description="风险时自动关闭", default=True)
    
    class Config:
        schema_extra = {
            "example": {
                "max_lot_size": 10.0,
                "min_lot_size": 0.01,
                "lot_multiplier": 1.0,
                "max_positions": 10,
                "max_drawdown": 0.2,
                "daily_loss_limit": 1000.0,
                "max_risk_per_trade": 0.02,
                "symbols_filter": ["EURUSD", "GBPUSD", "USDJPY"],
                "copy_sl_tp": True,
                "auto_close_on_risk": True
            }
        }


class TradingPairModel(BaseModel):
    """交易配对模型"""
    pair_id: str = Field(description="配对ID", min_length=1, max_length=100)
    master_account: str = Field(description="主账户ID", min_length=1, max_length=100)
    slave_accounts: List[str] = Field(description="从账户ID列表", min_items=1, max_items=50)
    strategy: TradingStrategy = Field(description="交易策略")
    risk_management: RiskManagementModel = Field(description="风险管理设置")
    enabled: bool = Field(description="是否启用", default=True)
    reverse_copy: bool = Field(description="反向复制", default=False)
    created_at: datetime = Field(description="创建时间", default_factory=datetime.now)
    updated_at: datetime = Field(description="更新时间", default_factory=datetime.now)
    statistics: Optional[Dict[str, Any]] = Field(description="统计信息", default=None)
    
    class Config:
        schema_extra = {
            "example": {
                "pair_id": "PAIR_001",
                "master_account": "MT5_LIVE_001",
                "slave_accounts": ["MT5_DEMO_001", "MT5_DEMO_002"],
                "strategy": "full_copy",
                "risk_management": {
                    "max_lot_size": 10.0,
                    "min_lot_size": 0.01,
                    "lot_multiplier": 1.0,
                    "max_positions": 10,
                    "max_drawdown": 0.2,
                    "daily_loss_limit": 1000.0
                },
                "enabled": True,
                "reverse_copy": False
            }
        }


class SystemHealthModel(BaseModel):
    """系统健康模型"""
    overall_status: str = Field(description="总体状态")
    uptime: float = Field(description="运行时间(秒)", ge=0)
    memory_usage: float = Field(description="内存使用率", ge=0, le=1)
    cpu_usage: float = Field(description="CPU使用率", ge=0, le=1)
    disk_usage: float = Field(description="磁盘使用率", ge=0, le=1)
    active_connections: int = Field(description="活跃连接数", ge=0)
    processing_latency: float = Field(description="处理延迟(ms)", ge=0)
    error_rate: float = Field(description="错误率", ge=0, le=1)
    last_check: datetime = Field(description="最后检查时间", default_factory=datetime.now)
    
    class Config:
        schema_extra = {
            "example": {
                "overall_status": "healthy",
                "uptime": 86400.0,
                "memory_usage": 0.65,
                "cpu_usage": 0.35,
                "disk_usage": 0.45,
                "active_connections": 25,
                "processing_latency": 12.5,
                "error_rate": 0.001,
                "last_check": "2024-01-01T12:00:00Z"
            }
        }


class PerformanceMetricsModel(BaseModel):
    """性能指标模型"""
    signal_latency_ms: float = Field(description="信号延迟(毫秒)", ge=0)
    throughput_per_second: float = Field(description="每秒吞吐量", ge=0)
    batch_size_avg: float = Field(description="平均批量大小", ge=0)
    memory_pool_hit_rate: float = Field(description="内存池命中率", ge=0, le=1)
    connection_pool_usage: float = Field(description="连接池使用率", ge=0, le=1)
    processing_time_avg: float = Field(description="平均处理时间(ms)", ge=0)
    error_count: int = Field(description="错误计数", ge=0)
    success_rate: float = Field(description="成功率", ge=0, le=1)
    
    class Config:
        schema_extra = {
            "example": {
                "signal_latency_ms": 12.5,
                "throughput_per_second": 1250.0,
                "batch_size_avg": 35.5,
                "memory_pool_hit_rate": 0.92,
                "connection_pool_usage": 0.68,
                "processing_time_avg": 8.2,
                "error_count": 5,
                "success_rate": 0.998
            }
        }


class TradingStatisticsModel(BaseModel):
    """交易统计模型"""
    total_signals: int = Field(description="总信号数", ge=0)
    successful_signals: int = Field(description="成功信号数", ge=0)
    failed_signals: int = Field(description="失败信号数", ge=0)
    total_trades: int = Field(description="总交易数", ge=0)
    profitable_trades: int = Field(description="盈利交易数", ge=0)
    losing_trades: int = Field(description="亏损交易数", ge=0)
    total_profit: float = Field(description="总盈利", default=0.0)
    total_loss: float = Field(description="总亏损", default=0.0)
    win_rate: float = Field(description="胜率", ge=0, le=1)
    profit_factor: float = Field(description="盈利因子", ge=0)
    max_drawdown: float = Field(description="最大回撤", ge=0)
    average_trade_time: float = Field(description="平均交易时间(秒)", ge=0)
    
    class Config:
        schema_extra = {
            "example": {
                "total_signals": 10000,
                "successful_signals": 9950,
                "failed_signals": 50,
                "total_trades": 500,
                "profitable_trades": 300,
                "losing_trades": 200,
                "total_profit": 15000.0,
                "total_loss": -8000.0,
                "win_rate": 0.6,
                "profit_factor": 1.875,
                "max_drawdown": 0.15,
                "average_trade_time": 3600.0
            }
        }


class HostInfoModel(BaseModel):
    """主机信息模型"""
    host_id: str = Field(description="主机ID", min_length=1, max_length=100)
    hostname: str = Field(description="主机名", min_length=1, max_length=100)
    ip_address: str = Field(description="IP地址", min_length=7, max_length=45)
    port: int = Field(description="端口", ge=1, le=65535)
    capabilities: List[str] = Field(description="功能列表", min_items=1)
    region: str = Field(description="区域", min_length=1, max_length=50)
    status: str = Field(description="状态", min_length=1, max_length=20)
    role: SystemRole = Field(description="系统角色")
    last_heartbeat: datetime = Field(description="最后心跳时间", default_factory=datetime.now)
    load_metrics: Optional[Dict[str, float]] = Field(description="负载指标", default=None)
    
    class Config:
        schema_extra = {
            "example": {
                "host_id": "host_001",
                "hostname": "trading-server-01",
                "ip_address": "*************",
                "port": 8000,
                "capabilities": ["api", "trading", "monitoring"],
                "region": "asia-pacific",
                "status": "online",
                "role": "master",
                "last_heartbeat": "2024-01-01T12:00:00Z",
                "load_metrics": {
                    "cpu": 0.35,
                    "memory": 0.65,
                    "disk": 0.45,
                    "network": 0.25
                }
            }
        }


# 请求模型
class CreatePairingRequest(BaseModel):
    """创建配对请求"""
    master_account: str = Field(description="主账户ID", min_length=1, max_length=100)
    slave_accounts: List[str] = Field(description="从账户ID列表", min_items=1, max_items=50)
    strategy: TradingStrategy = Field(description="交易策略", default=TradingStrategy.FULL_COPY)
    risk_management: RiskManagementModel = Field(description="风险管理设置")
    enabled: bool = Field(description="是否启用", default=True)
    reverse_copy: bool = Field(description="反向复制", default=False)
    
    class Config:
        schema_extra = {
            "example": {
                "master_account": "MT5_LIVE_001",
                "slave_accounts": ["MT5_DEMO_001", "MT5_DEMO_002"],
                "strategy": "full_copy",
                "risk_management": {
                    "max_lot_size": 10.0,
                    "min_lot_size": 0.01,
                    "lot_multiplier": 1.0,
                    "max_positions": 10,
                    "max_drawdown": 0.2,
                    "daily_loss_limit": 1000.0
                },
                "enabled": True,
                "reverse_copy": False
            }
        }


class UpdatePairingRequest(BaseModel):
    """更新配对请求"""
    enabled: Optional[bool] = Field(description="是否启用", default=None)
    strategy: Optional[TradingStrategy] = Field(description="交易策略", default=None)
    risk_management: Optional[RiskManagementModel] = Field(description="风险管理设置", default=None)
    reverse_copy: Optional[bool] = Field(description="反向复制", default=None)
    
    class Config:
        schema_extra = {
            "example": {
                "enabled": True,
                "strategy": "full_copy",
                "risk_management": {
                    "max_lot_size": 5.0,
                    "lot_multiplier": 0.5
                }
            }
        }


class RegisterAccountRequest(BaseModel):
    """注册账户请求"""
    account: MT5AccountModel = Field(description="账户信息")
    role: SystemRole = Field(description="系统角色")
    auto_start: bool = Field(description="自动启动", default=True)
    
    class Config:
        schema_extra = {
            "example": {
                "account": {
                    "account_id": "MT5_DEMO_001",
                    "login": 5000001,
                    "password": "password123",
                    "server": "ICMarkets-Demo",
                    "terminal_path": "/opt/mt5/terminal.exe",
                    "account_type": "demo"
                },
                "role": "slave",
                "auto_start": True
            }
        }


class RegisterHostRequest(BaseModel):
    """注册主机请求"""
    host_info: HostInfoModel = Field(description="主机信息")
    auto_discover: bool = Field(description="自动发现", default=True)
    
    class Config:
        schema_extra = {
            "example": {
                "host_info": {
                    "host_id": "host_001",
                    "hostname": "trading-server-01",
                    "ip_address": "*************",
                    "port": 8000,
                    "capabilities": ["api", "trading"],
                    "region": "asia-pacific",
                    "status": "online",
                    "role": "master"
                },
                "auto_discover": True
            }
        }


# 响应模型
class PairingResponse(BaseResponseModel):
    """配对响应"""
    data: Optional[TradingPairModel] = Field(description="配对数据", default=None)


class AccountResponse(BaseResponseModel):
    """账户响应"""
    data: Optional[MT5AccountModel] = Field(description="账户数据", default=None)


class HostResponse(BaseResponseModel):
    """主机响应"""
    data: Optional[HostInfoModel] = Field(description="主机数据", default=None)


class SystemStatusResponse(BaseResponseModel):
    """系统状态响应"""
    data: Optional[SystemHealthModel] = Field(description="系统健康数据", default=None)


class PerformanceResponse(BaseResponseModel):
    """性能响应"""
    data: Optional[PerformanceMetricsModel] = Field(description="性能指标数据", default=None)


class TradingStatsResponse(BaseResponseModel):
    """交易统计响应"""
    data: Optional[TradingStatisticsModel] = Field(description="交易统计数据", default=None)


class PaginatedResponse(BaseResponseModel):
    """分页响应"""
    data: List[Any] = Field(description="数据列表", default=[])
    pagination: PaginationModel = Field(description="分页信息")


# 错误响应模型
class ErrorResponse(BaseResponseModel):
    """错误响应"""
    error_code: Optional[str] = Field(description="错误代码", default=None)
    details: Optional[Dict[str, Any]] = Field(description="错误详情", default=None)
    
    class Config:
        schema_extra = {
            "example": {
                "success": False,
                "message": "操作失败",
                "error_code": "VALIDATION_ERROR",
                "details": {
                    "field": "account_id",
                    "reason": "账户ID不能为空"
                },
                "timestamp": **********.0
            }
        }