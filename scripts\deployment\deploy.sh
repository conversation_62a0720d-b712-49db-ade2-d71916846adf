#!/bin/bash
# MT5高频异步轮询系统部署脚本
# 支持分布式部署和性能优化配置

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 配置变量
PROJECT_NAME="mt5-trade-copier"
VERSION="2.0.0"
DOCKER_REGISTRY="your-registry.com"
CONFIG_DIR="config"
DEPLOY_ENV=${DEPLOY_ENV:-"production"}

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查依赖
check_dependencies() {
    log_info "检查部署依赖..."
    
    # 检查Docker
    if ! command -v docker &> /dev/null; then
        log_error "Docker未安装，请先安装Docker"
        exit 1
    fi
    
    # 检查Docker Compose
    if ! command -v docker-compose &> /dev/null; then
        log_error "Docker Compose未安装，请先安装Docker Compose"
        exit 1
    fi
    
    # 检查Python
    if ! command -v python3 &> /dev/null; then
        log_error "Python3未安装，请先安装Python3"
        exit 1
    fi
    
    log_success "依赖检查完成"
}

# 构建Docker镜像
build_images() {
    log_info "构建Docker镜像..."
    
    # 构建主镜像
    docker build -t ${PROJECT_NAME}:${VERSION} -f docker/Dockerfile .
    docker tag ${PROJECT_NAME}:${VERSION} ${PROJECT_NAME}:latest
    
    log_success "Docker镜像构建完成"
}

# 部署中央服务
deploy_central_services() {
    log_info "部署中央服务 (NATS, Redis, API)..."
    
    # 创建网络
    docker network create ${PROJECT_NAME}-network 2>/dev/null || true
    
    # 启动中央服务
    docker-compose -f docker/docker-compose.yml up -d nats redis prometheus grafana
    
    # 等待服务启动
    log_info "等待中央服务启动..."
    sleep 10
    
    # 检查服务状态
    if ! docker-compose -f docker/docker-compose.yml ps | grep -q "Up"; then
        log_error "中央服务启动失败"
        exit 1
    fi
    
    log_success "中央服务部署完成"
}

# 部署API服务
deploy_api() {
    log_info "部署API服务..."
    
    # 启动API服务
    docker-compose -f docker/docker-compose.yml up -d api
    
    # 等待API启动
    sleep 5
    
    # 健康检查
    if curl -f http://localhost:8000/health > /dev/null 2>&1; then
        log_success "API服务部署成功"
    else
        log_warning "API服务可能未完全启动，请检查日志"
    fi
}

# 部署主账户监控器
deploy_master_monitor() {
    local host=$1
    local config_file=$2
    
    log_info "部署主账户监控器到 $host"
    
    if [ "$host" = "localhost" ]; then
        # 本地部署
        docker run -d \
            --name ${PROJECT_NAME}-master-${host} \
            --network ${PROJECT_NAME}-network \
            -v $(pwd)/${config_file}:/app/config/config.yaml \
            -e SERVICE_TYPE=master \
            -e CONFIG_FILE=/app/config/config.yaml \
            ${PROJECT_NAME}:${VERSION} \
            python scripts/start_master.py
    else
        # 远程部署
        scp -r src/ config/ scripts/ requirements.txt docker/Dockerfile $host:/tmp/${PROJECT_NAME}/
        
        ssh $host << EOF
            cd /tmp/${PROJECT_NAME}
            docker build -t ${PROJECT_NAME}:${VERSION} .
            docker run -d \
                --name ${PROJECT_NAME}-master \
                --network host \
                -v /tmp/${PROJECT_NAME}/${config_file}:/app/config/config.yaml \
                -e SERVICE_TYPE=master \
                ${PROJECT_NAME}:${VERSION} \
                python scripts/start_master.py
EOF
    fi
    
    log_success "主账户监控器部署完成: $host"
}

# 部署从账户执行器
deploy_slave_executor() {
    local host=$1
    local config_file=$2
    
    log_info "部署从账户执行器到 $host"
    
    if [ "$host" = "localhost" ]; then
        # 本地部署
        docker run -d \
            --name ${PROJECT_NAME}-slave-${host} \
            --network ${PROJECT_NAME}-network \
            -v $(pwd)/${config_file}:/app/config/config.yaml \
            -e SERVICE_TYPE=slave \
            -e CONFIG_FILE=/app/config/config.yaml \
            ${PROJECT_NAME}:${VERSION} \
            python scripts/start_slave.py
    else
        # 远程部署
        scp -r src/ config/ scripts/ requirements.txt docker/Dockerfile $host:/tmp/${PROJECT_NAME}/
        
        ssh $host << EOF
            cd /tmp/${PROJECT_NAME}
            docker build -t ${PROJECT_NAME}:${VERSION} .
            docker run -d \
                --name ${PROJECT_NAME}-slave \
                --network host \
                -v /tmp/${PROJECT_NAME}/${config_file}:/app/config/config.yaml \
                -e SERVICE_TYPE=slave \
                ${PROJECT_NAME}:${VERSION} \
                python scripts/start_slave.py
EOF
    fi
    
    log_success "从账户执行器部署完成: $host"
}

# 性能优化配置
optimize_performance() {
    log_info "应用性能优化配置..."
    
    # 系统内核参数优化
    if [ "$(id -u)" = "0" ]; then
        # 网络优化
        echo 'net.core.rmem_max = 134217728' >> /etc/sysctl.conf
        echo 'net.core.wmem_max = 134217728' >> /etc/sysctl.conf
        echo 'net.ipv4.tcp_rmem = 4096 87380 134217728' >> /etc/sysctl.conf
        echo 'net.ipv4.tcp_wmem = 4096 65536 134217728' >> /etc/sysctl.conf
        
        # 应用配置
        sysctl -p
        
        log_success "系统性能优化完成"
    else
        log_warning "需要root权限进行系统优化，跳过..."
    fi
    
    # Docker容器资源限制优化
    log_info "优化Docker容器配置..."
    
    # 更新docker-compose配置以优化性能
    cat > docker/docker-compose.override.yml << EOF
version: '3.8'
services:
  api:
    deploy:
      resources:
        limits:
          cpus: '2.0'
          memory: 2G
        reservations:
          cpus: '1.0'
          memory: 1G
    environment:
      - PYTHONUNBUFFERED=1
      - PYTHONOPTIMIZE=1
  
  nats:
    deploy:
      resources:
        limits:
          cpus: '1.0'
          memory: 1G
        reservations:
          cpus: '0.5'
          memory: 512M
  
  redis:
    deploy:
      resources:
        limits:
          cpus: '0.5'
          memory: 512M
        reservations:
          cpus: '0.25'
          memory: 256M
EOF
    
    log_success "Docker性能优化完成"
}

# 验证部署
verify_deployment() {
    log_info "验证部署状态..."
    
    # 检查容器状态
    log_info "检查容器状态..."
    docker-compose -f docker/docker-compose.yml ps
    
    # 检查API健康状态
    log_info "检查API健康状态..."
    if curl -f http://localhost:8000/api/system/health > /dev/null 2>&1; then
        log_success "API服务正常"
    else
        log_error "API服务异常"
        return 1
    fi
    
    # 检查NATS连接
    log_info "检查NATS连接..."
    if docker exec ${PROJECT_NAME}_nats_1 nats-server --help > /dev/null 2>&1; then
        log_success "NATS服务正常"
    else
        log_warning "NATS服务检查失败"
    fi
    
    # 检查Redis连接
    log_info "检查Redis连接..."
    if docker exec ${PROJECT_NAME}_redis_1 redis-cli ping | grep -q "PONG"; then
        log_success "Redis服务正常"
    else
        log_warning "Redis服务检查失败"
    fi
    
    log_success "部署验证完成"
}

# 运行性能测试
run_performance_tests() {
    log_info "运行性能测试..."
    
    # 安装测试依赖
    pip3 install pytest pytest-asyncio numpy
    
    # 运行延迟测试
    python3 -m pytest tests/performance/test_latency.py -v
    
    log_success "性能测试完成"
}

# 显示部署信息
show_deployment_info() {
    log_success "🚀 MT5高频异步轮询系统部署完成!"
    echo ""
    echo "📊 服务访问地址:"
    echo "  - API服务: http://localhost:8000"
    echo "  - API文档: http://localhost:8000/docs"
    echo "  - Grafana监控: http://localhost:3000 (admin/admin)"
    echo "  - Prometheus: http://localhost:9090"
    echo ""
    echo "🔧 管理命令:"
    echo "  - 查看日志: docker-compose -f docker/docker-compose.yml logs -f"
    echo "  - 停止服务: docker-compose -f docker/docker-compose.yml down"
    echo "  - 重启服务: docker-compose -f docker/docker-compose.yml restart"
    echo ""
    echo "📈 性能监控:"
    echo "  - 系统健康: curl http://localhost:8000/api/system/health"
    echo "  - 性能指标: curl http://localhost:9090/metrics"
}

# 主函数
main() {
    local action=${1:-"full"}
    
    echo "🚀 MT5高频异步轮询系统部署脚本 v${VERSION}"
    echo "环境: ${DEPLOY_ENV}"
    echo "操作: ${action}"
    echo "=" * 50
    
    case $action in
        "check")
            check_dependencies
            ;;
        "build")
            check_dependencies
            build_images
            ;;
        "central")
            check_dependencies
            build_images
            deploy_central_services
            deploy_api
            ;;
        "master")
            deploy_master_monitor ${2:-"localhost"} ${3:-"config/config.yaml"}
            ;;
        "slave")
            deploy_slave_executor ${2:-"localhost"} ${3:-"config/config.yaml"}
            ;;
        "optimize")
            optimize_performance
            ;;
        "test")
            run_performance_tests
            ;;
        "verify")
            verify_deployment
            ;;
        "full")
            check_dependencies
            build_images
            deploy_central_services
            deploy_api
            optimize_performance
            verify_deployment
            show_deployment_info
            ;;
        *)
            echo "用法: $0 {check|build|central|master|slave|optimize|test|verify|full}"
            echo ""
            echo "操作说明:"
            echo "  check   - 检查部署依赖"
            echo "  build   - 构建Docker镜像"
            echo "  central - 部署中央服务"
            echo "  master  - 部署主账户监控器"
            echo "  slave   - 部署从账户执行器"
            echo "  optimize- 性能优化配置"
            echo "  test    - 运行性能测试"
            echo "  verify  - 验证部署状态"
            echo "  full    - 完整部署流程"
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"
