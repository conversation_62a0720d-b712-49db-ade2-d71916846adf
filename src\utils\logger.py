"""
日志工具模块
"""
import logging
import sys
import json
from datetime import datetime
from typing import Dict, Any, Optional
from pathlib import Path


class JsonFormatter(logging.Formatter):
    """JSON格式化器"""
    
    def format(self, record: logging.LogRecord) -> str:
        log_entry = {
            'timestamp': datetime.fromtimestamp(record.created).isoformat(),
            'level': record.levelname,
            'logger': record.name,
            'message': record.getMessage(),
            'module': record.module,
            'function': record.funcName,
            'line': record.lineno
        }
        
        # 添加异常信息
        if record.exc_info:
            log_entry['exception'] = self.formatException(record.exc_info)
        
        # 添加自定义字段
        if hasattr(record, 'extra_fields'):
            log_entry.update(record.extra_fields)
            
        return json.dumps(log_entry, ensure_ascii=False)


class MT5Logger:
    """MT5交易系统日志器"""
    
    def __init__(self, name: str, level: str = "INFO", format_type: str = "json"):
        self.name = name
        self.level = level
        self.format_type = format_type
        self.logger = logging.getLogger(name)
        self._setup_logger()
    
    def _setup_logger(self):
        """设置日志器"""
        self.logger.setLevel(getattr(logging, self.level.upper()))
        
        # 清除已有的处理器
        self.logger.handlers.clear()
        
        # 创建控制台处理器
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setLevel(getattr(logging, self.level.upper()))
        
        # 设置格式化器
        if self.format_type == "json":
            formatter = JsonFormatter()
        else:
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
        
        console_handler.setFormatter(formatter)
        self.logger.addHandler(console_handler)
        
        # 防止重复日志
        self.logger.propagate = False
    
    def add_file_handler(self, file_path: str, level: str = None):
        """添加文件处理器"""
        if level is None:
            level = self.level
            
        # 确保目录存在
        Path(file_path).parent.mkdir(parents=True, exist_ok=True)
        
        file_handler = logging.FileHandler(file_path, encoding='utf-8')
        file_handler.setLevel(getattr(logging, level.upper()))
        
        if self.format_type == "json":
            formatter = JsonFormatter()
        else:
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
        
        file_handler.setFormatter(formatter)
        self.logger.addHandler(file_handler)
    
    def log_with_context(self, level: str, message: str, **kwargs):
        """记录带上下文的日志"""
        record = self.logger.makeRecord(
            name=self.name,
            level=getattr(logging, level.upper()),
            fn="",
            lno=0,
            msg=message,
            args=(),
            exc_info=None
        )
        
        # 添加额外字段
        if kwargs:
            record.extra_fields = kwargs
        
        self.logger.handle(record)
    
    def debug(self, message: str, **kwargs):
        """调试日志"""
        self.log_with_context("DEBUG", message, **kwargs)
    
    def info(self, message: str, **kwargs):
        """信息日志"""
        self.log_with_context("INFO", message, **kwargs)
    
    def warning(self, message: str, **kwargs):
        """警告日志"""
        self.log_with_context("WARNING", message, **kwargs)
    
    def error(self, message: str, **kwargs):
        """错误日志"""
        self.log_with_context("ERROR", message, **kwargs)
    
    def critical(self, message: str, **kwargs):
        """严重错误日志"""
        self.log_with_context("CRITICAL", message, **kwargs)


# 全局日志器实例
_loggers: Dict[str, MT5Logger] = {}


def get_logger(name: str, level: str = "INFO", format_type: str = "json") -> MT5Logger:
    """获取或创建日志器"""
    if name not in _loggers:
        _loggers[name] = MT5Logger(name, level, format_type)
    return _loggers[name]


def setup_logging(config: Dict[str, Any]):
    """设置全局日志配置"""
    level = config.get('level', 'INFO')
    format_type = config.get('format', 'json')
    log_file = config.get('file')
    
    # 设置root logger
    root_logger = get_logger('root', level, format_type)
    
    if log_file:
        root_logger.add_file_handler(log_file)
    
    # 设置第三方库日志级别
    logging.getLogger('urllib3').setLevel(logging.WARNING)
    logging.getLogger('requests').setLevel(logging.WARNING)
    logging.getLogger('asyncio').setLevel(logging.WARNING)


class LoggingContext:
    """日志上下文管理器"""
    
    def __init__(self, logger: MT5Logger, **context):
        self.logger = logger
        self.context = context
        self.original_name = logger.name
    
    def __enter__(self):
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        if exc_type:
            self.logger.error(
                f"异常发生: {exc_type.__name__}: {exc_val}",
                **self.context
            )
    
    def info(self, message: str, **kwargs):
        """信息日志"""
        self.logger.info(message, **{**self.context, **kwargs})
    
    def debug(self, message: str, **kwargs):
        """调试日志"""
        self.logger.debug(message, **{**self.context, **kwargs})
    
    def warning(self, message: str, **kwargs):
        """警告日志"""
        self.logger.warning(message, **{**self.context, **kwargs})
    
    def error(self, message: str, **kwargs):
        """错误日志"""
        self.logger.error(message, **{**self.context, **kwargs})


def with_logging_context(logger: MT5Logger, **context):
    """创建日志上下文"""
    return LoggingContext(logger, **context)