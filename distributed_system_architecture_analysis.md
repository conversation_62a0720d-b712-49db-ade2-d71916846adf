我明白了！你说得对，我需要进行更深度的思考和分析。让我重新检查问题的本质。


● Read(src/messaging/jetstream_client.py)

  ⎿  Read 30 lines (ctrl+r to expand)


✻ Thinking…

  现在我看到了问题的根本所在！

  问题确实非常隐蔽且严重：

  1. create_layered_streams 方法仍然是硬编码的：它硬编码地调用了4个特定的方法：
    - _create_local_stream(host_id)
    - _create_signals_stream()
    - _create_rpc_stream()
    - _create_control_stream()
  2. 这完全违背了配置驱动的原则：
    - 系统仍然假设只有这4种流
    - 如果要添加新的流类型，必须修改代码
    - 流的创建顺序是硬编码的
    - 流的创建逻辑是硬编码的
  3. StreamConfigManager 确实没有真正被使用：
    - 虽然各个 _create_*_stream 方法会调用 StreamConfigManager，但整体的流程仍然是硬编码的
    - 系统并没有真正从配置中读取"需要创建哪些流"，而是硬编码地创建4种流

  正确的配置驱动架构应该是：
  1. 从 StreamConfigManager 获取所有需要创建的流的列表
  2. 动态地遍历这个列表
  3. 为每个流调用通用的流创建方法

  同样的问题也存在于消费者创建中。

  让我实施真正的配置驱动架构。


● 🔍 深度问题分析

  您的分析完全正确！我发现了问题的根本症结：

  问题的真实本质

  虽然我修改了各个 _create_*_stream 方法从 StreamConfigManager 获取配置，但 整个架构流程仍然是硬编码的：

  # 当前的问题代码 - 仍然是硬编码流程
  async def create_layered_streams(self, host_id: str) -> bool:
      # 硬编码地创建4种特定流
      await self._create_local_stream(host_id)      # 硬编码调用
      await self._create_signals_stream()           # 硬编码调用
      await self._create_rpc_stream()               # 硬编码调用
      await self._create_control_stream()           # 硬编码调用

  这严重违背了配置驱动原则：
  - 系统假设只有这4种流
  - 添加新流类型需要修改代码
  - 流创建顺序和逻辑都是硬编码的
  - StreamConfigManager 仍然没有真正控制系统行为

  🚀 彻底解决方案实施

  让我实施真正的配置驱动架构：


● Update(src/messaging/jetstream_client.py)

  ⎿  Updated src/messaging/jetstream_client.py with 35 additions and 14 removals
       415        # ==================== 分层流架构方法 ====================
       416
       417        async def create_layered_streams(self, host_id: str) -> bool:
       418 -          """创建分层流架构 - 根据优化方案实现4层流"""
       418 +          """创建分层流架构 - 完全配置驱动"""
       419            self.host_id = host_id
       420
       421            if not self.js:
     ...
       423                return False
       424
       425            try:
       426 -              logger.info(f"开始创建分层流架构，主机ID: {host_id}")
       426 +              logger.info(f"开始创建配置驱动的分层流架构，主机ID: {host_id}")
       427
       428 -              # 1. 创建本地高速通道流
       429 -              await self._create_local_stream(host_id)
       428 +              # 从配置管理器获取所有需要创建的流
       429 +              from ..core.config_manager import get_stream_config_manager
       430 +              stream_config_manager = get_stream_config_manager()
       431 +              all_streams = stream_config_manager.get_all_streams()
       432
       433 -              # 2. 创建跨主机交易信号流
       434 -              await self._create_signals_stream()
       433 +              if not all_streams:
       434 +                  logger.error("未找到任何流配置，无法创建流架构")
       435 +                  return False
       436
       437 -              # 3. 创建跨主机RPC通信流
       438 -              await self._create_rpc_stream()
       437 +              # 动态创建所有配置中定义的流
       438 +              created_streams = 0
       439 +              for stream_type, stream_config in all_streams.items():
       440 +                  try:
       441 +                      logger.info(f"创建流: {stream_type} ({stream_config.get('description', 'No description')})")
       442 +
       443 +                      # 使用通用方法动态创建流
       444 +                      success = await self._create_stream_from_config(stream_type, stream_config, host_id)
       445 +
       446 +                      if success:
       447 +                          created_streams += 1
       448 +                          self._layered_streams[stream_config.get('name', stream_type)] = True
       449 +                          logger.info(f"✅ 流创建成功: {stream_type}")
       450 +                      else:
       451 +                          logger.warning(f"⚠️ 流创建失败: {stream_type}")
       452 +
       453 +                  except Exception as e:
       454 +                      logger.error(f"创建流 {stream_type} 时发生异常: {e}")
       455 +                      continue
       456
       457 -              # 4. 创建系统控制和监控流
       458 -              await self._create_control_stream()
       457 +              if created_streams > 0:
       458 +                  logger.info(f"✅ 配置驱动流架构创建完成: {created_streams}/{len(all_streams)} 个流")
       459 +                  return True
       460 +              else:
       461 +                  logger.error("❌ 没有成功创建任何流")
       462 +                  return False
       463
       464 -              logger.info("✅ 分层流架构创建完成")
       465 -              return True
       466 -
       464            except Exception as e:
       465 -              logger.error(f"创建分层流架构失败: {e}")
       465 +              logger.error(f"创建配置驱动流架构失败: {e}")
       466                return False
       467
       468        async def _create_local_stream(self, host_id: str):


✻ Thinking…

  好的，现在我需要实现通用的 _create_stream_from_config 方法，这是关键的配置驱动方法。让我添加这个方法。


● 现在添加通用的配置驱动流创建方法：


● Update(src/messaging/jetstream_client.py)

  ⎿  Updated src/messaging/jetstream_client.py with 51 additions
       465                logger.error(f"创建配置驱动流架构失败: {e}")
       466                return False
       467
       468 +      async def _create_stream_from_config(self, stream_type: str, stream_config: Dict[str, Any], host_id: str) ->
           + bool:
       469 +          """通用的配置驱动流创建方法 - 真正的配置驱动核心"""
       470 +          try:
       471 +              # 处理模板变量
       472 +              stream_name = stream_config.get('name', f'MT5_{stream_type.upper()}')
       473 +              if '{host_id}' in stream_name:
       474 +                  stream_name = stream_name.format(host_id=host_id)
       475 +
       476 +              # 处理主题模板
       477 +              subjects = []
       478 +              for subject_template in stream_config.get('subjects', []):
       479 +                  if '{host_id}' in subject_template:
       480 +                      subjects.append(subject_template.format(host_id=host_id))
       481 +                  else:
       482 +                      subjects.append(subject_template)
       483 +
       484 +              if not subjects:
       485 +                  logger.error(f"流 {stream_type} 没有定义主题，跳过创建")
       486 +                  return False
       487 +
       488 +              # 创建流配置
       489 +              stream_nats_config = StreamConfig(
       490 +                  name=stream_name,
       491 +                  subjects=subjects,
       492 +                  max_age=stream_config.get('max_age', 3600),
       493 +                  max_msgs=stream_config.get('max_msgs', 100000),
       494 +                  max_bytes=stream_config.get('max_bytes', 1073741824),
       495 +                  storage=stream_config.get('storage', 'file'),
       496 +                  num_replicas=stream_config.get('replicas', 1),
       497 +                  retention=stream_config.get('retention', 'limits'),
       498 +                  duplicate_window=stream_config.get('duplicate_window', 120)
       499 +              )
       500 +
       501 +              # 创建流
       502 +              try:
       503 +                  await self.js.add_stream(stream_nats_config)
       504 +                  logger.info(f"✅ 流创建成功: {stream_name} (类型: {stream_type})")
       505 +                  return True
       506 +
       507 +              except BadRequestError as e:
       508 +                  if "already exists" in str(e).lower():
       509 +                      logger.info(f"流已存在: {stream_name} (类型: {stream_type})")
       510 +                      return True
       511 +                  else:
       512 +                      logger.error(f"流创建失败: {stream_name} - {e}")
       513 +                      return False
       514 +
       515 +          except Exception as e:
       516 +              logger.error(f"配置驱动流创建异常: {stream_type} - {e}")
       517 +              return False
       518 +
       519        async def _create_local_stream(self, host_id: str):
       520            """创建本地高速通道流 - 使用配置驱动"""
       521            try:


✻ Thinking…

  好的，现在我需要删除那些旧的硬编码方法，因为它们已经不再需要了。我现在有了通用的 _create_stream_from_config
  方法，它完全是配置驱动的。

  让我删除那些旧的方法：
  - _create_local_stream
  - _create_signals_stream
  - _create_rpc_stream
  - _create_control_stream

  这些方法现在都是冗余的，因为通用方法可以处理所有类型的流。


● 现在删除所有旧的硬编码流创建方法，因为它们已被通用配置驱动方法替代：


● Search(pattern: "async def _create_.*_stream", path: "src/messaging/jetstream_client.py", output_mode: "content")

  ⎿  Found 4 lines (ctrl+r to expand)


✻ Thinking…

  我需要删除这些硬编码的方法。让我先查看每个方法的范围，然后删除它们。


● Read(src/messaging/jetstream_client.py)

  ⎿  Read 60 lines (ctrl+r to expand)


✻ Thinking…

  我可以看到这些方法已经变得冗余了，因为我现在有了通用的 _create_stream_from_config
  方法。让我删除这些旧方法。我需要找到这些方法的结束位置。


● 让我删除所有旧的硬编码流创建方法，因为它们已被通用方法替代：


● Read(src/messaging/jetstream_client.py)

  ⎿  Read 30 lines (ctrl+r to expand)


✻ Thinking…

  看起来setup_priority_consumers方法从第691行开始。让我找到所有这些旧方法的完整范围，然后删除它们。

  让我先查看第680行左右的setup_priority_consumers方法的开始：


● Read(src/messaging/jetstream_client.py)

  ⎿  Read 20 lines (ctrl+r to expand)



✻ Thinking…

  我需要找到_create_control_stream方法的结束位置。看起来是在第689行结束。现在让我删除从519行到689行之间的所有旧方法。

  但是我需要小心，因为setup_priority_consumers方法可能仍然有用。让我重新检查文件结构。


● Update(src/messaging/jetstream_client.py)

  ⎿  Updated src/messaging/jetstream_client.py with 1 addition and 171 removals
       516                logger.error(f"配置驱动流创建异常: {stream_type} - {e}")
       517                return False
       518
       519 -      async def _create_local_stream(self, host_id: str):
       520 -          """创建本地高速通道流 - 使用配置驱动"""
       521 -          try:
       522 -              # 从配置获取本地流配置
       523 -              from ..core.config_manager import get_stream_config_manager
       524 -              stream_config_manager = get_stream_config_manager()
       525 -              config = stream_config_manager.get_stream_config('local')
       526 -
       527 -              if not config:
       528 -                  logger.error("未找到本地流配置，使用默认配置")
       529 -                  config = {
       530 -                      'name_template': 'MT5_LOCAL_{host_id}',
       531 -                      'subjects': ['MT5.LOCAL.{host_id}.PRIORITY.*', 'MT5.LOCAL.{host_id}.INTERNAL.*'],
       532 -                      'storage': 'memory',
       533 -                      'retention': 'workqueue',
       534 -                      'max_age': 300,
       535 -                      'max_msgs': 100000,
       536 -                      'replicas': 1
       537 -                  }
       538 -
       539 -              # 处理模板变量
       540 -              stream_name = config.get('name_template', 'MT5_LOCAL_{host_id}').format(host_id=host_id)
       541 -
       542 -              # 处理主题模板
       543 -              subjects = []
       544 -              for subject_template in config.get('subjects', []):
       545 -                  subjects.append(subject_template.format(host_id=host_id))
       546 -
       547 -              stream_config = StreamConfig(
       548 -                  name=stream_name,
       549 -                  subjects=subjects,
       550 -                  max_age=config.get('max_age', 300),
       551 -                  max_msgs=config.get('max_msgs', 100000),
       552 -                  max_bytes=config.get('max_bytes', 104857600),
       553 -                  storage=config.get('storage', 'memory'),
       554 -                  num_replicas=config.get('replicas', 1),
       555 -                  retention=config.get('retention', 'workqueue'),
       556 -                  duplicate_window=config.get('duplicate_window', 60)
       557 -              )
       558 -
       559 -              await self.js.add_stream(stream_config)
       560 -              self._layered_streams[stream_name] = True
       561 -              logger.info(f"✅ 本地高速流创建成功: {stream_name}")
       562 -
       563 -          except BadRequestError as e:
       564 -              if "already exists" in str(e).lower():
       565 -                  self._layered_streams[stream_name] = True
       566 -                  logger.info(f"本地高速流已存在: {stream_name}")
       567 -              else:
       568 -                  raise
       519 +      # 旧的硬编码流创建方法已删除，统一使用 _create_stream_from_config
       520
       521 -      async def _create_signals_stream(self):
       522 -          """创建跨主机交易信号流 - 使用配置驱动"""
       523 -          try:
       524 -              # 从配置获取信号流配置
       525 -              from ..core.config_manager import get_stream_config_manager
       526 -              stream_config_manager = get_stream_config_manager()
       527 -              config = stream_config_manager.get_stream_config('signals')
       528 -
       529 -              if not config:
       530 -                  logger.error("未找到信号流配置，使用默认配置")
       531 -                  config = {
       532 -                      'name': 'MT5_SIGNALS',
       533 -                      'subjects': ['MT5.SIGNALS.*'],
       534 -                      'storage': 'file',
       535 -                      'retention': 'limits',
       536 -                      'max_age': 3600,
       537 -                      'max_msgs': 1000000,
       538 -                      'replicas': 3
       539 -                  }
       540 -
       541 -              stream_name = config['name']
       542 -
       543 -              stream_config = StreamConfig(
       544 -                  name=stream_name,
       545 -                  subjects=config['subjects'],
       546 -                  max_age=config.get('max_age', 3600),
       547 -                  max_msgs=config.get('max_msgs', 1000000),
       548 -                  max_bytes=config.get('max_bytes', 1073741824),  # 1GB
       549 -                  storage=config.get('storage', 'file'),
       550 -                  num_replicas=config.get('replicas', 3),
       551 -                  retention=config.get('retention', 'limits'),
       552 -                  duplicate_window=config.get('duplicate_window', 120)
       553 -              )
       554 -
       555 -              await self.js.add_stream(stream_config)
       556 -              self._layered_streams[stream_name] = True
       557 -              logger.info(f"✅ 交易信号流创建成功: {stream_name}")
       558 -
       559 -          except BadRequestError as e:
       560 -              if "already exists" in str(e).lower():
       561 -                  self._layered_streams[stream_name] = True
       562 -                  logger.info(f"交易信号流已存在: {stream_name}")
       563 -              else:
       564 -                  raise
       565 -
       566 -      async def _create_rpc_stream(self):
       567 -          """创建跨主机RPC通信流 - 使用配置驱动"""
       568 -          try:
       569 -              # 从配置获取RPC流配置
       570 -              from ..core.config_manager import get_stream_config_manager
       571 -              stream_config_manager = get_stream_config_manager()
       572 -              config = stream_config_manager.get_stream_config('rpc')
       573 -
       574 -              if not config:
       575 -                  logger.error("未找到RPC流配置，使用默认配置")
       576 -                  config = {
       577 -                      'name': 'MT5_RPC',
       578 -                      'subjects': ['MT5.RPC.*'],
       579 -                      'storage': 'file',
       580 -                      'retention': 'workqueue',
       581 -                      'max_age': 600,
       582 -                      'max_msgs': 50000,
       583 -                      'replicas': 2
       584 -                  }
       585 -
       586 -              stream_name = config['name']
       587 -
       588 -              stream_config = StreamConfig(
       589 -                  name=stream_name,
       590 -                  subjects=config['subjects'],
       591 -                  max_age=config.get('max_age', 600),
       592 -                  max_msgs=config.get('max_msgs', 50000),
       593 -                  max_bytes=config.get('max_bytes', 52428800),  # 50MB
       594 -                  storage=config.get('storage', 'file'),
       595 -                  num_replicas=config.get('replicas', 2),
       596 -                  retention=config.get('retention', 'workqueue'),
       597 -                  duplicate_window=config.get('duplicate_window', 30)
       598 -              )
       599 -
       600 -              await self.js.add_stream(stream_config)
       601 -              self._layered_streams[stream_name] = True
       602 -              logger.info(f"✅ RPC通信流创建成功: {stream_name}")
       603 -
       604 -          except BadRequestError as e:
       605 -              if "already exists" in str(e).lower():
       606 -                  self._layered_streams[stream_name] = True
       607 -                  logger.info(f"RPC通信流已存在: {stream_name}")
       608 -              else:
       609 -                  raise
       610 -
       611 -      async def _create_control_stream(self):
       612 -          """创建系统控制和监控流 - 24小时保留"""
       613 -          # 从配置获取控制流名称
       614 -          stream_config_manager = get_stream_config_manager()
       615 -          control_config = stream_config_manager.get_stream_config('control')
       616 -          stream_name = control_config.get('name', 'MT5_CONTROL')
       617 -
       618 -          try:
       619 -              stream_config = StreamConfig(
       620 -                  name=stream_name,
       621 -                  subjects=[
       622 -                      "MT5.CONTROL.*"     # 系统控制和监控数据
       623 -                  ],
       624 -                  max_age=86400,  # 24小时保留
       625 -                  max_msgs=500000,  # 监控数据限制
       626 -                  storage="file",  # 文件存储
       627 -                  num_replicas=1,  # 单副本即可
       628 -                  retention="limits"  # 保留限制
       629 -              )
       630 -
       631 -              await self.js.add_stream(stream_config)
       632 -              self._layered_streams[stream_name] = True
       633 -              logger.info(f"✅ 控制监控流创建成功: {stream_name}")
       634 -
       635 -          except BadRequestError as e:
       636 -              if "already exists" in str(e).lower():
       637 -                  self._layered_streams[stream_name] = True
       638 -                  logger.info(f"控制监控流已存在: {stream_name}")
       639 -              else:
       640 -                  raise
       641 -
       521        async def setup_priority_consumers(self, priority_queue=None) -> bool:
       522            """设置优先级消费者 - 完全配置驱动"""
       523            if not self.js:


✻ Thinking…

  好的，现在我已经删除了所有硬编码的流创建方法。接下来，我需要检查NATSManager，看看是否需要对它进行类似的修改。

  让我再检查一下NATSManager的初始化方法是否也需要类似的重构。


● 现在让我检查NATSManager是否需要类似的重构：


● Read(src/messaging/nats_manager.py)

  ⎿  Read 50 lines (ctrl+r to expand)


✻ Thinking…

  我看到NATSManager仍然有硬编码的流创建逻辑。它在_create_all_layered_streams方法中遍历self.layer_configs，但这仍然是硬编码的逻辑。

  我需要将NATSManager也改为真正的配置驱动架构。但实际上，既然我已经在JetStreamClient中实现了完全配置驱动的架构，我可以让NATSManage
  r直接使用JetStreamClient的方法，这样可以避免重复。

  这样我就能彻底解决"架构层级混乱"的问题。


● Update(src/messaging/nats_manager.py)

  ⎿  Updated src/messaging/nats_manager.py with 7 additions and 13 removals
       213                return False
       214
       215        async def _create_all_layered_streams(self):
       216 -          """创建所有分层流"""
       217 -          logger.info("创建分层流架构...")
       216 +          """创建所有分层流 - 委托给JetStreamClient的配置驱动方法"""
       217 +          logger.info("使用JetStreamClient的配置驱动架构创建分层流...")
       218
       219 -          for layer_name, config in self.layer_configs.items():
       220 -              try:
       221 -                  success = await self.jetstream.add_stream(
       222 -                      name=config.name,
       223 -                      subjects=config.subjects,
       224 -                      retention=config.retention,
       225 -                      max_age=config.max_age,
       226 -                      max_msgs=config.max_msgs
       227 -                  )
       228 -
       229 -                  if success:
       219 +          try:
       220 +              # 委托给JetStreamClient的完全配置驱动方法
       221 +              success = await self.jetstream.create_layered_streams(self.host_id)
       222 +
       223 +              if success:
       224                        self.active_streams.add(config.name)
       225                        logger.info(f"✅ {config.description} 创建成功: {config.name}")
       226                    else:


✻ Thinking…

  我看到还有一些残留的代码需要清理。让我重新检查这部分并完整地修复它。


● Read(src/messaging/nats_manager.py)

  ⎿  Read 30 lines (ctrl+r to expand)


✻ Thinking…

  我看到有残留的代码需要清理。让我完整地重写这个方法：


● Update(src/messaging/nats_manager.py)

  ⎿  Updated src/messaging/nats_manager.py with 6 additions and 8 removals
       220                # 委托给JetStreamClient的完全配置驱动方法
       221                success = await self.jetstream.create_layered_streams(self.host_id)
       222
       223 -              if success:
       224 -                      self.active_streams.add(config.name)
       225 -                      logger.info(f"✅ {config.description} 创建成功: {config.name}")
       226 -                  else:
       227 -                      logger.warning(f"⚠️  {config.description} 创建失败: {config.name}")
       228 -
       229 -              except Exception as e:
       230 -                  logger.error(f"创建流失败 {config.name}: {e}")
       223 +                  logger.info("✅ 分层流架构创建成功 (通过JetStreamClient)")
       224 +              else:
       225 +                  logger.error("❌ 分层流架构创建失败")
       226 +
       227 +          except Exception as e:
       228 +              logger.error(f"创建分层流失败: {e}")
       229
       230        async def _setup_all_consumers(self):
       231            """设置所有消费者"""


✻ Thinking…

  我需要修复一个语法错误。代码中缺少了if success:：


● Update(src/messaging/nats_manager.py)

  ⎿  Updated src/messaging/nats_manager.py with 1 addition
       220                # 委托给JetStreamClient的完全配置驱动方法
       221                success = await self.jetstream.create_layered_streams(self.host_id)
       222
       223 +              if success:
       224                    logger.info("✅ 分层流架构创建成功 (通过JetStreamClient)")
       225                else:
       226                    logger.error("❌ 分层流架构创建失败")



✻ Thinking…

  现在我也需要修复消费者设置方法，让它也委托给JetStreamClient：


● Update(src/messaging/nats_manager.py)

  ⎿  Updated src/messaging/nats_manager.py with 12 additions and 18 removals
       229                logger.error(f"创建分层流失败: {e}")
       230
       231        async def _setup_all_consumers(self):
       232 -          """设置所有消费者"""
       233 -          logger.info("设置优先级消费者...")
       232 +          """设置所有消费者 - 委托给JetStreamClient的配置驱动方法"""
       233 +          logger.info("使用JetStreamClient的配置驱动架构设置优先级消费者...")
       234
       235 -          for strategy in self.consumer_strategies:
       236 -              if not strategy.enabled:
       237 -                  continue
       235 +          try:
       236 +              # 委托给JetStreamClient的完全配置驱动方法
       237 +              success = await self.jetstream.setup_priority_consumers()
       238 +
       239 +              if success:
       240 +                  logger.info("✅ 优先级消费者设置成功 (通过JetStreamClient)")
       241 +              else:
       242 +                  logger.error("❌ 优先级消费者设置失败")
       243
       244 -              try:
       245 -                  # 根据模式创建不同类型的消费者
       246 -                  if strategy.mode == "broadcast":
       247 -                      success = await self._create_broadcast_consumer(strategy)
       248 -                  elif strategy.mode == "queue_group":
       249 -                      success = await self._create_queue_consumer(strategy)
       250 -                  elif strategy.mode == "pull":
       251 -                      success = await self._create_pull_consumer(strategy)
       252 -                  else:
       253 -                      logger.warning(f"未知消费者模式: {strategy.mode}")
       254 -                      continue
       255 -
       256 -                  if success:
       244 +          except Exception as e:
       245 +              logger.error(f"设置消费者失败: {e}")
       246                        self.active_consumers[strategy.name] = strategy
       247                        logger.info(f"✅ 消费者创建成功: {strategy.name} ({strategy.mode})")
       248                    else:


✻ Thinking…

  还有一些残留的代码需要清理。让我继续清理：


● Read(src/messaging/nats_manager.py)

  ⎿  Read 20 lines (ctrl+r to expand)


✻ Thinking…

  我看到还有更多残留的代码。我需要清理所有这些旧的消费者创建方法，因为它们现在都不需要了。让我找到整个方法的结束并删除这些残留代码
  。


● Update(src/messaging/nats_manager.py)

  ⎿  Updated src/messaging/nats_manager.py with 7 removals
       243
       244            except Exception as e:
       245                logger.error(f"设置消费者失败: {e}")
       246 -                      self.active_consumers[strategy.name] = strategy
       247 -                      logger.info(f"✅ 消费者创建成功: {strategy.name} ({strategy.mode})")
       248 -                  else:
       249 -                      logger.warning(f"⚠️  消费者创建失败: {strategy.name}")
       250 -
       251 -              except Exception as e:
       252 -                  logger.error(f"创建消费者失败 {strategy.name}: {e}")
       246
       247        async def _create_broadcast_consumer(self, strategy: ConsumerStrategyConfig) -> bool:
       248            """创建广播模式消费者 - 每个实例都处理"""