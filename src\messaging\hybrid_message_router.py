#!/usr/bin/env python3
"""
混合消息路由器 - 替代src/distributed/message_router.py的精简版本
智能路由：本地直达，跨主机NATS
集成MT5 API速率限制处理
"""

import asyncio
import time
from typing import Dict, Any, Optional, List, Set, Callable
from dataclasses import dataclass
from datetime import datetime
import json

from .nats_manager import NATSManager
from .priority_queue import MessagePriority, PriorityAnalyzer, get_priority_queue
from ..utils.logger import get_logger

logger = get_logger(__name__)


@dataclass
class RouteRule:
    """路由规则"""
    source: str
    target: str
    host_id: str
    priority: MessagePriority
    route_type: str  # "local", "remote", "broadcast"
    enabled: bool = True


@dataclass
class RateLimitConfig:
    """API速率限制配置"""
    max_calls_per_second: int = 80
    burst_size: int = 10
    window_size: int = 1  # 秒
    cooldown_period: int = 5  # 秒


class HybridMessageRouter:
    """
    混合消息路由器 - 替代src/distributed/message_router.py的精简版本
    
    核心功能：
    1. 智能路由决策（本 地直达 vs 跨主机NATS）
    2. MT5 API速率限制处理
    3. 优先级感知路由
    4. 批量消息处理优化
    5. 故障转移和重试机制
    """
    
    def __init__(self, nats_manager: NATSManager):
        self.nats_manager = nats_manager
        self.host_id = nats_manager.host_id
        self.running = False
        
        # 路由表
        self.local_routes: Dict[str, str] = {}  # account_id -> local_target
        self.cross_host_routes: Dict[str, str] = {}  # account_id -> target_host_id
        self.route_rules: List[RouteRule] = []
        
        # 优先级队列集成
        self.priority_queue = get_priority_queue()
        
        # API速率限制
        self.rate_limits: Dict[str, RateLimitConfig] = {}  # account_id -> config
        self.api_call_history: Dict[str, List[float]] = {}  # account_id -> [timestamps]
        self.cooldown_accounts: Set[str] = set()
        
        # 消息处理器
        self.message_handlers: Dict[str, Callable] = {}
        
        # 性能统计
        self.stats = {
            'local_routes': 0,
            'remote_routes': 0,
            'broadcast_routes': 0,
            'rate_limited_calls': 0,
            'failed_routes': 0,
            'total_messages': 0,
            'avg_route_time_ms': 0.0,
            'last_activity': None
        }
        
        logger.info(f"✅ 混合消息路由器初始化完成 - 主机: {self.host_id}")
    
    async def start(self) -> bool:
        """启动路由器"""
        try:
            self.running = True
            
            # 初始化默认路由规则
            self._initialize_default_routes()
            
            # 启动速率限制清理任务
            asyncio.create_task(self._rate_limit_cleaner_task())
            
            logger.info("🚀 混合消息路由器启动成功")
            return True
            
        except Exception as e:
            logger.error(f"启动混合路由器失败: {e}")
            return False
    
    def _initialize_default_routes(self):
        """初始化默认路由规则"""
        # 本地路由规则示例
        self.route_rules.extend([
            RouteRule(
                source="monitor",
                target="executor", 
                host_id=self.host_id,
                priority=MessagePriority.HIGH,
                route_type="local"
            ),
            RouteRule(
                source="coordinator",
                target="any",
                host_id="*",
                priority=MessagePriority.NORMAL,
                route_type="broadcast"
            )
        ])
        
        logger.info(f"默认路由规则已加载: {len(self.route_rules)} 条")
    
    async def route_signal(self, signal_data: Dict[str, Any]) -> bool:
        """智能路由信号 - 核心路由决策方法"""
        start_time = time.perf_counter()
        
        try:
            # 更新统计
            self.stats['total_messages'] += 1
            self.stats['last_activity'] = time.time()
            
            # 解析路由信息
            target_host = signal_data.get('target_host_id')
            target_account = signal_data.get('target_account') 
            source_type = signal_data.get('source_type', 'unknown')
            
            logger.debug(f"路由信号: {source_type} -> {target_account}@{target_host}")
            
            # 路由决策
            if self._should_route_locally(target_host, target_account):
                # 本地路由 - 直接使用优先级队列
                success = await self._route_local(signal_data)
                if success:
                    self.stats['local_routes'] += 1
            elif target_host and target_host != self.host_id:
                # 跨主机路由 - 使用NATS
                success = await self._route_remote(signal_data, target_host)
                if success:
                    self.stats['remote_routes'] += 1
            else:
                # 广播路由 - 发送到所有相关节点
                success = await self._route_broadcast(signal_data)
                if success:
                    self.stats['broadcast_routes'] += 1
            
            # 更新性能统计
            if not success:
                self.stats['failed_routes'] += 1
            
            route_time = (time.perf_counter() - start_time) * 1000
            self._update_avg_route_time(route_time)
            
            return success
            
        except Exception as e:
            logger.error(f"信号路由失败: {e}")
            self.stats['failed_routes'] += 1
            return False
    
    def _should_route_locally(self, target_host: str, target_account: str) -> bool:
        """判断是否应该本地路由"""
        # 如果没有指定目标主机，或者目标主机是当前主机
        if not target_host or target_host == self.host_id:
            return True
        
        # 检查账户是否在本地路由表中
        if target_account in self.local_routes:
            return True
        
        return False
    
    async def _route_local(self, signal_data: Dict[str, Any]) -> bool:
        """本地路由 - 直接使用优先级队列，最低延迟"""
        try:
            # 分析优先级
            priority = self._analyze_message_priority(signal_data)
            
            # 检查API速率限制
            target_account = signal_data.get('target_account')
            if target_account and not await self._check_rate_limit(target_account):
                # API速率限制触发，降级处理
                return await self._handle_rate_limited_message(signal_data, priority)
            
            # 直接入队，无需网络传输
            success = await self.priority_queue.enqueue(priority, signal_data)
            
            if success:
                logger.debug(f"本地路由成功: {priority.name} -> {target_account}")
            
            return success
            
        except Exception as e:
            logger.error(f"本地路由失败: {e}")
            return False
    
    async def _route_remote(self, signal_data: Dict[str, Any], target_host: str) -> bool:
        """跨主机路由 - 使用NATS分层流"""
        try:
            # 设置目标主机信息
            signal_data['target_host_id'] = target_host
            signal_data['source_host_id'] = self.host_id
            
            # 使用NATS管理器发布
            success = await self.nats_manager.publish_with_priority(signal_data)
            
            if success:
                logger.debug(f"跨主机路由成功: {self.host_id} -> {target_host}")
            
            return success
            
        except Exception as e:
            logger.error(f"跨主机路由失败: {e}")
            return False
    
    async def _route_broadcast(self, signal_data: Dict[str, Any]) -> bool:
        """广播路由 - 发送到所有相关节点"""
        try:
            # 广播到本地
            local_success = await self._route_local(signal_data.copy())
            
            # 广播到远程节点（如果有配置）
            remote_success = True
            if self.cross_host_routes:
                broadcast_data = signal_data.copy()
                broadcast_data['broadcast'] = True
                remote_success = await self.nats_manager.publish_with_priority(broadcast_data)
            
            success = local_success and remote_success
            if success:
                logger.debug("广播路由成功")
            
            return success
            
        except Exception as e:
            logger.error(f"广播路由失败: {e}")
            return False
    
    def _analyze_message_priority(self, signal_data: Dict[str, Any]) -> MessagePriority:
        """分析消息优先级"""
        try:
            # 使用现有的优先级分析器
            method = signal_data.get('command_type', 'unknown')
            priority = PriorityAnalyzer.get_command_priority(method, signal_data)
            
            # 根据消息类型调整优先级
            if signal_data.get('urgent', False):
                priority = MessagePriority.CRITICAL
            elif signal_data.get('broadcast', False):
                priority = MessagePriority.LOW  # 广播消息降低优先级
            
            return priority
            
        except Exception as e:
            logger.warning(f"优先级分析失败，使用默认: {e}")
            return MessagePriority.NORMAL
    
    async def _check_rate_limit(self, account_id: str) -> bool:
        """检查API速率限制"""
        try:
            if account_id in self.cooldown_accounts:
                return False
            
            current_time = time.time()
            
            # 获取或创建速率限制配置
            if account_id not in self.rate_limits:
                self.rate_limits[account_id] = RateLimitConfig()
            
            config = self.rate_limits[account_id]
            
            # 获取调用历史
            if account_id not in self.api_call_history:
                self.api_call_history[account_id] = []
            
            call_history = self.api_call_history[account_id]
            
            # 清理过期的调用记录
            window_start = current_time - config.window_size
            call_history[:] = [t for t in call_history if t >= window_start]
            
            # 检查是否超过速率限制
            if len(call_history) >= config.max_calls_per_second:
                logger.warning(f"API速率限制触发: {account_id} ({len(call_history)}/s)")
                self.stats['rate_limited_calls'] += 1
                
                # 将账户加入冷却列表
                self.cooldown_accounts.add(account_id)
                asyncio.create_task(self._remove_from_cooldown(account_id, config.cooldown_period))
                
                return False
            
            # 记录本次调用
            call_history.append(current_time)
            return True
            
        except Exception as e:
            logger.error(f"速率限制检查失败: {e}")
            return True  # 出错时允许通过
    
    async def _handle_rate_limited_message(self, signal_data: Dict[str, Any], 
                                         priority: MessagePriority) -> bool:
        """处理被速率限制的消息"""
        try:
            # 降级到低优先级队列，延迟处理
            signal_data['rate_limited'] = True
            signal_data['delayed'] = True
            
            # 使用低优先级入队
            success = await self.priority_queue.enqueue(MessagePriority.LOW, signal_data)
            
            if success:
                logger.debug("速率限制消息已降级处理")
            
            return success
            
        except Exception as e:
            logger.error(f"处理速率限制消息失败: {e}")
            return False
    
    async def _remove_from_cooldown(self, account_id: str, delay: int):
        """从冷却列表中移除账户"""
        await asyncio.sleep(delay)
        self.cooldown_accounts.discard(account_id)
        logger.debug(f"账户 {account_id} 冷却期结束")
    
    async def _rate_limit_cleaner_task(self):
        """定期清理速率限制历史数据"""
        while self.running:
            try:
                current_time = time.time()
                
                for account_id in list(self.api_call_history.keys()):
                    call_history = self.api_call_history[account_id]
                    
                    # 清理5分钟前的记录
                    cutoff_time = current_time - 300
                    call_history[:] = [t for t in call_history if t >= cutoff_time]
                    
                    # 如果历史记录为空，删除条目
                    if not call_history:
                        del self.api_call_history[account_id]
                
                await asyncio.sleep(60)  # 每分钟清理一次
                
            except Exception as e:
                logger.error(f"速率限制清理任务异常: {e}")
                await asyncio.sleep(60)
    
    def add_local_route(self, account_id: str, target: str):
        """添加本地路由"""
        self.local_routes[account_id] = target
        logger.info(f"添加本地路由: {account_id} -> {target}")
    
    def add_remote_route(self, account_id: str, target_host: str):
        """添加远程路由"""
        self.cross_host_routes[account_id] = target_host
        logger.info(f"添加远程路由: {account_id} -> {target_host}")
    
    def remove_route(self, account_id: str):
        """移除路由"""
        self.local_routes.pop(account_id, None)
        self.cross_host_routes.pop(account_id, None)
        logger.info(f"移除路由: {account_id}")
    
    def set_rate_limit(self, account_id: str, config: RateLimitConfig):
        """设置账户的API速率限制"""
        self.rate_limits[account_id] = config
        logger.info(f"设置速率限制: {account_id} - {config.max_calls_per_second}/s")
    
    def register_message_handler(self, message_type: str, handler: Callable):
        """注册消息处理器"""
        self.message_handlers[message_type] = handler
        logger.info(f"注册消息处理器: {message_type}")
    
    async def handle_message(self, message_type: str, message_data: Dict[str, Any]) -> bool:
        """处理消息"""
        if message_type in self.message_handlers:
            try:
                handler = self.message_handlers[message_type]
                return await handler(message_data)
            except Exception as e:
                logger.error(f"消息处理器异常: {message_type} - {e}")
                return False
        else:
            # 默认使用路由处理
            return await self.route_signal(message_data)
    
    def _update_avg_route_time(self, route_time: float):
        """更新平均路由时间"""
        current_avg = self.stats['avg_route_time_ms']
        total_messages = self.stats['total_messages']
        
        if total_messages > 0:
            self.stats['avg_route_time_ms'] = (
                (current_avg * (total_messages - 1) + route_time) / total_messages
            )
    
    async def stop(self):
        """停止路由器"""
        if not self.running:
            return
        
        try:
            self.running = False
            
            # 清理资源
            self.route_rules.clear()
            self.local_routes.clear()
            self.cross_host_routes.clear()
            self.message_handlers.clear()
            self.api_call_history.clear()
            self.cooldown_accounts.clear()
            
            logger.info("🛑 混合消息路由器已停止")
            
        except Exception as e:
            logger.error(f"停止混合路由器失败: {e}")
    
    def get_status(self) -> Dict[str, Any]:
        """获取路由器状态"""
        return {
            'running': self.running,
            'host_id': self.host_id,
            'routes': {
                'local_routes': len(self.local_routes),
                'remote_routes': len(self.cross_host_routes),
                'route_rules': len(self.route_rules)
            },
            'rate_limiting': {
                'tracked_accounts': len(self.api_call_history),
                'cooldown_accounts': len(self.cooldown_accounts),
                'rate_limited_calls': self.stats['rate_limited_calls']
            },
            'performance': {
                'total_messages': self.stats['total_messages'],
                'local_routes': self.stats['local_routes'],
                'remote_routes': self.stats['remote_routes'],
                'broadcast_routes': self.stats['broadcast_routes'],
                'failed_routes': self.stats['failed_routes'],
                'success_rate': self._calculate_success_rate(),
                'avg_route_time_ms': self.stats['avg_route_time_ms']
            },
            'message_handlers': list(self.message_handlers.keys())
        }
    
    def _calculate_success_rate(self) -> float:
        """计算成功率"""
        total = self.stats['total_messages']
        if total == 0:
            return 0.0
        
        failed = self.stats['failed_routes']
        return ((total - failed) / total) * 100.0
    
    def get_performance_metrics(self) -> Dict[str, Any]:
        """获取性能指标"""
        total_routes = (
            self.stats['local_routes'] + 
            self.stats['remote_routes'] + 
            self.stats['broadcast_routes']
        )
        
        return {
            'routing_distribution': {
                'local_percentage': (self.stats['local_routes'] / max(total_routes, 1)) * 100,
                'remote_percentage': (self.stats['remote_routes'] / max(total_routes, 1)) * 100,
                'broadcast_percentage': (self.stats['broadcast_routes'] / max(total_routes, 1)) * 100
            },
            'efficiency_metrics': {
                'success_rate': self._calculate_success_rate(),
                'avg_route_time_ms': self.stats['avg_route_time_ms'],
                'rate_limit_ratio': (self.stats['rate_limited_calls'] / max(self.stats['total_messages'], 1)) * 100
            },
            'capacity_utilization': {
                'active_routes': len(self.local_routes) + len(self.cross_host_routes),
                'rate_limit_active': len(self.cooldown_accounts),
                'message_handlers_count': len(self.message_handlers)
            }
        }


class MT5APIRateLimiter:
    """MT5 API专用速率限制器"""
    
    def __init__(self):
        self.account_limits: Dict[str, RateLimitConfig] = {}
        self.call_counters: Dict[str, int] = {}
        self.last_reset_time: Dict[str, float] = {}
        
    def set_account_limit(self, account_id: str, max_calls_per_second: int = 80):
        """设置账户API调用限制"""
        self.account_limits[account_id] = RateLimitConfig(
            max_calls_per_second=max_calls_per_second
        )
        self.call_counters[account_id] = 0
        self.last_reset_time[account_id] = time.time()
    
    async def acquire_permission(self, account_id: str) -> bool:
        """获取API调用许可"""
        if account_id not in self.account_limits:
            # 未配置限制的账户默认允许
            return True
        
        current_time = time.time()
        last_reset = self.last_reset_time.get(account_id, 0)
        
        # 检查是否需要重置计数器
        if current_time - last_reset >= 1.0:
            self.call_counters[account_id] = 0
            self.last_reset_time[account_id] = current_time
        
        # 检查是否超过限制
        config = self.account_limits[account_id]
        if self.call_counters[account_id] >= config.max_calls_per_second:
            return False
        
        # 增加计数器
        self.call_counters[account_id] += 1
        return True
    
    def get_remaining_calls(self, account_id: str) -> int:
        """获取剩余调用次数"""
        if account_id not in self.account_limits:
            return 999  # 未限制账户返回大数值
        
        config = self.account_limits[account_id]
        used = self.call_counters.get(account_id, 0)
        return max(0, config.max_calls_per_second - used)