#!/usr/bin/env python3
"""
RPC修复验证测试
验证RPC主题匹配和请求格式修复是否成功
"""

import asyncio
import sys
import time
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# 导入组件
try:
    from src.core.mt5_rpc_client import MT5RPCClient
    from src.core.mt5_request_handler import MT5<PERSON>e<PERSON><PERSON>andler
    from src.messaging.jetstream_client import JetStreamClient
    
    print("RPC修复验证组件导入成功")
    
except ImportError as e:
    print(f"导入组件失败: {e}")
    sys.exit(1)


class TestMT5ProcessManager:
    """测试用的MT5ProcessManager"""
    
    def __init__(self):
        self.request_count = 0
        self.request_history = []
    
    def is_account_available(self, account_id: str) -> bool:
        """检查账户是否可用"""
        return True

    def is_account_connected(self, account_id: str) -> bool:
        """检查账户是否连接"""
        return True

    @property
    def process_status(self) -> str:
        """获取进程状态"""
        return "running"
    
    async def handle_request(self, request_data):
        """处理请求"""
        self.request_count += 1
        self.request_history.append(request_data)
        
        # 支持新的请求格式 (account_id + command)
        command = request_data.get('command', request_data.get('method', 'unknown'))
        params = request_data.get('params', {})
        account_id = request_data.get('account_id', params.get('account_id', 'unknown'))
        
        print(f"  处理请求: {command} for {account_id}")
        
        # 模拟响应
        if command == 'get_account_info':
            return {
                "status": "success",
                "data": {
                    "account_id": account_id,
                    "balance": 10000.0,
                    "equity": 10000.0,
                    "margin": 0.0,
                    "free_margin": 10000.0,
                    "server": "Test-Demo",
                    "currency": "USD",
                    "leverage": 100
                },
                "timestamp": time.time()
            }
        elif command == 'get_positions':
            return {
                "status": "success",
                "data": {
                    "positions": [
                        {
                            "ticket": 1000001,
                            "symbol": "EURUSD",
                            "volume": 0.1,
                            "type": "BUY",
                            "price": 1.1234,
                            "profit": 10.5
                        }
                    ],
                    "count": 1
                },
                "timestamp": time.time()
            }
        elif command == 'send_order' or command == 'place_order':
            return {
                "status": "success",
                "data": {
                    "order_id": f"ORDER_{self.request_count:06d}",
                    "ticket": 2000000 + self.request_count,
                    "symbol": params.get('symbol', 'EURUSD'),
                    "volume": params.get('volume', 0.1),
                    "price": params.get('price', 1.1234),
                    "execution_time": time.time()
                }
            }
        else:
            return {
                "status": "error",
                "error": f"Unknown command: {command}",
                "timestamp": time.time()
            }


async def test_rpc_fix():
    """测试RPC修复"""
    print("开始RPC修复验证测试...")
    
    try:
        # 创建JetStream客户端
        config = {
            'servers': ['nats://localhost:4222'],
            'stream_name': 'RPC_FIX_TEST',
            'subjects': ['RPC.FIX.>']
        }
        
        client = JetStreamClient(config)
        connected = await client.connect()
        
        if not connected:
            print("  无法连接到NATS服务器")
            return False
        
        print("  JetStream连接成功")
        
        # 创建测试管理器
        test_manager = TestMT5ProcessManager()
        
        # 创建RPC组件
        rpc_client = MT5RPCClient(client)
        rpc_handler = MT5RequestHandler(client, test_manager)
        
        print("  RPC组件创建成功")
        
        # 启动RPC处理器
        await rpc_handler.start()
        print("  RPC处理器启动成功")
        
        # 等待一下确保订阅生效
        await asyncio.sleep(1)
        
        # 测试RPC调用
        test_calls = [
            ('get_account_info', {'account_id': 'TEST_001'}),
            ('get_positions', {'account_id': 'TEST_001'}),
            ('send_order', {
                'account_id': 'TEST_001',
                'symbol': 'EURUSD',
                'action': 'BUY',
                'volume': 0.1,
                'price': 1.1234
            })
        ]
        
        successful_calls = 0
        total_calls = len(test_calls)
        
        for i, (method, params) in enumerate(test_calls):
            print(f"  测试RPC调用 {i+1}: {method}")
            
            try:
                response = await rpc_client.call_rpc(
                    account_id='TEST_001',
                    method=method,
                    params=params,
                    timeout=5.0
                )
                
                if response and response.get('status') == 'success':
                    print(f"    成功: {response.get('data', {}).get('account_id', 'N/A')}")
                    successful_calls += 1
                else:
                    print(f"    失败: {response}")
                
            except Exception as e:
                print(f"    异常: {e}")
        
        # 停止RPC处理器
        await rpc_handler.stop()
        
        # 断开连接
        await client.disconnect()
        
        # 计算成功率
        success_rate = (successful_calls / total_calls) * 100
        
        print(f"\nRPC修复验证结果:")
        print(f"  总调用数: {total_calls}")
        print(f"  成功调用数: {successful_calls}")
        print(f"  成功率: {success_rate:.1f}%")
        print(f"  处理器处理请求数: {test_manager.request_count}")
        
        # 检查RPC客户端指标
        metrics = rpc_client.get_performance_metrics()
        print(f"  RPC客户端指标:")
        print(f"    总调用: {metrics['overview']['total_calls']}")
        print(f"    成功调用: {metrics['overview']['successful_calls']}")
        print(f"    失败调用: {metrics['overview']['failed_calls']}")
        print(f"    客户端成功率: {metrics['overview']['success_rate_percent']:.1f}%")
        
        # 成功条件：成功率 >= 80%
        return success_rate >= 80.0
        
    except Exception as e:
        print(f"  RPC修复验证测试失败: {e}")
        return False


async def main():
    """主函数"""
    print("RPC修复验证测试")
    print("=" * 40)
    
    success = await test_rpc_fix()
    
    if success:
        print("\nRPC修复验证成功!")
        print("主题匹配和请求格式问题已解决")
        return 0
    else:
        print("\nRPC修复验证失败")
        print("需要进一步调试")
        return 1


if __name__ == '__main__':
    result = asyncio.run(main())
    sys.exit(result)
