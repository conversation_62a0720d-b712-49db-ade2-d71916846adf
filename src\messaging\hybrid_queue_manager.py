#!/usr/bin/env python3
"""
混合消息队列管理器
实现多后端双写备份、自动故障转移和智能路由切换
消除NATS单点依赖，提供高可用性保障
"""

import asyncio
import time
from enum import Enum
from typing import Dict, Any, Optional, List, Callable, Union
from dataclasses import dataclass, field

from .message_queue_interface import (
    MessageQueueInterface, QueueConfig, QueueStatus, QueueBackendType, QueueManager
)
from .message_types import MessageEnvelope
from .priority_queue import MessagePriority
from .nats_message_queue import NATSMessageQueue
from .redis_message_queue import RedisMessageQueue
from .local_message_queue import LocalMessageQueue
from ..utils.logger import get_logger
import json

logger = get_logger(__name__)


def encode_message(data: Any) -> bytes:
    """统一消息编码"""
    if isinstance(data, bytes):
        return data
    elif isinstance(data, str):
        return data.encode('utf-8')
    else:
        return json.dumps(data, ensure_ascii=False).encode('utf-8')


def decode_message(data: bytes) -> Any:
    """统一消息解码"""
    if isinstance(data, str):
        data = data.encode('utf-8')
    
    try:
        text = data.decode('utf-8')
        return json.loads(text)
    except (json.JSONDecodeError, UnicodeDecodeError):
        return data.decode('utf-8', errors='ignore')


class FailoverStrategy(Enum):
    """故障转移策略"""
    IMMEDIATE = "immediate"      # 立即切换
    DELAYED = "delayed"          # 延迟切换（等待恢复）
    MANUAL = "manual"           # 手动切换
    CIRCUIT_BREAKER = "circuit_breaker"  # 熔断器模式


class RoutingMode(Enum):
    """路由模式"""
    DUAL_WRITE = "dual_write"           # 双写模式（主+备）
    LOAD_BALANCE = "load_balance"       # 负载均衡
    PRIMARY_ONLY = "primary_only"       # 仅主队列
    BACKUP_ONLY = "backup_only"         # 仅备份队列
    LOCAL_FALLBACK = "local_fallback"   # 本地降级


@dataclass
class BackendHealth:
    """后端健康状态"""
    backend_type: QueueBackendType
    is_healthy: bool = False
    last_check: float = field(default_factory=time.time)
    consecutive_failures: int = 0
    avg_latency_ms: float = 0.0
    error_rate: float = 0.0
    last_error: Optional[str] = None


@dataclass
class HybridQueueConfig:
    """混合队列配置"""
    # 后端配置
    primary_backend: QueueBackendType = QueueBackendType.NATS
    backup_backends: List[QueueBackendType] = field(default_factory=lambda: [QueueBackendType.REDIS_STREAMS])
    local_fallback: bool = True
    
    # 故障转移配置
    failover_strategy: FailoverStrategy = FailoverStrategy.CIRCUIT_BREAKER
    health_check_interval: float = 5.0  # 秒
    failure_threshold: int = 3           # 连续失败次数阈值
    recovery_threshold: int = 2          # 恢复检查次数
    circuit_breaker_timeout: float = 30.0  # 熔断器超时时间
    
    # 性能配置
    max_latency_ms: float = 100.0       # 最大延迟阈值
    max_error_rate: float = 0.05        # 最大错误率阈值
    sync_timeout: float = 2.0           # 同步超时时间
    
    # 双写配置
    enable_dual_write: bool = True      # 是否启用双写
    dual_write_async: bool = True       # 异步双写
    ignore_backup_failures: bool = True # 忽略备份失败
    
    # 后端特定配置
    backend_configs: Dict[QueueBackendType, Dict[str, Any]] = field(default_factory=dict)


class HybridQueueManager:
    """
    混合消息队列管理器
    
    功能特性：
    1. 多后端支持：NATS + Redis Streams + 本地内存
    2. 双写备份：主备队列同时写入，确保数据安全
    3. 自动故障转移：智能检测故障并切换到可用后端
    4. 熔断器模式：防止级联故障，自动恢复
    5. 性能监控：实时监控延迟、错误率等指标
    6. 优雅降级：网络故障时自动切换到本地模式
    """
    
    def __init__(self, config: HybridQueueConfig):
        self.config = config
        self.current_routing_mode = RoutingMode.DUAL_WRITE
        
        # 后端实例管理
        self.backends: Dict[QueueBackendType, MessageQueueInterface] = {}
        self.backend_health: Dict[QueueBackendType, BackendHealth] = {}
        
        # 当前活跃后端
        self.primary_backend: Optional[QueueBackendType] = None
        self.backup_backends: List[QueueBackendType] = []
        self.fallback_backend: Optional[QueueBackendType] = None
        
        # 订阅管理
        self._subscriptions: Dict[str, Dict[str, Any]] = {}  # subject -> subscription_info
        
        # 监控和任务
        self._health_check_task: Optional[asyncio.Task] = None
        self._metrics_task: Optional[asyncio.Task] = None
        self._running = False
        
        # 熔断器状态
        self._circuit_breakers: Dict[QueueBackendType, Dict[str, Any]] = {}
        
        # 性能统计
        self.stats = {
            'total_messages': 0,
            'dual_write_success': 0,
            'dual_write_partial': 0,
            'failover_count': 0,
            'circuit_breaks': 0,
            'backend_switches': 0
        }
    
    async def initialize(self) -> bool:
        """初始化混合队列管理器"""
        try:
            logger.info("初始化混合消息队列管理器...")
            
            # 1. 创建后端实例
            await self._create_backend_instances()
            
            # 2. 连接后端
            await self._connect_backends()
            
            # 3. 选择初始后端
            await self._select_initial_backends()
            
            # 4. 启动监控任务
            await self._start_monitoring()
            
            self._running = True
            logger.info(f"混合队列管理器初始化成功 - 主后端: {self.primary_backend}")
            return True
            
        except Exception as e:
            logger.error(f"混合队列管理器初始化失败: {e}")
            return False
    
    async def shutdown(self):
        """关闭混合队列管理器"""
        try:
            self._running = False
            
            # 停止监控任务
            if self._health_check_task:
                self._health_check_task.cancel()
            if self._metrics_task:
                self._metrics_task.cancel()
            
            # 等待任务完成
            tasks = [t for t in [self._health_check_task, self._metrics_task] if t]
            if tasks:
                await asyncio.gather(*tasks, return_exceptions=True)
            
            # 断开所有后端
            for backend in self.backends.values():
                try:
                    await backend.disconnect()
                except Exception as e:
                    logger.warning(f"断开后端连接失败: {e}")
            
            logger.info("混合队列管理器已关闭")
            
        except Exception as e:
            logger.error(f"关闭混合队列管理器失败: {e}")
    
    async def publish(
        self, 
        subject: str, 
        message: MessageEnvelope,
        priority: MessagePriority = MessagePriority.REALTIME_QUERY
    ) -> bool:
        """发布消息（支持双写和故障转移）"""
        if not self._running:
            logger.error("混合队列管理器未运行")
            return False
        
        start_time = time.perf_counter()
        
        try:
            # 根据当前路由模式发布消息
            if self.current_routing_mode == RoutingMode.DUAL_WRITE:
                success = await self._dual_write_publish(subject, message, priority)
            elif self.current_routing_mode == RoutingMode.PRIMARY_ONLY:
                success = await self._single_backend_publish(
                    self.primary_backend, subject, message, priority
                )
            elif self.current_routing_mode == RoutingMode.LOCAL_FALLBACK:
                success = await self._local_fallback_publish(subject, message, priority)
            else:
                success = await self._adaptive_publish(subject, message, priority)
            
            # 更新统计
            self.stats['total_messages'] += 1
            
            # 记录延迟
            latency_ms = (time.perf_counter() - start_time) * 1000
            if latency_ms > self.config.max_latency_ms:
                logger.warning(f"消息发布延迟过高: {latency_ms:.2f}ms")
            
            return success
            
        except Exception as e:
            logger.error(f"混合发布消息失败: {e}")
            # 尝试降级到本地队列
            return await self._emergency_local_publish(subject, message, priority)
    
    async def publish_batch(
        self, 
        messages: List[tuple[str, MessageEnvelope, MessagePriority]]
    ) -> int:
        """批量发布消息"""
        if not messages:
            return 0
        
        if self.current_routing_mode == RoutingMode.DUAL_WRITE:
            return await self._dual_write_batch_publish(messages)
        else:
            # 单后端批量发布
            backend_type = self._get_active_backend()
            if backend_type and backend_type in self.backends:
                return await self.backends[backend_type].publish_batch(messages)
            else:
                # 降级到本地
                return await self._local_batch_publish(messages)
    
    async def subscribe(
        self, 
        subject: str, 
        callback: Callable[[MessageEnvelope], None],
        queue_group: Optional[str] = None
    ) -> bool:
        """订阅消息（多后端订阅）"""
        if not self._running:
            return False
        
        try:
            subscription_info = {
                'callback': callback,
                'queue_group': queue_group,
                'active_backends': set()
            }
            
            # 在所有健康的后端上订阅
            success_count = 0
            for backend_type, backend in self.backends.items():
                if self._is_backend_healthy(backend_type):
                    try:
                        if await backend.subscribe(subject, callback, queue_group):
                            subscription_info['active_backends'].add(backend_type)
                            success_count += 1
                    except Exception as e:
                        logger.warning(f"后端{backend_type}订阅失败: {e}")
            
            if success_count > 0:
                self._subscriptions[subject] = subscription_info
                logger.info(f"订阅成功: {subject} (活跃后端: {success_count})")
                return True
            else:
                logger.error(f"所有后端订阅失败: {subject}")
                return False
                
        except Exception as e:
            logger.error(f"混合订阅失败: {e}")
            return False
    
    async def unsubscribe(self, subject: str) -> bool:
        """取消订阅"""
        if subject not in self._subscriptions:
            return True
        
        try:
            subscription_info = self._subscriptions[subject]
            success_count = 0
            
            for backend_type in subscription_info['active_backends']:
                if backend_type in self.backends:
                    try:
                        if await self.backends[backend_type].unsubscribe(subject):
                            success_count += 1
                    except Exception as e:
                        logger.warning(f"后端{backend_type}取消订阅失败: {e}")
            
            del self._subscriptions[subject]
            logger.info(f"取消订阅成功: {subject} (后端: {success_count})")
            return success_count > 0
            
        except Exception as e:
            logger.error(f"混合取消订阅失败: {e}")
            return False
    
    async def request(
        self, 
        subject: str, 
        message: MessageEnvelope,
        timeout_ms: int = 5000
    ) -> Optional[MessageEnvelope]:
        """请求-响应模式"""
        # 使用最佳性能的后端进行请求
        backend_type = self._get_best_backend_for_request()
        if backend_type and backend_type in self.backends:
            return await self.backends[backend_type].request(subject, message, timeout_ms)
        return None
    
    # 私有方法实现
    
    async def _create_backend_instances(self):
        """创建后端实例"""
        # 创建主后端
        if self.config.primary_backend == QueueBackendType.NATS:
            nats_config = QueueConfig(
                backend_type=QueueBackendType.NATS,
                connection_params=self.config.backend_configs.get(QueueBackendType.NATS, {})
            )
            self.backends[QueueBackendType.NATS] = NATSMessageQueue(nats_config)
        
        # 创建备份后端
        for backend_type in self.config.backup_backends:
            if backend_type == QueueBackendType.REDIS_STREAMS:
                redis_config = QueueConfig(
                    backend_type=QueueBackendType.REDIS_STREAMS,
                    connection_params=self.config.backend_configs.get(QueueBackendType.REDIS_STREAMS, {})
                )
                self.backends[QueueBackendType.REDIS_STREAMS] = RedisMessageQueue(redis_config)
        
        # 创建本地后备
        if self.config.local_fallback:
            local_config = QueueConfig(
                backend_type=QueueBackendType.LOCAL_MEMORY,
                connection_params={}
            )
            self.backends[QueueBackendType.LOCAL_MEMORY] = LocalMessageQueue(local_config)
        
        # 初始化健康状态
        for backend_type in self.backends.keys():
            self.backend_health[backend_type] = BackendHealth(backend_type=backend_type)
    
    async def _connect_backends(self):
        """连接所有后端"""
        connection_tasks = []
        
        for backend_type, backend in self.backends.items():
            async def connect_backend(bt, b):
                try:
                    success = await b.connect()
                    self.backend_health[bt].is_healthy = success
                    if success:
                        logger.info(f"后端连接成功: {bt}")
                    else:
                        logger.warning(f"后端连接失败: {bt}")
                except Exception as e:
                    logger.error(f"后端连接异常: {bt} - {e}")
                    self.backend_health[bt].is_healthy = False
            
            connection_tasks.append(connect_backend(backend_type, backend))
        
        await asyncio.gather(*connection_tasks, return_exceptions=True)
    
    async def _select_initial_backends(self):
        """选择初始后端"""
        # 选择主后端
        if (self.config.primary_backend in self.backends and 
            self.backend_health[self.config.primary_backend].is_healthy):
            self.primary_backend = self.config.primary_backend
        else:
            # 从健康的后端中选择
            for backend_type, health in self.backend_health.items():
                if health.is_healthy and backend_type != QueueBackendType.LOCAL_MEMORY:
                    self.primary_backend = backend_type
                    break
        
        # 选择备份后端
        self.backup_backends = [
            bt for bt in self.config.backup_backends 
            if bt in self.backends and self.backend_health[bt].is_healthy
        ]
        
        # 设置本地降级
        if (QueueBackendType.LOCAL_MEMORY in self.backends and 
            self.backend_health[QueueBackendType.LOCAL_MEMORY].is_healthy):
            self.fallback_backend = QueueBackendType.LOCAL_MEMORY
        
        logger.info(f"初始后端选择 - 主: {self.primary_backend}, 备: {self.backup_backends}")
    
    async def _start_monitoring(self):
        """启动监控任务"""
        self._health_check_task = asyncio.create_task(self._health_check_loop())
        self._metrics_task = asyncio.create_task(self._metrics_collection_loop())
    
    async def _health_check_loop(self):
        """健康检查循环"""
        while self._running:
            try:
                await self._perform_health_checks()
                await self._evaluate_failover()
                await asyncio.sleep(self.config.health_check_interval)
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"健康检查失败: {e}")
                await asyncio.sleep(1)
    
    async def _perform_health_checks(self):
        """执行健康检查"""
        for backend_type, backend in self.backends.items():
            try:
                start_time = time.perf_counter()
                is_healthy = await backend.health_check()
                latency = (time.perf_counter() - start_time) * 1000
                
                health = self.backend_health[backend_type]
                health.last_check = time.time()
                health.avg_latency_ms = (health.avg_latency_ms * 0.9) + (latency * 0.1)
                
                if is_healthy:
                    if not health.is_healthy:
                        logger.info(f"后端恢复健康: {backend_type}")
                    health.is_healthy = True
                    health.consecutive_failures = 0
                else:
                    health.consecutive_failures += 1
                    if health.is_healthy:
                        logger.warning(f"后端健康检查失败: {backend_type}")
                    
                    if health.consecutive_failures >= self.config.failure_threshold:
                        health.is_healthy = False
                        logger.error(f"后端标记为不健康: {backend_type}")
                        
            except Exception as e:
                logger.error(f"健康检查异常 {backend_type}: {e}")
                self.backend_health[backend_type].is_healthy = False
    
    async def _evaluate_failover(self):
        """评估是否需要故障转移"""
        # 检查主后端健康状态
        if (self.primary_backend and 
            not self._is_backend_healthy(self.primary_backend)):
            
            logger.warning(f"主后端不健康，执行故障转移: {self.primary_backend}")
            await self._perform_failover()
    
    async def _perform_failover(self):
        """执行故障转移"""
        old_primary = self.primary_backend
        new_primary = None
        
        # 从备份后端中选择新的主后端
        for backend_type in self.backup_backends:
            if self._is_backend_healthy(backend_type):
                new_primary = backend_type
                break
        
        # 如果没有健康的备份，尝试本地降级
        if not new_primary and self.fallback_backend:
            if self._is_backend_healthy(self.fallback_backend):
                new_primary = self.fallback_backend
                self.current_routing_mode = RoutingMode.LOCAL_FALLBACK
        
        if new_primary:
            self.primary_backend = new_primary
            if old_primary in self.backup_backends:
                self.backup_backends.remove(old_primary)
            
            self.stats['failover_count'] += 1
            self.stats['backend_switches'] += 1
            
            logger.info(f"故障转移完成: {old_primary} -> {new_primary}")
        else:
            logger.error("无可用后端，系统进入紧急模式")
            self.current_routing_mode = RoutingMode.LOCAL_FALLBACK
    
    async def _dual_write_publish(
        self, 
        subject: str, 
        message: MessageEnvelope, 
        priority: MessagePriority
    ) -> bool:
        """双写发布"""
        primary_success = False
        backup_success = False
        
        # 发布到主后端
        if self.primary_backend:
            primary_success = await self._single_backend_publish(
                self.primary_backend, subject, message, priority
            )
        
        # 发布到备份后端（异步或同步）
        if self.config.dual_write_async:
            # 异步双写
            asyncio.create_task(
                self._backup_write(subject, message, priority)
            )
            backup_success = True  # 异步模式认为备份成功
        else:
            # 同步双写
            backup_success = await self._backup_write(subject, message, priority)
        
        # 统计双写结果
        if primary_success and backup_success:
            self.stats['dual_write_success'] += 1
        elif primary_success or backup_success:
            self.stats['dual_write_partial'] += 1
        
        # 主后端成功即可认为发布成功（备份失败可以忽略）
        return primary_success or (backup_success and self.config.ignore_backup_failures)
    
    async def _backup_write(
        self, 
        subject: str, 
        message: MessageEnvelope, 
        priority: MessagePriority
    ) -> bool:
        """备份写入"""
        for backend_type in self.backup_backends:
            if self._is_backend_healthy(backend_type):
                try:
                    return await self._single_backend_publish(
                        backend_type, subject, message, priority
                    )
                except Exception as e:
                    logger.warning(f"备份写入失败 {backend_type}: {e}")
        return False
    
    async def _single_backend_publish(
        self, 
        backend_type: QueueBackendType, 
        subject: str, 
        message: MessageEnvelope, 
        priority: MessagePriority
    ) -> bool:
        """单后端发布"""
        if backend_type not in self.backends:
            return False
        
        try:
            return await self.backends[backend_type].publish(subject, message, priority)
        except Exception as e:
            logger.error(f"后端发布失败 {backend_type}: {e}")
            # 更新错误统计
            health = self.backend_health.get(backend_type)
            if health:
                health.consecutive_failures += 1
            return False
    
    def _is_backend_healthy(self, backend_type: QueueBackendType) -> bool:
        """检查后端是否健康"""
        health = self.backend_health.get(backend_type)
        if not health:
            return False
        
        return (health.is_healthy and 
                health.avg_latency_ms < self.config.max_latency_ms and
                health.error_rate < self.config.max_error_rate)
    
    def _get_active_backend(self) -> Optional[QueueBackendType]:
        """获取当前活跃后端"""
        if self.primary_backend and self._is_backend_healthy(self.primary_backend):
            return self.primary_backend
        
        for backend_type in self.backup_backends:
            if self._is_backend_healthy(backend_type):
                return backend_type
        
        return self.fallback_backend
    
    def _get_best_backend_for_request(self) -> Optional[QueueBackendType]:
        """获取请求最佳后端（延迟最低）"""
        best_backend = None
        best_latency = float('inf')
        
        for backend_type, health in self.backend_health.items():
            if (health.is_healthy and 
                backend_type != QueueBackendType.LOCAL_MEMORY and
                health.avg_latency_ms < best_latency):
                best_backend = backend_type
                best_latency = health.avg_latency_ms
        
        return best_backend or self.fallback_backend
    
    async def _metrics_collection_loop(self):
        """指标收集循环"""
        while self._running:
            try:
                # 收集各个后端的指标
                for backend_type, backend in self.backends.items():
                    try:
                        metrics = backend.get_metrics()
                        health = self.backend_health[backend_type]
                        
                        # 更新错误率
                        if metrics.messages_sent > 0:
                            health.error_rate = metrics.messages_failed / metrics.messages_sent
                        
                    except Exception as e:
                        logger.debug(f"收集后端指标失败 {backend_type}: {e}")
                
                await asyncio.sleep(10)  # 每10秒收集一次
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"指标收集失败: {e}")
                await asyncio.sleep(5)
    
    # 辅助方法
    
    async def _local_fallback_publish(
        self, 
        subject: str, 
        message: MessageEnvelope, 
        priority: MessagePriority
    ) -> bool:
        """本地降级发布"""
        if self.fallback_backend and self.fallback_backend in self.backends:
            return await self._single_backend_publish(
                self.fallback_backend, subject, message, priority
            )
        return False
    
    async def _emergency_local_publish(
        self, 
        subject: str, 
        message: MessageEnvelope, 
        priority: MessagePriority
    ) -> bool:
        """紧急本地发布"""
        logger.warning("进入紧急本地发布模式")
        return await self._local_fallback_publish(subject, message, priority)
    
    async def _adaptive_publish(
        self, 
        subject: str, 
        message: MessageEnvelope, 
        priority: MessagePriority
    ) -> bool:
        """自适应发布（根据当前状态选择最佳策略）"""
        active_backend = self._get_active_backend()
        if active_backend:
            return await self._single_backend_publish(
                active_backend, subject, message, priority
            )
        return False
    
    def get_status(self) -> Dict[str, Any]:
        """获取混合队列状态"""
        return {
            'running': self._running,
            'routing_mode': self.current_routing_mode.value,
            'primary_backend': self.primary_backend.value if self.primary_backend else None,
            'backup_backends': [b.value for b in self.backup_backends],
            'fallback_backend': self.fallback_backend.value if self.fallback_backend else None,
            'backend_health': {
                bt.value: {
                    'healthy': health.is_healthy,
                    'latency_ms': health.avg_latency_ms,
                    'error_rate': health.error_rate,
                    'consecutive_failures': health.consecutive_failures
                }
                for bt, health in self.backend_health.items()
            },
            'statistics': self.stats
        }