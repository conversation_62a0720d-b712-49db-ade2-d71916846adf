#!/usr/bin/env python3
"""
Protocol Buffers编解码器
高性能二进制序列化，替代JSON提升性能
"""
import time
import gzip
import hashlib
import threading
from typing import Dict, Any, Optional, Union, List
from dataclasses import dataclass
from collections import OrderedDict

try:
    import google.protobuf.message
    from google.protobuf.json_format import MessageToDict, ParseDict
    PROTOBUF_AVAILABLE = True
except ImportError:
    PROTOBUF_AVAILABLE = False

from ..utils.logger import get_logger
from .message_types import TradeSignal

logger = get_logger(__name__)


class LRUMessageCache:
    """LRU消息缓存 - 缓存解码结果以提升性能"""
    
    def __init__(self, max_size: int = 1000, ttl_seconds: int = 300):
        self.max_size = max_size
        self.ttl_seconds = ttl_seconds
        self.cache = OrderedDict()  # 保持插入顺序，实现LRU
        self.access_times = {}  # 记录访问时间用于TTL
        self.lock = threading.RLock()  # 线程安全
        
        # 缓存统计
        self.stats = {
            'hits': 0,
            'misses': 0,
            'evictions': 0,
            'expired': 0,
            'total_size': 0
        }
    
    def _generate_cache_key(self, data: bytes) -> str:
        """生成缓存键"""
        # 使用SHA256的前16位作为缓存键
        return hashlib.sha256(data).hexdigest()[:16]
    
    def _is_expired(self, key: str) -> bool:
        """检查缓存项是否过期"""
        if key not in self.access_times:
            return True
        
        age = time.time() - self.access_times[key]
        return age > self.ttl_seconds
    
    def _evict_expired(self):
        """清理过期的缓存项"""
        current_time = time.time()
        expired_keys = []
        
        for key, access_time in self.access_times.items():
            if current_time - access_time > self.ttl_seconds:
                expired_keys.append(key)
        
        for key in expired_keys:
            if key in self.cache:
                del self.cache[key]
            if key in self.access_times:
                del self.access_times[key]
            self.stats['expired'] += 1
    
    def _evict_lru(self):
        """清理最久未使用的缓存项"""
        while len(self.cache) >= self.max_size:
            # OrderedDict的FIFO特性：最早插入的在最前面
            oldest_key = next(iter(self.cache))
            del self.cache[oldest_key]
            if oldest_key in self.access_times:
                del self.access_times[oldest_key]
            self.stats['evictions'] += 1
    
    def get(self, data: bytes) -> Optional[Any]:
        """从缓存获取解码结果"""
        with self.lock:
            key = self._generate_cache_key(data)
            
            if key in self.cache and not self._is_expired(key):
                self.access_times[key] = time.time()
                
                # 移到最后（最近使用）
                value = self.cache.pop(key)
                self.cache[key] = value
                
                self.stats['hits'] += 1
                return value
            
            if key in self.cache:
                del self.cache[key]
                if key in self.access_times:
                    del self.access_times[key]
                self.stats['expired'] += 1
            
            self.stats['misses'] += 1
            return None
    
    def put(self, data: bytes, decoded_result: Any):
        """将解码结果放入缓存"""
        with self.lock:
            key = self._generate_cache_key(data)
            current_time = time.time()
            
            self._evict_expired()
            
            if len(self.cache) >= self.max_size:
                self._evict_lru()
            
            self.cache[key] = decoded_result
            self.access_times[key] = current_time
            
            self.stats['total_size'] = len(self.cache)
    
    def clear(self):
        """清空缓存"""
        with self.lock:
            self.cache.clear()
            self.access_times.clear()
            self.stats = {
                'hits': 0,
                'misses': 0,
                'evictions': 0,
                'expired': 0,
                'total_size': 0
            }
    
    def get_stats(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        with self.lock:
            total_requests = self.stats['hits'] + self.stats['misses']
            hit_rate = (self.stats['hits'] / total_requests * 100) if total_requests > 0 else 0
            
            return {
                **self.stats,
                'hit_rate_percent': hit_rate,
                'total_requests': total_requests,
                'cache_utilization_percent': (len(self.cache) / self.max_size * 100) if self.max_size > 0 else 0,
                'memory_estimate_kb': len(self.cache) * 0.5  # 粗略估算每项0.5KB
            }
    
    def get_health_status(self) -> Dict[str, Any]:
        """获取缓存健康状态"""
        stats = self.get_stats()
        health_score = 100
        
        hit_rate = stats['hit_rate_percent']
        if hit_rate < 50:
            health_score -= (50 - hit_rate)
        
        utilization = stats['cache_utilization_percent']
        if utilization > 90:
            health_score -= 10  # 接近满容量
        elif utilization < 20:
            health_score -= 5   # 利用率太低
        
        if health_score >= 90:
            status = "excellent"
        elif health_score >= 75:
            status = "good"
        elif health_score >= 50:
            status = "fair"
        else:
            status = "poor"
        
        return {
            "status": status,
            "score": max(0, min(100, health_score)),
            "recommendations": self._get_cache_recommendations(stats)
        }
    
    def _get_cache_recommendations(self, stats: Dict[str, Any]) -> List[str]:
        """获取缓存优化建议"""
        recommendations = []
        
        if stats['hit_rate_percent'] < 30:
            recommendations.append("缓存命中率过低，考虑增加缓存大小或调整TTL")
        
        if stats['cache_utilization_percent'] > 95:
            recommendations.append("缓存接近满容量，考虑增加最大缓存大小")
        
        if stats['expired'] > stats['hits']:
            recommendations.append("过期项目过多，考虑增加TTL时间")
        
        if stats['evictions'] > stats['hits'] * 0.1:
            recommendations.append("频繁LRU清理，建议增加缓存大小")
        
        return recommendations


@dataclass
class ProtobufConfig:
    """Protocol Buffers配置"""
    compression_enabled: bool = True
    compression_threshold: int = 1024  # 1KB
    compression_level: int = 6
    use_fallback_json: bool = True  # 当protobuf不可用时使用JSON

    cache_enabled: bool = True
    cache_max_size: int = 1000
    cache_ttl_seconds: int = 300


class ProtobufCodec:
    """Protocol Buffers编解码器"""
    
    def __init__(self, config: ProtobufConfig = None):
        self.config = config or ProtobufConfig()
        self.protobuf_available = PROTOBUF_AVAILABLE
        
        # 初始化LRU缓存
        if self.config.cache_enabled:
            self.cache = LRUMessageCache(
                max_size=self.config.cache_max_size,
                ttl_seconds=self.config.cache_ttl_seconds
            )
            logger.info(f"LRU消息缓存已启用 (大小: {self.config.cache_max_size}, TTL: {self.config.cache_ttl_seconds}s)")
        else:
            self.cache = None
            logger.info("LRU消息缓存已禁用")
        
        if not self.protobuf_available:
            logger.warning("Protocol Buffers不可用，将使用JSON作为后备方案")
        else:
            logger.info("Protocol Buffers编解码器初始化成功")
    
    def encode_trade_signal(self, signal: Union[TradeSignal, Dict[str, Any]]) -> bytes:
        """编码交易信号"""
        try:
            if not self.protobuf_available:
                return self._encode_json_fallback(signal)
            
            if isinstance(signal, TradeSignal):
                signal_dict = signal.__dict__
            else:
                signal_dict = signal
            
            pb_signal = self._create_trade_signal_pb(signal_dict)
            data = pb_signal.SerializeToString()
            
            if self.config.compression_enabled and len(data) > self.config.compression_threshold:
                data = gzip.compress(data, compresslevel=self.config.compression_level)
                data = b'\x01' + data
            else:
                data = b'\x00' + data
            
            return data
            
        except Exception as e:
            logger.error(f"Protocol Buffers编码失败: {e}")
            if self.config.use_fallback_json:
                return self._encode_json_fallback(signal)
            raise
    
    def decode_trade_signal(self, data: bytes) -> Optional[Dict[str, Any]]:
        """解码交易信号"""
        try:
            if self.cache:
                cached_result = self.cache.get(data)
                if cached_result is not None:
                    logger.debug("缓存命中：交易信号解码")
                    return cached_result
            
            if not self.protobuf_available:
                result = self._decode_json_fallback(data)
                if self.cache and result is not None:
                    self.cache.put(data, result)
                return result
            
            if not data or len(data) < 1:
                return None
            
            is_compressed = data[0] == 1
            payload = data[1:]
            
            if is_compressed:
                payload = gzip.decompress(payload)
            
            pb_signal = self._create_empty_trade_signal_pb()
            pb_signal.ParseFromString(payload)

            signal_dict = {
                'signal_id': pb_signal.signal_id,
                'account_id': pb_signal.account_id,
                'symbol': pb_signal.symbol,
                'action': pb_signal.action,
                'volume': pb_signal.volume,
                'price': pb_signal.price,
                'timestamp': pb_signal.timestamp,
                'ticket': pb_signal.ticket
            }
            
            if self.cache:
                self.cache.put(data, signal_dict)
            
            return signal_dict
            
        except Exception as e:
            logger.error(f"Protocol Buffers解码失败: {e}")
            if self.config.use_fallback_json:
                result = self._decode_json_fallback(data)
                if self.cache and result is not None:
                    self.cache.put(data, result)
                return result
            return None
    
    def encode_batch_signals(self, signals: List[Union[TradeSignal, Dict[str, Any]]]) -> bytes:
        """编码批量交易信号"""
        try:
            if not self.protobuf_available:
                return self._encode_json_fallback({"signals": signals})
            
            batch_pb = self._create_batch_message_pb()
            batch_pb.batch_id = f"batch_{int(time.time() * 1000)}"
            batch_pb.batch_timestamp = int(time.time() * 1000)
            batch_pb.batch_size = len(signals)
            
            for signal in signals:
                if isinstance(signal, TradeSignal):
                    signal_dict = signal.__dict__
                else:
                    signal_dict = signal
                
                pb_signal = batch_pb.signals.add()
                self._populate_trade_signal_pb(pb_signal, signal_dict)
            
            data = batch_pb.SerializeToString()
            
            if self.config.compression_enabled and len(data) > self.config.compression_threshold:
                data = gzip.compress(data, compresslevel=self.config.compression_level)
                batch_pb.is_compressed = True
                data = b'\x01' + data
            else:
                data = b'\x00' + data
            
            return data
            
        except Exception as e:
            logger.error(f"批量信号编码失败: {e}")
            if self.config.use_fallback_json:
                return self._encode_json_fallback({"signals": signals})
            raise
    
    def decode_batch_signals(self, data: bytes) -> Optional[List[Dict[str, Any]]]:
        """解码批量交易信号"""
        try:
            if self.cache:
                cached_result = self.cache.get(data)
                if cached_result is not None:
                    logger.debug("缓存命中：批量信号解码")
                    return cached_result
            
            if not self.protobuf_available:
                result = self._decode_json_fallback(data)
                batch_result = result.get("signals", []) if result else None
                if self.cache and batch_result is not None:
                    self.cache.put(data, batch_result)
                return batch_result
            
            if not data or len(data) < 1:
                return None
            
            is_compressed = data[0] == 1
            payload = data[1:]
            
            if is_compressed:
                payload = gzip.decompress(payload)
            
            batch_pb = self._create_batch_message_pb()
            batch_pb.ParseFromString(payload)

            signals = []
            for pb_signal in batch_pb.signals:
                signal_dict = {
                    'signal_id': pb_signal.signal_id,
                    'account_id': pb_signal.account_id,
                    'symbol': pb_signal.symbol,
                    'action': pb_signal.action,
                    'volume': pb_signal.volume,
                    'price': pb_signal.price,
                    'timestamp': pb_signal.timestamp,
                    'ticket': pb_signal.ticket
                }
                signals.append(signal_dict)
            
            if self.cache:
                self.cache.put(data, signals)
            
            return signals
            
        except Exception as e:
            logger.error(f"批量信号解码失败: {e}")
            if self.config.use_fallback_json:
                result = self._decode_json_fallback(data)
                batch_result = result.get("signals", []) if result else None
                if self.cache and batch_result is not None:
                    self.cache.put(data, batch_result)
                return batch_result
            return None
    
    def _create_trade_signal_pb(self, signal_dict: Dict[str, Any]):
        """创建交易信号protobuf消息"""
        # 这里需要导入生成的protobuf类
        # 由于我们还没有编译proto文件，这里使用模拟实现
        if not self.protobuf_available:
            raise RuntimeError("Protocol Buffers不可用")
        
        # 模拟protobuf消息创建
        class MockTradeSignalPB:
            def __init__(self):
                self.signal_id = ""
                self.account_id = ""
                self.symbol = ""
                self.action = ""
                self.volume = 0.0
                self.price = 0.0
                self.timestamp = 0
                self.ticket = 0

            def SerializeToString(self):
                import json
                data = {
                    'signal_id': self.signal_id,
                    'account_id': self.account_id,
                    'symbol': self.symbol,
                    'action': self.action,
                    'volume': self.volume,
                    'price': self.price,
                    'timestamp': self.timestamp,
                    'ticket': self.ticket
                }
                return json.dumps(data).encode('utf-8')

            def ParseFromString(self, data):
                import json
                try:
                    parsed_data = json.loads(data.decode('utf-8'))
                    self.signal_id = parsed_data.get('signal_id', '')
                    self.account_id = parsed_data.get('account_id', '')
                    self.symbol = parsed_data.get('symbol', '')
                    self.action = parsed_data.get('action', '')
                    self.volume = float(parsed_data.get('volume', 0.0))
                    self.price = float(parsed_data.get('price', 0.0))
                    self.timestamp = int(parsed_data.get('timestamp', 0))
                    self.ticket = int(parsed_data.get('ticket', 0))
                except Exception as e:
                    logger.error(f"模拟protobuf解析失败: {e}")
                    raise
        
        pb_signal = MockTradeSignalPB()
        self._populate_trade_signal_pb(pb_signal, signal_dict)
        return pb_signal
    
    def _populate_trade_signal_pb(self, pb_signal, signal_dict: Dict[str, Any]):
        """填充交易信号protobuf消息"""
        pb_signal.signal_id = signal_dict.get('signal_id', '')
        pb_signal.account_id = signal_dict.get('account_id', '')
        pb_signal.symbol = signal_dict.get('symbol', '')
        pb_signal.action = signal_dict.get('action', '')
        pb_signal.volume = float(signal_dict.get('volume', 0.0))
        pb_signal.price = float(signal_dict.get('price', 0.0))
        pb_signal.timestamp = int(signal_dict.get('timestamp', 0))
        pb_signal.ticket = int(signal_dict.get('ticket', 0))
    
    def _create_empty_trade_signal_pb(self):
        """创建空的交易信号protobuf消息"""
        return self._create_trade_signal_pb({})
    
    def _create_batch_message_pb(self):
        """创建批量消息protobuf"""
        # 模拟实现
        class MockSignalList:
            def __init__(self):
                self._signals = []

            def add(self):
                signal = self._create_trade_signal_pb({})
                self._signals.append(signal)
                return signal

            def __iter__(self):
                return iter(self._signals)

        class MockBatchMessagePB:
            def __init__(self):
                self.signals = MockSignalList()
                self.batch_id = ""
                self.batch_timestamp = 0
                self.batch_size = 0
                self.is_compressed = False

            def SerializeToString(self):
                import json
                signals_data = []
                for signal in self.signals:
                    if hasattr(signal, '__dict__'):
                        signals_data.append(signal.__dict__)
                    else:
                        signals_data.append({
                            'signal_id': getattr(signal, 'signal_id', ''),
                            'account_id': getattr(signal, 'account_id', ''),
                            'symbol': getattr(signal, 'symbol', ''),
                            'action': getattr(signal, 'action', ''),
                            'volume': getattr(signal, 'volume', 0.0),
                            'price': getattr(signal, 'price', 0.0),
                            'timestamp': getattr(signal, 'timestamp', 0),
                            'ticket': getattr(signal, 'ticket', 0)
                        })

                data = {
                    'signals': signals_data,
                    'batch_id': self.batch_id,
                    'batch_timestamp': self.batch_timestamp,
                    'batch_size': self.batch_size,
                    'is_compressed': self.is_compressed
                }
                return json.dumps(data).encode('utf-8')

            def ParseFromString(self, data):
                import json
                try:
                    parsed_data = json.loads(data.decode('utf-8'))
                    self.batch_id = parsed_data.get('batch_id', '')
                    self.batch_timestamp = parsed_data.get('batch_timestamp', 0)
                    self.batch_size = parsed_data.get('batch_size', 0)
                    self.is_compressed = parsed_data.get('is_compressed', False)

                    # 重新创建信号列表
                    self.signals = MockSignalList()
                    for signal_data in parsed_data.get('signals', []):
                        signal = self.signals.add()
                        self._populate_trade_signal_pb(signal, signal_data)

                except Exception as e:
                    logger.error(f"模拟批量消息解析失败: {e}")
                    raise

        batch_pb = MockBatchMessagePB()
        # 添加_create_trade_signal_pb方法的引用
        batch_pb._create_trade_signal_pb = self._create_trade_signal_pb
        batch_pb._populate_trade_signal_pb = self._populate_trade_signal_pb
        return batch_pb
    
    def _encode_json_fallback(self, data: Any) -> bytes:
        """JSON后备编码"""
        import json
        try:
            if isinstance(data, TradeSignal):
                data = data.__dict__
            json_str = json.dumps(data, ensure_ascii=False)
            return json_str.encode('utf-8')
        except Exception as e:
            logger.error(f"JSON后备编码失败: {e}")
            raise
    
    def _decode_json_fallback(self, data: bytes) -> Optional[Dict[str, Any]]:
        """JSON后备解码"""
        import json
        try:
            if isinstance(data, str):
                json_str = data
            else:
                json_str = data.decode('utf-8')
            return json.loads(json_str)
        except Exception as e:
            logger.error(f"JSON后备解码失败: {e}")
            return None
    
    def get_compression_stats(self, original_size: int, compressed_size: int) -> Dict[str, Any]:
        """获取压缩统计信息"""
        compression_ratio = compressed_size / original_size if original_size > 0 else 1.0
        space_saved = original_size - compressed_size
        space_saved_percent = (space_saved / original_size * 100) if original_size > 0 else 0.0
        
        return {
            'original_size': original_size,
            'compressed_size': compressed_size,
            'compression_ratio': compression_ratio,
            'space_saved_bytes': space_saved,
            'space_saved_percent': space_saved_percent
        }
    
    def get_cache_stats(self) -> Optional[Dict[str, Any]]:
        """获取缓存统计信息"""
        if self.cache:
            return self.cache.get_stats()
        return None
    
    def get_cache_health(self) -> Optional[Dict[str, Any]]:
        """获取缓存健康状态"""
        if self.cache:
            return self.cache.get_health_status()
        return None
    
    def clear_cache(self):
        """清空缓存"""
        if self.cache:
            self.cache.clear()
            logger.info("🧹 LRU消息缓存已清空")
    
    def get_performance_metrics(self) -> Dict[str, Any]:
        """获取性能指标"""
        metrics = {
            'protobuf_available': self.protobuf_available,
            'cache_enabled': self.config.cache_enabled,
            'compression_enabled': self.config.compression_enabled,
            'compression_threshold': self.config.compression_threshold
        }
        
        # 添加缓存指标
        if self.cache:
            cache_stats = self.cache.get_stats()
            metrics.update({
                'cache_hit_rate': cache_stats['hit_rate_percent'],
                'cache_size': cache_stats['total_size'],
                'cache_utilization': cache_stats['cache_utilization_percent']
            })
        
        return metrics


# 全局编解码器实例
_protobuf_codec = None

def get_protobuf_codec(config: ProtobufConfig = None) -> ProtobufCodec:
    """获取全局protobuf编解码器实例"""
    global _protobuf_codec
    if _protobuf_codec is None:
        _protobuf_codec = ProtobufCodec(config)
    return _protobuf_codec


def get_cache_stats() -> Optional[Dict[str, Any]]:
    """获取全局缓存统计信息"""
    return get_protobuf_codec().get_cache_stats()


def get_cache_health() -> Optional[Dict[str, Any]]:
    """获取全局缓存健康状态"""
    return get_protobuf_codec().get_cache_health()


def clear_cache():
    """清空全局缓存"""
    get_protobuf_codec().clear_cache()


# 便捷函数
def encode_signal(signal: Union[TradeSignal, Dict[str, Any]]) -> bytes:
    """编码交易信号"""
    return get_protobuf_codec().encode_trade_signal(signal)


def decode_signal(data: bytes) -> Optional[Dict[str, Any]]:
    """解码交易信号"""
    return get_protobuf_codec().decode_trade_signal(data)


def encode_batch(signals: List[Union[TradeSignal, Dict[str, Any]]]) -> bytes:
    """编码批量信号"""
    return get_protobuf_codec().encode_batch_signals(signals)


def decode_batch(data: bytes) -> Optional[List[Dict[str, Any]]]:
    """解码批量信号"""
    return get_protobuf_codec().decode_batch_signals(data)


def get_performance_metrics() -> Dict[str, Any]:
    """获取全局性能指标"""
    return get_protobuf_codec().get_performance_metrics()
