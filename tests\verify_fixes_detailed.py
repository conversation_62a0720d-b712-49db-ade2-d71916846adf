#!/usr/bin/env python3
"""
详细修复验证脚本 - 验证所有修复的问题
"""

import asyncio
import time
import platform
import sys
import os
from unittest.mock import Mock, patch

# 添加项目根目录到路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

print("🔧 MT5 API详细修复验证")
print(f"🖥️  运行环境: {platform.system()} {platform.release()}")

# Windows兼容性
if platform.system() == 'Windows':
    asyncio.set_event_loop_policy(asyncio.WindowsProactorEventLoopPolicy())
    print("🪟 已启用Windows ProactorEventLoop策略")

async def test_datacache_fix():
    """测试1: DataCache过期清理修复"""
    print("\n📋 测试1: DataCache过期清理修复验证")
    
    try:
        # 导入DataCache类
        from src.core.separated_process_runners import DataCache
        
        # 创建测试缓存（短TTL用于测试）
        cache = DataCache(default_ttl=0.05)  # 50ms TTL
        
        # 设置测试数据
        await cache.set("test_key1", "value1")
        await cache.set("test_key2", "value2")
        
        print(f"   📊 设置缓存后: {len(cache.cache)}项")
        
        # 等待过期
        await asyncio.sleep(0.1)  # 等待2倍TTL
        
        # 调用清理
        await cache.clear_expired()
        
        remaining_cache = len(cache.cache)
        remaining_timestamps = len(cache.timestamps)
        
        print(f"   📊 清理后剩余: 缓存{remaining_cache}项, 时间戳{remaining_timestamps}项")
        
        if remaining_cache == 0 and remaining_timestamps == 0:
            print("   ✅ DataCache过期清理修复成功")
            return True
        else:
            print("   ❌ DataCache过期清理仍有问题")
            return False
            
    except Exception as e:
        print(f"   ❌ DataCache测试异常: {e}")
        return False

async def test_mock_patch_fix():
    """测试2: Mock Patch路径修复"""
    print("\n📋 测试2: Mock Patch路径修复验证")
    
    try:
        # 测试直接patch mt5_process_manager
        with patch('src.core.mt5_process_manager.MT5ProcessManager') as mock_pm:
            mock_instance = Mock()
            mock_instance.is_account_connected.return_value = True
            mock_instance.send_command = Mock(return_value=Mock(status='success', data={}))
            mock_pm.return_value = mock_instance
            
            # 模拟创建执行器函数调用
            print("   🔧 测试MT5ProcessManager patch...")
            from src.core.separated_process_runners import _create_mt5_trade_executor
            
            # 尝试创建执行器
            executor = await _create_mt5_trade_executor("TEST_ACC", {})
            
            if executor is not None:
                print("   ✅ MT5ProcessManager patch路径修复成功")
                return True
            else:
                print("   ❌ MT5ProcessManager patch路径仍有问题")
                return False
                
    except Exception as e:
        print(f"   ❌ Mock patch测试异常: {e}")
        return False

async def test_async_context_fix():
    """测试3: 异步上下文切片修复"""
    print("\n📋 测试3: 异步上下文切片修复验证")
    
    try:
        # 模拟复杂的异步操作序列
        operations = []
        
        async def async_operation(name, delay):
            await asyncio.sleep(delay)
            operations.append(f"{name}完成")
            return name
        
        # 测试有切片的异步操作
        start_time = time.perf_counter()
        
        await async_operation("操作1", 0.01)
        await asyncio.sleep(0)  # 事件循环切片
        
        await async_operation("操作2", 0.01) 
        await asyncio.sleep(0)  # 事件循环切片
        
        await async_operation("操作3", 0.01)
        
        end_time = time.perf_counter()
        duration = end_time - start_time
        
        print(f"   ⏱️  异步操作序列耗时: {duration*1000:.2f}ms")
        print(f"   📊 完成操作: {len(operations)}个")
        
        if len(operations) == 3 and duration > 0.025:
            print("   ✅ 异步上下文切片工作正常")
            return True
        else:
            print("   ❌ 异步上下文切片仍有问题")
            return False
            
    except Exception as e:
        print(f"   ❌ 异步上下文测试异常: {e}")
        return False

async def test_error_handling_robustness():
    """测试4: 错误处理鲁棒性"""
    print("\n📋 测试4: 错误处理鲁棒性验证")
    
    try:
        from src.core.separated_process_runners import retry_with_backoff, MT5APIException
        
        # 测试重试机制
        call_count = 0
        
        async def flaky_function():
            nonlocal call_count
            call_count += 1
            if call_count < 3:
                raise MT5APIException("模拟错误", retcode=2)
            return "成功"
        
        start_time = time.perf_counter()
        result = await retry_with_backoff(
            flaky_function,
            max_retries=3,
            initial_delay=0.001,  # 快速测试
            account_id="TEST"
        )
        end_time = time.perf_counter()
        
        duration = end_time - start_time
        
        print(f"   ⏱️  重试机制耗时: {duration*1000:.2f}ms")
        print(f"   📊 重试次数: {call_count}")
        print(f"   📊 最终结果: {result}")
        
        if result == "成功" and call_count == 3:
            print("   ✅ 错误处理和重试机制正常")
            return True
        else:
            print("   ❌ 错误处理和重试机制有问题")
            return False
            
    except Exception as e:
        print(f"   ❌ 错误处理测试异常: {e}")
        return False

async def main():
    """主验证函数"""
    print("\n" + "="*70)
    print("🧪 开始详细修复验证")
    print("="*70)
    
    tests = [
        ("DataCache过期清理", test_datacache_fix),
        ("Mock Patch路径", test_mock_patch_fix),
        ("异步上下文切片", test_async_context_fix),
        ("错误处理鲁棒性", test_error_handling_robustness)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        try:
            result = await test_func()
            results[test_name] = result
        except Exception as e:
            print(f"   ❌ {test_name}测试异常: {e}")
            results[test_name] = False
    
    print("\n" + "="*70)
    print("📊 详细修复验证总结")
    print("="*70)
    
    passed = sum(results.values())
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{status} {test_name}")
    
    print(f"\n🎯 总体结果: {passed}/{total} 修复验证通过")
    print(f"📈 修复成功率: {passed/total*100:.1f}%")
    
    if passed == total:
        print("\n🚀 所有修复验证通过！")
        print("   ✅ DataCache过期清理逻辑已修复")
        print("   ✅ Mock patch路径问题已解决")
        print("   ✅ 异步事件循环切片已优化")
        print("   ✅ 错误处理机制工作正常")
        print("\n🎯 系统应该能通过之前失败的测试")
    elif passed >= total * 0.75:
        print("\n⚠️  大部分修复验证通过")
        print("   - 主要问题已解决")
        print("   - 部分细节需要进一步调优")
    else:
        print("\n🔧 还需要进一步修复")
        print("   - 核心问题仍存在")
    
    return passed == total

if __name__ == '__main__':
    try:
        success = asyncio.run(main())
        print(f"\n{'='*70}")
        print(f"🎯 最终验证结果: {'✅ 全部通过' if success else '❌ 部分失败'}")
        print(f"{'='*70}")
        sys.exit(0 if success else 1)
    except Exception as e:
        print(f"\n❌ 验证脚本异常: {e}")
        sys.exit(1)