#!/usr/bin/env python3
"""
消息路由协调器 - 核心分离进程架构统一版
连接监控器发布的事件和执行器接收的命令
集成高性能跟单逻辑，支持批处理和压缩优化
"""

import asyncio
import time
import json
import gzip
from typing import Dict, Any, Optional, List, Callable, Set
from dataclasses import dataclass, asdict
from datetime import datetime

from ..messaging.jetstream_client import JetStreamClient
from ..messaging.message_types import TradeSignal, TradeAction, OrderType
from ..utils.logger import get_logger

logger = get_logger(__name__)

# 优化metrics实现 - 使用最新API
class OptimizedMetrics:
    def __init__(self):
        self._metrics = None
        self._initialized = False
        
    def _ensure_initialized(self):
        if not self._initialized:
            try:
                from ..utils.metrics import get_metrics_collector
                self._metrics = get_metrics_collector()
            except ImportError:
                self._metrics = None
            self._initialized = True
    
    def increment(self, name: str, value: int = 1, **kwargs):
        try:
            self._ensure_initialized()
            if self._metrics:
                # 使用兼容的调用方式，移除不支持的参数
                self._metrics.increment(name, value)
        except Exception:
            pass  # 静默忽略metrics错误
    
    def histogram(self, name: str, value: float, **kwargs):
        try:
            self._ensure_initialized()
            if self._metrics and hasattr(self._metrics, 'histogram'):
                self._metrics.histogram(name, value)
        except Exception:
            pass
    
    def set_gauge(self, name: str, value: float, **kwargs):
        try:
            self._ensure_initialized()
            if self._metrics and hasattr(self._metrics, 'set_gauge'):
                self._metrics.set_gauge(name, value)
        except Exception:
            pass

metrics = OptimizedMetrics()


@dataclass 
class RoutingRule:
    """增强路由规则"""
    master_account: str
    slave_accounts: List[str]
    copy_mode: str  # 'forward', 'reverse', 'disabled'
    volume_ratio: float = 1.0
    max_volume: float = 10.0
    allowed_symbols: List[str] = None
    symbol_filter: List[str] = None
    enabled: bool = True
    
    # 高级功能
    risk_limits: Dict[str, Any] = None
    timing_constraints: Dict[str, Any] = None
    position_correlation: bool = True


@dataclass
class PositionMapping:
    """持仓映射"""
    master_ticket: int
    slave_ticket: int  
    master_account: str
    slave_account: str
    symbol: str
    volume: float
    created_time: float
    
@dataclass
class BatchedCommand:
    """批处理命令"""
    commands: List[Dict[str, Any]]
    batch_id: str
    created_time: float
    priority: int = 0


class MessageRoutingCoordinator:
    """
    增强消息路由协调器 - 核心分离进程架构统一版
    
    集成功能：
    1. 高性能批处理和压缩
    2. 持仓映射跟踪
    3. 高级跟单策略  
    4. 性能监控和指标
    5. 统一消息流 MT5.CONTROL.MONITOR.* → MT5.SIGNALS.HIGH.* → MT5.CONTROL.RESULT.*
    """
    
    def __init__(self, jetstream_client: Optional[JetStreamClient] = None):
        self.jetstream = jetstream_client
        self.running = False
        
        # 路由规则
        self.routing_rules: Dict[str, List[RoutingRule]] = {}
        
        # 消息主题 - 新架构统一主题
        self.monitor_topic_pattern = "MT5.MONITOR.>"  # 使用NATS多层通配符匹配所有监控消息
        self.executor_topic_pattern = "MT5.SIGNALS.HIGH.*"  
        self.result_topic_pattern = "MT5.CONTROL.RESULT.*"
        
        # 持仓映射管理
        self.position_mappings: Dict[str, List[PositionMapping]] = {}  # master_ticket -> [mappings]
        self.slave_positions: Dict[str, Dict[int, PositionMapping]] = {}  # slave_account -> {slave_ticket: mapping}
        
        # 批处理优化
        self.batch_size = 10
        self.batch_timeout_ms = 100
        self.compression_threshold = 1024
        self.command_buffer: List[Dict[str, Any]] = []
        self.last_batch_time = time.time()
        
        # 性能统计
        self.routing_stats = {
            'events_received': 0,
            'commands_sent': 0,
            'batches_sent': 0,
            'routing_errors': 0,
            'avg_routing_latency_ms': 0.0,
            'compression_ratio': 0.0,
            'position_mappings_active': 0,
            'last_activity': None
        }
        
        # 订阅句柄
        self.subscriptions = []
        
        # 在线状态跟踪
        self.active_masters: Set[str] = set()
        self.active_slaves: Set[str] = set()
        
        logger.info("✅ 增强消息路由协调器初始化完成 - 核心分离进程架构")
    
    async def start_routing(self) -> bool:
        """启动消息路由"""
        if self.running:
            logger.warning("路由协调器已在运行")
            return False
        
        try:
            if not self.jetstream:
                logger.warning("⚠️ 未配置JetStream，路由器将在本地模式运行")
                self.running = True
                return True
            
            # 订阅监控事件
            await self._subscribe_monitor_events()
            
            # 订阅执行结果
            await self._subscribe_execution_results()
            
            self.running = True
            logger.info("🔄 消息路由协调器已启动")
            return True
            
        except Exception as e:
            logger.error(f"启动消息路由失败: {e}")
            return False
    
    async def stop_routing(self):
        """停止消息路由"""
        if not self.running:
            return
        
        self.running = False
        
        # 取消订阅
        for subscription in self.subscriptions:
            try:
                await subscription.unsubscribe()
            except Exception as e:
                logger.error(f"取消订阅失败: {e}")
        
        self.subscriptions.clear()
        logger.info("🛑 消息路由协调器已停止")
    
    async def _subscribe_monitor_events(self):
        """订阅监控器事件"""
        try:
            subscription = await self.jetstream.subscribe(
                subject=self.monitor_topic_pattern,
                callback=self._handle_monitor_event
            )
            # 检查订阅对象是否有效
            if subscription is not None:
                self.subscriptions.append(subscription)
                logger.info(f"✅ 已订阅监控器事件: {self.monitor_topic_pattern}")
            else:
                logger.error(f"❌ 监控器事件订阅失败: 返回None")
        except Exception as e:
            logger.error(f"订阅监控器事件失败: {e}")
    
    async def _subscribe_execution_results(self):
        """订阅执行结果"""
        try:
            subscription = await self.jetstream.subscribe(
                subject=self.result_topic_pattern,
                callback=self._handle_execution_result
            )
            # 检查订阅对象是否有效
            if subscription is not None:
                self.subscriptions.append(subscription)
                logger.info(f"✅ 已订阅执行结果: {self.result_topic_pattern}")
            else:
                logger.error(f"❌ 执行结果订阅失败: 返回None")
        except Exception as e:
            logger.error(f"订阅执行结果失败: {e}")
    
    async def _handle_monitor_event(self, msg):
        """处理监控器事件"""
        try:
            # 解析监控事件
            event_data = msg.data if isinstance(msg.data, dict) else eval(msg.data.decode('utf-8'))
            
            event_type = event_data.get('event_type')
            account_id = event_data.get('account_id')
            
            self.routing_stats['events_received'] += 1
            self.routing_stats['last_activity'] = time.time()
            
            logger.debug(f"📥 收到监控事件: {event_type} from {account_id}")
            
            # 处理不同类型的监控事件
            if event_type in ['position_opened', 'position_closed', 'position_modified']:
                await self._route_trading_event(event_data)
            elif event_type in ['monitor_started', 'monitor_stopped']:
                await self._handle_monitor_lifecycle_event(event_data)
            else:
                logger.debug(f"忽略监控事件类型: {event_type}")
            
            await msg.ack()
            
        except Exception as e:
            logger.error(f"处理监控事件失败: {e}")
            await msg.nak()
            self.routing_stats['routing_errors'] += 1
    
    async def _route_trading_event(self, event_data: Dict[str, Any]):
        """增强路由交易事件到执行器 - 支持批处理和高级策略"""
        start_time = time.perf_counter()
        
        try:
            master_account = event_data.get('account_id')
            event_type = event_data.get('event_type')
            
            # 记录主账户活跃状态
            self.active_masters.add(master_account)
            
            # 查找路由规则
            routing_rules = self.routing_rules.get(master_account, [])
            if not routing_rules:
                logger.debug(f"未找到 {master_account} 的路由规则")
                return
            
            # 转换为交易信号
            trade_signal = self._convert_event_to_signal(event_data)
            if not trade_signal:
                return
            
            # 高级事件处理
            commands_to_send = []
            
            # 向每个从账户发送执行命令
            for rule in routing_rules:
                if not rule.enabled:
                    continue
                
                # 应用符号过滤
                if (rule.symbol_filter and 
                    trade_signal.get('symbol') not in rule.symbol_filter):
                    continue
                
                for slave_account in rule.slave_accounts:
                    # 记录从账户活跃状态
                    self.active_slaves.add(slave_account)
                    
                    # 应用路由规则创建执行命令
                    command = await self._create_execution_command(
                        slave_account, trade_signal, rule, event_data
                    )
                    
                    if command:
                        commands_to_send.append(command)
            
            # 批处理发送命令
            if commands_to_send:
                await self._batch_send_commands(commands_to_send)
            
            # 更新性能统计
            routing_time_ms = (time.perf_counter() - start_time) * 1000
            self._update_routing_stats(routing_time_ms)
            
            # 记录指标 - 优化API调用
            metrics.increment('routing_events_processed')
            metrics.histogram('routing_latency_ms', routing_time_ms)
            
        except Exception as e:
            logger.error(f"路由交易事件失败: {e}")
            self.routing_stats['routing_errors'] += 1
            metrics.increment('routing_errors_total')
    
    def _convert_event_to_signal(self, event_data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """将监控事件转换为交易信号"""
        try:
            event_type = event_data.get('event_type')
            position_data = event_data.get('data', {}).get('position', {})
            
            if event_type == 'position_opened':
                return {
                    'command_type': 'open',
                    'symbol': position_data.get('symbol'),
                    'volume': position_data.get('volume'),
                    'order_type': 'BUY' if position_data.get('type') == 0 else 'SELL',
                    'price': position_data.get('price_open'),
                    'sl': position_data.get('sl'),
                    'tp': position_data.get('tp'),
                    'metadata': {
                        'original_account': event_data.get('account_id'),
                        'original_ticket': position_data.get('ticket'),
                        'event_timestamp': event_data.get('timestamp')
                    }
                }
            elif event_type == 'position_closed':
                return {
                    'command_type': 'close',
                    'symbol': position_data.get('symbol'),
                    'volume': position_data.get('volume'),
                    'position_id': position_data.get('ticket'),
                    'metadata': {
                        'original_account': event_data.get('account_id'),
                        'original_ticket': position_data.get('ticket'),
                        'event_timestamp': event_data.get('timestamp')
                    }
                }
            elif event_type == 'position_modified':
                return {
                    'command_type': 'modify',
                    'symbol': position_data.get('symbol'),
                    'position_id': position_data.get('ticket'),
                    'sl': position_data.get('sl'),
                    'tp': position_data.get('tp'),
                    'metadata': {
                        'original_account': event_data.get('account_id'),
                        'original_ticket': position_data.get('ticket'),
                        'event_timestamp': event_data.get('timestamp')
                    }
                }
            
            return None
            
        except Exception as e:
            logger.error(f"转换事件到信号失败: {e}")
            return None
    
    async def _send_execution_command(self, slave_account: str, signal: Dict[str, Any], rule: RoutingRule):
        """发送执行命令到指定账户的执行器"""
        try:
            # 应用路由规则调整
            adjusted_signal = self._apply_routing_rule(signal, rule)
            
            # 添加命令ID
            adjusted_signal['command_id'] = f"{slave_account}_{int(time.time() * 1000)}"
            adjusted_signal['account_id'] = slave_account
            
            # 发布到新架构信号主题
            executor_topic = f"MT5.SIGNALS.HIGH.{slave_account}"
            
            await self.jetstream.publish(
                subject=executor_topic,
                data=adjusted_signal
            )
            
            self.routing_stats['commands_sent'] += 1
            
            logger.info(f"📤 执行命令已发送: {adjusted_signal['command_type']} -> {slave_account}")
            
        except Exception as e:
            logger.error(f"发送执行命令失败: {e}")
            self.routing_stats['routing_errors'] += 1
    
    def _apply_routing_rule(self, signal: Dict[str, Any], rule: RoutingRule) -> Dict[str, Any]:
        """应用路由规则调整信号"""
        adjusted_signal = signal.copy()
        
        # 应用音量比例
        if 'volume' in adjusted_signal:
            adjusted_signal['volume'] = adjusted_signal['volume'] * rule.volume_ratio
        
        # 应用复制模式
        if rule.copy_mode == 'reverse' and 'order_type' in adjusted_signal:
            # 反向复制：买变卖，卖变买
            if adjusted_signal['order_type'] == 'BUY':
                adjusted_signal['order_type'] = 'SELL'
            elif adjusted_signal['order_type'] == 'SELL':
                adjusted_signal['order_type'] = 'BUY'
        
        # 添加路由元数据
        adjusted_signal['metadata'] = adjusted_signal.get('metadata', {})
        adjusted_signal['metadata'].update({
            'copy_mode': rule.copy_mode,
            'volume_ratio': rule.volume_ratio,
            'routing_timestamp': time.time()
        })
        
        return adjusted_signal
    
    async def _handle_execution_result(self, msg):
        """处理执行结果"""
        try:
            result_data = msg.data if isinstance(msg.data, dict) else eval(msg.data.decode('utf-8'))
            
            command_type = result_data.get('command_type')
            account_id = result_data.get('account_id')
            success = result_data.get('success')
            
            logger.debug(f"📬 执行结果: {command_type} {account_id} {'✅' if success else '❌'}")
            
            # 这里可以添加结果处理逻辑，比如记录统计、发送通知等
            
            await msg.ack()
            
        except Exception as e:
            logger.error(f"处理执行结果失败: {e}")
            await msg.nak()
    
    async def _handle_monitor_lifecycle_event(self, event_data: Dict[str, Any]):
        """处理监控器生命周期事件"""
        event_type = event_data.get('event_type')
        account_id = event_data.get('account_id')
        
        if event_type == 'monitor_started':
            logger.info(f"🔍 监控器已启动: {account_id}")
        elif event_type == 'monitor_stopped':
            logger.info(f"🛑 监控器已停止: {account_id}")
    
    def add_routing_rule(self, master_account: str, slave_accounts: List[str], 
                        copy_mode: str = 'forward', volume_ratio: float = 1.0):
        """添加路由规则"""
        rule = RoutingRule(
            master_account=master_account,
            slave_accounts=slave_accounts,
            copy_mode=copy_mode,
            volume_ratio=volume_ratio
        )
        
        if master_account not in self.routing_rules:
            self.routing_rules[master_account] = []
        
        self.routing_rules[master_account].append(rule)
        logger.info(f"✅ 已添加路由规则: {master_account} -> {slave_accounts} ({copy_mode})")
    
    def remove_routing_rule(self, master_account: str):
        """移除路由规则"""
        if master_account in self.routing_rules:
            del self.routing_rules[master_account]
            logger.info(f"🗑️ 已移除路由规则: {master_account}")
    
    def load_routing_rules_from_config(self, config_data: Dict[str, Any]):
        """从配置加载路由规则"""
        try:
            copy_relationships = config_data.get('copy_relationships', {})
            
            for master_account, slaves_config in copy_relationships.items():
                slave_accounts = []
                for slave_config in slaves_config:
                    if isinstance(slave_config, dict):
                        slave_accounts.append(slave_config.get('account_id'))
                        copy_mode = slave_config.get('copy_mode', 'forward')
                        volume_ratio = slave_config.get('volume_ratio', 1.0)
                    else:
                        slave_accounts.append(slave_config)
                        copy_mode = 'forward'
                        volume_ratio = 1.0
                
                if slave_accounts:
                    self.add_routing_rule(master_account, slave_accounts, copy_mode, volume_ratio)
            
            logger.info(f"✅ 已加载 {len(self.routing_rules)} 个路由规则")
            
        except Exception as e:
            logger.error(f"加载路由规则失败: {e}")
    
    async def _create_execution_command(self, slave_account: str, signal: Dict[str, Any], 
                                       rule: RoutingRule, event_data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """创建增强执行命令"""
        try:
            # 应用路由规则调整
            adjusted_signal = self._apply_routing_rule(signal, rule)
            
            # 添加命令ID和账户信息
            adjusted_signal['command_id'] = f"{slave_account}_{int(time.time() * 1000)}"
            adjusted_signal['account_id'] = slave_account
            adjusted_signal['priority'] = self._calculate_command_priority(adjusted_signal, rule)
            
            # 添加持仓映射信息（开仓事件）
            if signal.get('command_type') == 'open':
                master_ticket = event_data.get('data', {}).get('position', {}).get('ticket')
                if master_ticket:
                    adjusted_signal['master_position_id'] = master_ticket
                    adjusted_signal['correlation_id'] = f"{signal.get('metadata', {}).get('original_account')}_{master_ticket}"
            
            # 风险检查
            if not await self._validate_risk_limits(adjusted_signal, rule):
                logger.warning(f"命令被风险限制拒绝: {adjusted_signal['command_type']} {slave_account}")
                return None
            
            return adjusted_signal
            
        except Exception as e:
            logger.error(f"创建执行命令失败: {e}")
            return None
    
    async def _batch_send_commands(self, commands: List[Dict[str, Any]]):
        """批处理发送命令 - 高性能优化"""
        try:
            if not commands:
                return
            
            # 按优先级和账户分组
            grouped_commands = self._group_commands_by_account(commands)
            
            # 为每个账户发送批量命令
            for account_id, account_commands in grouped_commands.items():
                if len(account_commands) == 1:
                    # 单个命令直接发送
                    await self._send_single_command(account_id, account_commands[0])
                else:
                    # 多个命令批量发送
                    await self._send_batch_commands(account_id, account_commands)
            
            self.routing_stats['commands_sent'] += len(commands)
            
        except Exception as e:
            logger.error(f"批处理发送命令失败: {e}")
            self.routing_stats['routing_errors'] += 1
    
    async def _send_batch_commands(self, account_id: str, commands: List[Dict[str, Any]]):
        """发送批量命令到指定账户"""
        try:
            # 创建批处理消息
            batch_data = {
                'batch_id': f"batch_{account_id}_{int(time.time() * 1000)}",
                'account_id': account_id,
                'commands': commands,
                'batch_size': len(commands),
                'timestamp': time.time(),
                'compression': False
            }
            
            # 序列化
            payload = json.dumps(batch_data).encode('utf-8')
            
            # 可选压缩
            headers = {}
            if len(payload) > self.compression_threshold:
                compressed_payload = gzip.compress(payload)
                compression_ratio = len(compressed_payload) / len(payload)
                
                if compression_ratio < 0.8:  # 压缩效果好才使用
                    payload = compressed_payload
                    headers['compression'] = 'gzip'
                    batch_data['compression'] = True
                    self.routing_stats['compression_ratio'] = compression_ratio
            
            # 发布到新架构信号主题
            executor_topic = f"MT5.SIGNALS.HIGH.{account_id}"
            
            await self.jetstream.publish(
                subject=executor_topic,
                data=payload,
                headers=headers
            )
            
            self.routing_stats['batches_sent'] += 1
            
            logger.info(f"📤 批量命令已发送: {len(commands)} 个命令 -> {account_id}")
            
        except Exception as e:
            logger.error(f"发送批量命令失败: {e}")
            # 回退到单个命令发送
            for command in commands:
                await self._send_single_command(account_id, command)
    
    async def _send_single_command(self, account_id: str, command: Dict[str, Any]):
        """发送单个命令"""
        try:
            executor_topic = f"MT5.SIGNALS.HIGH.{account_id}"
            
            await self.jetstream.publish(
                subject=executor_topic,
                data=command
            )
            
            logger.debug(f"📤 执行命令已发送: {command['command_type']} -> {account_id}")
            
        except Exception as e:
            logger.error(f"发送执行命令失败: {e}")
            self.routing_stats['routing_errors'] += 1
    
    def _group_commands_by_account(self, commands: List[Dict[str, Any]]) -> Dict[str, List[Dict[str, Any]]]:
        """按账户分组命令"""
        grouped = {}
        for command in commands:
            account_id = command.get('account_id')
            if account_id:
                if account_id not in grouped:
                    grouped[account_id] = []
                grouped[account_id].append(command)
        return grouped
    
    def _calculate_command_priority(self, signal: Dict[str, Any], rule: RoutingRule) -> int:
        """计算命令优先级"""
        # 平仓命令优先级最高
        if signal.get('command_type') == 'close':
            return 100
        # 止损/止盈修改优先级高
        elif signal.get('command_type') == 'modify':
            return 80
        # 开仓命令优先级正常
        else:
            return 50
    
    async def _validate_risk_limits(self, signal: Dict[str, Any], rule: RoutingRule) -> bool:
        """验证风险限制"""
        try:
            # 检查最大手数限制
            if signal.get('volume', 0) > rule.max_volume:
                logger.warning(f"手数超过限制: {signal.get('volume')} > {rule.max_volume}")
                return False
            
            # 检查符号限制
            if (rule.allowed_symbols and 
                signal.get('symbol') not in rule.allowed_symbols):
                logger.warning(f"符号不在允许列表: {signal.get('symbol')}")
                return False
            
            # 可以添加更多风险检查
            return True
            
        except Exception as e:
            logger.error(f"风险验证失败: {e}")
            return False
    
    def _update_routing_stats(self, routing_time_ms: float):
        """更新路由统计"""
        # 更新平均延迟
        events_processed = self.routing_stats['events_received']
        if events_processed > 0:
            current_avg = self.routing_stats['avg_routing_latency_ms']
            self.routing_stats['avg_routing_latency_ms'] = (
                (current_avg * (events_processed - 1) + routing_time_ms) / events_processed
            )
        
        # 更新活跃持仓映射数量
        total_mappings = sum(len(mappings) for mappings in self.position_mappings.values())
        self.routing_stats['position_mappings_active'] = total_mappings
        
        self.routing_stats['last_activity'] = time.time()
    
    async def _handle_execution_result(self, msg):
        """增强执行结果处理 - 包含持仓映射跟踪"""
        try:
            result_data = msg.data if isinstance(msg.data, dict) else eval(msg.data.decode('utf-8'))
            
            command_type = result_data.get('command_type')
            account_id = result_data.get('account_id')
            success = result_data.get('success')
            command_id = result_data.get('command_id')
            
            # 处理持仓映射
            if success and command_type == 'open':
                await self._track_position_mapping(result_data)
            elif success and command_type == 'close':
                await self._remove_position_mapping(result_data)
            
            logger.debug(f"📬 执行结果: {command_type} {account_id} {'✅' if success else '❌'}")
            
            # 记录执行结果指标 - 优化API调用  
            metrics.increment('execution_results_processed')
            
            await msg.ack()
            
        except Exception as e:
            logger.error(f"处理执行结果失败: {e}")
            await msg.nak()
    
    async def _track_position_mapping(self, result_data: Dict[str, Any]):
        """跟踪持仓映射"""
        try:
            data = result_data.get('data', {})
            master_position_id = result_data.get('master_position_id')
            slave_ticket = data.get('ticket')
            account_id = result_data.get('account_id')
            
            if master_position_id and slave_ticket and account_id:
                mapping = PositionMapping(
                    master_ticket=master_position_id,
                    slave_ticket=slave_ticket,
                    master_account=result_data.get('correlation_id', '').split('_')[0],
                    slave_account=account_id,
                    symbol=data.get('symbol', ''),
                    volume=data.get('volume', 0),
                    created_time=time.time()
                )
                
                # 添加到映射表
                if master_position_id not in self.position_mappings:
                    self.position_mappings[master_position_id] = []
                self.position_mappings[master_position_id].append(mapping)
                
                # 添加到从账户映射
                if account_id not in self.slave_positions:
                    self.slave_positions[account_id] = {}
                self.slave_positions[account_id][slave_ticket] = mapping
                
                logger.info(f"📌 持仓映射创建: {master_position_id} -> {slave_ticket} ({account_id})")
                
        except Exception as e:
            logger.error(f"跟踪持仓映射失败: {e}")
    
    async def _remove_position_mapping(self, result_data: Dict[str, Any]):
        """移除持仓映射"""
        try:
            master_position_id = result_data.get('master_position_id')
            account_id = result_data.get('account_id')
            
            if master_position_id and account_id:
                # 从主映射表移除
                if master_position_id in self.position_mappings:
                    self.position_mappings[master_position_id] = [
                        m for m in self.position_mappings[master_position_id] 
                        if m.slave_account != account_id
                    ]
                    if not self.position_mappings[master_position_id]:
                        del self.position_mappings[master_position_id]
                
                # 从从账户映射移除
                if account_id in self.slave_positions:
                    to_remove = []
                    for slave_ticket, mapping in self.slave_positions[account_id].items():
                        if mapping.master_ticket == master_position_id:
                            to_remove.append(slave_ticket)
                    
                    for ticket in to_remove:
                        del self.slave_positions[account_id][ticket]
                
                logger.info(f"🗑️ 持仓映射移除: {master_position_id} ({account_id})")
                
        except Exception as e:
            logger.error(f"移除持仓映射失败: {e}")
    
    def get_routing_stats(self) -> Dict[str, Any]:
        """获取增强路由统计"""
        return {
            **self.routing_stats,
            'running': self.running,
            'routing_rules_count': len(self.routing_rules),
            'active_masters': len(self.active_masters),
            'active_slaves': len(self.active_slaves),
            'total_position_mappings': sum(len(mappings) for mappings in self.position_mappings.values()),
            'routing_rules': {
                master: [
                    {
                        'slaves': rule.slave_accounts,
                        'copy_mode': rule.copy_mode,
                        'volume_ratio': rule.volume_ratio,
                        'max_volume': rule.max_volume,
                        'enabled': rule.enabled,
                        'symbol_filter': rule.symbol_filter
                    }
                    for rule in rules
                ]
                for master, rules in self.routing_rules.items()
            }
        }


# 用于本地模式的模拟路由器
class LocalMessageRouter:
    """本地消息路由器（无NATS环境）"""
    
    def __init__(self):
        self.running = False
        self.routing_stats = {
            'events_received': 0,
            'commands_sent': 0,
            'routing_errors': 0
        }
        logger.info("✅ 本地消息路由器初始化完成")
    
    async def start_routing(self):
        self.running = True
        logger.info("🔄 本地消息路由器已启动")
        return True
    
    async def stop_routing(self):
        self.running = False
        logger.info("🛑 本地消息路由器已停止")
    
    def add_routing_rule(self, *args, **kwargs):
        logger.debug("本地模式：添加路由规则")
    
    def get_routing_stats(self):
        return {**self.routing_stats, 'running': self.running, 'mode': 'local'}