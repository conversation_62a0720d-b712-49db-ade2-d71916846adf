# Docker配置使用指南

## 📋 配置文件说明

### 🏭 生产环境配置
**文件**: `docker-compose-production.yml`

**包含服务**:
1. `mt5-redis-optimized` - Redis Hash优化
2. `mt5-nats-optimized` - NATS 100%可用
3. `mt5-prometheus-optimized` - 优化监控
4. `mt5-pushgateway-optimized` - Hash指标支持
5. `mt5-grafana-optimized` - 性能仪表板
6. `mt5-redis-commander-optimized` - Hash数据查看
7. `mt5-hash-manager` - 专用Hash API服务

**启动命令**:
```bash
# 启动生产环境
docker-compose -f docker-compose-production.yml up -d

# 查看服务状态
docker-compose -f docker-compose-production.yml ps

# 查看日志
docker-compose -f docker-compose-production.yml logs -f
```

### 🧪 测试环境配置
**文件**: `docker-compose-test.yml`

**包含服务**:
- 核心服务: Redis, NATS, MT5系统
- 可选监控: Prometheus, Grafana (使用 `--profile monitoring`)
- 可选工具: Redis Commander (使用 `--profile tools`)

**启动命令**:
```bash
# 启动基础测试环境
docker-compose -f docker-compose-test.yml up -d

# 启动包含监控的测试环境
docker-compose -f docker-compose-test.yml --profile monitoring up -d

# 启动包含所有工具的测试环境
docker-compose -f docker-compose-test.yml --profile monitoring --profile tools up -d
```

### 🔧 优化配置参考
**文件**: `docker-compose-optimized.yml`

这是完整的优化配置文件，包含所有高级特性，可作为自定义配置的参考。

## 🚀 快速开始

### 生产部署
```bash
# 1. 设置环境变量
cp .env.example .env
# 编辑 .env 文件设置密码等

# 2. 启动生产环境
docker-compose -f docker-compose-production.yml up -d

# 3. 验证服务
curl http://localhost:9090/-/healthy  # Prometheus
curl http://localhost:3000/api/health # Grafana
curl http://localhost:8082/health     # Hash Manager
```

### 开发测试
```bash
# 1. 启动测试环境
docker-compose -f docker-compose-test.yml up -d

# 2. 运行测试
python -m pytest tests/

# 3. 查看Redis数据
docker-compose -f docker-compose-test.yml --profile tools up -d
# 访问 http://localhost:8083
```

## 📊 服务端口映射

### 生产环境端口
- Redis: 6379
- NATS: 4222 (客户端), 8222 (监控)
- Prometheus: 9090
- Pushgateway: 9091
- Grafana: 3000
- Redis Commander: 8081
- Hash Manager API: 8082

### 测试环境端口
- Redis: 6380
- NATS: 4223 (客户端), 8223 (监控)
- Prometheus: 9091
- Grafana: 3001
- Redis Commander: 8083

## 🛠️ 维护操作

### 数据备份
```bash
# 备份Redis数据
docker exec mt5-redis-optimized redis-cli BGSAVE

# 备份Prometheus数据
docker run --rm -v mt5-production_prometheus_production_data:/data -v $(pwd):/backup alpine tar czf /backup/prometheus-backup.tar.gz -C /data .
```

### 日志管理
```bash
# 查看特定服务日志
docker-compose -f docker-compose-production.yml logs mt5-redis-optimized

# 清理日志
docker system prune -f
```

### 性能监控
- Grafana仪表板: http://localhost:3000
- Prometheus指标: http://localhost:9090
- NATS监控: http://localhost:8222
- Redis管理: http://localhost:8081

## 🔒 安全配置

生产环境建议:
1. 设置强密码 (通过.env文件)
2. 启用TLS加密
3. 配置防火墙规则
4. 定期更新镜像版本
5. 监控资源使用情况

## 📈 扩展配置

如需自定义配置，可以:
1. 复制 `docker-compose-optimized.yml`
2. 根据需求修改服务配置
3. 调整资源限制和网络设置
4. 添加额外的监控或工具服务
