"""
跟单交易引擎 - 分布式架构核心实现
基于NATS JetStream实现跨主机分布式跟单
支持智能手数计算、状态持久化和多账户管理
"""

import asyncio
import time
from typing import Dict, List, Optional, Callable
from dataclasses import dataclass
from datetime import datetime
from enum import Enum

from .mt5_client import MT5Client, TradeRequest, TradeAction, OrderType, Position
from ..messaging.jetstream_client import JetStreamClient
from ..utils.logger import get_logger

logger = get_logger(__name__)


class CopyStrategy(Enum):
    """跟单策略"""
    REGULAR = "regular"      # 正向跟单
    REVERSE = "reverse"      # 反向跟单
    DISABLED = "disabled"    # 禁用跟单


@dataclass
class CopyRule:
    """跟单规则"""
    master_account: str
    slave_account: str
    strategy: CopyStrategy
    volume_ratio: float = 1.0  # 手数比例
    max_volume: float = 10.0   # 最大手数
    symbols: Optional[List[str]] = None  # 允许的交易品种
    enabled: bool = True


# 使用统一的TradeSignal定义
from ..messaging.message_types import TradeSignal, SignalType


# 使用统一的手数计算器
from .copy_utils import UnifiedVolumeCalculator


class CopyTradingEngine:
    """跟单交易引擎 - 分布式架构实现"""
    
    def __init__(self, jetstream_client: JetStreamClient, state_manager=None):
        self.js_client = jetstream_client
        self.mt5_clients: Dict[str, MT5Client] = {}
        self.copy_rules: Dict[str, List[CopyRule]] = {}  # master_account -> rules
        self.position_mapping: Dict[str, Dict[int, int]] = {}  # account -> {master_pos_id: slave_pos_id}
        self.running = False
        
        # 分布式状态管理
        self.state_manager = state_manager
        self.use_persistent_state = state_manager is not None
        self._state_file = "logs/copy_trading_state.json"
        
        # 统一手数计算器
        self.volume_calculator = UnifiedVolumeCalculator()
        
        # 统计信息
        self.stats = {
            'signals_received': 0,
            'trades_executed': 0,
            'trades_failed': 0,
            'positions_opened': 0,
            'positions_closed': 0
        }
    
    async def start(self):
        """启动跟单引擎"""
        if self.running:
            return
        
        self.running = True
        logger.info("🚀 跟单交易引擎启动")
        
        # 启动信号监听
        await self._start_signal_listener()
        
        self.start_time = time.time()  # 记录启动时间
        
        # 恢复持仓状态
        await self._restore_position_state()
        
        logger.info(f"✅ 跟单交易引擎启动完成 - 状态管理: {'persistent' if self.use_persistent_state else 'file_based'}")
    
    async def stop(self):
        """停止跟单引擎"""
        self.running = False
        
        # 保存持仓状态
        await self._save_position_state()
        
        # 断开所有MT5连接
        for client in self.mt5_clients.values():
            await client.disconnect()
        
        logger.info("跟单交易引擎已停止")
    
    def add_mt5_client(self, account_id: str, client: MT5Client):
        """添加MT5客户端"""
        self.mt5_clients[account_id] = client
        self.position_mapping[account_id] = {}
        logger.info(f"添加MT5客户端: {account_id}")
    
    def add_copy_rule(self, rule: CopyRule):
        """添加跟单规则"""
        if rule.master_account not in self.copy_rules:
            self.copy_rules[rule.master_account] = []
        
        self.copy_rules[rule.master_account].append(rule)
        logger.info(f"添加跟单规则: {rule.master_account} -> {rule.slave_account} ({rule.strategy.value})")
    
    async def _start_signal_listener(self):
        """启动信号监听器"""
        try:
            # 使用新的分层流订阅方式
            await self.js_client.subscribe(
                subject="MT5.SIGNALS.*",
                callback=self._handle_trade_signal_message
            )

            logger.info("✅ 交易信号监听器启动成功")

        except Exception as e:
            logger.error(f"启动信号监听器失败: {e}")
    
    async def _handle_trade_signal_message(self, msg):
        """处理交易信号消息"""
        try:
            # 解析交易信号
            signal = self._parse_trade_signal(msg.data)

            if signal:
                # 处理信号
                await self._handle_trade_signal(signal)
                self.stats['signals_received'] += 1

            await msg.ack()

        except Exception as e:
            logger.error(f"处理交易信号失败: {e}")
            try:
                await msg.nak()
            except:
                pass
    
    def _parse_trade_signal(self, data) -> Optional[TradeSignal]:
        """解析交易信号"""
        try:
            # 如果数据已经是字典，直接使用；否则解码
            if isinstance(data, dict):
                signal_data = data
            else:
                import json
                signal_data = json.loads(data.decode('utf-8'))
            
            # 使用message_types.py中定义的TradeSignal模型
            # 创建PositionSignalData
            from ..messaging.message_types import PositionSignalData
            
            position_data = PositionSignalData(
                symbol=signal_data.get('symbol', ''),
                volume=float(signal_data.get('volume', 0)),
                action=signal_data.get('action', signal_data.get('order_type', 'BUY')),
                price=float(signal_data.get('price', 0)),
                sl=float(signal_data.get('sl', 0)),
                tp=float(signal_data.get('tp', 0)),
                comment=signal_data.get('comment', ''),
                account_id=signal_data.get('master_account', signal_data.get('account_id', '')),
                copy_magic_number=12345  # 默认魔术号
            )
            
            # 创建TradeSignal
            signal_type = SignalType.POSITION_OPEN
            if signal_data.get('action') == 'close':
                signal_type = SignalType.POSITION_CLOSE
            elif signal_data.get('action') == 'modify':
                signal_type = SignalType.POSITION_MODIFY
            
            return TradeSignal(
                type=signal_type,
                master_id=signal_data.get('master_account', signal_data.get('account_id', '')),
                ticket=int(signal_data.get('position_id', 0)) if signal_data.get('position_id') else 0,
                data=position_data,
                timestamp=signal_data.get('timestamp', time.time())
            )
        except Exception as e:
            logger.error(f"解析交易信号失败: {e}")
            return None
    
    async def _handle_trade_signal(self, signal: TradeSignal):
        """处理交易信号"""
        # 从TradeSignal中提取数据
        position_data = signal.data
        logger.info(f"📡 收到交易信号: {signal.master_id} {signal.type.value} {position_data.symbol}")
        
        # 获取跟单规则
        rules = self.copy_rules.get(signal.master_id, [])
        if not rules:
            logger.debug(f"无跟单规则: {signal.master_id}")
            return
        
        # 执行跟单
        for rule in rules:
            if not rule.enabled:
                continue
            
            # 检查品种过滤
            if rule.symbols and position_data.symbol not in rule.symbols:
                continue
            
            # 获取从账户MT5客户端
            slave_client = self.mt5_clients.get(rule.slave_account)
            if not slave_client or not slave_client.is_connected():
                logger.warning(f"从账户未连接: {rule.slave_account}")
                continue
            
            try:
                if signal.type == SignalType.POSITION_OPEN:
                    await self._copy_open_position(signal, rule, slave_client)
                elif signal.type == SignalType.POSITION_CLOSE:
                    await self._copy_close_position(signal, rule, slave_client)
                elif signal.type == SignalType.POSITION_MODIFY:
                    await self._copy_modify_position(signal, rule, slave_client)
                
            except Exception as e:
                logger.error(f"执行跟单失败: {rule.slave_account} - {e}")
                self.stats['trades_failed'] += 1
    
    async def _copy_open_position(self, signal: TradeSignal, rule: CopyRule, client: MT5Client):
        """跟单开仓"""
        try:
            position_data = signal.data
            
            # 计算跟单手数
            copy_volume = self._calculate_copy_volume(position_data.volume, rule)
            
            # 确定订单类型
            if rule.strategy == CopyStrategy.REGULAR:
                order_type = OrderType.BUY if position_data.action.upper() == "BUY" else OrderType.SELL
            elif rule.strategy == CopyStrategy.REVERSE:
                order_type = OrderType.SELL if position_data.action.upper() == "BUY" else OrderType.BUY
            else:
                return
            
            # 获取当前价格
            prices = await client.get_market_price(position_data.symbol)
            if not prices:
                logger.error(f"获取价格失败: {position_data.symbol}")
                return
            
            price = prices[1] if order_type == OrderType.BUY else prices[0]  # ask for buy, bid for sell
            
            # 计算止损止盈
            sl, tp = self._calculate_sl_tp_from_data(position_data, rule, order_type, price)
            
            # 获取魔术号配置
            magic_number = position_data.copy_magic_number
            
            # 创建交易请求
            request = TradeRequest(
                action=TradeAction.DEAL,
                symbol=position_data.symbol,
                volume=copy_volume,
                type=order_type,
                price=price,
                sl=sl,
                tp=tp,
                magic=magic_number,
                comment=f"Copy from {signal.master_id}"
            )
            
            # 执行交易
            result = await client.send_order(request)
            
            if result.retcode == 10009:  # TRADE_RETCODE_DONE
                # 记录持仓映射
                if signal.ticket and result.order:
                    self.position_mapping[rule.slave_account][signal.ticket] = result.order
                
                self.stats['trades_executed'] += 1
                self.stats['positions_opened'] += 1
                
                logger.info(f"✅ 跟单开仓成功: {rule.slave_account} {position_data.symbol} {copy_volume}")
            else:
                logger.error(f"❌ 跟单开仓失败: {result.comment}")
                self.stats['trades_failed'] += 1
                
        except Exception as e:
            logger.error(f"跟单开仓异常: {e}")
            self.stats['trades_failed'] += 1
    
    async def _copy_close_position(self, signal: TradeSignal, rule: CopyRule, client: MT5Client):
        """跟单平仓"""
        try:
            position_data = signal.data
            
            # 查找对应的从账户持仓
            master_pos_id = signal.ticket
            if not master_pos_id:
                return
            
            slave_pos_id = self.position_mapping[rule.slave_account].get(master_pos_id)
            if not slave_pos_id:
                logger.warning(f"未找到对应持仓: {master_pos_id}")
                return
            
            # 获取持仓信息
            positions = await client.get_positions()
            target_position = None
            
            for pos in positions:
                if pos.ticket == slave_pos_id:
                    target_position = pos
                    break
            
            if not target_position:
                logger.warning(f"持仓不存在: {slave_pos_id}")
                return
            
            # 执行平仓
            result = await client.close_position(target_position)
            
            if result.retcode == 10009:  # TRADE_RETCODE_DONE
                # 清除持仓映射
                del self.position_mapping[rule.slave_account][master_pos_id]
                
                self.stats['trades_executed'] += 1
                self.stats['positions_closed'] += 1
                
                logger.info(f"✅ 跟单平仓成功: {rule.slave_account} {position_data.symbol}")
            else:
                logger.error(f"❌ 跟单平仓失败: {result.comment}")
                self.stats['trades_failed'] += 1
                
        except Exception as e:
            logger.error(f"跟单平仓异常: {e}")
            self.stats['trades_failed'] += 1
    
    async def _copy_modify_position(self, signal: TradeSignal, rule: CopyRule, client: MT5Client):
        """跟单修改持仓"""
        try:
            position_data = signal.data
            
            # 查找对应的从账户持仓
            master_pos_id = signal.ticket
            if not master_pos_id:
                return
            
            slave_pos_id = self.position_mapping[rule.slave_account].get(master_pos_id)
            if not slave_pos_id:
                return
            
            # 获取持仓信息
            positions = await client.get_positions()
            target_position = None
            
            for pos in positions:
                if pos.ticket == slave_pos_id:
                    target_position = pos
                    break
            
            if not target_position:
                return
            
            # 计算新的止损止盈
            new_sl = position_data.sl
            new_tp = position_data.tp
            
            if rule.strategy == CopyStrategy.REVERSE:
                # 反向跟单需要调整止损止盈
                if new_sl and new_tp:
                    new_sl, new_tp = new_tp, new_sl
            
            # 执行修改
            result = await client.modify_position(target_position, new_sl, new_tp)
            
            if result.retcode == 10009:  # TRADE_RETCODE_DONE
                self.stats['trades_executed'] += 1
                logger.info(f"✅ 跟单修改成功: {rule.slave_account} {position_data.symbol}")
            else:
                logger.error(f"❌ 跟单修改失败: {result.comment}")
                self.stats['trades_failed'] += 1
                
        except Exception as e:
            logger.error(f"跟单修改异常: {e}")
            self.stats['trades_failed'] += 1
    
    def _calculate_copy_volume(self, original_volume: float, rule: CopyRule) -> float:
        """计算跟单手数 - 采用高级LotSizingCalculator技术"""
        try:
            # 使用统一手数计算
            relationship_config = {
                'copy_ratio': rule.volume_ratio,
                'limits': {
                    'max_volume_per_trade': rule.max_volume,
                    'min_volume_per_trade': 0.01
                }
            }
            copy_volume = self.volume_calculator.calculate_copy_volume(
                original_volume=original_volume,
                relationship_config=relationship_config
            )
            
            logger.debug(
                f"高级手数计算: 原始={original_volume}, 比例={rule.volume_ratio}, "
                f"结果={copy_volume}"
            )
            
            return copy_volume
            
        except Exception as e:
            logger.error(f"高级手数计算失败，使用简单计算: {e}")
            # 回退到简单计算
            copy_volume = original_volume * rule.volume_ratio
            return min(copy_volume, rule.max_volume)
    
    def _calculate_sl_tp_from_data(self, position_data, rule: CopyRule, order_type: OrderType, price: float) -> tuple:
        """从PositionSignalData计算止损止盈"""
        sl = position_data.sl if position_data.sl > 0 else None
        tp = position_data.tp if position_data.tp > 0 else None
        
        if rule.strategy == "reverse":
            # 反向跟单需要调整止损止盈
            if sl and tp:
                sl, tp = tp, sl
        
        return sl, tp
    
    def get_statistics(self) -> Dict:
        """获取统计信息"""
        return {
            **self.stats,
            'active_rules': sum(len(rules) for rules in self.copy_rules.values()),
            'connected_accounts': len([c for c in self.mt5_clients.values() if c.is_connected()]),
            'position_mappings': sum(len(mapping) for mapping in self.position_mapping.values()),
            'state_management': 'persistent' if self.use_persistent_state else 'file_based',
            'uptime_seconds': time.time() - getattr(self, 'start_time', time.time())
        }
