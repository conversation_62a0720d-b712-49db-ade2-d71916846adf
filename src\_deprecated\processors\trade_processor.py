"""
交易处理器

负责处理交易执行、订单管理、持仓同步等交易相关逻辑。
"""

import asyncio
from dataclasses import dataclass
from datetime import datetime
from decimal import Decimal
from typing import Dict, List, Optional, Tuple, Set
from enum import Enum
import logging
import MetaTrader5 as mt5

from ..models import Trade, Order, Signal, SignalType, OrderType, TradeStatus
from ..engines import RiskEngine
from .event_processor import EventProcessor, Event, EventType
from ...storage.cache import CacheManager
from ...storage.database import TradeRepository
from ...utils.decorators import async_timed, with_retry


logger = logging.getLogger(__name__)


@dataclass
class TradeEvent:
    """交易事件"""
    account_id: str
    event_type: str  # order_placed, position_opened, etc.
    ticket: int
    data: Dict[str, Any]
    timestamp: datetime


@dataclass
class ExecutionResult:
    """执行结果"""
    success: bool
    ticket: Optional[int] = None
    executed_price: Optional[Decimal] = None
    executed_volume: Optional[Decimal] = None
    slippage: Optional[Decimal] = None
    execution_time_ms: Optional[int] = None
    error: Optional[str] = None


class TradeProcessor:
    """交易处理器 - 负责交易执行和管理"""
    
    def __init__(self,
                 cache_manager: CacheManager,
                 trade_repo: TradeRepository,
                 risk_engine: RiskEngine,
                 event_processor: EventProcessor):
        
        self.cache = cache_manager
        self.trade_repo = trade_repo
        self.risk_engine = risk_engine
        self.event_processor = event_processor
        
        # MT5连接池
        self.mt5_connections: Dict[str, Any] = {}  # terminal_id -> connection
        
        # 执行队列
        self.execution_queue: asyncio.Queue = asyncio.Queue(maxsize=1000)
        self.urgent_queue: asyncio.Queue = asyncio.Queue(maxsize=100)
        
        # 订单跟踪
        self.pending_orders: Dict[str, Order] = {}  # order_id -> Order
        self.active_positions: Dict[str, Dict[int, Trade]] = {}  # account_id -> {ticket: Trade}
        
        # 执行锁（防止并发执行冲突）
        self.execution_locks: Dict[str, asyncio.Lock] = {}  # account_id -> Lock
        
        # 性能统计
        self.execution_stats = {
            'total_orders': 0,
            'successful_orders': 0,
            'failed_orders': 0,
            'total_volume': Decimal('0'),
            'avg_execution_time_ms': 0,
            'avg_slippage_pips': 0
        }
        
        # 工作器
        self.workers: List[asyncio.Task] = []
        self.worker_count = 3
    
    async def start(self):
        """启动交易处理器"""
        # 启动执行工作器
        for i in range(self.worker_count):
            worker = asyncio.create_task(self._execution_worker(i))
            self.workers.append(worker)
        
        # 启动紧急执行工作器
        urgent_worker = asyncio.create_task(self._urgent_execution_worker())
        self.workers.append(urgent_worker)
        
        # 启动持仓同步任务
        sync_task = asyncio.create_task(self._position_sync_task())
        self.workers.append(sync_task)
        
        logger.info(f"Trade processor started with {self.worker_count} workers")
    
    async def stop(self):
        """停止交易处理器"""
        # 取消所有工作器
        for worker in self.workers:
            worker.cancel()
        
        await asyncio.gather(*self.workers, return_exceptions=True)
        
        # 关闭MT5连接
        for terminal_id in list(self.mt5_connections.keys()):
            await self.disconnect_mt5(terminal_id)
        
        logger.info("Trade processor stopped")
    
    async def connect_mt5(self, terminal_id: str, terminal_path: str, 
                         login: int, password: str, server: str) -> bool:
        """连接MT5终端"""
        try:
            # 这里应该实现实际的MT5连接
            # 示例代码
            logger.info(f"Connecting to MT5 terminal {terminal_id}")
            
            # 存储连接信息
            self.mt5_connections[terminal_id] = {
                'path': terminal_path,
                'login': login,
                'server': server,
                'connected': True,
                'last_ping': datetime.utcnow()
            }
            
            # 发出连接事件
            await self.event_processor.emit_event(Event(
                id=f"mt5_connected_{terminal_id}",
                type=EventType.MT5_CONNECTED,
                timestamp=datetime.utcnow(),
                source=terminal_id,
                data={'login': login, 'server': server}
            ))
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to connect MT5 terminal {terminal_id}: {e}")
            return False
    
    async def disconnect_mt5(self, terminal_id: str):
        """断开MT5连接"""
        if terminal_id in self.mt5_connections:
            # 实际断开连接
            # ...
            
            del self.mt5_connections[terminal_id]
            
            # 发出断开连接事件
            await self.event_processor.emit_event(Event(
                id=f"mt5_disconnected_{terminal_id}",
                type=EventType.MT5_DISCONNECTED,
                timestamp=datetime.utcnow(),
                source=terminal_id,
                data={}
            ))
    
    @async_timed
    async def execute_order(self, order: Order, 
                          urgent: bool = False) -> ExecutionResult:
        """执行订单"""
        start_time = datetime.utcnow()
        
        try:
            # 获取账户锁
            if order.account_id not in self.execution_locks:
                self.execution_locks[order.account_id] = asyncio.Lock()
            
            async with self.execution_locks[order.account_id]:
                # 验证订单
                validation_result = await self._validate_order(order)
                if not validation_result['valid']:
                    return ExecutionResult(
                        success=False,
                        error=validation_result['error']
                    )
                
                # 风险检查
                account = await self._get_account(order.account_id)
                positions = list(self.active_positions.get(order.account_id, {}).values())
                
                risk_result = await self.risk_engine.validate_trade(
                    account, order, positions
                )
                
                if not risk_result.approved:
                    return ExecutionResult(
                        success=False,
                        error=f"Risk check failed: {', '.join(risk_result.reasons)}"
                    )
                
                # 执行订单
                if urgent:
                    await self.urgent_queue.put(order)
                else:
                    await self.execution_queue.put(order)
                
                # 等待执行结果（实际应该通过回调或事件）
                result = await self._wait_for_execution(order.id, timeout=5)
                
                # 计算执行时间
                execution_time = int((datetime.utcnow() - start_time).total_seconds() * 1000)
                result.execution_time_ms = execution_time
                
                return result
                
        except Exception as e:
            logger.error(f"Order execution error: {e}")
            return ExecutionResult(
                success=False,
                error=str(e)
            )
    
    @with_retry(max_attempts=3)
    async def _execute_mt5_order(self, order: Order, terminal_id: str) -> ExecutionResult:
        """在MT5中执行订单"""
        try:
            # 这里应该调用实际的MT5 API
            # 示例实现
            
            # 模拟执行
            await asyncio.sleep(0.01)  # 模拟网络延迟
            
            # 生成模拟结果
            ticket = ********
            executed_price = order.price if order.price else Decimal('1.10000')
            executed_volume = order.volume
            
            # 计算滑点
            slippage = Decimal('0')
            if order.price:
                if order.type in ['BUY', 'BUY_LIMIT', 'BUY_STOP']:
                    slippage = executed_price - order.price
                else:
                    slippage = order.price - executed_price
            
            # 记录到活跃持仓
            if order.account_id not in self.active_positions:
                self.active_positions[order.account_id] = {}
            
            trade = Trade(
                ticket=ticket,
                position_id=ticket,
                account_id=order.account_id,
                symbol=order.symbol,
                type=order.type,
                volume=executed_volume,
                open_price=executed_price,
                open_time=datetime.utcnow(),
                sl=order.sl,
                tp=order.tp,
                status=TradeStatus.OPEN,
                comment=order.comment
            )
            
            self.active_positions[order.account_id][ticket] = trade
            
            # 保存到数据库
            await self.trade_repo.save(trade)
            
            # 发出开仓事件
            await self.event_processor.emit_event(Event(
                id=f"position_opened_{ticket}",
                type=EventType.POSITION_OPENED,
                timestamp=datetime.utcnow(),
                source=terminal_id,
                data=trade.to_dict()
            ))
            
            # 更新统计
            self._update_execution_stats(True, executed_volume, slippage)
            
            return ExecutionResult(
                success=True,
                ticket=ticket,
                executed_price=executed_price,
                executed_volume=executed_volume,
                slippage=slippage
            )
            
        except Exception as e:
            logger.error(f"MT5 execution error: {e}")
            self._update_execution_stats(False, order.volume, None)
            
            return ExecutionResult(
                success=False,
                error=str(e)
            )
    
    async def close_position(self, account_id: str, ticket: int, 
                           volume: Optional[Decimal] = None) -> ExecutionResult:
        """平仓"""
        try:
            # 获取持仓信息
            position = self.active_positions.get(account_id, {}).get(ticket)
            if not position:
                return ExecutionResult(
                    success=False,
                    error=f"Position {ticket} not found"
                )
            
            # 确定平仓量
            close_volume = volume if volume else position.volume
            
            # 创建平仓订单
            close_order = Order(
                id=f"close_{ticket}_{datetime.utcnow().timestamp()}",
                account_id=account_id,
                symbol=position.symbol,
                type='SELL' if position.is_buy else 'BUY',
                order_type=OrderType.MARKET,
                volume=close_volume,
                source_type='close',
                parent_order_id=str(ticket),
                comment=f"Close position {ticket}"
            )
            
            # 执行平仓
            result = await self.execute_order(close_order, urgent=True)
            
            if result.success:
                # 更新持仓状态
                position.status = TradeStatus.CLOSED
                position.close_price = result.executed_price
                position.close_time = datetime.utcnow()
                
                # 计算盈亏
                if position.is_buy:
                    position.profit = (result.executed_price - position.open_price) * close_volume * 100000
                else:
                    position.profit = (position.open_price - result.executed_price) * close_volume * 100000
                
                # 从活跃持仓中移除
                del self.active_positions[account_id][ticket]
                
                # 更新数据库
                await self.trade_repo.update(position)
                
                # 发出平仓事件
                await self.event_processor.emit_event(Event(
                    id=f"position_closed_{ticket}",
                    type=EventType.POSITION_CLOSED,
                    timestamp=datetime.utcnow(),
                    source=account_id,
                    data={
                        'ticket': ticket,
                        'close_price': float(result.executed_price),
                        'profit': float(position.profit)
                    }
                ))
            
            return result
            
        except Exception as e:
            logger.error(f"Close position error: {e}")
            return ExecutionResult(
                success=False,
                error=str(e)
            )
    
    async def modify_position(self, account_id: str, ticket: int,
                            sl: Optional[Decimal] = None,
                            tp: Optional[Decimal] = None) -> bool:
        """修改持仓"""
        try:
            # 获取持仓
            position = self.active_positions.get(account_id, {}).get(ticket)
            if not position:
                logger.error(f"Position {ticket} not found")
                return False
            
            # 这里应该调用MT5 API修改订单
            # ...
            
            # 更新本地持仓信息
            if sl is not None:
                position.sl = sl
            if tp is not None:
                position.tp = tp
            
            # 更新数据库
            await self.trade_repo.update(position)
            
            # 发出修改事件
            await self.event_processor.emit_event(Event(
                id=f"position_modified_{ticket}",
                type=EventType.POSITION_MODIFIED,
                timestamp=datetime.utcnow(),
                source=account_id,
                data={
                    'ticket': ticket,
                    'sl': float(sl) if sl else None,
                    'tp': float(tp) if tp else None
                }
            ))
            
            return True
            
        except Exception as e:
            logger.error(f"Modify position error: {e}")
            return False
    
    async def get_account_positions(self, account_id: str) -> List[Trade]:
        """获取账户持仓"""
        return list(self.active_positions.get(account_id, {}).values())
    
    async def get_account_history(self, account_id: str, 
                                days: int = 30) -> List[Trade]:
        """获取账户历史交易"""
        return await self.trade_repo.get_account_history(account_id, days)
    
    async def _execution_worker(self, worker_id: int):
        """执行工作器"""
        logger.info(f"Execution worker {worker_id} started")
        
        while True:
            try:
                # 获取待执行订单
                order = await self.execution_queue.get()
                
                # 获取终端ID
                terminal_id = await self._get_account_terminal(order.account_id)
                if not terminal_id:
                    logger.error(f"No terminal found for account {order.account_id}")
                    continue
                
                # 执行订单
                result = await self._execute_mt5_order(order, terminal_id)
                
                # 通知执行结果（实际应该通过事件或回调）
                await self._notify_execution_result(order.id, result)
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Execution worker {worker_id} error: {e}")
                await asyncio.sleep(1)
    
    async def _urgent_execution_worker(self):
        """紧急执行工作器"""
        logger.info("Urgent execution worker started")
        
        while True:
            try:
                # 优先处理紧急订单
                order = await self.urgent_queue.get()
                
                # 获取终端ID
                terminal_id = await self._get_account_terminal(order.account_id)
                if not terminal_id:
                    logger.error(f"No terminal found for account {order.account_id}")
                    continue
                
                # 立即执行
                result = await self._execute_mt5_order(order, terminal_id)
                
                # 通知执行结果
                await self._notify_execution_result(order.id, result)
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Urgent execution worker error: {e}")
                await asyncio.sleep(0.1)
    
    async def _position_sync_task(self):
        """持仓同步任务"""
        logger.info("Position sync task started")
        
        while True:
            try:
                # 同步所有账户的持仓
                for account_id in list(self.active_positions.keys()):
                    await self._sync_account_positions(account_id)
                
                # 每10秒同步一次
                await asyncio.sleep(10)
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Position sync error: {e}")
                await asyncio.sleep(30)
    
    async def _sync_account_positions(self, account_id: str):
        """同步账户持仓"""
        try:
            # 获取MT5实际持仓
            # 这里应该调用MT5 API
            # ...
            
            # 更新本地持仓缓存
            # ...
            
            pass
            
        except Exception as e:
            logger.error(f"Sync positions error for {account_id}: {e}")
    
    async def _validate_order(self, order: Order) -> Dict[str, Any]:
        """验证订单"""
        # 基础验证
        if not order.symbol:
            return {'valid': False, 'error': 'Symbol is required'}
        
        if not order.volume or order.volume <= 0:
            return {'valid': False, 'error': 'Invalid volume'}
        
        if order.order_type == OrderType.LIMIT and not order.price:
            return {'valid': False, 'error': 'Price required for limit order'}
        
        # 账户验证
        account = await self._get_account(order.account_id)
        if not account:
            return {'valid': False, 'error': 'Account not found'}
        
        if not account.is_active:
            return {'valid': False, 'error': 'Account is not active'}
        
        return {'valid': True}
    
    async def _wait_for_execution(self, order_id: str, timeout: float) -> ExecutionResult:
        """等待执行结果"""
        # 实际应该通过事件或回调机制
        # 这里简化为等待
        await asyncio.sleep(0.1)
        
        # 返回模拟结果
        return ExecutionResult(
            success=True,
            ticket=********,
            executed_price=Decimal('1.10000'),
            executed_volume=Decimal('0.1')
        )
    
    async def _notify_execution_result(self, order_id: str, result: ExecutionResult):
        """通知执行结果"""
        # 实际应该通过消息队列或回调
        cache_key = f"execution_result:{order_id}"
        await self.cache.set(cache_key, result.__dict__, ttl=300)
    
    def _update_execution_stats(self, success: bool, volume: Decimal, 
                              slippage: Optional[Decimal]):
        """更新执行统计"""
        self.execution_stats['total_orders'] += 1
        
        if success:
            self.execution_stats['successful_orders'] += 1
        else:
            self.execution_stats['failed_orders'] += 1
        
        self.execution_stats['total_volume'] += volume
        
        if slippage is not None:
            # 更新平均滑点
            total = self.execution_stats['successful_orders']
            if total > 0:
                current_avg = self.execution_stats['avg_slippage_pips']
                new_avg = (current_avg * (total - 1) + float(slippage * 10000)) / total
                self.execution_stats['avg_slippage_pips'] = new_avg
    
    async def _get_account(self, account_id: str):
        """获取账户信息"""
        # 从缓存获取
        cache_key = f"account:{account_id}"
        cached = await self.cache.get(cache_key)
        
        if cached:
            return cached
        
        # 实际应该从数据库获取
        return None
    
    async def _get_account_terminal(self, account_id: str) -> Optional[str]:
        """获取账户对应的终端ID"""
        # 从缓存获取
        cache_key = f"account_terminal:{account_id}"
        terminal_id = await self.cache.get(cache_key)
        
        return terminal_id
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取交易统计"""
        success_rate = 0
        if self.execution_stats['total_orders'] > 0:
            success_rate = (self.execution_stats['successful_orders'] / 
                          self.execution_stats['total_orders'] * 100)
        
        return {
            **self.execution_stats,
            'success_rate': success_rate,
            'execution_queue_size': self.execution_queue.qsize(),
            'urgent_queue_size': self.urgent_queue.qsize(),
            'active_positions_count': sum(
                len(positions) for positions in self.active_positions.values()
            ),
            'connected_terminals': len(self.mt5_connections)
        }