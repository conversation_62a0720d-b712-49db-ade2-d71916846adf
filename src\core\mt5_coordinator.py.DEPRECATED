# src/core/mt5_coordinator_enhanced.py
# 🚨 AUTO-MIGRATED: Old messaging components replaced with HybridQueueManager
# See MIGRATION_GUIDE.md for details

"""
分布式MT5协调器 - 第4层系统协调器 
完全符合4层架构，支持动态主从分配和pairing配置
"""
import asyncio
import os
import sys
import yaml
import multiprocessing as mp
import logging
from typing import Dict, List, Optional, Any
from pathlib import Path
import time
from dotenv import load_dotenv

load_dotenv()

from .mt5_process_manager import MT5ProcessManager
from .separated_process_runners import run_account_monitor_process, run_account_executor_process
from .mt5_configuration import AccountConfigManager
# 移除了重复的SignalRouter导入，统一使用HybridMessageRouter
from .mt5_rpc_client import MT5RPCClient
from .mt5_request_handler import MT5RequestHandler
from ..messaging.hybrid_queue_manager import HybridQueueManager
from ..messaging.hybrid_message_router import HybridMessageRouter

logger = logging.getLogger(__name__)


class DistributedMT5Coordinator:
    """
    分布式MT5协调器 - 第4层系统协调器
    """
    
    def __init__(self, host_id: str, config_path: str):
        self.host_id = host_id
        self.config_path = config_path
        self.config = self._load_config()
        
        self.mt5_process_manager = MT5ProcessManager(config_path)
        
        self.monitor_processes: Dict[str, mp.Process] = {}
        self.executor_processes: Dict[str, mp.Process] = {}
        self.monitor_queues: Dict[str, mp.Queue] = {}
        self.executor_queues: Dict[str, mp.Queue] = {}
        
        config_root = str(Path(config_path).parent.parent) if config_path else "config"
        self.account_manager = AccountConfigManager(config_dir=config_root)

        # 现代架构组件（完全替代旧组件）
        self.hybrid_queue_manager: Optional[HybridQueueManager] = None
        self.hybrid_message_router: Optional[HybridMessageRouter] = None
        
        # RPC组件
        self.rpc_handler: Optional[MT5RequestHandler] = None
        self.rpc_client: Optional[MT5RPCClient] = None
        
        # 核心组件
        self.jetstream = None
        self.state_manager = None
        self.running = False
        self.local_accounts: Dict[str, Dict] = {}
        
        # 架构模式标识 - 只支持优化架构和本地模式
        self.architecture_mode = 'optimized'  # 只支持 'optimized' 或 'local'
        
        logger.info(f"DistributedMT5Coordinator初始化完成，主机: {host_id}")
    
    def _load_config(self) -> Dict:
        """Load configuration file"""
        try:
            from .config_manager import get_config_manager
            config_mgr = get_config_manager()
            all_config = config_mgr.get_all()

            nats_config = config_mgr.get_nats_config()
            if nats_config:
                all_config['nats'] = nats_config

            return all_config
        except Exception as e:
            logger.error(f"Failed to load configuration file: {e}")
            return {}
    
    async def start(self):
        """启动协调器"""
        self.running = True
        logger.info(f"启动分布式MT5协调器 - 主机: {self.host_id}")
        
        try:
            await self._init_distributed_components()
            await self._discover_local_accounts()
            await self._start_account_processes_enhanced()

            asyncio.create_task(self._monitor_processes())
            asyncio.create_task(self._heartbeat_task())
            
            logger.info("分布式MT5协调器启动完成")
            
        except Exception as e:
            logger.error(f"协调器启动失败: {e}")
            raise
    
    async def _init_distributed_components(self):
        """初始化分布式组件 - 优化版"""
        try:
            # 优先尝试初始化现代化架构
            if 'nats' in self.config:
                success = await self._init_optimized_architecture()
                if success:
                    logger.info("🎉 现代化架构初始化成功")
                    return
                else:
                    logger.warning("⚠️ 现代化架构初始化失败，回退到本地模式")
                    self.architecture_mode = 'local'
            
            # 本地模式初始化（不NATS环境）
            await self._init_local_mode()
            
        except Exception as e:
            logger.error(f"分布式组件初始化失败: {e}")
            # 最后降级到本地模式
            await self._init_local_mode()
    
    async def _init_optimized_architecture(self) -> bool:
        """初始化真正的优化架构 - 完全使用现代组件"""
        try:
            logger.info("🚀 启动现代化4层流架构初始化")
            
            # 1. 使用StreamConfigManager创建配置驱动的JetStream客户端
            from ..messaging.hybrid_queue_manager import HybridQueueManager
            from .config_manager import get_config_manager, get_stream_config_manager
            
            # 获取配置管理器
            config_manager = get_config_manager()
            stream_config_manager = get_stream_config_manager(config_manager)
            
            # 创建配置驱动的JetStream客户端
            nats_config = config_manager.get_nats_config()
            if not nats_config:
                raise RuntimeError("NATS配置缺失")
                
            self.jetstream = HybridQueueManager(nats_config)
            
            # 2. 强制使用分层流架构（不允许回退）
            logger.info("🔧 创建4层分层流架构...")
            success = await self.jetstream.initialize(host_id=self.host_id, use_layered_streams=True)
            if not success:
                raise RuntimeError("分层流架构创建失败 - 系统无法在单流模式下运行")
            
            # 验证所有4层流都已创建
            streams_status = self.jetstream.get_layered_streams_status()
            if len(streams_status['layered_streams']) < 4:
                raise RuntimeError(f"分层流创建不完整，仅创建了 {len(streams_status['layered_streams'])} 个流")
            
            logger.info(f"✅ 4层流架构创建成功: {list(streams_status['layered_streams'].keys())}")
            
            # 4. 创建混合队列管理器 - 统一权威实现
            from ..messaging.hybrid_queue_manager import HybridQueueConfig, QueueBackendType
            
            hybrid_config = HybridQueueConfig(
                primary_backend=QueueBackendType.NATS,
                backup_backends=[QueueBackendType.REDIS_STREAMS],
                local_fallback=True,
                enable_dual_write=True,
                backend_configs={
                    QueueBackendType.NATS: {
                        'nats': {
                            'host_id': self.host_id,
                            'servers': self.config.get('nats_servers', ['nats://localhost:4222'])
                        }
                    },
                    QueueBackendType.REDIS_STREAMS: {
                        'redis': {
                            'host': self.config.get('redis_host', 'localhost'),
                            'port': self.config.get('redis_port', 6379),
                            'consumer_group': f'mt5-workers-{self.host_id}'
                        }
                    }
                }
            )
            
            self.hybrid_queue_manager = HybridQueueManager(hybrid_config)
            
            # 5. 初始化混合队列管理器
            success = await self.hybrid_queue_manager.initialize()
            if not success:
                raise RuntimeError("混合队列管理器初始化失败")
            
            logger.info("✅ 混合队列管理器初始化成功")
            
            # 6. 创建混合消息路由器 - 使用新的队列管理器
            logger.info("🔀 初始化混合消息路由器")
            self.hybrid_message_router = HybridMessageRouter(
                config=self.config,
                host_id=self.host_id
            )
            
            # 注入队列管理器
            self.hybrid_message_router.queue_manager = self.hybrid_queue_manager
            
            await self.hybrid_message_router.start()
            logger.info("✅ 混合消息路由器启动成功")
            
            # 7. 设置优先级消费者（确保优先级系统端到端工作）
            logger.info("⚡ 设置优先级感知消费者")
            await self.jetstream.setup_priority_consumers()
            
            # 8. 注册直接路由规则（避免中心化瓶颈）
            await self._setup_direct_account_routing()
            
            # 9. 初始化RPC组件
            await self._init_rpc_components()
            
            # 10. 验证架构完整性
            await self._verify_architecture_integrity()
            
            logger.info("🎉 现代化4层架构初始化完成！")
            logger.info("✨ 系统现在只使用最新的核心组件: HybridQueueManager + HybridMessageRouter + StreamConfigManager")
            return True
            
        except Exception as e:
            logger.error(f"优化架构初始化失败: {e}")
            # 清理资源
            if self.hybrid_message_router:
                await self.hybrid_message_router.stop()
                self.hybrid_message_router = None
            if self.hybrid_queue_manager:
                await self.hybrid_queue_manager.shutdown()
                self.hybrid_queue_manager = None
            if self.jetstream:
                await self.jetstream.disconnect()
                self.jetstream = None
            return False
    
    async def _setup_direct_account_routing(self):
        """设置直接账户路由 - 避免中心化瓶颈"""
        try:
            logger.info("🔀 设置账户间直接路由")
            
            if not self.hybrid_message_router:
                logger.warning("混合消息路由器未初始化，跳过直接路由设置")
                return
            
            # 为每个本地账户设置直接路由规则
            for account_id in self.local_accounts:
                # 设置本地优化路由
                self.hybrid_message_router.register_local_account(account_id)
                
                # 设置监控消息直达路由
                await self._setup_monitor_direct_routing(account_id)
                
                # 设置执行命令直达路由  
                await self._setup_executor_direct_routing(account_id)
            
            logger.info(f"✅ 已为 {len(self.local_accounts)} 个账户设置直接路由")
            
        except Exception as e:
            logger.error(f"设置直接路由失败: {e}")
            raise
    
    async def _setup_monitor_direct_routing(self, account_id: str):
        """为监控器设置直接路由"""
        # 监控器发布到: MT5.MONITOR.{host_id}.{account_id}
        monitor_subject = f"MT5.MONITOR.{self.host_id}.{account_id}"
        
        # 直接路由到本地执行器，避免通过中央路由器
        if self.hybrid_message_router:
            self.hybrid_message_router.add_direct_route(
                source_subject=monitor_subject,
                target_account=account_id,
                route_type="local"
            )
    
    async def _setup_executor_direct_routing(self, account_id: str):
        """为执行器设置直接路由"""
        # 使用配置驱动的信号主题
        try:
            from .config_manager import get_stream_config_manager
            stream_config_manager = get_stream_config_manager()
            
            signal_subjects = []
            for priority in ['CRITICAL', 'HIGH', 'NORMAL', 'LOW']:
                subject = stream_config_manager.get_subject_pattern(
                    'global_signals', priority=priority, master_account=account_id
                )
                if subject:
                    signal_subjects.append(subject)
            
            # 如果配置获取失败，使用默认主题
            if not signal_subjects:
                signal_subjects = [
                    f"MT5.SIGNALS.CRITICAL.{account_id}",
                    f"MT5.SIGNALS.HIGH.{account_id}",
                    f"MT5.SIGNALS.NORMAL.{account_id}",
                    f"MT5.SIGNALS.LOW.{account_id}"
                ]
                logger.warning("配置获取失败，使用默认信号主题")
            
        except Exception as e:
            logger.error(f"获取信号主题配置失败: {e}")
            signal_subjects = [
                f"MT5.SIGNALS.CRITICAL.{account_id}",
                f"MT5.SIGNALS.HIGH.{account_id}",
                f"MT5.SIGNALS.NORMAL.{account_id}",
                f"MT5.SIGNALS.LOW.{account_id}"
            ]
        
        for subject in signal_subjects:
            if self.hybrid_message_router:
                self.hybrid_message_router.add_direct_route(
                    source_subject=subject,
                    target_account=account_id,
                    route_type="priority"
                )
    
    async def _verify_architecture_integrity(self):
        """验证架构完整性"""
        try:
            logger.info("🔍 验证现代架构完整性")
            
            issues = []
            
            # 1. 验证分层流状态
            if not self.jetstream:
                issues.append("JetStream客户端未初始化")
            else:
                streams_status = self.jetstream.get_layered_streams_status()
                if len(streams_status['layered_streams']) < 4:
                    issues.append(f"分层流不完整: {streams_status['layered_streams']}")
            
            # 2. 验证优化NATS管理器
            if not self.hybrid_queue_manager:
                issues.append("优化NATS管理器未初始化")
            
            # 3. 验证混合消息路由器
            if not self.hybrid_message_router:
                issues.append("混合消息路由器未初始化")
            
            # 4. 验证RPC组件
            if not self.rpc_handler:
                issues.append("RPC处理器未初始化")
            
            if issues:
                error_msg = f"架构完整性验证失败: {'; '.join(issues)}"
                logger.error(error_msg)
                raise RuntimeError(error_msg)
            
            # 5. 验证消息流连通性
            await self._test_message_flow_connectivity()
            
            logger.info("✅ 架构完整性验证通过")
            
        except Exception as e:
            logger.error(f"架构完整性验证失败: {e}")
            raise
    
    async def _test_message_flow_connectivity(self):
        """测试消息流连通性"""
        try:
            # 发送测试消息验证连通性 - 使用配置驱动的主题
            try:
                from .config_manager import get_stream_config_manager
                stream_config_manager = get_stream_config_manager()
                test_subject = stream_config_manager.get_subject_pattern(
                    'global_signals', priority='HIGH', master_account='test'
                )
                
                # 如果配置获取失败，使用默认
                if not test_subject:
                    test_subject = "MT5.SIGNALS.HIGH.test"
                    
            except Exception:
                test_subject = "MT5.SIGNALS.HIGH.test"
            test_data = {
                "type": "connectivity_test",
                "timestamp": time.time(),
                "host_id": self.host_id
            }
            
            if self.jetstream:
                success = await self.jetstream.publish(test_subject, test_data)
                if success:
                    logger.info("✅ 消息流连通性测试通过")
                else:
                    logger.warning("⚠️ 消息流连通性测试失败")
        except Exception as e:
            logger.warning(f"消息流连通性测试异常: {e}")
            # 不抛出异常，因为这只是测试
    
 
    
    async def _init_local_mode(self):
        """初始化本地模式 - 无NATS环境的后备方案"""
        logger.info("🏠 初始化本地模式（无NATS环境）")
        
        try:
            # 本地模式下，直接使用内存队列进行进程间通信
            # 不需要复杂的消息路由器，直接使用本地队列
            logger.info("✨ 本地模式: 使用内存队列进行进程间通信")
            
        except Exception as e:
            logger.error(f"本地模式初始化失败: {e}")
    
    async def _init_rpc_components(self):
        """初始化RPC组件（公共逻辑）"""
        try:
            if self.jetstream:
                self.rpc_handler = MT5RequestHandler(self.jetstream)
                if await self.rpc_handler.start():
                    logger.info("RPC Handler启动成功")
                else:
                    logger.error("RPC Handler启动失败")

                self.rpc_client = MT5RPCClient(self.jetstream)
                logger.info("RPC Client创建成功")
        except Exception as e:
            logger.error(f"RPC组件初始化失败: {e}")
    
    async def _register_accounts_to_optimized_manager(self):
        """注册账户到优化管理器"""
        if not self.hybrid_queue_manager:
            return
        
        try:
            # 注册本地账户
            for account_id in self.local_accounts:
                # Account registration handled by HybridQueueManager internally
                # self.hybrid_queue_manager.register_account(account_id, self.host_id)
            
            logger.info(f"已注册 {len(self.local_accounts)} 个本地账户到优化管理器")
        except Exception as e:
            logger.error(f"注册账户失败: {e}")
    
    async def _register_optimized_handlers(self):
        """注册优化架构的消息处理器"""
        if not self.hybrid_queue_manager:
            return
        
        try:
            from ..messaging.priority_queue import MessagePriority
            
            # 注册信号处理器
            for priority in MessagePriority:
                # Signal handling now through HybridMessageRouter
                # self.hybrid_message_router.register_message_handler(
                    priority, 
                    self._create_optimized_signal_handler(priority)
                )
            
            # 注册RPC处理器
            # RPC handling modernized through HybridQueueManager
            # self.hybrid_queue_manager.register_rpc_handler(
                'get_account_info', self._handle_get_account_info
            )
            # RPC handling modernized through HybridQueueManager
            # self.hybrid_queue_manager.register_rpc_handler(
                'send_order', self._handle_send_order
            )
            # RPC handling modernized through HybridQueueManager
            # self.hybrid_queue_manager.register_rpc_handler(
                'get_positions', self._handle_get_positions
            )
            
            logger.info("优化架构消息处理器注册完成")
            
        except Exception as e:
            logger.error(f"注册优化处理器失败: {e}")
    
    def _create_optimized_signal_handler(self, priority):
        """创建优化信号处理器"""
        async def handler(signal_data: Dict[str, Any]):
            try:
                account_id = signal_data.get('target_account')
                signal_type = signal_data.get('signal_type', 'trade_signal')
                
                logger.debug(f"处理{priority.name}优先级信号: {signal_type} -> {account_id}")
                
                # 根据信号类型分发到相应的执行器
                if account_id in self.executor_queues:
                    message = {
                        'type': 'trade_signal',
                        'priority': priority.name,
                        'data': signal_data,
                        'timestamp': time.time()
                    }
                    self.executor_queues[account_id].put_nowait(message)
                    logger.debug(f"信号已发送到执行器: {account_id}")
                else:
                    logger.warning(f"执行器队列不存在: {account_id}")
                    
            except Exception as e:
                logger.error(f"优化信号处理失败: {e}")
        
        return handler
    
    async def _handle_get_account_info(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """处理获取账户信息RPC"""
        try:
            account_id = params.get('account_id')
            if account_id and self.rpc_client:
                result = await self.rpc_client.get_account_info(account_id)
                return result
            return {'error': 'Invalid account_id or RPC client not available'}
        except Exception as e:
            return {'error': str(e)}
    
    async def _handle_send_order(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """处理发送订单RPC"""
        try:
            account_id = params.get('account_id')
            if account_id and self.rpc_client:
                result = await self.rpc_client.send_order(account_id, params)
                return result
            return {'error': 'Invalid account_id or RPC client not available'}
        except Exception as e:
            return {'error': str(e)}
    
    async def _handle_get_positions(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """处理获取持仓RPC"""
        try:
            account_id = params.get('account_id')
            if account_id and self.rpc_client:
                result = await self.rpc_client.get_positions(account_id)
                return result
            return {'error': 'Invalid account_id or RPC client not available'}
        except Exception as e:
            return {'error': str(e)}
    
    async def _discover_local_accounts(self):
        """发现本主机负责的账户"""
        try:
            all_accounts = self.account_manager.load_all_accounts()

            self.local_accounts = {}

            for account_id, account_config in all_accounts.items():
                account_host = account_config.host_id

                if account_host == self.host_id or not account_host:
                    config_dict = {
                        'login': account_config.login,
                        'password': account_config.password,  # 添加密码字段
                        'server': account_config.server,
                        'terminal_path': account_config.terminal_path,
                        'account_name': account_config.name,
                        'account_type': 'demo',  # 默认为demo类型
                        'magic_number': account_config.magic_number,
                        'max_volume': account_config.max_volume,
                        'min_volume': account_config.min_volume,
                        'allowed_symbols': account_config.allowed_symbols,
                        'max_daily_loss': account_config.max_daily_loss,
                        'max_positions': account_config.max_positions
                    }

                    self.local_accounts[account_id] = config_dict
                    logger.info(f"发现本地账户: {account_id} (主机: {account_config.host_id})")

            logger.info(f"本主机负责 {len(self.local_accounts)} 个账户")

        except Exception as e:
            logger.error(f"发现本地账户失败: {e}")
            raise
    
    
    async def _start_account_processes_enhanced(self):
        """启动账户进程"""
        for account_id, account_config in self.local_accounts.items():
            try:
                logger.info(f"启动账户: {account_id}")
                
                try:
                    terminal_started = await self.mt5_process_manager.start_terminal_process_new(
                        account_id, account_config
                    )
                    
                    if not terminal_started:
                        logger.error(f"终端启动失败，跳过账户: {account_id}")
                        continue
                        
                    logger.info(f"终端进程启动成功: {account_id}")
                except Exception as terminal_error:
                    logger.error(f"启动终端进程异常 {account_id}: {terminal_error}")
                    continue
                
                await asyncio.sleep(1)
                
                try:
                    terminal_status = self.mt5_process_manager.get_terminal_status_new(account_id)
                    logger.info(f"终端状态: {terminal_status}")
                except Exception as status_error:
                    logger.warning(f"获取终端状态失败 {account_id}: {status_error}")
                
                try:
                    logger.info(f"开始创建分离进程: {account_id}")
                    
                    await asyncio.wait_for(
                        self._create_separated_account_processes(account_id, account_config),
                        timeout=30  # 30秒超时
                    )
                    logger.info(f"分离进程创建成功: {account_id}")
                    
                except asyncio.TimeoutError:
                    logger.error(f"创建分离进程超时 {account_id}: 30秒超时")
                    continue
                except Exception as proc_error:
                    logger.error(f"创建分离进程失败 {account_id}: {proc_error}")

                    import traceback
                    logger.error(f"详细错误信息: {traceback.format_exc()}")
                    continue
                
                logger.info(f"账户启动成功: {account_id}")
                
            except Exception as e:
                logger.error(f"启动账户失败 {account_id}: {e}")
                continue
        
        logger.info(f"账户进程启动完成，成功启动 {len(self.monitor_processes)} 个监控器和 {len(self.executor_processes)} 个执行器")
    
    async def _create_separated_account_processes(self, account_id: str, account_config: Dict):
        """创建分离的账户进程 - 监控器和执行器"""
        try:
            process_config = {
                'login': account_config.get('login'),
                'password': account_config.get('password'),  
                'server': account_config.get('server'),
                'terminal_path': account_config.get('terminal_path'),
                'host_id': self.host_id,
                'nats': self.config.get('nats', {}),
                'account_name': account_config.get('account_name'),
                'account_type': account_config.get('account_type'),
                'magic_number': account_config.get('magic_number', 12345),
                'max_volume': account_config.get('max_volume', 1.0),
                'min_volume': account_config.get('min_volume', 0.01),
                # 架构配置 - 确保子进程知道架构类型
                'architecture_mode': self.architecture_mode,
                'use_modern_routing': self.architecture_mode == 'optimized'
            }
            
            logger.info(f"账户 {account_id} 配置传递:")
            logger.info(f"  login: {process_config.get('login')}")
            logger.info(f"  password: {'***' if process_config.get('password') else 'None'}")
            logger.info(f"  server: {process_config.get('server')}")
            logger.info(f"  terminal_path: {process_config.get('terminal_path')}")
            
            nats_config = self.config.get('nats', {})
            logger.info(f"协调器NATS配置: {nats_config}")
            logger.info(f"传递给进程的NATS配置: {process_config.get('nats', {})}")
            
            await self._create_monitor_process(account_id, process_config)
            await self._create_executor_process(account_id, process_config)

            await self._create_rpc_service(account_id, account_config)

            logger.info(f"分离的账户进程已启动: {account_id} (监控器 + 执行器 + RPC服务)")
            
            if self.state_manager:
                monitor_pid = self.monitor_processes[account_id].pid if account_id in self.monitor_processes else None
                executor_pid = self.executor_processes[account_id].pid if account_id in self.executor_processes else None
                
                await self.state_manager.set(
                    f"account:{account_id}:processes",
                    {
                        'host_id': self.host_id,
                        'monitor_pid': monitor_pid,
                        'executor_pid': executor_pid,
                        'status': 'running',
                        'architecture': 'separated_processes',
                        'started_at': time.time()
                    },
                    ttl=60
                )
            
        except Exception as e:
            logger.error(f"创建分离进程失败 {account_id}: {e}")
            raise

    async def _create_monitor_process(self, account_id: str, process_config: Dict):
        """创建监控器进程"""
        try:
            monitor_queue = mp.Queue()
            self.monitor_queues[account_id] = monitor_queue
            
            monitor_process = mp.Process(
                target=run_account_monitor_process,
                args=(account_id, process_config, monitor_queue),
                name=f"mt5_monitor_{account_id}"
            )
            
            monitor_process.start()
            self.monitor_processes[account_id] = monitor_process
            
            logger.info(f"监控器进程启动: {account_id} (PID: {monitor_process.pid})")
            
            await asyncio.sleep(0.5)
            
        except Exception as e:
            logger.error(f"创建监控器进程失败 {account_id}: {e}")
            raise

    async def _create_executor_process(self, account_id: str, process_config: Dict):
        """创建执行器进程"""
        try:
            executor_queue = mp.Queue()
            self.executor_queues[account_id] = executor_queue
            
            executor_process = mp.Process(
                target=run_account_executor_process,
                args=(account_id, process_config, executor_queue),
                name=f"mt5_executor_{account_id}"
            )
            
            executor_process.start()
            self.executor_processes[account_id] = executor_process
            
            logger.info(f"执行器进程启动: {account_id} (PID: {executor_process.pid})")
            
            await asyncio.sleep(0.5)
            
        except Exception as e:
            logger.error(f"创建执行器进程失败 {account_id}: {e}")
            raise

    async def _create_rpc_service(self, account_id: str, account_config: Dict):
        """创建账户的RPC服务 """
        try:
            login = account_config.get('login')
            password = account_config.get('password')
            server = account_config.get('server')
            terminal_path = account_config.get('terminal_path')

            if not all([login, password, server, terminal_path]):
                logger.error(f"账户配置不完整，无法创建RPC服务: {account_id}")
                return

            success = await self.rpc_handler.add_account_service(
                account_id=account_id,
                login=int(login),
                password=password,
                server=server,
                terminal_path=terminal_path
            )

            if success:
                logger.info(f"RPC服务创建成功: {account_id}")
            else:
                logger.error(f"RPC服务创建失败: {account_id}")

        except Exception as e:
            logger.error(f"创建RPC服务失败 {account_id}: {e}")
            raise
    
    async def _monitor_processes(self):
        """监控分离的进程状态"""
        logger.info("启动分离进程监控")
        
        monitor_interval = 30   
        startup_grace_period = 60  
        startup_time = asyncio.get_event_loop().time()
        
        while self.running:
            try:
                current_time = asyncio.get_event_loop().time()
                is_startup_period = (current_time - startup_time) < startup_grace_period
                
                total_processes = len(self.monitor_processes) + len(self.executor_processes)
                if total_processes == 0 and is_startup_period:
                    logger.debug("启动期间：等待账户进程启动...")
                    await asyncio.sleep(5)  # 启动期间更频繁检查
                    continue
                
                logger.info(f"监控循环: 监控器进程={len(self.monitor_processes)}, 执行器进程={len(self.executor_processes)}")
                
                for account_id, monitor_process in list(self.monitor_processes.items()):
                    if not monitor_process.is_alive():
                        logger.warning(f"监控器进程异常退出: {account_id}")
                        # TODO: 实现自动重启监控器逻辑
                
                for account_id, executor_process in list(self.executor_processes.items()):
                    if not executor_process.is_alive():
                        logger.warning(f"执行器进程异常退出: {account_id}")
                        # TODO: 实现自动重启执行器逻辑
                
                try:
                    terminal_status = self.mt5_process_manager.get_all_process_status()
                    for account_id, status_info in terminal_status.items():
                        status = status_info.status if hasattr(status_info, 'status') else 'unknown'
                        if status not in ['running', 'connecting', 'unknown']:
                            logger.warning(f"终端进程状态异常: {account_id} - {status}")
                except Exception as e:
                    logger.warning(f"获取终端状态时出错: {e}")
                
                if total_processes > 0:
                    monitor_alive = sum(1 for p in self.monitor_processes.values() if p.is_alive())
                    executor_alive = sum(1 for p in self.executor_processes.values() if p.is_alive())
                    logger.info(f"进程状态: 监控器 {monitor_alive}/{len(self.monitor_processes)}, 执行器 {executor_alive}/{len(self.executor_processes)}")
                
                if total_processes == 0 and not is_startup_period:
                    logger.warning("没有任何账户进程在运行，系统可能存在配置问题")
                
                await asyncio.sleep(monitor_interval)
                
            except Exception as e:
                logger.error(f"进程监控异常: {e}")
                await asyncio.sleep(monitor_interval)
    
    async def _heartbeat_task(self):
        """心跳任务 """
        while self.running:
            try:
                # 发送系统心跳
                heartbeat = {
                    'host_id': self.host_id,
                    'timestamp': time.time(),
                    'accounts': list(self.local_accounts.keys()),
                    'architecture': 'separated_processes',
                    'monitor_processes': {
                        account_id: process.is_alive()
                        for account_id, process in self.monitor_processes.items()
                    },
                    'executor_processes': {
                        account_id: process.is_alive()
                        for account_id, process in self.executor_processes.items()
                    }
                }
                
                # TODO: 发送心跳到分布式系统
                logger.info(f"系统心跳: 主机={self.host_id}, 账户={len(self.local_accounts)}, 监控器={len([p for p in self.monitor_processes.values() if p.is_alive()])}, 执行器={len([p for p in self.executor_processes.values() if p.is_alive()])}")
                
                await asyncio.sleep(60)  # 1分钟心跳间隔
                
            except Exception as e:
                logger.error(f"心跳异常: {e}")
    
    async def stop(self):
        """停止协调器"""
        self.running = False
        logger.info("停止分布式MT5协调器 (分离进程)")
        
        try:
            # 停止新架构组件
            if self.hybrid_message_router:
                logger.info("停止混合消息路由器")
                await self.hybrid_message_router.stop()
                self.hybrid_message_router = None
            
            if self.optimized_nats_manager:
                logger.info("停止优化NATS管理器")
                await self.hybrid_queue_manager.shutdown()
                self.hybrid_queue_manager = None
            
            # 停止RPC组件
            if hasattr(self, 'rpc_handler') and self.rpc_handler:
                logger.info("停止RPC处理器")
                await self.rpc_handler.stop()

            for account_id, monitor_process in self.monitor_processes.items():
                if monitor_process.is_alive():
                    logger.info(f"停止监控器进程: {account_id}")
                    if account_id in self.monitor_queues:
                        try:
                            self.monitor_queues[account_id].put_nowait({'type': 'stop'})
                        except:
                            pass
                    
                    monitor_process.terminate()
                    monitor_process.join(timeout=10)
                    
                    if monitor_process.is_alive():
                        logger.warning(f"强制结束监控器进程: {account_id}")
                        monitor_process.kill()
            
            for account_id, executor_process in self.executor_processes.items():
                if executor_process.is_alive():
                    logger.info(f"停止执行器进程: {account_id}")
                    if account_id in self.executor_queues:
                        try:
                            self.executor_queues[account_id].put_nowait({'type': 'stop'})
                        except:
                            pass
                    
                    executor_process.terminate()
                    executor_process.join(timeout=10)
                    
                    if executor_process.is_alive():
                        logger.warning(f"强制结束执行器进程: {account_id}")
                        executor_process.kill()
            
            logger.info("停止所有终端进程")
            await asyncio.to_thread(self.mt5_process_manager.shutdown)
            
            # 无需停止传统组件 - 已全部移除

            if self.jetstream:
                await self.jetstream.disconnect()

            if self.state_manager:
                await self.state_manager.stop()
            
            logger.info("分布式MT5协调器已完全停止 (分离进程)")
            
        except Exception as e:
            logger.error(f"停止协调器时出错: {e}")
    
    async def get_system_status(self) -> Dict[str, Any]:
        """获取系统状态"""
        return {
            'host_id': self.host_id,
            'running': self.running,
            'local_accounts': list(self.local_accounts.keys()),
            'architecture': 'separated_processes',
            'monitor_processes': {
                account_id: {
                    'pid': process.pid,
                    'alive': process.is_alive(),
                    'name': process.name
                }
                for account_id, process in self.monitor_processes.items()
            },
            'executor_processes': {
                account_id: {
                    'pid': process.pid,
                    'alive': process.is_alive(),
                    'name': process.name
                }
                for account_id, process in self.executor_processes.items()
            },
            'terminal_processes': self.mt5_process_manager.get_all_process_status(),
            'architecture_layer': '4-layer separated architecture',
            'features': [
                'Strict responsibility separation',
                'Monitor/Executor split',
                'Message-based communication',
                'Dynamic role assignment',
                'Pairing-based configuration',
                'Process isolation',
                'Distributed coordination'
            ],
            'process_summary': {
                'total_monitors': len(self.monitor_processes),
                'alive_monitors': sum(1 for p in self.monitor_processes.values() if p.is_alive()),
                'total_executors': len(self.executor_processes),
                'alive_executors': sum(1 for p in self.executor_processes.values() if p.is_alive())
            },
            'modern_architecture': {
                'queue_manager': self.hybrid_queue_manager is not None,
                'hybrid_message_router': self.hybrid_message_router is not None,
                'architecture_mode': self.architecture_mode,
                'layered_streams': self.jetstream.get_layered_streams_status() if self.jetstream and hasattr(self.jetstream, 'get_layered_streams_status') else None,
                'legacy_components_removed': True,
                'clean_architecture': '4-tier stream only',
                'supported_modes': ['optimized', 'local']
            }
        }
    
    def send_message_to_monitor(self, account_id: str, message: Dict[str, Any]):
        """向监控器进程发送消息"""
        if account_id in self.monitor_queues:
            try:
                self.monitor_queues[account_id].put_nowait(message)
                logger.info(f"消息已发送到监控器 {account_id}: {message.get('type', 'unknown')}")
            except Exception as e:
                logger.error(f"发送消息到监控器失败 {account_id}: {e}")
        else:
            logger.warning(f"监控器消息队列不存在: {account_id}")
    
    def send_message_to_executor(self, account_id: str, message: Dict[str, Any]):
        """向执行器进程发送消息"""
        if account_id in self.executor_queues:
            try:
                self.executor_queues[account_id].put_nowait(message)
                logger.info(f"消息已发送到执行器 {account_id}: {message.get('type', 'unknown')}")
            except Exception as e:
                logger.error(f"发送消息到执行器失败 {account_id}: {e}")
        else:
            logger.warning(f"执行器消息队列不存在: {account_id}")
    
    def send_message_to_account(self, account_id: str, message: Dict[str, Any], target: str = 'both'):
        """向账户进程发送消息"""
        if target in ['both', 'monitor']:
            self.send_message_to_monitor(account_id, message)
        if target in ['both', 'executor']:
            self.send_message_to_executor(account_id, message)
    
    async def reload_pairing_config(self):
        """重新加载配对配置"""
        logger.info("重新加载配对配置 (分离进程)")
        
        reload_message = {'type': 'reload_config'}
        
        for account_id in self.monitor_processes:
            self.send_message_to_account(account_id, reload_message, 'both')
        
        logger.info("配对配置重载消息已发送到所有分离进程")
