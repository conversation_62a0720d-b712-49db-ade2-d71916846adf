核心设计理念

账户部署静态化 - 每个账户固定在特定主机
跟单关系动态化 - 主从关系可随时调整
信号路由智能化 - 通过NATS实现跨主机通信
配置管理集中化 - 支持热更新和动态调整



┌─────────────────────────────────────────────────────────────┐
│                    生产环境架构                              │
├─────────────────────────────────────────────────────────────┤
│  应用层: main.py → TradingSystem                           │
│  ├─ 配置管理: ConfigManager (从文件读取，避免硬编码)        │
│  ├─ 账户服务: MasterMonitor + SlaveExecutor               │
│  └─ 策略处理: CopyStrategyProcessor (工业级实现)           │
├─────────────────────────────────────────────────────────────┤
│  消息层: JetStream (MT5_TEST_STREAM)                       │
│  ├─ 主题: MT5.TRADES.*, MT5.COPY.*, mt5.master.*         │
│  ├─ 流管理: 自动处理冲突，使用现有流                       │
│  └─ 可靠性: 重试机制，自动恢复                             │
├─────────────────────────────────────────────────────────────┤
│  数据层: UnifiedTradeSignal (dataclass)                   │
│  ├─ 信号类型: OPEN, CLOSE, MODIFY                         │
│  ├─ 策略应用: 正向/反向跟单，比例调整                      │
│  └─ 序列化: to_dict/from_dict 方法                        │
├─────────────────────────────────────────────────────────────┤
│  基础设施: Docker Compose                                  │
│  ├─ NATS: nats-test (localhost:4222)                     │
│  ├─ Redis: redis-test (localhost:6379)                   │
│  └─ 网络: docker_default                                  │
└─────────────────────────────────────────────────────────────┘







新架构层次
第5层：DistributedMT5Coordinator (系统协调器)
    ↓ 管理
第4层：AccountConfigManager + CopyRelationshipManager (配置管理层)
    ↓ 配置
第3层：MT5AccountProcess + SignalRouter (业务进程层)
    ↓ 使用
第2层：MT5ProcessManager (进程管理层)
    ↓ 控制
第1层：MT5ClientEnhanced (API封装层)
    ↓
  MT5终端实例

  架构图
```mermaid
graph TB
    subgraph "主机A (Host A)"
        A_Coord[DistributedMT5Coordinator A]
        A_Config[AccountConfigManager]
        A_Copy[CopyRelationshipManager]
        
        subgraph "账户进程"
            A_ACC001[MT5AccountProcess<br/>ACC001 主/从]
            A_ACC003[MT5AccountProcess<br/>ACC003 从]
        end
        
        A_Router[SignalRouter A]
        A_PM[MT5ProcessManager A]
        
        A_Coord --> A_Config
        A_Coord --> A_Copy
        A_Config --> A_ACC001
        A_Config --> A_ACC003
        A_Copy --> A_Router
        A_ACC001 --> A_PM
        A_ACC003 --> A_PM
    end
    
    subgraph "主机B (Host B)"
        B_Coord[DistributedMT5Coordinator B]
        B_Config[AccountConfigManager]
        B_Copy[CopyRelationshipManager]
        
        subgraph "账户进程"
            B_ACC002[MT5AccountProcess<br/>ACC002 从]
            B_ACC004[MT5AccountProcess<br/>ACC004 主]
        end
        
        B_Router[SignalRouter B]
        B_PM[MT5ProcessManager B]
        
        B_Coord --> B_Config
        B_Coord --> B_Copy
        B_Config --> B_ACC002
        B_Config --> B_ACC004
        B_Copy --> B_Router
        B_ACC002 --> B_PM
        B_ACC004 --> B_PM
    end
    
    subgraph "NATS消息总线"
        NATS[NATS JetStream]
        Topic1[MT5.TRADES.*]
        Topic2[MT5.COPY.*]
        Topic3[MT5.SYSTEM.*]
    end
    
    A_ACC001 -.->|发布交易信号| NATS
    B_ACC004 -.->|发布交易信号| NATS
    
    NATS -.->|路由跟单信号| B_ACC002
    NATS -.->|路由跟单信号| A_ACC003
    
    style A_ACC001 fill:#90EE90
    style B_ACC004 fill:#90EE90
    style B_ACC002 fill:#FFB6C1
    style A_ACC003 fill:#FFB6C1
```

组件职责详解
```mermaid 
classDiagram
    class DistributedMT5Coordinator {
        -host_id: str
        -account_processes: Dict
        -config_manager: AccountConfigManager
        -relationship_manager: CopyRelationshipManager
        +start() 启动所有本地账户进程
        +monitor_processes() 监控进程健康
        +handle_config_updates() 处理配置更新
    }
    
    class AccountConfigManager {
        -configs: Dict[str, AccountConfig]
        -pairings: Dict obsolete
        +get_account_config(account_id) 获取账户配置
        +get_accounts_by_host(host_id) 获取主机账户
        +reload_configs() 重载配置
    }
    
    class CopyRelationshipManager {
        -relationships: Dict
        -config_file: str
        +get_slaves_for_master(master_id) 获取从账户
        +get_masters_for_slave(slave_id) 获取主账户
        +update_relationship() 更新关系
        +watch_config_changes() 监控配置变化
    }
    
    class MT5AccountProcess {
        -account_id: str
        -mt5_client: MT5ClientEnhanced
        -signal_router: SignalRouter
        -can_be_master: bool
        -can_be_slave: bool
        +monitor_positions() 监控持仓
        +handle_copy_signal() 处理跟单信号
        +execute_trade() 执行交易
    }
    
    class SignalRouter {
        -jetstream: JetStreamClient
        -relationship_manager: CopyRelationshipManager
        +route_signal(signal) 路由信号到从账户
        +build_copy_signal() 构建跟单信号
        +publish_to_slaves() 发布到从账户
    }
    
    DistributedMT5Coordinator --> AccountConfigManager
    DistributedMT5Coordinator --> CopyRelationshipManager
    MT5AccountProcess --> SignalRouter
    SignalRouter --> CopyRelationshipManager
```

信号流程图
```mermaid 
sequenceDiagram
    participant M as 主账户(ACC001)<br/>主机A
    participant MT5_M as MT5终端<br/>主账户
    participant SR as SignalRouter<br/>主机A
    participant CRM as CopyRelationshipManager
    participant NATS as NATS消息总线
    participant S1 as 从账户(ACC002)<br/>主机B
    participant S2 as 从账户(ACC003)<br/>主机A
    participant MT5_S1 as MT5终端<br/>从账户1
    participant MT5_S2 as MT5终端<br/>从账户2
    
    Note over M,MT5_M: 1. 主账户检测到交易
    M->>MT5_M: get_positions()
    MT5_M-->>M: 返回持仓变化
    
    Note over M,SR: 2. 生成交易信号
    M->>M: create_trade_signal()
    M->>SR: route_signal(signal)
    
    Note over SR,CRM: 3. 查询跟单关系
    SR->>CRM: get_slaves_for_master(ACC001)
    CRM-->>SR: [ACC002(正向), ACC003(反向)]
    
    Note over SR,NATS: 4. 构建并发布跟单信号
    SR->>SR: build_copy_signal(正向)
    SR->>NATS: publish("MT5.COPY.ACC002", signal)
    SR->>SR: build_copy_signal(反向)
    SR->>NATS: publish("MT5.COPY.ACC003", signal)
    
    Note over NATS,S1: 5. 从账户接收信号
    NATS->>S1: 订阅消息(ACC002)
    NATS->>S2: 订阅消息(ACC003)
    
    Note over S1,MT5_S1: 6. 执行跟单交易
    S1->>S1: validate_signal()
    S1->>MT5_S1: order_send(正向)
    MT5_S1-->>S1: 返回结果
    
    S2->>S2: validate_signal()
    S2->>MT5_S2: order_send(反向)
    MT5_S2-->>S2: 返回结果
    
    Note over S1,NATS: 7. 反馈执行结果
    S1->>NATS: publish("MT5.COPY.RESULT", result)
    S2->>NATS: publish("MT5.COPY.RESULT", result)
```

配置更新流程
```mermaid 
flowchart TB
    Start([配置文件修改]) --> Watch[CopyRelationshipManager<br/>检测到文件变化]
    Watch --> Reload[重新加载关系配置]
    Reload --> Validate[验证配置有效性]
    
    Validate -->|有效| Update[更新内存中的关系]
    Validate -->|无效| Log[记录错误日志]
    
    Update --> Notify[发布配置更新事件]
    Notify --> NATS[NATS: MT5.CONFIG.UPDATE]
    
    NATS --> Process1[AccountProcess 1<br/>接收更新]
    NATS --> Process2[AccountProcess 2<br/>接收更新]
    NATS --> ProcessN[AccountProcess N<br/>接收更新]
    
    Process1 --> Apply1[应用新的跟单关系]
    Process2 --> Apply2[应用新的跟单关系]
    ProcessN --> ApplyN[应用新的跟单关系]
```

完整的系统启动流程
```mermaid
flowchart TD
    Start([系统启动]) --> LoadConfig[加载系统配置]
    LoadConfig --> InitCoord[初始化Coordinator]
    
    InitCoord --> InitManagers[初始化管理器]
    InitManagers --> ACM[AccountConfigManager]
    InitManagers --> CRM[CopyRelationshipManager]
    
    ACM --> LoadAccounts[加载账户配置]
    CRM --> LoadRelations[加载跟单关系]
    
    LoadAccounts --> FilterLocal[筛选本主机账户]
    FilterLocal --> StartProcesses[启动账户进程]
    
    StartProcesses --> |每个账户| CreateProcess[创建MT5AccountProcess]
    CreateProcess --> ConnectMT5[连接MT5终端]
    CreateProcess --> InitRouter[初始化SignalRouter]
    
    ConnectMT5 --> CheckRole{检查账户能力}
    CheckRole -->|can_be_master| StartMonitor[启动持仓监控]
    CheckRole -->|can_be_slave| SubscribeCopy[订阅跟单主题]
    
    StartMonitor --> Running[系统运行中]
    SubscribeCopy --> Running
    
    Running --> |定期| HealthCheck[健康检查]
    Running --> |配置变化| HotReload[热重载配置]
    Running --> |信号产生| SignalFlow[信号流转]
```
