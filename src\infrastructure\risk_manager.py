"""
风险管理模块
"""
import asyncio
import time
import MetaTrader5 as mt5
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass, field
from enum import Enum
from datetime import datetime, timedelta

from ..messaging.message_types import TradeSignal, SignalType
from ..utils.logger import get_logger
from ..utils.metrics import get_metrics_collector


logger = get_logger(__name__)
metrics = get_metrics_collector()


class RiskLevel(str, Enum):
    """风险级别"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


@dataclass
class RiskSettings:
    """风险设置"""
    max_positions: int = 10
    max_lot_size: float = 10.0
    max_daily_loss: float = 1000.0
    max_drawdown: float = 0.2  # 20%
    max_exposure_per_symbol: float = 5.0
    max_total_exposure: float = 50.0
    
    max_trades_per_hour: int = 100
    max_trades_per_day: int = 1000
    
    allowed_symbols: List[str] = field(default_factory=list)
    blocked_symbols: List[str] = field(default_factory=list)
    
    trading_hours: Dict[str, Dict[str, str]] = field(default_factory=dict)
    
    emergency_stop_loss: float = 2000.0
    emergency_stop_drawdown: float = 0.3


@dataclass
class RiskCheckResult:
    """风险检查结果"""
    allowed: bool
    risk_level: RiskLevel
    reason: str
    details: Dict = field(default_factory=dict)


@dataclass
class RiskStatus:
    """风险状态"""
    has_violations: bool = False
    violations: List[str] = field(default_factory=list)
    current_exposure: float = 0.0
    daily_pnl: float = 0.0
    drawdown: float = 0.0
    active_positions: int = 0
    trades_today: int = 0
    trades_this_hour: int = 0


class RiskManager:
    """风险管理器"""
    
    def __init__(self, account_config: dict, risk_settings: Optional[RiskSettings] = None):
        self.account_config = account_config
        self.settings = risk_settings or self._create_default_settings()
        
        # 状态跟踪
        self.daily_trades = 0
        self.hourly_trades = 0
        self.daily_pnl = 0.0
        self.peak_equity = 0.0
        self.current_drawdown = 0.0
        
        # 交易历史
        self.trade_history = []
        self.position_history = []
        
        # 时间跟踪
        self.last_reset_day = datetime.now().date()
        self.last_reset_hour = datetime.now().hour
        
        # 紧急停止状态
        self.emergency_stop = False
        self.emergency_reason = ""
        
        # 初始化账户信息
        self.initial_balance = 0.0
        self.current_equity = 0.0
        
    def _create_default_settings(self) -> RiskSettings:
        """创建默认风险设置"""
        account_risk = self.account_config.get('risk_settings', {})
        
        return RiskSettings(
            max_positions=account_risk.get('max_positions', 10),
            max_lot_size=account_risk.get('max_lot_size', 10.0),
            max_daily_loss=account_risk.get('max_daily_loss', 1000.0),
            max_drawdown=account_risk.get('max_drawdown', 0.2),
            allowed_symbols=account_risk.get('allowed_symbols', []),
            blocked_symbols=account_risk.get('blocked_symbols', [])
        )
    
    async def initialize(self):
        """初始化风险管理器"""
        try:
            # 获取账户信息
            account_info = mt5.account_info()
            if account_info:
                self.initial_balance = account_info.balance
                self.current_equity = account_info.equity
                self.peak_equity = account_info.equity
                
                logger.info(f"风险管理器初始化成功: 初始余额={self.initial_balance}, 当前净值={self.current_equity}")
            else:
                logger.warning("无法获取账户信息")
                
        except Exception as e:
            logger.error(f"风险管理器初始化失败: {e}")
    
    async def check_signal(self, signal: TradeSignal) -> RiskCheckResult:
        """检查信号是否符合风险要求"""
        try:
            self._update_time_counters()
            
            if self.emergency_stop:
                return RiskCheckResult(
                    allowed=False,
                    risk_level=RiskLevel.CRITICAL,
                    reason=f"紧急停止: {self.emergency_reason}"
                )
            
            basic_result = self._check_basic_limits(signal)
            if not basic_result.allowed:
                return basic_result
            
            position_result = self._check_position_limits(signal)
            if not position_result.allowed:
                return position_result
            
            exposure_result = self._check_exposure_limits(signal)
            if not exposure_result.allowed:
                return exposure_result
            
            frequency_result = self._check_trading_frequency(signal)
            if not frequency_result.allowed:
                return frequency_result
            
            symbol_result = self._check_symbol_restrictions(signal)
            if not symbol_result.allowed:
                return symbol_result
            
            # 时间检查
            time_result = self._check_trading_hours(signal)
            if not time_result.allowed:
                return time_result
            
            # 所有检查通过
            return RiskCheckResult(
                allowed=True,
                risk_level=RiskLevel.LOW,
                reason="通过所有风险检查"
            )
            
        except Exception as e:
            logger.error(f"风险检查异常: {e}")
            return RiskCheckResult(
                allowed=False,
                risk_level=RiskLevel.CRITICAL,
                reason=f"风险检查异常: {e}"
            )
    
    def _check_basic_limits(self, signal: TradeSignal) -> RiskCheckResult:
        """检查基本限制"""
        if signal.type == SignalType.POSITION_OPEN:
            data = signal.data
            
            # 检查手数限制
            if data.get('volume', 0) > self.settings.max_lot_size:
                return RiskCheckResult(
                    allowed=False,
                    risk_level=RiskLevel.HIGH,
                    reason=f"手数超过限制: {data.get('volume')} > {self.settings.max_lot_size}"
                )
        
        return RiskCheckResult(allowed=True, risk_level=RiskLevel.LOW, reason="基本检查通过")
    
    def _check_position_limits(self, signal: TradeSignal) -> RiskCheckResult:
        """检查持仓限制"""
        if signal.type == SignalType.POSITION_OPEN:
            # 获取当前持仓数量
            positions = mt5.positions_get()
            current_positions = len(positions) if positions else 0
            
            if current_positions >= self.settings.max_positions:
                return RiskCheckResult(
                    allowed=False,
                    risk_level=RiskLevel.HIGH,
                    reason=f"持仓数量超过限制: {current_positions} >= {self.settings.max_positions}"
                )
        
        return RiskCheckResult(allowed=True, risk_level=RiskLevel.LOW, reason="持仓检查通过")
    
    def _check_exposure_limits(self, signal: TradeSignal) -> RiskCheckResult:
        """检查风险敞口限制"""
        if signal.type == SignalType.POSITION_OPEN:
            data = signal.data
            symbol = data.get('symbol', '')
            volume = data.get('volume', 0)
            
            # 检查单个品种敞口
            positions = mt5.positions_get(symbol=symbol)
            if positions:
                current_exposure = sum(pos.volume for pos in positions)
                if current_exposure + volume > self.settings.max_exposure_per_symbol:
                    return RiskCheckResult(
                        allowed=False,
                        risk_level=RiskLevel.MEDIUM,
                        reason=f"品种敞口超过限制: {symbol} {current_exposure + volume} > {self.settings.max_exposure_per_symbol}"
                    )
            
            # 检查总敞口
            all_positions = mt5.positions_get()
            if all_positions:
                total_exposure = sum(pos.volume for pos in all_positions)
                if total_exposure + volume > self.settings.max_total_exposure:
                    return RiskCheckResult(
                        allowed=False,
                        risk_level=RiskLevel.MEDIUM,
                        reason=f"总敞口超过限制: {total_exposure + volume} > {self.settings.max_total_exposure}"
                    )
        
        return RiskCheckResult(allowed=True, risk_level=RiskLevel.LOW, reason="敞口检查通过")
    
    def _check_trading_frequency(self, signal: TradeSignal) -> RiskCheckResult:
        """检查交易频率限制"""
        if signal.type == SignalType.POSITION_OPEN:
            # 检查每小时交易次数
            if self.hourly_trades >= self.settings.max_trades_per_hour:
                return RiskCheckResult(
                    allowed=False,
                    risk_level=RiskLevel.MEDIUM,
                    reason=f"每小时交易次数超过限制: {self.hourly_trades} >= {self.settings.max_trades_per_hour}"
                )
            
            # 检查每日交易次数
            if self.daily_trades >= self.settings.max_trades_per_day:
                return RiskCheckResult(
                    allowed=False,
                    risk_level=RiskLevel.MEDIUM,
                    reason=f"每日交易次数超过限制: {self.daily_trades} >= {self.settings.max_trades_per_day}"
                )
        
        return RiskCheckResult(allowed=True, risk_level=RiskLevel.LOW, reason="频率检查通过")
    
    def _check_symbol_restrictions(self, signal: TradeSignal) -> RiskCheckResult:
        """检查品种限制"""
        if signal.type == SignalType.POSITION_OPEN:
            symbol = signal.data.get('symbol', '')
            
            # 检查被禁止的品种
            if symbol in self.settings.blocked_symbols:
                return RiskCheckResult(
                    allowed=False,
                    risk_level=RiskLevel.HIGH,
                    reason=f"品种被禁止: {symbol}"
                )
            
            # 检查允许的品种
            if self.settings.allowed_symbols and symbol not in self.settings.allowed_symbols:
                return RiskCheckResult(
                    allowed=False,
                    risk_level=RiskLevel.MEDIUM,
                    reason=f"品种不在允许列表中: {symbol}"
                )
        
        return RiskCheckResult(allowed=True, risk_level=RiskLevel.LOW, reason="品种检查通过")
    
    def _check_trading_hours(self, signal: TradeSignal) -> RiskCheckResult:
        """检查交易时间限制"""
        if not self.settings.trading_hours:
            return RiskCheckResult(allowed=True, risk_level=RiskLevel.LOW, reason="无时间限制")
        
        current_time = datetime.now().time()
        current_day = datetime.now().strftime('%A').lower()
        
        # 检查是否在允许的交易时间内
        if current_day in self.settings.trading_hours:
            hours = self.settings.trading_hours[current_day]
            start_time = datetime.strptime(hours.get('start', '00:00'), '%H:%M').time()
            end_time = datetime.strptime(hours.get('end', '23:59'), '%H:%M').time()
            
            if not (start_time <= current_time <= end_time):
                return RiskCheckResult(
                    allowed=False,
                    risk_level=RiskLevel.MEDIUM,
                    reason=f"超出交易时间: {current_time} 不在 {start_time}-{end_time} 范围内"
                )
        
        return RiskCheckResult(allowed=True, risk_level=RiskLevel.LOW, reason="时间检查通过")
    
    def _update_time_counters(self):
        """更新时间计数器"""
        current_date = datetime.now().date()
        current_hour = datetime.now().hour
        
        # 重置日计数器
        if current_date != self.last_reset_day:
            self.daily_trades = 0
            self.daily_pnl = 0.0
            self.last_reset_day = current_date
        
        # 重置小时计数器
        if current_hour != self.last_reset_hour:
            self.hourly_trades = 0
            self.last_reset_hour = current_hour
    
    async def post_execution_check(self, signal: TradeSignal, success: bool):
        """执行后检查"""
        try:
            if success and signal.type == SignalType.POSITION_OPEN:
                # 更新交易计数
                self.daily_trades += 1
                self.hourly_trades += 1
                
                # 记录交易历史
                self.trade_history.append({
                    'timestamp': time.time(),
                    'signal': signal.dict(),
                    'success': success
                })
                
                # 限制历史记录长度
                if len(self.trade_history) > 1000:
                    self.trade_history = self.trade_history[-1000:]
            
            # 更新账户状态
            await self._update_account_status()
            
            # 检查紧急停止条件
            await self._check_emergency_stop()
            
        except Exception as e:
            logger.error(f"执行后检查错误: {e}")
    
    async def _update_account_status(self):
        """更新账户状态"""
        try:
            account_info = mt5.account_info()
            if account_info:
                self.current_equity = account_info.equity
                
                # 更新最高净值
                if self.current_equity > self.peak_equity:
                    self.peak_equity = self.current_equity
                
                # 计算回撤
                if self.peak_equity > 0:
                    self.current_drawdown = (self.peak_equity - self.current_equity) / self.peak_equity
                
                # 计算当日盈亏
                self.daily_pnl = self.current_equity - self.initial_balance
                
        except Exception as e:
            logger.error(f"更新账户状态失败: {e}")
    
    async def _check_emergency_stop(self):
        """检查紧急停止条件"""
        try:
            if self.daily_pnl < -self.settings.emergency_stop_loss:
                self.emergency_stop = True
                self.emergency_reason = f"日亏损超过限制: {self.daily_pnl}"
                logger.critical(f"触发紧急停止: {self.emergency_reason}")
                return
            
            if self.current_drawdown > self.settings.emergency_stop_drawdown:
                self.emergency_stop = True
                self.emergency_reason = f"回撤超过限制: {self.current_drawdown:.2%}"
                logger.critical(f"触发紧急停止: {self.emergency_reason}")
                return
            
        except Exception as e:
            logger.error(f"检查紧急停止条件失败: {e}")
    
    async def periodic_check(self) -> RiskStatus:
        """定期风险检查"""
        try:
            await self._update_account_status()
            
            violations = []
            
            if self.daily_pnl < -self.settings.max_daily_loss:
                violations.append(f"日亏损超过限制: {self.daily_pnl}")
            
            if self.current_drawdown > self.settings.max_drawdown:
                violations.append(f"回撤超过限制: {self.current_drawdown:.2%}")
            
            positions = mt5.positions_get()
            current_positions = len(positions) if positions else 0
            
            if current_positions > self.settings.max_positions:
                violations.append(f"持仓数量超过限制: {current_positions}")
            
            total_exposure = sum(pos.volume for pos in positions) if positions else 0
            
            return RiskStatus(
                has_violations=len(violations) > 0,
                violations=violations,
                current_exposure=total_exposure,
                daily_pnl=self.daily_pnl,
                drawdown=self.current_drawdown,
                active_positions=current_positions,
                trades_today=self.daily_trades,
                trades_this_hour=self.hourly_trades
            )
            
        except Exception as e:
            logger.error(f"定期风险检查失败: {e}")
            return RiskStatus(has_violations=True, violations=[f"检查失败: {e}"])
    
    async def reset_emergency_stop(self):
        """重置紧急停止状态"""
        self.emergency_stop = False
        self.emergency_reason = ""
        logger.info("紧急停止状态已重置")
    
    async def update_settings(self, new_settings: RiskSettings):
        """更新风险设置"""
        self.settings = new_settings
        logger.info("风险设置已更新")
    
    def get_status(self) -> Dict:
        """获取风险管理器状态"""
        return {
            'emergency_stop': self.emergency_stop,
            'emergency_reason': self.emergency_reason,
            'daily_trades': self.daily_trades,
            'hourly_trades': self.hourly_trades,
            'daily_pnl': self.daily_pnl,
            'current_drawdown': self.current_drawdown,
            'peak_equity': self.peak_equity,
            'current_equity': self.current_equity,
            'settings': {
                'max_positions': self.settings.max_positions,
                'max_lot_size': self.settings.max_lot_size,
                'max_daily_loss': self.settings.max_daily_loss,
                'max_drawdown': self.settings.max_drawdown
            }
        }
    
    async def stop(self):
        """停止风险管理器"""
        logger.info("风险管理器已停止")