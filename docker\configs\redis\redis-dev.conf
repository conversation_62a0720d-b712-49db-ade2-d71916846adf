# Redis主节点配置 - MT5分布式交易系统
# 支持所有账户(ACC001, ACC002, ACC003)的统一数据存储
# 配置为主从架构的主节点

# 基本配置
port 6379
bind 0.0.0.0
protected-mode no

# 主从复制配置
replica-serve-stale-data yes
replica-read-only yes
repl-diskless-sync no
repl-diskless-sync-delay 5
repl-ping-replica-period 10
repl-timeout 60
repl-disable-tcp-nodelay no
repl-backlog-size 1mb
repl-backlog-ttl 3600

# 数据持久化
save 900 1
save 300 10
save 60 10000

# AOF持久化 (更安全)
appendonly yes
appendfilename "appendonly.aof"
appendfsync everysec
no-appendfsync-on-rewrite no
auto-aof-rewrite-percentage 100
auto-aof-rewrite-min-size 64mb

# 内存管理
maxmemory 1gb
maxmemory-policy allkeys-lru

# 网络配置
tcp-keepalive 300
timeout 0

# 日志配置
loglevel notice
logfile "/data/redis-master.log"

# 数据库数量 (为不同账户分配不同数据库)
databases 16
dir /data

# 客户端连接
maxclients 10000

# 慢查询日志
slowlog-log-slower-than 10000
slowlog-max-len 128

# 键空间通知 (用于MT5事件监听)
notify-keyspace-events "Ex"

# 禁用危险命令
rename-command FLUSHDB ""
rename-command FLUSHALL ""
rename-command DEBUG ""

# 性能优化
tcp-backlog 511
hz 10

# 内存碎片整理
activedefrag yes

# 高可用配置
min-replicas-to-write 1
min-replicas-max-lag 10