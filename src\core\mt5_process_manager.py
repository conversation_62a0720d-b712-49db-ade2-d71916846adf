# src/core/mt5_process_manager.py
"""
MT5进程管理器   - 第2层进程管理器
"""

import os
import asyncio
import psutil
import yaml
import logging
import time
import threading
import uuid
import queue
import traceback
from typing import Dict, Optional, List, Any
from dataclasses import dataclass
import multiprocessing as mp
from pathlib import Path
from datetime import datetime
from logging.handlers import RotatingFileHandler

try:
    import MetaTrader5 as mt5
except ImportError:
    mt5 = None
    print("警告: MetaTrader5模块未安装")

logger = logging.getLogger("TradeCopier.MT5ProcessManager")

# ==================== 导入数据模型 ====================

from ..messaging.message_types import (
    CommandRequest, CommandResponse, ProcessStatus,
    SystemHealth, ProcessState
)

@dataclass
class MT5TerminalConfig:
    """MT5终端配置 """
    account_id: str
    login: int
    password: str  # 从环境变量读取
    server: str
    terminal_exe_path: str
    data_folder: str
    config_path: str

    portable_mode: bool = True
    isolated_data_dir: Optional[str] = None
    log_level: str = "INFO"
    timeout: int = 30000
    retry_attempts: int = 3
    retry_delay: int = 5

    process_id: Optional[int] = None
    status: str = "not_started"
    created_at: Optional[datetime] = None

    @classmethod
    def from_config_file(cls, config_path: str, account_id: str):
        """从YAML配置文件加载"""
        try:
            account_config_path = Path(config_path).parent / "accounts" / f"{account_id}.yaml"

            if not account_config_path.exists():
                raise FileNotFoundError(f"账户配置文件不存在: {account_config_path}")

            with open(account_config_path, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)

            mt5_config = config['mt5']['connection']

            # 从环境变量读取密码
            password_env_key = f"MT5_{account_id}_PASSWORD"
            password = os.environ.get(password_env_key)
            if not password:
                raise ValueError(f"环境变量 {password_env_key} 未设置")

            return cls(
                account_id=account_id,
                login=mt5_config['login'],
                password=password,
                server=mt5_config['server'],
                terminal_exe_path=mt5_config['terminal_path'],
                data_folder="",   
                config_path=str(account_config_path),
                timeout=mt5_config.get('timeout', 30000),
                retry_attempts=mt5_config.get('retry_attempts', 3),
                retry_delay=mt5_config.get('retry_delay', 5),
                created_at=datetime.now()
            )

        except Exception as e:
            logger.error(f"加载配置文件失败 {account_id}: {e}")
            raise

    def to_dict(self) -> Dict:
        """转换为字典"""
        return {
            'account_id': self.account_id,
            'login': self.login,
            'server': self.server,
            'terminal_exe_path': self.terminal_exe_path,
            'data_folder': self.data_folder,
            'portable_mode': self.portable_mode,
            'isolated_data_dir': self.isolated_data_dir,
            'timeout': self.timeout,
            'process_id': self.process_id,
            'status': self.status,
            'created_at': self.created_at.isoformat() if self.created_at else None
        }


# ==================== 真进程隔离核心类 ====================

class MT5Process(mp.Process):
    """独立的MT5连接进程 - 集成process.py优秀技术"""

    def __init__(self, account_name: str, account_config: Dict[str, Any],
                 command_queue: mp.Queue,
                 result_queue: mp.Queue,
                 system_result_queue: mp.Queue):
        super().__init__()
        self.account_name = account_name
        self.account_config = account_config
        self.command_queue = command_queue
        self.result_queue = result_queue
        self.system_result_queue = system_result_queue

        self.login = account_config.get('login')
        self.password = str(account_config.get('password', ''))  # 确保密码是字符串
        self.server = account_config.get('server')
        self.terminal_path = account_config.get('terminal_path')

        self.mt5_connected = False
        self.daemon = True
        self.start_time = time.time()
        self.error_count = 0
        self.running = True  # 添加running属性

    def stop(self):
        """停止进程"""
        self.running = False
        logger.info(f"设置停止标志 - 账户: {self.account_name}")

    def run(self):
        """进程主循环"""
        logger.info(f"MT5进程启动 - 账户: {self.account_name} (PID: {self.pid})")
        self.system_result_queue.put(CommandResponse(
            type='process_started',
            account=self.account_name,
            status='success',
            data={'pid': self.pid, 'connecting': True},
            request_id=f'startup_{self.account_name}_{int(time.time())}'
        ).model_dump())

        self._initial_connect()

        last_heartbeat = time.time()
        last_connection_check = time.time()
        logger.info(f"进入主循环 - 账户: {self.account_name}, 连接状态: {self.mt5_connected}")

        while self.running:
            try:
                try:
                    command_dict = self.command_queue.get(timeout=0.5)
                    command = CommandRequest(**command_dict)
                    logger.info(f"收到命令 - 账户: {self.account_name}, 类型: {command.type}, ID: {command.request_id}")

                    if command.type == 'shutdown':
                        logger.info(f"收到关闭命令 - 账户: {self.account_name}")
                        self.running = False
                        break

                    if not self._check_and_reconnect():
                        self.result_queue.put(CommandResponse(
                            type='error', account=self.account_name, status='error',
                            error_message='MT5 not connected. Reconnection failed.',
                            request_id=command.request_id
                        ).model_dump())
                        continue

                    result = self._process_command(command)
                    self.result_queue.put(result.model_dump())

                except queue.Empty:
                    current_time = time.time()
                    if current_time - last_heartbeat > 5:
                        self._send_heartbeat()
                        last_heartbeat = current_time

                    if current_time - last_connection_check > 30:
                        self._check_and_reconnect()
                        last_connection_check = current_time

            except Exception as e:
                logger.error(f"进程主循环异常 - 账户: {self.account_name}: {e}")
                logger.error(traceback.format_exc())
                self.error_count += 1
                if self.error_count > 10:
                    logger.error(f"错误次数过多，退出进程 - 账户: {self.account_name}")
                    break
                time.sleep(1)

        self._cleanup()
        logger.info(f"MT5进程结束 - 账户: {self.account_name}")

    def _initial_connect(self):
        """执行首次连接MT5的逻辑，包含延迟和重试"""
        import hashlib
        hash_value = int(hashlib.md5(self.account_name.encode()).hexdigest()[:8], 16)
        connection_delay = (hash_value % 15) + 3
        logger.info(f"等待 {connection_delay} 秒后连接MT5 - 账户: {self.account_name}")

        for i in range(connection_delay):
            if not self.running:
                logger.info(f"收到停止信号，中断连接等待: {self.account_name}")
                return
            time.sleep(1)

        max_attempts = 3
        for attempt in range(max_attempts):
            if not self.running:
                logger.info(f"收到停止信号，中断连接尝试: {self.account_name}")
                return

            if self._connect_mt5():
                self.system_result_queue.put(CommandResponse(
                    type='connection_status', account=self.account_name, status='success',
                    data={'connected': True, 'message': 'MT5 connected successfully'},
                    request_id=f'connect_{self.account_name}_{int(time.time())}'
                ).model_dump())
                return
            else:
                logger.warning(f"连接尝试 {attempt + 1}/{max_attempts} 失败 - 账户: {self.account_name}")
                if attempt < max_attempts - 1:
                    for i in range(10):
                        if not self.running:
                            logger.info(f"收到停止信号，中断重试等待: {self.account_name}")
                            return
                        time.sleep(1)

        self.system_result_queue.put(CommandResponse(
            type='connection_status', account=self.account_name, status='error',
            error_message='All connection attempts failed',
            request_id=f'connect_fail_{self.account_name}_{int(time.time())}'
        ).model_dump())

    def _connect_mt5(self) -> bool:
        """连接MT5"""
        try:
            self._shutdown_mt5()  
            if self._initialize_mt5() and self._verify_connection():
                return True
        except Exception as e:
            logger.error(f"连接MT5异常 - 账户: {self.account_name}: {e}")
        return False

    def _initialize_mt5(self) -> bool:
        """执行mt5.initialize()"""
        logger.info(f"尝试初始化MT5 - 账户: {self.account_name}")
        logger.info(f"连接参数: login={self.login}, server={self.server}, path={self.terminal_path}, password_len={len(self.password) if self.password else 0}")
        logger.info(f"密码内容: '{self.password}'")
        
        if not self.login or self.login <= 0:
            logger.error(f"无效的登录号: {self.login}")
            return False
        if not self.password:
            logger.error("密码为空")
            return False
        if not self.server:
            logger.error("服务器为空")
            return False
        if not self.terminal_path:
            logger.error("终端路径为空")
            return False
        
        init_success = mt5.initialize(
            path=self.terminal_path,
            login=int(self.login),          # 确保login是整数
            password=str(self.password),    # 确保password是字符串
            server=str(self.server),        # 确保server是字符串
            timeout=60000,
            portable=False
        )
        if not init_success:
            error = mt5.last_error()
            logger.error(f"MT5初始化失败 - 账户: {self.account_name}: {error}")
            import os
            if not os.path.exists(self.terminal_path):
                logger.error(f"终端路径不存在: {self.terminal_path}")
        return init_success

    def _verify_connection(self) -> bool:
        """验证MT5连接是否正确"""
        time.sleep(3)   
        account_info = mt5.account_info()
        if account_info and account_info.login == self.login:
            self.mt5_connected = True
            logger.info(f"成功连接MT5 - 账户: {self.account_name} ({self.login}), 余额: {account_info.balance}")
            if not account_info.trade_allowed:
                logger.warning(f"账户 {self.account_name} 交易权限被禁用")
            return True
        else:
            actual_login = account_info.login if account_info else "Unknown"
            logger.error(f"连接到错误账户 - 期望: {self.login}, 实际: {actual_login}")
            self._shutdown_mt5()
            return False

    def _check_and_reconnect(self) -> bool:
        """检查连接，如果断开则尝试重连一次"""
        if self.mt5_connected and self._check_connection():
            self.error_count = 0
            return True

        logger.warning(f"连接丢失，尝试重连 - 账户: {self.account_name}")
        if self._connect_mt5():
            self.error_count = 0
            return True
        else:
            self.error_count += 1
            if self.error_count > 5:
                logger.error(f"重连失败次数过多，将退出进程 - 账户: {self.account_name}")
                raise RuntimeError("Too many reconnection failures")
        return False

    def _check_connection(self) -> bool:
        """轻量级地检查MT5连接状态"""
        if not self.mt5_connected:
            return False
        try:
            return mt5.terminal_info() is not None
        except Exception:
            self.mt5_connected = False
            return False

    def _shutdown_mt5(self):
        """关闭MT5连接"""
        try:
            if self.mt5_connected:
                mt5.shutdown()
                self.mt5_connected = False
        except Exception as e:
            logger.warning(f"关闭MT5连接时出错 - 账户: {self.account_name}: {e}")

    def _send_heartbeat(self):
        """发送心跳"""
        try:
            account_info = None
            if self.mt5_connected:
                account_info = mt5.account_info()
                if account_info:
                    account_info = {
                        'login': account_info.login,
                        'balance': account_info.balance,
                        'equity': account_info.equity,
                        'server': account_info.server
                    }

            self.system_result_queue.put(CommandResponse(
                type='heartbeat', account=self.account_name, status='success',
                data={
                    'connected': self.mt5_connected,
                    'uptime': time.time() - self.start_time,
                    'error_count': self.error_count,
                    'account_info': account_info
                },
                request_id=f'heartbeat_{self.account_name}_{int(time.time())}'
            ).model_dump())
        except Exception as e:
            logger.error(f"发送心跳失败 - 账户: {self.account_name}: {e}")

    def _process_command(self, command: CommandRequest) -> CommandResponse:
        """处理命令分发"""
        start_time = time.time()
        
        handlers = {
            'get_account_info': self._get_account_info,
            'execute_trade': self._execute_trade,
            'send_order': self._execute_trade,  # 兼容性别名
            'get_positions': self._get_positions,
            'get_symbol_info': self._get_symbol_info,
            'close_position': self._close_position,
            'modify_position': self._modify_position,
            'get_rates': self._get_rates,
            'get_history': self._get_history,
            'get_symbols': self._get_symbols,
        }
        
        handler = handlers.get(command.type)
        
        try:
            if handler:
                result = handler(command)  # 传递完整的command对象
            else:
                result = CommandResponse(
                    type='error', account=self.account_name, status='error',
                    error_message=f'Unknown command: {command.type}',
                    request_id=command.request_id
                )
            
            result.request_id = command.request_id
            result.account = self.account_name
            result.execution_time_ms = (time.time() - start_time) * 1000
            
            return result
            
        except Exception as e:
            logger.error(f"处理命令'{command.type}'时异常: {e}", exc_info=True)
            return CommandResponse(
                type='error', account=self.account_name, status='error',
                error_message=str(e), data={'traceback': traceback.format_exc()},
                request_id=command.request_id,
                execution_time_ms=(time.time() - start_time) * 1000
            )

    def _get_account_info(self, command: CommandRequest) -> CommandResponse:
        """获取账户信息"""
        try:
            info = mt5.account_info()
            if info:
                from ..messaging.message_types import AccountInfoModel
                account_data = AccountInfoModel.model_validate(info._asdict()).model_dump()
                return CommandResponse(
                    type='account_info', account=self.account_name, status='success',
                    data=account_data,
                    request_id=command.request_id
                )
            else:
                return CommandResponse(
                    type='account_info', account=self.account_name, status='error',
                    error_message="Failed to get account info",
                    request_id=command.request_id
                )
        except Exception as e:
            logger.error(f"获取账户信息异常: {e}")
            return CommandResponse(
                type='account_info', account=self.account_name, status='error',
                error_message=f"Account info error: {str(e)}",
                request_id=command.request_id
            )

    def _get_positions(self, command: CommandRequest) -> CommandResponse:
        """获取持仓信息"""
        try:
            symbol = command.params.get('symbol') if command.params else None
            
            if symbol:
                positions = mt5.positions_get(symbol=symbol)
            else:
                positions = mt5.positions_get()
                
            from ..messaging.message_types import PositionModel
            positions_data = [
                PositionModel.model_validate(p._asdict()).model_dump() 
                for p in positions
            ] if positions else []
            
            return CommandResponse(
                type='positions', account=self.account_name, status='success',
                data={'positions': positions_data},
                request_id=command.request_id
            )
            
        except Exception as e:
            logger.error(f"获取持仓信息异常: {e}")
            return CommandResponse(
                type='positions', account=self.account_name, status='error',
                error_message=f"Positions error: {str(e)}",
                request_id=command.request_id
            )
    
    def _get_symbol_info(self, command: CommandRequest) -> CommandResponse:
        """获取交易品种信息"""
        try:
            symbol = command.params.get('symbol')
            if not symbol:
                return CommandResponse(
                    type='symbol_info', account=self.account_name, status='error',
                    error_message='Symbol not provided',
                    request_id=command.request_id
                )
            
            info = mt5.symbol_info(symbol)
            if info:
                from ..messaging.message_types import SymbolInfoModel
                symbol_info = SymbolInfoModel.model_validate(info._asdict()).model_dump()
                return CommandResponse(
                    type='symbol_info', account=self.account_name, status='success', 
                    data=symbol_info,
                    request_id=command.request_id
                )
            else:
                return CommandResponse(
                    type='symbol_info', account=self.account_name, status='error',
                    error_message=f"Failed to get info for symbol {symbol}",
                    request_id=command.request_id
                )
                
        except Exception as e:
            logger.error(f"获取品种信息异常: {e}")
            return CommandResponse(
                type='symbol_info', account=self.account_name, status='error',
                error_message=f"Symbol info error: {str(e)}",
                request_id=command.request_id
            )
    
    def _execute_trade(self, command: CommandRequest) -> CommandResponse:
        """执行交易"""
        try:
            trade_request = command.params.get('request', command.params)
            symbol = trade_request.get('symbol')
            
            if not symbol:
                return CommandResponse(
                    type='trade_result', account=self.account_name, status='error', 
                    error_message="Symbol not provided",
                    request_id=command.request_id
                )
            
            symbol_info = mt5.symbol_info(symbol)
            if not symbol_info:
                return CommandResponse(
                    type='trade_result', account=self.account_name, status='error', 
                    error_message=f"Symbol {symbol} not found",
                    request_id=command.request_id
                )
            
            if not symbol_info.visible:
                mt5.symbol_select(symbol, True)
            
            request_dict = self._prepare_trade_request(trade_request)
            result = mt5.order_send(request_dict)
            
            response_dict = self._build_trade_response(result, request_dict)
            return CommandResponse(
                type='trade_result', account=self.account_name,
                request_id=command.request_id, **response_dict
            )

        except Exception as e:
            logger.error(f"执行交易异常: {e}")
            return CommandResponse(
                type='trade_result', account=self.account_name, status='error',
                error_message=f"Trade execution error: {str(e)}",
                request_id=command.request_id
            )
    
    def _prepare_trade_request(self, request: Dict[str, Any]) -> Dict[str, Any]:
        """准备发送给mt5.order_send的字典，处理枚举和默认值 """
        try:
            logger.info(f"原始交易请求: {request}")

            request_dict = request.copy()

            action_value = request_dict.get('action')
            if isinstance(action_value, str):
                action_map = {'DEAL': 1, 'PENDING': 2, 'SLTP': 3, 'MODIFY': 4, 'REMOVE': 5}
                request_dict['action'] = action_map.get(action_value, 1)
            elif isinstance(action_value, int):
                request_dict['action'] = action_value
            else:
                request_dict['action'] = 1

            type_value = request_dict.get('type')
            if isinstance(type_value, str):
                type_map = {
                    'BUY': 0, 'SELL': 1, 'BUY_LIMIT': 2, 'SELL_LIMIT': 3,
                    'BUY_STOP': 4, 'SELL_STOP': 5, 'BUY_STOP_LIMIT': 6, 'SELL_STOP_LIMIT': 7
                }
                request_dict['type'] = type_map.get(type_value, 0)
            elif isinstance(type_value, int):
                request_dict['type'] = type_value
            else:
                request_dict['type'] = 0

            if request_dict.get('price') is None:
                symbol = request_dict.get('symbol')
                order_type = request_dict.get('type', 0)
                if symbol:
                    try:
                        symbol_info = mt5.symbol_info_tick(symbol)
                        if symbol_info:
                            # 买单使用ask价格，卖单使用bid价格
                            request_dict['price'] = symbol_info.ask if order_type == 0 else symbol_info.bid
                        else:
                            logger.warning(f"无法获取{symbol}的价格信息，使用默认价格0")
                            request_dict['price'] = 0.0
                    except Exception as e:
                        logger.warning(f"获取{symbol}价格失败: {e}，使用默认价格0")
                        request_dict['price'] = 0.0
                else:
                    request_dict['price'] = 0.0

            request_dict.setdefault('type_time', mt5.ORDER_TIME_GTC)
            request_dict.setdefault('type_filling', mt5.ORDER_FILLING_IOC)
            request_dict.setdefault('sl', 0.0)
            request_dict.setdefault('tp', 0.0)
            request_dict.setdefault('deviation', 20)
            request_dict.setdefault('magic', 12345)
            request_dict.setdefault('comment', 'TradeCopier')

            if 'comment' in request_dict and len(request_dict['comment']) > 31:
                request_dict['comment'] = request_dict['comment'][:31]

            logger.info(f"处理后交易请求: {request_dict}")
            return request_dict

        except Exception as e:
            logger.error(f"准备交易请求异常: {e}")
            import traceback
            logger.error(f"详细错误: {traceback.format_exc()}")
            return request
    
    def _build_trade_response(self, result: Any, request: Dict[str, Any]) -> Dict[str, Any]:
        """统一构建交易结果响应"""
        try:
            if result is None:
                logger.error("交易结果为None，可能是MT5连接问题或交易被拒绝")
                return {
                    "status": "error",
                    "error_message": "Trade execution failed: MT5 returned None (connection issue or trade rejected)",
                    "data": {
                        "result": None,
                        "request": request
                    }
                }

            if not hasattr(result, '_asdict'):
                logger.error(f"交易结果格式错误，不是namedtuple: {type(result)}")
                return {
                    "status": "error",
                    "error_message": f"Invalid trade result format: {type(result)}",
                    "data": {
                        "result": str(result) if result else None,
                        "request": request
                    }
                }

            from ..messaging.message_types import TradeResultModel
            result_dict = result._asdict()
            logger.info(f"交易结果字典: {result_dict}")

            trade_result_data = TradeResultModel.model_validate(result_dict).model_dump()

            retcode = result.retcode
            if retcode == mt5.TRADE_RETCODE_DONE:
                status = 'success'
                error_message = None
                logger.info(f"交易成功: retcode={retcode}, deal={result.deal}, order={result.order}")
            else:
                status = 'error'
                error_message = f"Trade execution failed with retcode {retcode}: {result.comment}"
                logger.error(f"交易失败: {error_message}")

            return {
                "status": status,
                "error_message": error_message,
                "data": {
                    "result": trade_result_data,
                    "request": request
                }
            }

        except Exception as e:
            logger.error(f"构建交易响应时出错: {e}", exc_info=True)
            return {
                "status": "error",
                "error_message": f"Failed to build trade response: {e}",
                "data": {"request": request}
            }
    
    def _close_position(self, command: CommandRequest) -> CommandResponse:
        """关闭指定持仓"""
        try:
            position_ticket = command.params.get('ticket') or command.params.get('position_id')
            if not position_ticket:
                return CommandResponse(
                    type='error', account=self.account_name, status='error', 
                    error_message='Position ticket not provided',
                    request_id=command.request_id
                )
            
            positions = mt5.positions_get(ticket=position_ticket)
            if not positions:
                return CommandResponse(
                    type='error', account=self.account_name, status='error',
                    error_message=f'Position {position_ticket} not found'
                )
                
            position = positions[0]
            
            close_request = {
                "action": "DEAL",
                "position": position_ticket,
                "symbol": position.symbol,
                "volume": command.params.get('volume', position.volume),
                "type": "SELL" if position.type == mt5.POSITION_TYPE_BUY else "BUY",
                "comment": command.params.get('comment', 'Closed by Process'),
            }
            
            return self._execute_trade({'request': close_request})
            
        except Exception as e:
            logger.error(f"关闭持仓异常: {e}")
            return CommandResponse(
                type='error', account=self.account_name, status='error',
                error_message=f"Close position error: {str(e)}",
                request_id=command.request_id
            )
    
    def _modify_position(self, command: CommandRequest) -> CommandResponse:
        """修改持仓止损止盈"""
        try:
            position_ticket = command.params.get('ticket') or command.params.get('position_id')
            sl = command.params.get('sl')
            tp = command.params.get('tp')
            
            if not position_ticket:
                return CommandResponse(
                    type='error', account=self.account_name, status='error',
                    error_message='Position ticket not provided',
                    request_id=command.request_id
                )
            
            positions = mt5.positions_get(ticket=position_ticket)
            if not positions:
                return CommandResponse(
                    type='error', account=self.account_name, status='error',
                    error_message=f'Position {position_ticket} not found'
                )
                
            position = positions[0]
            
            modify_request = {
                "action": "SLTP",
                "position": position_ticket,
                "symbol": position.symbol,
                "volume": position.volume,
                "type": "BUY" if position.type == mt5.POSITION_TYPE_BUY else "SELL",
                "sl": sl or position.sl,
                "tp": tp or position.tp,
                "comment": command.params.get('comment', 'Modified by Process'),
            }
            
            return self._execute_trade({'request': modify_request})
            
        except Exception as e:
            logger.error(f"修改持仓异常: {e}")
            return CommandResponse(
                type='error', account=self.account_name, status='error',
                error_message=f"Modify position error: {str(e)}",
                request_id=command.request_id
            )
    
    def _get_rates(self, command: CommandRequest) -> CommandResponse:
        """获取K线数据"""
        try:
            symbol = command.params.get('symbol')
            count = command.params.get('count', 100)
            timeframe_str = command.params.get('timeframe', 'H1')
            
            if not symbol:
                return CommandResponse(
                    type='rates', account=self.account_name, status='error',
                    error_message='Symbol not provided',
                    request_id=command.request_id
                )
            
            timeframe_map = {
                'M1': mt5.TIMEFRAME_M1, 'M5': mt5.TIMEFRAME_M5, 'M15': mt5.TIMEFRAME_M15,
                'M30': mt5.TIMEFRAME_M30, 'H1': mt5.TIMEFRAME_H1, 'H4': mt5.TIMEFRAME_H4,
                'D1': mt5.TIMEFRAME_D1, 'W1': mt5.TIMEFRAME_W1, 'MN1': mt5.TIMEFRAME_MN1
            }
            timeframe = timeframe_map.get(timeframe_str, mt5.TIMEFRAME_H1)
            
            rates = mt5.copy_rates_from_pos(symbol, timeframe, 0, count)
            if rates is None or len(rates) == 0:
                return CommandResponse(
                    type='rates', account=self.account_name, status='error',
                    error_message=f'Failed to get rates for {symbol}',
                    request_id=command.request_id
                )
            
            # 将numpy数组转换为JSON友好的格式
            rates_list = [
                {
                    "time": int(r[0]), "open": r[1], "high": r[2], "low": r[3],
                    "close": r[4], "tick_volume": int(r[5]), "spread": int(r[6]),
                    "real_volume": int(r[7])
                }
                for r in rates
            ]
            
            return CommandResponse(
                type='rates', account=self.account_name, status='success',
                data={'rates': rates_list},
                request_id=command.request_id
            )
            
        except Exception as e:
            logger.error(f"获取K线数据异常: {e}")
            return CommandResponse(
                type='rates', account=self.account_name, status='error',
                error_message=f"Rates error: {str(e)}",
                request_id=command.request_id
            )
    
    def _get_history(self, command: CommandRequest) -> CommandResponse:
        """获取历史数据"""
        try:
            # 这里可以实现获取历史订单、成交等数据
            # 暂时返回空结果
            return CommandResponse(
                type='history', account=self.account_name, status='success',
                data={'history': []},
                request_id=command.request_id
            )
        except Exception as e:
            logger.error(f"获取历史数据异常: {e}")
            return CommandResponse(
                type='history', account=self.account_name, status='error',
                error_message=f"History error: {str(e)}",
                request_id=command.request_id
            )
    
    def _get_symbols(self, command: CommandRequest) -> CommandResponse:
        """获取所有交易品种"""
        try:
            symbols = mt5.symbols_get()
            if symbols:
                symbols_list = [symbol.name for symbol in symbols]
                return CommandResponse(
                    type='symbols', account=self.account_name, status='success',
                    data={'symbols': symbols_list},
                    request_id=command.request_id
                )
            else:
                return CommandResponse(
                    type='symbols', account=self.account_name, status='error',
                    error_message='Failed to get symbols',
                    request_id=command.request_id
                )
        except Exception as e:
            logger.error(f"获取品种列表异常: {e}")
            return CommandResponse(
                type='symbols', account=self.account_name, status='error',
                error_message=f"Symbols error: {str(e)}",
                request_id=command.request_id
            )
    
    def _cleanup(self):
        """清理资源"""
        try:
            self._shutdown_mt5()
        except Exception as e:
            logger.error(f"清理资源时出错 - 账户: {self.account_name}: {e}")


class MT5ProcessManager:
    """进程管理器"""

    def __init__(self, base_config_path: str = None):
        self.processes: Dict[str, MT5Process] = {}
        self.command_queues: Dict[str, mp.Queue] = {}
        self.result_queues: Dict[str, mp.Queue] = {}
        self.system_result_queue = mp.Queue()
        self.running = False

        self.monitor_thread: Optional[threading.Thread] = None
        self.restart_policy: Dict[str, Dict[str, Any]] = {}
        self.process_status: Dict[str, ProcessStatus] = {}

        self.pending_requests: Dict[str, threading.Event] = {}
        self.results_cache: Dict[str, CommandResponse] = {}
        self.pending_lock = threading.Lock()
        self.command_result_handler_thread: Optional[threading.Thread] = None

        self.base_config_path = base_config_path

        self.base_data_dir = Path("mt5_terminals")
        self.base_data_dir.mkdir(exist_ok=True)

        self.log_dir = self.base_data_dir / "logs"
        self.log_dir.mkdir(exist_ok=True)

        self._init_log_manager()

        self.delayed_restart_queue: Dict[str, Dict[str, Any]] = {}
        self.queue_lock = threading.Lock()

        self.monitoring_enabled = True
        self.health_check_interval = 10  # 缩短至10秒，提高响应速度

        self._start_time = time.time()

        self.resource_limits = {
            'cpu_limit': 80,      # CPU使用率限制 (%)
            'memory_limit': 1024, # 内存使用限制 (MB)
            'max_processes': 10,  # 最大进程数
            'max_connections': 50 # 最大连接数
        }

        logger.info(f"MT5ProcessManager初始化完成，数据目录: {self.base_data_dir}")
        logger.info(f"资源限制: CPU={self.resource_limits['cpu_limit']}%, 内存={self.resource_limits['memory_limit']}MB")

    def start_manager(self):
        """启动进程管理器"""
        if self.running:
            logger.warning("进程管理器已在运行")
            return

        self.running = True

        self.command_result_handler_thread = threading.Thread(
            target=self._handle_command_results, daemon=True
        )
        self.command_result_handler_thread.start()

        self.monitor_thread = threading.Thread(
            target=self._handle_system_results, daemon=True
        )
        self.monitor_thread.start()

        self.health_monitor_thread = threading.Thread(
            target=self._monitor_process_health, daemon=True
        )
        self.health_monitor_thread.start()

        logger.info("MT5进程管理器已启动")

    def _should_restart_process(self, account_name: str) -> bool:
        """判断是否应该重启进程"""
        policy = self.restart_policy.get(account_name, {})
        if not policy.get('enabled', True):
            return False

        max_restarts = policy.get('max_restarts', 5)
        time_window = policy.get('time_window', 3600)  # 1小时

        now = time.time()
        count = policy.get('count', 0)
        last_time = policy.get('last_time', 0)

        if now - last_time > time_window:
            policy['count'] = 0
            count = 0

        return count < max_restarts

    def _restart_process(self, account_name: str):
        """重启进程"""
        policy = self.restart_policy.get(account_name)
        if not policy:
            logger.error(f"进程 [{account_name}] 缺少重启策略，无法重启")
            self._cleanup_process(account_name)
            return
                        
        now = time.time()
        time_since_last = now - policy.get('last_time', 0)

        if time_since_last < 60 and policy.get('count', 0) > 5:
            logger.error(f"进程 [{account_name}] 在1分钟内重启超过5次，暂停重启！")
            self._cleanup_process(account_name)
            return
                            
        backoff_time = min(2 ** policy.get('count', 0), 60)
        if time_since_last < backoff_time:
            logger.info(f"进程 [{account_name}] 处于退避期，等待 {backoff_time - time_since_last:.1f}s 后重启")
            self._schedule_delayed_restart(account_name, backoff_time - time_since_last)
            return
                            
        logger.info(f"正在重启进程 [{account_name}]...")
        self._cleanup_process(account_name)
        
        if self.add_account(account_name, policy['config']):
            policy['count'] = policy.get('count', 0) + 1
            policy['last_time'] = now
            logger.info(f"进程 [{account_name}] 重启成功 (重启次数: {policy['count']})")
        else:
            logger.error(f"进程 [{account_name}] 重启失败")

    def _monitor_process_health(self):
        """进程健康监控"""
        logger.info("启动进程健康监控")

        monitor_config = {
            'heartbeat_timeout': 60,      # 心跳超时（秒）
            'max_error_count': 10,        # 最大错误数
            'resource_check_interval': 30, # 资源检查间隔
            'memory_limit_mb': 1024,      # 内存限制（MB）
            'cpu_limit_percent': 80       # CPU限制（%）
        }

        last_resource_check = 0

        while self.running:
            try:
                current_time = time.time()
                
                self._process_restart_queue(current_time)

                for account_name, process in list(self.processes.items()):
                    try:
                        if not process.is_alive():
                            logger.warning(f"检测到进程死亡: {account_name}")
                            self._handle_dead_process(account_name)
                            continue

                        status = self.process_status.get(account_name)
                        if status:
                            heartbeat_age = current_time - status.last_heartbeat.timestamp()
                            if heartbeat_age > monitor_config['heartbeat_timeout']:
                                logger.warning(f"进程心跳超时: {account_name} ({heartbeat_age:.1f}s)")
                                self._handle_heartbeat_timeout(account_name)
                                continue

                            if status.error_count > monitor_config['max_error_count']:
                                logger.warning(f"进程错误过多: {account_name} (错误数: {status.error_count})")
                                self._handle_high_error_count(account_name)
                                continue
                            elif status.error_count > 5:  # 中等错误警告
                                logger.warning(f"进程错误增多: {account_name} (错误数: {status.error_count})")

                            if status.is_alive and not status.connected:
                                uptime = current_time - (status.last_heartbeat.timestamp() - status.uptime_seconds)
                                if uptime > 300:  # 5分钟仍未连接
                                    logger.warning(f"进程长时间未连接MT5: {account_name} (运行时间: {uptime:.1f}s)")
                                    self._handle_connection_issue(account_name)
                                    continue

                        if current_time - last_resource_check > monitor_config['resource_check_interval']:
                            self._enhanced_check_process_resources(account_name, process, monitor_config)

                    except Exception as e:
                        logger.error(f"监控进程健康时出错 [{account_name}]: {e}")
                        if account_name in self.process_status:
                            self.process_status[account_name].error_count += 1

                if current_time - last_resource_check > monitor_config['resource_check_interval']:
                    last_resource_check = current_time

                if len(self.processes) > 10:
                    time.sleep(15)  # 进程多时检查更频繁
                else:
                    time.sleep(30)  # 正常检查间隔

            except Exception as e:
                logger.error(f"进程健康监控异常: {e}", exc_info=True)
                time.sleep(10)  # 出错时短暂等待

        logger.info("进程健康监控已停止")

    def _handle_dead_process(self, account_name: str):
        """处理死亡进程"""
        logger.error(f"处理死亡进程: {account_name}")

        if account_name in self.process_status:
            self.process_status[account_name].status = ProcessState.CRASHED
            self.process_status[account_name].is_alive = False

        if self._should_restart_process(account_name):
            logger.info(f"自动重启死亡进程: {account_name}")
            self._restart_process(account_name)
        else:
            logger.error(f"进程 [{account_name}] 不满足重启条件，将被移除")
            self._cleanup_process(account_name)

    def _handle_heartbeat_timeout(self, account_name: str):
        """处理心跳超时"""
        logger.warning(f"处理心跳超时: {account_name}")

        if account_name in self.process_status:
            self.process_status[account_name].status = ProcessState.ERROR
            self.process_status[account_name].error_count += 1

        if self._should_restart_process(account_name):
            logger.info(f"因心跳超时重启进程: {account_name}")
            self._restart_process(account_name)

    def _handle_high_error_count(self, account_name: str):
        """处理高错误计数"""
        logger.warning(f"处理高错误计数: {account_name}")

        if self._should_restart_process(account_name):
            logger.info(f"因错误过多重启进程: {account_name}")
            self._restart_process(account_name)
        else:
            logger.error(f"进程 [{account_name}] 错误过多且不满足重启条件")

    def _check_process_resources(self, account_name: str, process: MT5Process):
        """检查进程资源使用"""
        try:
            import psutil
            ps_process = psutil.Process(process.pid)

            cpu_percent = ps_process.cpu_percent(interval=1)
            memory_info = ps_process.memory_info()
            memory_mb = memory_info.rss / 1024 / 1024

            if account_name in self.process_status:
                self.process_status[account_name].cpu_percent = cpu_percent
                self.process_status[account_name].memory_usage_mb = memory_mb

            if cpu_percent > 80:  # CPU使用率过高
                logger.warning(f"进程CPU使用率过高: {account_name} ({cpu_percent:.1f}%)")

            if memory_mb > 1024:  # 内存使用过高（1GB）
                logger.warning(f"进程内存使用过高: {account_name} ({memory_mb:.1f}MB)")

        except (psutil.NoSuchProcess, psutil.AccessDenied) as e:
            logger.warning(f"无法获取进程资源信息 [{account_name}]: {e}")
        except Exception as e:
            logger.error(f"检查进程资源时出错 [{account_name}]: {e}")

    def _handle_system_results(self):
        """处理来自子进程的系统消息"""
        while self.running:
            try:
                result_dict = self.system_result_queue.get(timeout=1)
                result = CommandResponse(**result_dict)
                if result.type == 'heartbeat':
                    self._update_process_status(result.account, result.data)
                else:
                    logger.info(f"系统消息 - [{result.account}] - {result.type}: {result.status}")
            except queue.Empty:
                continue
            except Exception as e:
                logger.error(f"处理系统结果时出错: {e}", exc_info=True)

    def _handle_command_results(self):
        """处理命令结果"""
        while self.running:
            try:
                for account_name, result_queue in list(self.result_queues.items()):
                    try:
                        result_dict = result_queue.get_nowait()
                        result = CommandResponse(**result_dict)

                        with self.pending_lock:
                            if result.request_id in self.pending_requests:
                                self.results_cache[result.request_id] = result
                                self.pending_requests[result.request_id].set()

                    except queue.Empty:
                        continue

                time.sleep(0.1)  # 避免CPU占用过高

            except Exception as e:
                logger.error(f"处理命令结果时出错: {e}", exc_info=True)

    def _update_process_status(self, account_name: str, data: Dict[str, Any]):
        """更新进程状态"""
        if account_name in self.process_status:
            status = self.process_status[account_name]
            status.last_heartbeat = datetime.now()
            status.connected = data.get('connected', False)
            status.uptime_seconds = data.get('uptime', 0)
            status.error_count = data.get('error_count', 0)
            status.account_info = data.get('account_info')
            status.is_alive = True

    def _cleanup_process(self, account_name: str):
        """清理进程资源"""
        try:
            if account_name in self.command_queues:
                try:
                    self.command_queues[account_name].put({
                        'type': 'shutdown',
                        'params': {},
                        'request_id': str(uuid.uuid4()),
                        'timestamp': datetime.now().isoformat()
                    })
                except:
                    pass

            process = self.processes.get(account_name)
            if process and process.is_alive():
                process.join(timeout=5)
                if process.is_alive():
                    logger.warning(f"强制终止进程: {account_name}")
                    process.terminate()
                    process.join(timeout=2)

            self.processes.pop(account_name, None)
            self.command_queues.pop(account_name, None)
            self.result_queues.pop(account_name, None)
            self.process_status.pop(account_name, None)

        except Exception as e:
            logger.error(f"清理进程资源时出错 [{account_name}]: {e}")

    def add_account(self, account_name: str, account_config: Dict[str, Any]) -> bool:
        """添加账户并启动其托管进程"""
        if account_name in self.processes:
            logger.warning(f"账户 {account_name} 已存在，将先移除旧进程")
            self.remove_account(account_name)

        try:
            if 'password' not in account_config or not account_config['password']:
                import os
                password = os.getenv(f'MT5_{account_name.upper()}_PASSWORD')
                if password:
                    account_config = account_config.copy()
                    account_config['password'] = password
                    logger.debug(f"从环境变量加载密码: {account_name}")
                else:
                    logger.error(f"未找到账户密码: {account_name}")
                    return False
                    
            self.restart_policy.setdefault(account_name, {'config': account_config.copy()})

            command_queue = mp.Queue()
            result_queue = mp.Queue()

            process = MT5Process(
                account_name, account_config,
                command_queue, result_queue, self.system_result_queue
            )
            process.start()

            time.sleep(1)

            if process.is_alive():
                self.processes[account_name] = process
                self.command_queues[account_name] = command_queue
                self.result_queues[account_name] = result_queue
                self.process_status[account_name] = ProcessStatus(
                    account_name=account_name,
                    pid=process.pid,
                    status="connecting",
                    is_alive=True,
                    uptime_seconds=0,
                    last_heartbeat=datetime.now()
                )
                logger.info(f"成功启动账户 [{account_name}] 的MT5进程, PID: {process.pid}")
                return True
            else:
                logger.error(f"账户 {account_name} 的MT5进程启动失败")
                return False

        except Exception as e:
            logger.error(f"添加账户 {account_name} 失败: {e}", exc_info=True)
            return False

    def remove_account(self, account_name: str) -> bool:
        """移除账户进程"""
        try:
            self._cleanup_process(account_name)
            logger.info(f"成功移除账户进程: {account_name}")
            return True
        except Exception as e:
            logger.error(f"移除账户进程失败 {account_name}: {e}")
            return False

    def send_command(self, account_name: str, command_type: str,
                     params: Dict[str, Any] = None) -> CommandResponse:
        """向指定账户进程发送命令并等待结果"""
        if account_name not in self.command_queues:
            return CommandResponse(type='error', account=account_name, status='error',
                                   error_message=f'Account {account_name} not found', request_id='')

        process = self.processes.get(account_name)
        if not process or not process.is_alive():
            return CommandResponse(type='error', account=account_name, status='error',
                                   error_message=f'Process for {account_name} is not alive', request_id='')

        request_id = str(uuid.uuid4())
        command = CommandRequest(type=command_type, params=params or {}, request_id=request_id)
        event = threading.Event()

        try:
            with self.pending_lock:
                self.pending_requests[request_id] = event

            self.command_queues[account_name].put(command.model_dump())

            if not event.wait(timeout=20):  # 增加超时时间
                return CommandResponse(type='error', account=account_name, status='error',
                                       error_message=f'Command timeout after 20s',
                                       request_id=request_id)

            with self.pending_lock:
                result = self.results_cache.pop(request_id, None)

            return result if result else CommandResponse(type='error', account=account_name, status='error',
                                                          error_message='Result not found after event set',
                                                          request_id=request_id)

        except Exception as e:
            logger.error(f"发送命令异常 [{account_name}] {command_type}: {e}")
            return CommandResponse(type='error', account=account_name, status='error',
                                   error_message=str(e), request_id=request_id)
        finally:
            with self.pending_lock:
                self.pending_requests.pop(request_id, None)
                self.results_cache.pop(request_id, None)

    async def send_command_async(self, account_name: str, command_type: str,
                                params: Dict[str, Any] = None) -> CommandResponse:
        """异步发送命令"""
        return await asyncio.to_thread(
            self.send_command,
            account_name,
            command_type,
            params
        )

    def shutdown(self):
        """关闭所有进程和管理器线程"""
        if not self.running:
            return
        logger.info("正在关闭MT5进程管理器...")
        self.running = False

        for account_name in list(self.processes.keys()):
            self.remove_account(account_name)

        if self.monitor_thread:
            self.monitor_thread.join(timeout=2)
        if self.command_result_handler_thread:
            self.command_result_handler_thread.join(timeout=2)
        if hasattr(self, 'health_monitor_thread') and self.health_monitor_thread:
            self.health_monitor_thread.join(timeout=2)

        self.processes.clear()
        self.command_queues.clear()
        self.result_queues.clear()
        self.process_status.clear()

        logger.info("MT5进程管理器已关闭")

    def get_process_status(self, account_name: str) -> Optional[ProcessStatus]:
        """获取进程状态"""
        return self.process_status.get(account_name)

    def get_all_process_status(self) -> Dict[str, ProcessStatus]:
        """获取所有进程状态"""
        return self.process_status.copy()

    def is_account_connected(self, account_name: str) -> bool:
        """检查账户是否连接"""
        status = self.process_status.get(account_name)
        return status and status.is_alive and status.connected

    def is_account_available(self, account_name: str) -> bool:
        """检查账户是否可用（进程存在且运行中，不要求完全连接）"""
        status = self.process_status.get(account_name)
        result = status and status.is_alive

        if status is None:
            logger.debug(f"账户状态为空: {account_name}, 已知账户: {list(self.process_status.keys())}")
        else:
            logger.debug(f"账户状态检查: {account_name} -> is_alive: {status.is_alive}, connected: {status.connected}")

        return result

    def get_system_health(self) -> SystemHealth:
        """获取系统健康状态"""
        total_processes = len(self.processes)
        running_processes = sum(1 for p in self.processes.values() if p.is_alive())
        connected_processes = sum(1 for s in self.process_status.values() if s.connected)
        error_processes = sum(1 for s in self.process_status.values() if s.status == ProcessState.ERROR)

        total_memory_mb = sum(s.memory_usage_mb or 0 for s in self.process_status.values())
        cpu_values = [s.cpu_percent for s in self.process_status.values() if s.cpu_percent is not None]
        average_cpu_percent = sum(cpu_values) / len(cpu_values) if cpu_values else 0

        uptime_seconds = time.time() - getattr(self, '_start_time', time.time())

        return SystemHealth(
            total_processes=total_processes,
            running_processes=running_processes,
            connected_processes=connected_processes,
            error_processes=error_processes,
            total_memory_mb=total_memory_mb,
            average_cpu_percent=average_cpu_percent,
            uptime_seconds=uptime_seconds
        )

    def enable_auto_restart(self, account_name: str, max_restarts: int = 5, time_window: int = 3600):
        """启用自动重启"""
        if account_name not in self.restart_policy:
            self.restart_policy[account_name] = {}

        self.restart_policy[account_name].update({
            'enabled': True,
            'max_restarts': max_restarts,
            'time_window': time_window,
            'count': 0,
            'last_time': 0
        })
        logger.info(f"已启用自动重启: {account_name} (最大重启次数: {max_restarts})")

    def disable_auto_restart(self, account_name: str):
        """禁用自动重启"""
        if account_name in self.restart_policy:
            self.restart_policy[account_name]['enabled'] = False
        logger.info(f"已禁用自动重启: {account_name}")

    def reset_restart_count(self, account_name: str):
        """重置重启计数"""
        if account_name in self.restart_policy:
            self.restart_policy[account_name]['count'] = 0
            self.restart_policy[account_name]['last_time'] = 0
        logger.info(f"已重置重启计数: {account_name}")

    # ==================== 兼容性方法 ====================
    # 保持与旧版本的兼容性

    async def start_terminal_process_new(self, account_name: str, account_config: Dict[str, Any]) -> bool:
        """新版本的启动终端进程方法（使用真进程隔离）"""
        if not self.running:
            self.start_manager()

        return await asyncio.to_thread(self.add_account, account_name, account_config)

    async def stop_terminal_process_new(self, account_name: str) -> bool:
        """新版本的停止终端进程方法"""
        return await asyncio.to_thread(self.remove_account, account_name)

    def get_terminal_status_new(self, account_name: str) -> Dict:
        """新版本的获取终端状态方法"""
        status = self.get_process_status(account_name)
        if not status:
            return {'status': 'not_started'}

        return {
            'status': 'running' if status.is_alive else 'stopped',
            'pid': status.pid,
            'connected': status.connected,
            'uptime_seconds': status.uptime_seconds,
            'error_count': status.error_count,
            'last_heartbeat': status.last_heartbeat.isoformat(),
            'account_info': status.account_info
        }
        
    def _init_log_manager(self):
        """初始化日志管理器"""
        self.log_handlers = {}
        
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        
        main_log_file = self.log_dir / 'mt5_process_manager.log'
        main_handler = RotatingFileHandler(
            main_log_file,
            maxBytes=10*1024*1024,  # 10MB
            backupCount=5
        )
        main_handler.setFormatter(formatter)
        logger.addHandler(main_handler)
        
        logger.info("日志管理器初始化完成")

    # ==================== 增强监控和重启机制 ====================
    # 支持指数退避重启和资源监控


    def _schedule_delayed_restart(self, account_name: str, delay_seconds: float):
        """安排延迟重启 - 支持指数退避策略"""
        restart_time = time.time() + delay_seconds
        
        with self.queue_lock:
            self.delayed_restart_queue[account_name] = {
                'restart_time': restart_time,
                'attempts': self.restart_policy.get(account_name, {}).get('count', 0),
                'account_name': account_name
            }
        
        logger.info(f"已安排延迟重启: {account_name} 将在 {delay_seconds:.1f}s 后重启")

    def _process_restart_queue(self, current_time: float):
        """处理延迟重启队列"""
        with self.queue_lock:
            ready_restarts = []
            
            for account_name, restart_info in list(self.delayed_restart_queue.items()):
                if current_time >= restart_info['restart_time']:
                    ready_restarts.append(account_name)
                    del self.delayed_restart_queue[account_name]
        
        for account_name in ready_restarts:
            logger.info(f"执行延迟重启: {account_name}")
            self._execute_delayed_restart(account_name)

    def _execute_delayed_restart(self, account_name: str):
        """执行延迟重启"""
        try:
            policy = self.restart_policy.get(account_name, {})
            if not policy:
                logger.error(f"延迟重启失败，缺少策略: {account_name}")
                return
            
            logger.info(f"开始执行延迟重启: {account_name}")
            self._cleanup_process(account_name)
            
            if self.add_account(account_name, policy['config']):
                policy['count'] = policy.get('count', 0) + 1
                policy['last_time'] = time.time()
                logger.info(f"延迟重启成功: {account_name} (重启次数: {policy['count']})")
            else:
                logger.error(f"延迟重启失败: {account_name}")
                
        except Exception as e:
            logger.error(f"执行延迟重启异常 [{account_name}]: {e}")

    def _enhanced_check_process_resources(self, account_name: str, process: Any, monitor_config: Dict[str, Any]):
        """增强的进程资源检查"""
        try:
            import psutil
            
            ps_process = psutil.Process(process.pid)
            
            cpu_percent = ps_process.cpu_percent(interval=0.1)
            memory_info = ps_process.memory_info()
            memory_mb = memory_info.rss / 1024 / 1024
            
            if account_name in self.process_status:
                self.process_status[account_name].cpu_percent = cpu_percent
                self.process_status[account_name].memory_usage_mb = memory_mb
            
            memory_limit = monitor_config.get('memory_limit_mb', 1024)
            cpu_limit = monitor_config.get('cpu_limit_percent', 80)
            
            if memory_mb > memory_limit:
                logger.warning(f"进程内存使用过高: {account_name} ({memory_mb:.1f}MB > {memory_limit}MB)")
                self._handle_high_memory_usage(account_name, memory_mb)
            
            if cpu_percent > cpu_limit:
                logger.warning(f"进程CPU使用过高: {account_name} ({cpu_percent:.1f}% > {cpu_limit}%)")
                self._handle_high_cpu_usage(account_name, cpu_percent)
            
            try:
                num_fds = ps_process.num_fds() if hasattr(ps_process, 'num_fds') else 0
                if num_fds > 500:  # 文件描述符过多
                    logger.warning(f"进程文件句柄过多: {account_name} ({num_fds} handles)")
            except (AttributeError, psutil.AccessDenied):
                pass
            
            try:
                num_threads = ps_process.num_threads()
                if num_threads > 100:  # 线程过多
                    logger.warning(f"进程线程过多: {account_name} ({num_threads} threads)")
            except psutil.AccessDenied:
                pass
                
        except (psutil.NoSuchProcess, psutil.AccessDenied) as e:
            logger.warning(f"无法获取进程资源信息 [{account_name}]: {e}")
        except Exception as e:
            logger.error(f"检查进程资源时出错 [{account_name}]: {e}")

    def _handle_high_memory_usage(self, account_name: str, memory_mb: float):
        """处理高内存使用"""
        if account_name in self.process_status:
            self.process_status[account_name].error_count += 1
        
        if memory_mb > 2048:
            logger.error(f"进程内存使用严重过高，将强制重启: {account_name} ({memory_mb:.1f}MB)")
            self._restart_process(account_name)

    def _handle_high_cpu_usage(self, account_name: str, cpu_percent: float):
        """处理高CPU使用"""
        if account_name in self.process_status:
            status = self.process_status[account_name]
            status.error_count += 0.5  # 轻微增加错误计数
            
            if cpu_percent > 95:
                logger.warning(f"进程CPU使用严重过高: {account_name} ({cpu_percent:.1f}%)")

    def _handle_connection_issue(self, account_name: str):
        """处理连接问题"""
        logger.warning(f"处理连接问题: {account_name}")
        
        if account_name in self.process_status:
            self.process_status[account_name].error_count += 1
            
        if self._should_restart_process(account_name):
            logger.info(f"因连接问题重启进程: {account_name}")
            self._restart_process(account_name)
        else:
            logger.warning(f"进程 [{account_name}] 连接问题但不满足重启条件")

    def _handle_high_error_count(self, account_name: str):
        """处理高错误计数"""
        logger.error(f"处理高错误计数: {account_name}")
        
        if account_name in self.process_status:
            self.process_status[account_name].status = ProcessState.ERROR
            
        if self._should_restart_process(account_name):
            logger.info(f"因错误过多重启进程: {account_name}")
            self._restart_process(account_name)
        else:
            logger.error(f"进程 [{account_name}] 错误过多但不满足重启条件，将被移除")
            self._cleanup_process(account_name)

    def get_restart_queue_status(self) -> Dict[str, Any]:
        """获取重启队列状态"""
        with self.queue_lock:
            current_time = time.time()
            queue_status = {}
            
            for account_name, restart_info in self.delayed_restart_queue.items():
                time_remaining = restart_info['restart_time'] - current_time
                queue_status[account_name] = {
                    'time_remaining_seconds': max(0, time_remaining),
                    'attempts': restart_info['attempts'],
                    'scheduled_time': restart_info['restart_time']
                }
            
            return {
                'queue_size': len(self.delayed_restart_queue),
                'pending_restarts': queue_status
            }

    def get_enhanced_system_health(self) -> Dict[str, Any]:
        """获取系统健康状态"""
        base_health = self.get_system_health()
        
        restart_queue_status = self.get_restart_queue_status()
        
        restart_counts = [policy.get('count', 0) for policy in self.restart_policy.values()]
        avg_restart_count = sum(restart_counts) / len(restart_counts) if restart_counts else 0
        
        enhanced_health = {
            **base_health.model_dump(),
            'restart_queue': restart_queue_status,
            'average_restart_count': avg_restart_count,
            'total_restart_policies': len(self.restart_policy),
            'monitoring_status': 'active' if self.running else 'stopped'
        }
        
        return enhanced_health