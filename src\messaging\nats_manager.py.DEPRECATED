# 🚨 DEPRECATED FILE - DO NOT USE
# This file has been moved to nats_manager.py.DEPRECATED
# 
# Use HybridQueueManager instead:
# from src.messaging.hybrid_queue_manager import HybridQueueManager
#
# See MIGRATION_GUIDE.md for complete migration instructions.

from .DEPRECATED import _raise_deprecated_error

def __getattr__(name):
    _raise_deprecated_error(f"nats_manager.{name}")

class NATSManager:
    def __init__(self, *args, **kwargs):
        _raise_deprecated_error("NATSManager")