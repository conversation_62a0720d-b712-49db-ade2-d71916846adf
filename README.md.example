# MT5分布式交易系统 - 工业级增强版

## 🎯 项目概述

这是一个完全工业化的MT5分布式交易系统，实现了真正的跨主机主从账户交易复制。系统采用微服务架构，具备高可用性、故障自愈和实时监控能力。

## 🏗️ 架构特点

### 核心架构
- **数据平面与控制平面分离**: 交易信号传递与系统管理完全分离
- **分布式消息总线**: 基于NATS JetStream的高性能消息传递
- **智能路由系统**: 跨主机信号路由和故障转移
- **工业级监控**: 全方位系统状态监控和告警

### 技术栈
- **消息队列**: NATS JetStream (持久化、高可用)
- **状态存储**: Redis (缓存、会话管理)
- **编程语言**: Python 3.8+ (异步编程)
- **容器化**: Docker + Docker Compose
- **监控**: Prometheus + Grafana

## 🚀 快速开始

### 1. 环境准备

```bash
# 克隆项目
git clone <repository-url>
cd mt5-python

# 安装依赖
pip install -r requirements.txt

# 创建必要目录
mkdir -p data/{nats,redis,prometheus,grafana}
mkdir -p logs/nats
```

### 2. 配置系统

```bash
# 复制配置模板
cp config/optimized_system.yaml.template config/optimized_system.yaml

# 配置账户信息
# 编辑 config/accounts/*.yaml 文件
```

### 3. 一键部署

```bash
# 完整部署（推荐）
python3 scripts/deploy_system.py --full-deploy

# 或者分步部署
python3 scripts/deploy_system.py --deploy-infrastructure
python3 scripts/start_enhanced_system.py
```

### 4. 验证系统

```bash
# 运行完整测试套件
python3 scripts/test_enhanced_system.py

# 系统诊断
python3 scripts/start_enhanced_system.py --diagnose-only
```

## 📁 项目结构

```
mt5-python/
├── src/                          # 源代码
│   ├── distributed/              # 分布式组件
│   │   ├── account_registry.py   # 账户注册表
│   │   ├── message_router.py     # 消息路由器
│   │   ├── failover_manager.py   # 故障转移管理
│   │   └── coordinator.py        # 系统协调器
│   ├── messaging/                # 消息传递
│   │   ├── jetstream_client.py   # JetStream客户端
│   │   └── message_codec.py      # 消息编解码
│   ├── monitors/                 # 监控组件
│   │   └── master_monitor.py     # 主账户监控器
│   ├── executors/                # 执行组件
│   │   └── slave_executor.py     # 从账户执行器
│   ├── services/                 # 服务组件
│   │   └── coordinator.py        # 系统协调器
│   ├── utils/                    # 工具类
│   │   ├── config_manager.py     # 配置管理器
│   │   └── logger.py             # 日志管理
│   ├── enhanced_main.py          # 增强主程序
│   └── distributed_main.py       # 分布式主程序
├── config/                       # 配置文件
│   ├── optimized_system.yaml     # 主配置文件
│   ├── accounts/                 # 账户配置
│   └── distributed/              # 分布式配置
├── scripts/                      # 脚本工具
│   ├── deploy_system.py          # 部署脚本
│   ├── start_enhanced_system.py  # 启动脚本
│   └── test_enhanced_system.py   # 测试脚本
├── docker-compose-enhanced.yml   # Docker配置
└── README_ENHANCED.md            # 本文档
```

## 🔧 核心功能

### 1. 跨主机交易复制
- 支持主从账户分布在不同主机
- 实时交易信号传递
- 智能路由和负载均衡

### 2. 故障转移
- 自动检测主机和账户故障
- 备用主账户自动切换
- 网络中断自动恢复

### 3. 系统监控
- 实时性能监控
- 延迟和吞吐量统计
- 告警和通知系统

### 4. 配置管理
- 统一配置管理
- 动态配置更新
- 环境变量支持

## 📊 性能指标

### 消息传递性能
- **吞吐量**: >1000 消息/秒
- **延迟**: <50ms (本地), <200ms (跨主机)
- **可靠性**: 99.9% 消息送达率

### 系统可用性
- **服务可用性**: 99.9%
- **故障恢复时间**: <30秒
- **数据持久化**: 支持

## 🛠️ 运维指南

### 启动系统
```bash
# 标准启动
python3 scripts/start_enhanced_system.py

# 指定主从账户
python3 scripts/start_enhanced_system.py --masters ACC001 ACC002 --slaves ACC003 ACC004

# 禁用协调器
python3 scripts/start_enhanced_system.py --no-coordinator
```

### 监控系统
```bash
# 访问监控面板
http://localhost:3000  # Grafana
http://localhost:9090  # Prometheus
http://localhost:8222  # NATS监控
```

### 故障排查
```bash
# 查看系统日志
docker-compose logs -f nats redis

# 检查服务状态
python3 scripts/deploy_system.py --validate-only

# 运行诊断
python3 scripts/start_enhanced_system.py --diagnose-only
```

## 🔒 安全特性

### 网络安全
- 内部网络隔离
- 端口访问控制
- 消息加密传输

### 数据安全
- 敏感信息加密存储
- 访问权限控制
- 审计日志记录

## 📈 扩展性

### 水平扩展
- 支持多主机部署
- 动态添加/移除节点
- 负载均衡

### 垂直扩展
- 资源配置优化
- 性能调优
- 缓存策略

## 🧪 测试

### 单元测试
```bash
# 运行所有测试
python3 scripts/test_enhanced_system.py

# 仅测试基础设施
python3 scripts/test_enhanced_system.py --infrastructure-only
```

### 集成测试
```bash
# 跨主机功能测试
python3 scripts/test_enhanced_system.py --distributed-test

# 性能压力测试
python3 scripts/test_enhanced_system.py --performance-test
```

## 📝 配置说明

### 主配置文件 (config/optimized_system.yaml)
```yaml
# 主机配置
host:
  id: "host001"
  ip_address: "*************"

# NATS配置
nats:
  servers: ["nats://localhost:4222"]
  jetstream:
    max_age: 3600
    max_msgs: 1000000

# Redis配置
redis:
  host: "localhost"
  port: 6379
```

### 账户配置 (config/accounts/ACC001.yaml)
```yaml
account:
  id: "ACC001"
  name: "主账户1"
  server: "MetaQuotes-Demo"
  login: 123456

current_role:
  role: "master"  # master/slave/auto

deployment:
  host_id: "host001"
  enabled: true
```

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 📞 支持

- 📧 邮箱: <EMAIL>
- 📱 微信: MT5Support
- 🌐 官网: https://example.com

---

**注意**: 这是一个工业级交易系统，请在生产环境中谨慎使用，并确保充分测试。
