# 加密密钥配置
# 定义系统中使用的各种加密密钥和安全策略

# 主加密密钥配置
master_keys:
  # 主密钥设置
  primary:
    key_id: "master_001"
    algorithm: "AES-256-GCM"
    key_source: "environment"    # environment, file, vault, hsm
    env_var: "MASTER_ENCRYPTION_KEY"
    created_at: "2024-01-01T00:00:00Z"
    rotation_period_days: 90
    
  # 备用主密钥
  secondary:
    key_id: "master_002"
    algorithm: "AES-256-GCM"
    key_source: "environment"
    env_var: "SECONDARY_ENCRYPTION_KEY"
    created_at: "2024-01-01T00:00:00Z"
    rotation_period_days: 90

# 数据加密密钥
data_encryption:
  # 数据库加密
  database:
    # 静态数据加密
    at_rest:
      enabled: true
      algorithm: "AES-256-CBC"
      key_derivation: "PBKDF2"
      iterations: 100000
      salt_length: 32
      
    # 字段级加密
    field_level:
      enabled: true
      sensitive_fields:
        - "password"
        - "api_key"
        - "private_key"
        - "mt5_password"
        - "telegram_token"
      algorithm: "AES-256-GCM"
      
  # 文件加密
  files:
    # 配置文件加密
    config_files:
      enabled: false
      algorithm: "AES-256-GCM"
      file_patterns:
        - "*.key"
        - "*.pem"
        - "*secret*"
        
    # 日志文件加密
    log_files:
      enabled: false
      algorithm: "AES-256-GCM"
      compress_before_encrypt: true
      
  # 内存加密
  memory:
    enabled: false
    algorithm: "ChaCha20-Poly1305"
    clear_on_dealloc: true

# 传输加密配置
transport_encryption:
  # TLS/SSL配置
  tls:
    # 最小TLS版本
    min_version: "TLS1.2"
    max_version: "TLS1.3"
    
    # 密码套件
    cipher_suites:
      - "ECDHE-ECDSA-AES256-GCM-SHA384"
      - "ECDHE-RSA-AES256-GCM-SHA384"
      - "ECDHE-ECDSA-AES128-GCM-SHA256"
      - "ECDHE-RSA-AES128-GCM-SHA256"
      
    # 证书配置
    certificates:
      # 服务器证书
      server:
        cert_file: "${TLS_CERT_FILE}"
        key_file: "${TLS_KEY_FILE}"
        ca_file: "${TLS_CA_FILE}"
        
      # 客户端证书
      client:
        enabled: false
        cert_file: "${CLIENT_CERT_FILE}"
        key_file: "${CLIENT_KEY_FILE}"
        
    # HSTS配置
    hsts:
      enabled: true
      max_age: 31536000
      include_subdomains: true
      preload: true
      
  # API传输加密
  api:
    # 端到端加密
    e2e_encryption:
      enabled: false
      algorithm: "ECDH-ES+A256KW"
      
    # 消息签名
    message_signing:
      enabled: true
      algorithm: "ECDSA-SHA256"
      
# 密钥管理配置
key_management:
  # 密钥存储
  storage:
    type: "environment"       # environment, file, vault, hsm
    
    # 环境变量存储
    environment:
      prefix: "MT5_KEY_"
      
    # 文件存储
    file:
      base_path: "/etc/mt5/keys"
      permissions: "600"
      owner: "mt5user"
      
    # Vault存储
    vault:
      url: "${VAULT_URL}"
      token: "${VAULT_TOKEN}"
      mount_path: "mt5-keys"
      
    # HSM存储
    hsm:
      provider: "pkcs11"
      library_path: "/usr/lib/libpkcs11.so"
      slot_id: 0
      
  # 密钥轮换
  rotation:
    # 自动轮换
    auto_rotation:
      enabled: true
      check_interval_hours: 24
      
    # 轮换策略
    policies:
      master_keys: 90          # 天
      api_keys: 30             # 天
      session_keys: 1          # 天
      
    # 轮换通知
    notifications:
      enabled: true
      advance_notice_days: 7
      channels: ["email", "slack"]
      
  # 密钥备份
  backup:
    enabled: true
    schedule: "0 2 * * *"      # 每天凌晨2点
    retention_days: 365
    encryption: true
    remote_storage: "s3://mt5-key-backups/"

# 密钥派生配置
key_derivation:
  # PBKDF2配置
  pbkdf2:
    iterations: 100000
    hash_algorithm: "SHA256"
    salt_length: 32
    
  # Scrypt配置
  scrypt:
    n: 32768
    r: 8
    p: 1
    
  # Argon2配置
  argon2:
    type: "argon2id"
    memory: 65536
    iterations: 3
    parallelism: 4

# 随机数生成
random_generation:
  # 随机数生成器
  generator:
    type: "system"             # system, hardware, combined
    
  # 系统随机数
  system:
    source: "/dev/urandom"
    
  # 硬件随机数
  hardware:
    enabled: false
    device: "/dev/hwrng"
    
  # 种子配置
  seeding:
    auto_reseed: true
    reseed_interval_hours: 24

# 密码策略
password_policies:
  # 用户密码策略
  user_passwords:
    min_length: 12
    max_length: 128
    require_uppercase: true
    require_lowercase: true
    require_digits: true
    require_special_chars: true
    forbidden_patterns:
      - "123456"
      - "password"
      - "admin"
    
  # API密钥策略
  api_keys:
    length: 32
    character_set: "alphanumeric"
    exclude_ambiguous: true
    
  # 系统密码策略
  system_passwords:
    min_length: 16
    entropy_bits: 128
    rotation_required: true

# 加密算法配置
algorithms:
  # 对称加密
  symmetric:
    preferred: "AES-256-GCM"
    allowed:
      - "AES-256-GCM"
      - "AES-256-CBC"
      - "ChaCha20-Poly1305"
    deprecated:
      - "AES-128-CBC"
      - "3DES"
      
  # 非对称加密
  asymmetric:
    preferred: "RSA-4096"
    allowed:
      - "RSA-4096"
      - "RSA-2048"
      - "ECDSA-P256"
      - "ECDSA-P384"
    deprecated:
      - "RSA-1024"
      - "DSA"
      
  # 哈希算法
  hashing:
    preferred: "SHA256"
    allowed:
      - "SHA256"
      - "SHA384"
      - "SHA512"
      - "BLAKE2b"
    deprecated:
      - "SHA1"
      - "MD5"

# 安全审计
security_audit:
  # 密钥使用审计
  key_usage:
    enabled: true
    log_access: true
    log_rotation: true
    log_derivation: true
    
  # 加密操作审计
  crypto_operations:
    enabled: true
    log_encrypt: false         # 不记录加密操作（性能考虑）
    log_decrypt: true          # 记录解密操作
    log_sign: true
    log_verify: true
    
  # 安全事件审计
  security_events:
    enabled: true
    failed_decryption: true
    invalid_signature: true
    key_rotation: true
    policy_violations: true

# 合规性配置
compliance:
  # FIPS 140-2
  fips_140_2:
    enabled: false
    level: 2
    
  # Common Criteria
  common_criteria:
    enabled: false
    level: "EAL4"
    
  # 数据保护法规
  data_protection:
    gdpr_compliance: true
    data_residency: "EU"
    encryption_required: true

# 环境特定配置
environments:
  development:
    data_encryption:
      database:
        at_rest:
          enabled: false
    key_management:
      rotation:
        auto_rotation:
          enabled: false
          
  testing:
    password_policies:
      user_passwords:
        min_length: 8
    algorithms:
      symmetric:
        preferred: "AES-128-GCM"
        
  production:
    data_encryption:
      database:
        at_rest:
          enabled: true
        field_level:
          enabled: true
    security_audit:
      key_usage:
        enabled: true
      crypto_operations:
        enabled: true

# 密钥配置示例（生产环境应使用环境变量）
# 注意：这些仅为配置示例，实际密钥应通过环境变量提供
example_keys:
  # JWT签名密钥
  jwt_signing_key:
    description: "JWT token signing key"
    env_var: "JWT_SECRET"
    algorithm: "HS256"
    length: 32
    
  # API加密密钥
  api_encryption_key:
    description: "API request/response encryption key"
    env_var: "API_ENCRYPTION_KEY"
    algorithm: "AES-256-GCM"
    length: 32
    
  # 数据库加密密钥
  database_encryption_key:
    description: "Database field encryption key"
    env_var: "DB_ENCRYPTION_KEY"
    algorithm: "AES-256-CBC"
    length: 32

# 密钥轮换计划
rotation_schedule:
  # 定期轮换
  scheduled:
    - key_type: "master_keys"
      frequency: "quarterly"
      next_rotation: "2024-04-01T00:00:00Z"
      
    - key_type: "api_keys"
      frequency: "monthly"
      next_rotation: "2024-02-01T00:00:00Z"
      
    - key_type: "jwt_keys"
      frequency: "weekly"
      next_rotation: "2024-01-08T00:00:00Z"
      
  # 紧急轮换
  emergency:
    enabled: true
    trigger_events:
      - "security_breach"
      - "key_compromise"
      - "compliance_requirement"
    notification_time: "immediate"