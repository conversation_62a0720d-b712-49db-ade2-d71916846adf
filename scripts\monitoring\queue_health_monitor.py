#!/usr/bin/env python3
"""
队列健康监控脚本
实时监控混合消息队列的健康状态，自动告警和故障转移
"""

import asyncio
import time
import json
from datetime import datetime, timedelta
from typing import Dict, Any, List
from dataclasses import dataclass
import smtplib
from email.mime.text import MimeText
from email.mime.multipart import MimeMultipart

from src.messaging.hybrid_queue_manager import HybridQueueManager, HybridQueueConfig, QueueBackendType
from src.utils.logger import get_logger

logger = get_logger(__name__)


@dataclass
class AlertConfig:
    """告警配置"""
    # 邮件配置
    smtp_host: str = "localhost"
    smtp_port: int = 587
    smtp_user: str = ""
    smtp_password: str = ""
    alert_recipients: List[str] = None
    
    # 告警阈值
    latency_warning_ms: float = 50.0
    latency_critical_ms: float = 200.0
    error_rate_warning: float = 0.02  # 2%
    error_rate_critical: float = 0.10  # 10%
    queue_depth_warning: int = 1000
    queue_depth_critical: int = 5000
    
    # 告警间隔（避免重复告警）
    alert_interval_minutes: int = 5
    
    def __post_init__(self):
        if self.alert_recipients is None:
            self.alert_recipients = []


class QueueHealthMonitor:
    """队列健康监控器"""
    
    def __init__(self, queue_manager: HybridQueueManager, alert_config: AlertConfig = None):
        self.queue_manager = queue_manager
        self.alert_config = alert_config or AlertConfig()
        self.running = False
        
        # 监控状态
        self.last_alert_times: Dict[str, datetime] = {}
        self.health_history: List[Dict[str, Any]] = []
        self.max_history_size = 1000
        
        # 统计数据
        self.monitoring_start_time = time.time()
        self.total_alerts_sent = 0
        self.backend_failover_count = 0
        
    async def start_monitoring(self):
        """开始监控"""
        if self.running:
            logger.warning("监控已在运行中")
            return
        
        self.running = True
        self.monitoring_start_time = time.time()
        
        logger.info("🚀 开始队列健康监控")
        
        # 启动监控任务
        monitoring_tasks = [
            asyncio.create_task(self._health_check_loop()),
            asyncio.create_task(self._metrics_collection_loop()),
            asyncio.create_task(self._alert_processor_loop())
        ]
        
        try:
            await asyncio.gather(*monitoring_tasks)
        except asyncio.CancelledError:
            logger.info("监控任务被取消")
        except Exception as e:
            logger.error(f"监控任务异常: {e}")
        finally:
            self.running = False
    
    async def stop_monitoring(self):
        """停止监控"""
        self.running = False
        logger.info("🛑 队列健康监控已停止")
    
    async def _health_check_loop(self):
        """健康检查循环"""
        while self.running:
            try:
                await self._perform_health_check()
                await asyncio.sleep(10)  # 每10秒检查一次
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"健康检查循环异常: {e}")
                await asyncio.sleep(5)
    
    async def _metrics_collection_loop(self):
        """指标收集循环"""
        while self.running:
            try:
                await self._collect_metrics()
                await asyncio.sleep(30)  # 每30秒收集一次
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"指标收集循环异常: {e}")
                await asyncio.sleep(10)
    
    async def _alert_processor_loop(self):
        """告警处理循环"""
        while self.running:
            try:
                await self._process_alerts()
                await asyncio.sleep(60)  # 每分钟处理一次告警
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"告警处理循环异常: {e}")
                await asyncio.sleep(30)
    
    async def _perform_health_check(self):
        """执行健康检查"""
        try:
            # 获取队列管理器状态
            status = self.queue_manager.get_status()
            
            # 记录健康状态历史
            health_record = {
                'timestamp': time.time(),
                'status': status,
                'backend_health': status.get('backend_health', {}),
                'statistics': status.get('statistics', {}),
                'routing_mode': status.get('routing_mode')
            }
            
            self.health_history.append(health_record)
            
            # 限制历史记录大小
            if len(self.health_history) > self.max_history_size:
                self.health_history.pop(0)
            
            # 检查是否有后端故障
            await self._check_backend_failures(health_record)
            
            # 检查性能指标
            await self._check_performance_metrics(health_record)
            
        except Exception as e:
            logger.error(f"健康检查失败: {e}")
    
    async def _collect_metrics(self):
        """收集监控指标"""
        try:
            status = self.queue_manager.get_status()
            
            # 发送指标到监控系统（如Prometheus）
            metrics_data = {
                'queue_manager_running': 1 if status.get('running') else 0,
                'primary_backend_healthy': 0,
                'backup_backends_count': len(status.get('backup_backends', [])),
                'total_messages': status.get('statistics', {}).get('total_messages', 0),
                'failover_count': status.get('statistics', {}).get('failover_count', 0),
                'dual_write_success_rate': 0,
                'timestamp': time.time()
            }
            
            # 计算主后端健康状态
            backend_health = status.get('backend_health', {})
            primary_backend = status.get('primary_backend')
            if primary_backend and primary_backend in backend_health:
                metrics_data['primary_backend_healthy'] = 1 if backend_health[primary_backend].get('healthy') else 0
            
            # 计算双写成功率
            stats = status.get('statistics', {})
            if stats.get('total_messages', 0) > 0:
                metrics_data['dual_write_success_rate'] = (
                    stats.get('dual_write_success', 0) / stats.get('total_messages', 1)
                )
            
            # 记录后端延迟和错误率
            for backend, health in backend_health.items():
                metrics_data[f'{backend}_latency_ms'] = health.get('latency_ms', 0)
                metrics_data[f'{backend}_error_rate'] = health.get('error_rate', 0)
                metrics_data[f'{backend}_healthy'] = 1 if health.get('healthy') else 0
            
            logger.debug(f"指标收集完成: {len(metrics_data)} 个指标")
            
        except Exception as e:
            logger.error(f"指标收集失败: {e}")
    
    async def _check_backend_failures(self, health_record: Dict[str, Any]):
        """检查后端故障"""
        try:
            backend_health = health_record.get('backend_health', {})
            current_routing_mode = health_record.get('routing_mode')
            
            failed_backends = []
            degraded_backends = []
            
            for backend, health in backend_health.items():
                if not health.get('healthy', True):
                    failed_backends.append(backend)
                elif health.get('latency_ms', 0) > self.alert_config.latency_warning_ms:
                    degraded_backends.append(backend)
            
            # 检查主后端故障
            primary_backend = health_record.get('status', {}).get('primary_backend')
            if primary_backend in failed_backends:
                await self._send_alert(
                    severity="CRITICAL",
                    title=f"主后端故障: {primary_backend}",
                    message=f"主后端 {primary_backend} 不可用，系统可能已切换到备用后端"
                )
                self.backend_failover_count += 1
            
            # 检查备用后端故障
            for backend in failed_backends:
                if backend != primary_backend:
                    await self._send_alert(
                        severity="WARNING", 
                        title=f"备用后端故障: {backend}",
                        message=f"备用后端 {backend} 不可用，系统容错能力降低"
                    )
            
            # 检查降级模式
            if current_routing_mode == "local_fallback":
                await self._send_alert(
                    severity="CRITICAL",
                    title="系统进入降级模式",
                    message="所有网络后端不可用，系统已切换到本地降级模式"
                )
            
            # 检查性能降级
            for backend in degraded_backends:
                health = backend_health[backend]
                await self._send_alert(
                    severity="WARNING",
                    title=f"后端性能降级: {backend}", 
                    message=f"后端 {backend} 延迟过高: {health.get('latency_ms', 0):.1f}ms"
                )
            
        except Exception as e:
            logger.error(f"检查后端故障失败: {e}")
    
    async def _check_performance_metrics(self, health_record: Dict[str, Any]):
        """检查性能指标"""
        try:
            backend_health = health_record.get('backend_health', {})
            
            for backend, health in backend_health.items():
                latency_ms = health.get('latency_ms', 0)
                error_rate = health.get('error_rate', 0)
                
                # 检查延迟告警
                if latency_ms > self.alert_config.latency_critical_ms:
                    await self._send_alert(
                        severity="CRITICAL",
                        title=f"后端延迟过高: {backend}",
                        message=f"延迟: {latency_ms:.1f}ms (阈值: {self.alert_config.latency_critical_ms}ms)"
                    )
                elif latency_ms > self.alert_config.latency_warning_ms:
                    await self._send_alert(
                        severity="WARNING", 
                        title=f"后端延迟告警: {backend}",
                        message=f"延迟: {latency_ms:.1f}ms (阈值: {self.alert_config.latency_warning_ms}ms)"
                    )
                
                # 检查错误率告警
                if error_rate > self.alert_config.error_rate_critical:
                    await self._send_alert(
                        severity="CRITICAL",
                        title=f"后端错误率过高: {backend}",
                        message=f"错误率: {error_rate:.2%} (阈值: {self.alert_config.error_rate_critical:.2%})"
                    )
                elif error_rate > self.alert_config.error_rate_warning:
                    await self._send_alert(
                        severity="WARNING",
                        title=f"后端错误率告警: {backend}",
                        message=f"错误率: {error_rate:.2%} (阈值: {self.alert_config.error_rate_warning:.2%})"
                    )
            
        except Exception as e:
            logger.error(f"检查性能指标失败: {e}")
    
    async def _process_alerts(self):
        """处理告警队列"""
        # 这里可以添加告警队列处理逻辑
        # 例如：批量发送告警、去重、聚合等
        pass
    
    async def _send_alert(self, severity: str, title: str, message: str):
        """发送告警"""
        try:
            # 检查告警间隔，避免重复告警
            alert_key = f"{severity}:{title}"
            now = datetime.now()
            
            if alert_key in self.last_alert_times:
                time_diff = now - self.last_alert_times[alert_key]
                if time_diff < timedelta(minutes=self.alert_config.alert_interval_minutes):
                    return  # 跳过重复告警
            
            self.last_alert_times[alert_key] = now
            
            # 构造告警消息
            alert_data = {
                'timestamp': now.isoformat(),
                'severity': severity,
                'title': title,
                'message': message,
                'host': self.queue_manager.config.backend_configs.get('host_id', 'unknown'),
                'monitoring_duration': time.time() - self.monitoring_start_time
            }
            
            # 记录日志
            log_msg = f"[{severity}] {title}: {message}"
            if severity == "CRITICAL":
                logger.error(log_msg)
            elif severity == "WARNING":
                logger.warning(log_msg)
            else:
                logger.info(log_msg)
            
            # 发送邮件告警
            if self.alert_config.alert_recipients:
                await self._send_email_alert(alert_data)
            
            # 发送到监控系统（如webhook、Slack等）
            await self._send_external_alert(alert_data)
            
            self.total_alerts_sent += 1
            
        except Exception as e:
            logger.error(f"发送告警失败: {e}")
    
    async def _send_email_alert(self, alert_data: Dict[str, Any]):
        """发送邮件告警"""
        try:
            if not self.alert_config.smtp_user or not self.alert_config.alert_recipients:
                return
            
            # 构造邮件内容
            subject = f"[MT5-Queue-Alert] {alert_data['severity']}: {alert_data['title']}"
            
            html_content = f"""
            <html>
            <body>
                <h2>MT5队列系统告警</h2>
                <table border="1" cellpadding="5">
                    <tr><td><b>告警级别</b></td><td>{alert_data['severity']}</td></tr>
                    <tr><td><b>告警标题</b></td><td>{alert_data['title']}</td></tr>
                    <tr><td><b>告警消息</b></td><td>{alert_data['message']}</td></tr>
                    <tr><td><b>主机</b></td><td>{alert_data['host']}</td></tr>
                    <tr><td><b>时间</b></td><td>{alert_data['timestamp']}</td></tr>
                    <tr><td><b>监控时长</b></td><td>{alert_data['monitoring_duration']:.1f}秒</td></tr>
                </table>
                
                <h3>系统状态</h3>
                <p>总告警数: {self.total_alerts_sent}</p>
                <p>故障转移次数: {self.backend_failover_count}</p>
            </body>
            </html>
            """
            
            # 创建邮件
            msg = MimeMultipart('alternative')
            msg['Subject'] = subject
            msg['From'] = self.alert_config.smtp_user
            msg['To'] = ', '.join(self.alert_config.alert_recipients)
            
            html_part = MimeText(html_content, 'html')
            msg.attach(html_part)
            
            # 发送邮件
            with smtplib.SMTP(self.alert_config.smtp_host, self.alert_config.smtp_port) as server:
                server.starttls()
                if self.alert_config.smtp_password:
                    server.login(self.alert_config.smtp_user, self.alert_config.smtp_password)
                server.send_message(msg)
            
            logger.info(f"邮件告警已发送: {subject}")
            
        except Exception as e:
            logger.error(f"发送邮件告警失败: {e}")
    
    async def _send_external_alert(self, alert_data: Dict[str, Any]):
        """发送到外部监控系统"""
        try:
            # 这里可以添加webhook、Slack、钉钉等告警集成
            # 示例：发送到webhook
            
            # import aiohttp
            # webhook_url = "https://hooks.slack.com/your-webhook-url"
            # async with aiohttp.ClientSession() as session:
            #     await session.post(webhook_url, json=alert_data)
            
            pass
            
        except Exception as e:
            logger.error(f"发送外部告警失败: {e}")
    
    def get_monitoring_report(self) -> Dict[str, Any]:
        """获取监控报告"""
        uptime = time.time() - self.monitoring_start_time
        
        # 分析健康历史
        recent_records = self.health_history[-100:] if self.health_history else []
        
        backend_uptime = {}
        if recent_records:
            for backend in ['nats', 'redis_streams', 'local_memory']:
                healthy_count = sum(
                    1 for record in recent_records 
                    if record.get('backend_health', {}).get(backend, {}).get('healthy', False)
                )
                backend_uptime[backend] = healthy_count / len(recent_records) if recent_records else 0
        
        return {
            'monitoring_uptime_seconds': uptime,
            'total_alerts_sent': self.total_alerts_sent,
            'backend_failover_count': self.backend_failover_count,
            'health_history_size': len(self.health_history),
            'backend_uptime_percentage': backend_uptime,
            'recent_alert_types': list(self.last_alert_times.keys())[-10:],  # 最近10种告警类型
            'current_status': self.queue_manager.get_status() if self.queue_manager else None
        }


async def main():
    """主函数 - 监控演示"""
    # 创建基本的队列管理器配置
    config = HybridQueueConfig(
        primary_backend=QueueBackendType.LOCAL_MEMORY,
        backup_backends=[],
        local_fallback=True
    )
    
    queue_manager = HybridQueueManager(config)
    
    # 创建告警配置
    alert_config = AlertConfig(
        alert_recipients=["<EMAIL>"],
        latency_warning_ms=50.0,
        latency_critical_ms=200.0
    )
    
    # 创建监控器
    monitor = QueueHealthMonitor(queue_manager, alert_config)
    
    try:
        # 初始化队列管理器
        await queue_manager.initialize()
        
        # 启动监控（运行5分钟演示）
        logger.info("启动队列健康监控演示（5分钟）")
        
        monitoring_task = asyncio.create_task(monitor.start_monitoring())
        
        # 等待5分钟或手动中断
        await asyncio.sleep(300)
        
    except KeyboardInterrupt:
        logger.info("收到中断信号，停止监控")
    finally:
        await monitor.stop_monitoring()
        await queue_manager.shutdown()
        
        # 打印监控报告
        report = monitor.get_monitoring_report()
        logger.info(f"监控报告: {json.dumps(report, indent=2, default=str)}")


if __name__ == "__main__":
    asyncio.run(main())