# MT5分布式交易系统 - 测试环境配置
# 覆盖core/system.yaml中的设置，适用于自动化测试

# ============================================================================
# 环境标识
# ============================================================================
environment:
  name: "testing"
  description: "自动化测试环境配置"
  debug_mode: true

# ============================================================================
# 应用程序配置覆盖
# ============================================================================
application:
  # 测试环境性能设置
  performance:
    max_workers: 2                  # 测试环境最少worker
    memory_limit_mb: 512            # 512MB内存限制
    
  # 测试环境安全设置
  security:
    authentication_enabled: false  # 测试环境关闭认证
    api_rate_limiting: false        # 关闭API限流
    data_encryption: false          # 关闭数据加密

# ============================================================================
# 基础设施配置覆盖
# ============================================================================
redis:
  connection:
    host: "localhost"
    port: 6379
    db: 2                          # 使用测试专用数据库
    
  cache:
    default_ttl: 60                # 测试环境极短缓存时间
    
nats:
  servers:
    - "nats://localhost:4222"
    
  jetstream:
    max_msgs: 100000               # 测试环境最小消息存储
    max_bytes: 104857600           # 100MB
    max_age: 300                   # 5分钟

# ============================================================================
# 日志配置覆盖
# ============================================================================
logging:
  global:
    level: "INFO"                  # 测试环境中等日志级别
    
  handlers:
    console:
      enabled: false               # 测试环境关闭控制台输出
      
    file:
      enabled: true
      level: "INFO"
      file: "logs/testing.log"
      max_size_mb: 20
      backup_count: 2
      
    json_file:
      enabled: true
      file: "logs/testing_structured.json"
      
  loggers:
    "mt5.coordinator": "INFO"
    "mt5.trading": "INFO"
    "mt5.messaging": "WARNING"
    "redis": "ERROR"
    "nats": "ERROR"

# ============================================================================
# 监控配置覆盖
# ============================================================================
prometheus:
  enabled: false                   # 测试环境关闭Prometheus
    
health_checks:
  enabled: false                   # 测试环境关闭健康检查

# ============================================================================
# 主机配置覆盖
# ============================================================================
deployment:
  default_strategy: "single_host"
  default_host: "UK-001"

# ============================================================================
# 账户配置覆盖
# ============================================================================
accounts:
  # 测试环境账户限制
  global_overrides:
    trading:
      volume_limits:
        max_volume: 0.01           # 测试环境最小手数
        default_volume: 0.01
        
    risk_management:
      limits:
        max_daily_loss: 1.0        # 测试环境最小亏损限制
        max_positions: 1           # 最多1个持仓
        max_daily_trades: 5        # 最多5笔交易
        
    monitoring:
      position_monitoring:
        polling_interval: 5.0      # 测试环境降低监控频率
      account_monitoring:
        polling_interval: 10.0

# ============================================================================
# 跟单关系配置覆盖
# ============================================================================
copy_trading:
  global_settings:
    # 测试环境跟单限制
    system_limits:
      max_concurrent_relationships: 1
      emergency_stop_enabled: true
      
    performance:
      batch_processing: false      # 关闭批处理简化测试
      
    monitoring:
      collect_statistics: true
      statistics_interval: 60     # 1分钟统计
      
  # 关系特定覆盖
  relationship_overrides:
    "ACC001_to_ACC002":
      copy_ratio: 1.0              # 测试环境1:1跟单
      
      limits:
        max_volume_per_trade: 0.01
        max_positions: 1
        max_daily_volume: 0.1
        
      execution:
        delay_ms: 500              # 增加延迟便于测试验证
        timeout_ms: 3000
        
      monitoring:
        track_performance: true
        log_all_trades: true

# ============================================================================
# 功能开关覆盖
# ============================================================================
features:
  copy_trading: true
  distributed_deployment: false   # 测试环境单机部署
  advanced_monitoring: false
  api_gateway: false               # 测试环境关闭API网关
  web_interface: false

# ============================================================================
# 网络配置覆盖
# ============================================================================
network:
  timeouts:
    connect: 3                     # 测试环境短超时快速失败
    read: 5
    write: 3
    
  retry:
    max_attempts: 1                # 测试环境不重试
    
# ============================================================================
# 数据配置覆盖
# ============================================================================
data:
  persistence:
    enabled: false                 # 测试环境关闭持久化
    backup_enabled: false
    
  cache:
    enabled: false                 # 测试环境关闭缓存

# ============================================================================
# 测试配置
# ============================================================================
testing:
  # 自动化测试设置
  automation:
    enabled: true
    timeout_per_test: 30           # 每个测试30秒超时
    cleanup_after_test: true      # 测试后清理
    
  # 模拟配置
  simulation:
    enabled: true
    mock_mt5_connections: true     # 测试环境模拟MT5连接
    mock_trading: true             # 模拟交易执行
    mock_responses_delay_ms: 100   # 模拟响应延迟
    
  # 测试数据
  test_data:
    enabled: true
    clean_before_test: true
    sample_balance: 1000.0
    sample_trades:
      - symbol: "EURUSD"
        volume: 0.01
        action: "BUY"
      - symbol: "GBPUSD"  
        volume: 0.01
        action: "SELL"
        
  # 验证配置
  validation:
    strict_mode: true              # 严格验证模式
    validate_all_configs: true
    fail_on_warning: true
    
  # 性能测试
  performance_testing:
    enabled: false                 # 默认关闭性能测试
    max_latency_ms: 1000
    min_throughput_ops: 10

# ============================================================================
# 错误处理覆盖
# ============================================================================
error_handling:
  fail_fast: true                  # 测试环境快速失败
  continue_on_error: false
  max_consecutive_failures: 1

# ============================================================================
# 环境变量默认值
# ============================================================================
environment_defaults:
  MT5_ENVIRONMENT: "testing"
  MT5_DEBUG: "true"
  MT5_LOG_LEVEL: "INFO"
  MT5_MAX_WORKERS: "2"
  MT5_MEMORY_LIMIT: "512"
  REDIS_HOST: "localhost"
  REDIS_DB: "2"
  NATS_HOST: "localhost"
  
  # 测试专用环境变量
  MT5_TESTING_MODE: "true"
  MT5_MOCK_TRADING: "true"
  MT5_TEST_TIMEOUT: "30"