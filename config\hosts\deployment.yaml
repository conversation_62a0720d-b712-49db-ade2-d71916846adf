# MT5分布式交易系统 - 主机部署配置
# 统一定义所有主机、账户分配和部署策略
# 这是主机分配的唯一权威来源

# ============================================================================
# 主机定义
# ============================================================================
hosts:
  # 主交易节点 1
  UK-001:
    hostname: "uk-001"
    ip_address: "**********"
    region: "UK"
    datacenter: "london-1"
    
    # 主机能力
    capabilities:
      - "master"        # 可运行主账户
      - "slave"         # 可运行从账户
      - "coordinator"   # 可运行系统协调器
      
    # 资源规格
    resources:
      cpu_cores: 8
      memory_gb: 16
      disk_gb: 500
      network_speed_mbps: 1000
      
    # 服务配置
    services:
      api_port: 8001
      metrics_port: 9001
      health_port: 8101
      
    # 分配的账户（权威定义）
    assigned_accounts:
      - account_id: "ACC001"
        role: "master"
        priority: 1
        
      - account_id: "ACC002"  
        role: "slave"
        priority: 2
        
  # 备用交易节点 - 英国伦敦
  UK-002:
    hostname: "uk-002"
    ip_address: "**********"
    region: "UK"
    datacenter: "london-1"
    
    capabilities:
      - "slave"
      - "backup"
      
    resources:
      cpu_cores: 4
      memory_gb: 8
      disk_gb: 200
      network_speed_mbps: 1000
      
    services:
      api_port: 8002
      metrics_port: 9002
      health_port: 8102
      
    assigned_accounts:
      - account_id: "ACC003"
        role: "slave"
        priority: 1
        enabled: false  # 当前禁用
        
  # Docker开发环境节点
  docker-dev:
    hostname: "docker-development"
    ip_address: "************"
    region: "local"
    datacenter: "docker"
    
    capabilities:
      - "master"
      - "slave"
      - "coordinator"
      - "api"
      
    resources:
      cpu_cores: 4
      memory_gb: 8
      disk_gb: 100
      network_speed_mbps: 1000
      
    services:
      api_port: 8000
      metrics_port: 9000
      health_port: 8100
      
    assigned_accounts:
      - account_id: "ACC001"
        role: "master"
        priority: 1
        
      - account_id: "ACC002"
        role: "slave"
        priority: 2

# ============================================================================
# 部署策略
# ============================================================================
deployment:
  # 默认部署策略
  default_strategy: "single_host"  # single_host/multi_host/distributed
  
  # 环境特定部署
  environments:
    development:
      strategy: "single_host"
      default_host: "docker-dev"
      
    testing:
      strategy: "single_host"
      default_host: "UK-001"
      
    production:
      strategy: "multi_host"
      load_balancing: true
      failover_enabled: true

# ============================================================================
# 账户-主机映射规则
# ============================================================================
account_placement:
  # 放置策略
  strategy: "performance_optimized"  # performance_optimized/load_balanced/manual
  
  # 约束条件
  constraints:
    # 主账户约束
    master_accounts:
      - must_have_coordinator: true
      - min_cpu_cores: 4
      - min_memory_gb: 8
      - max_latency_ms: 50
      
    # 从账户约束  
    slave_accounts:
      - min_cpu_cores: 2
      - min_memory_gb: 4
      - max_latency_ms: 100
      
  # 反亲和性规则（避免冲突）
  anti_affinity:
    enabled: true
    rules:
      - "same_master_slaves_different_hosts"  # 同一主账户的从账户尽量分散
      - "max_accounts_per_host: 5"           # 每台主机最多5个账户

# ============================================================================
# 故障转移配置
# ============================================================================
failover:
  enabled: true
  
  # 检测配置
  detection:
    check_interval: 30
    timeout: 120
    max_failures: 3
    
  # 转移策略
  strategies:
    - name: "backup_host"
      enabled: true
      delay_seconds: 60
      
    - name: "load_balancing"
      enabled: true
      threshold: 80  # CPU使用率
      
    - name: "graceful_degradation"
      enabled: true
      essential_services_only: true
      
  # 故障转移规则
  rules:
    - source_host: "UK-001"
      target_host: "UK-002"
      account_types: ["master", "slave"]
      strategy: "delayed"
      delay_seconds: 60
      
    - source_host: "UK-002"
      target_host: "UK-001"
      account_types: ["slave"]
      strategy: "immediate"

# ============================================================================
# 网络配置
# ============================================================================
network:
  # 内部通信
  internal:
    protocol: "http"  # http/https
    port_range: "8000-8999"
    
  # 外部访问
  external:
    load_balancer:
      enabled: false
      algorithm: "round_robin"
      
  # 网络安全
  security:
    firewall:
      enabled: false  # 开发环境关闭
      allowed_ports: [8000, 8001, 8002, 9000, 9001, 9002]
      
    vpn:
      enabled: false
      required_for_production: true

# ============================================================================
# 资源限制和监控
# ============================================================================
resources:
  # 全局资源限制
  limits:
    max_cpu_usage: 80
    max_memory_usage: 85
    max_disk_usage: 90
    
  # 监控阈值
  monitoring:
    cpu_alert_threshold: 75
    memory_alert_threshold: 80
    disk_alert_threshold: 85
    network_alert_threshold: 500  # Mbps

# ============================================================================
# 部署验证规则
# ============================================================================
validation:
  # 部署前检查
  pre_deployment:
    - "host_resources_sufficient"
    - "network_connectivity"
    - "service_ports_available"
    - "account_conflicts_none"
    
  # 运行时检查
  runtime:
    - "resource_usage_within_limits"
    - "service_health_ok"
    - "account_assignments_valid"
    
  # 配置一致性检查
  consistency:
    - "no_duplicate_account_assignments"
    - "all_referenced_hosts_exist"
    - "all_assigned_accounts_have_configs"