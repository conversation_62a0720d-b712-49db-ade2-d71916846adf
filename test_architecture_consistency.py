#!/usr/bin/env python3
"""
架构一致性验证脚本
验证新架构的完整性和一致性
"""

import sys
import os
import re
from pathlib import Path

# 添加src路径
sys.path.insert(0, str(Path(__file__).parent / 'src'))

def test_imports():
    """测试所有核心组件的导入"""
    try:
        print("🔍 测试组件导入...")
        
        # 测试新架构组件导入
        from src.messaging.optimized_nats_manager import OptimizedNATSManager
        from src.messaging.hybrid_message_router import HybridMessageRouter  
        from src.messaging.jetstream_client import JetStreamClient
        from src.messaging.priority_queue import MessagePriority, PriorityAnalyzer
        from src.core.message_routing_coordinator import MessageRoutingCoordinator
        
        print("✅ 所有核心组件导入成功")
        return True
        
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
        return False

def check_topic_consistency():
    """检查主题一致性"""
    print("🔍 检查主题模式一致性...")
    
    # 新架构主题模式
    expected_topics = {
        'MT5.SIGNALS.CRITICAL.*',
        'MT5.SIGNALS.HIGH.*', 
        'MT5.SIGNALS.NORMAL.*',
        'MT5.SIGNALS.LOW.*',
        'MT5.RPC.*',
        'MT5.CONTROL.*',
        'MT5.LOCAL.*'
    }
    
    # 旧主题模式（不应存在）
    deprecated_topics = {
        'MT5.TRADES.*',
        'MT5.COPY.*', 
        'MT5.EXECUTION.*',
        'MT5.EXECUTE.*',
        'MT5.MONITOR.*'
    }
    
    issues = []
    src_dir = Path(__file__).parent / 'src'
    
    for py_file in src_dir.rglob('*.py'):
        content = py_file.read_text(encoding='utf-8')
        
        # 检查是否使用了旧主题
        for old_topic in deprecated_topics:
            pattern = old_topic.replace('*', r'\*').replace('.', r'\.')
            if re.search(pattern, content) and 'removed' not in content.lower() and 'deprecated' not in content.lower():
                # 排除注释中的引用
                lines = content.split('\n')
                for i, line in enumerate(lines, 1):
                    if re.search(pattern, line) and not line.strip().startswith('#'):
                        issues.append(f"{py_file}:{i} - 使用了旧主题: {old_topic}")
    
    if issues:
        print("❌ 发现主题不一致问题:")
        for issue in issues:
            print(f"  {issue}")
        return False
    else:
        print("✅ 主题模式一致性检查通过")
        return True

def check_architecture_components():
    """检查架构组件"""
    print("🔍 检查架构组件配置...")
    
    try:
        # 验证核心架构类
        from src.messaging.optimized_nats_manager import OptimizedNATSManager
        from src.messaging.hybrid_message_router import HybridMessageRouter
        
        # 检查关键方法是否存在
        manager_methods = [
            'initialize_optimized_architecture',
            'shutdown', 
            'get_status'
        ]
        
        router_methods = [
            'route_signal',
            'start',
            'stop',
            'get_status'
        ]
        
        for method in manager_methods:
            if not hasattr(OptimizedNATSManager, method):
                print(f"❌ OptimizedNATSManager缺少方法: {method}")
                return False
        
        for method in router_methods:
            if not hasattr(HybridMessageRouter, method):
                print(f"❌ HybridMessageRouter缺少方法: {method}")
                return False
        
        print("✅ 架构组件检查通过")
        return True
        
    except Exception as e:
        print(f"❌ 架构组件检查失败: {e}")
        return False

def check_file_cleanup():
    """检查文件清理情况"""
    print("🔍 检查旧文件清理...")
    
    src_dir = Path(__file__).parent / 'src'
    
    # 应该被删除的文件
    removed_files = [
        'src/distributed/message_router.py',
        'src/messaging/nats_manager.py'
    ]
    
    issues = []
    for file_path in removed_files:
        full_path = Path(__file__).parent / file_path
        if full_path.exists():
            issues.append(f"旧文件未删除: {file_path}")
    
    if issues:
        print("❌ 文件清理问题:")
        for issue in issues:
            print(f"  {issue}")
        return False
    else:
        print("✅ 旧文件清理完成")
        return True

def main():
    """主函数"""
    print("🏗️ MT5架构一致性验证\n")
    
    all_passed = True
    
    # 运行所有检查
    checks = [
        ("组件导入", test_imports),
        ("主题一致性", check_topic_consistency),
        ("架构组件", check_architecture_components),
        ("文件清理", check_file_cleanup)
    ]
    
    for name, check_func in checks:
        print(f"\n--- {name}检查 ---")
        if not check_func():
            all_passed = False
    
    print("\n" + "="*50)
    if all_passed:
        print("🎉 架构一致性验证通过！")
        print("新架构特性:")
        print("  ✓ 4层分层流架构 (LOCAL/SIGNALS/RPC/CONTROL)")
        print("  ✓ 统一主题模式 (MT5.SIGNALS.*, MT5.RPC.*, MT5.CONTROL.*)")
        print("  ✓ 混合消息路由 (本地直达 + 跨主机NATS)")
        print("  ✓ 优先级感知处理 (CRITICAL/HIGH/NORMAL/LOW)")
        print("  ✓ MT5 API速率限制集成")
        print("  ✓ 旧架构完全清理")
        return 0
    else:
        print("❌ 架构一致性验证失败！")
        print("请修复上述问题后重新运行验证")
        return 1

if __name__ == '__main__':
    sys.exit(main())