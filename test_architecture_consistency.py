#!/usr/bin/env python3
"""
架构一致性验证测试 - 无需依赖导入
直接检查代码结构和配置文件一致性
"""

import os
import re
import yaml
from typing import List, Dict, Any, Tuple
import logging

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class ArchitectureConsistencyChecker:
    """架构一致性检查器"""
    
    def __init__(self):
        self.issues = []
        self.passed_checks = []
        
    def check_critical_issue_1_message_flow(self) -> bool:
        """检查关键问题1: 消息流主题一致性"""
        logger.info("🔍 检查关键问题1: 消息流主题一致性")
        
        issues = []
        
        # 检查Monitor发布的主题
        monitor_file = "src/core/mt5_account_monitor.py"
        if os.path.exists(monitor_file):
            with open(monitor_file, 'r', encoding='utf-8') as f:
                content = f.read()
                
            # 查找Monitor发布的主题模式
            monitor_topic_patterns = re.findall(r'self\.event_topic = f?"([^"]+)"', content)
            
            if monitor_topic_patterns:
                monitor_pattern = monitor_topic_patterns[0]
                logger.info(f"Monitor发布主题模式: {monitor_pattern}")
                
                # 检查Router订阅的主题
                router_file = "src/core/message_routing_coordinator.py"
                if os.path.exists(router_file):
                    with open(router_file, 'r', encoding='utf-8') as f:
                        router_content = f.read()
                    
                    # 查找Router订阅模式
                    router_patterns = re.findall(r'monitor_topic_pattern = "([^"]+)"', router_content)
                    
                    if router_patterns:
                        router_pattern = router_patterns[0]
                        logger.info(f"Router订阅主题模式: {router_pattern}")
                        
                        # 检查是否匹配
                        if self._topics_match(monitor_pattern, router_pattern):
                            self.passed_checks.append("✅ 消息流主题匹配: Monitor → Router")
                            return True
                        else:
                            issues.append(f"❌ 消息流主题不匹配: Monitor='{monitor_pattern}' Router='{router_pattern}'")
                    else:
                        issues.append("❌ Router中未找到monitor_topic_pattern")
                else:
                    issues.append("❌ Router文件不存在")
            else:
                issues.append("❌ Monitor中未找到event_topic设置")
        else:
            issues.append("❌ Monitor文件不存在")
        
        if issues:
            self.issues.extend(issues)
            return False
        
        return True
    
    def check_critical_issue_2_layered_streams(self) -> bool:
        """检查关键问题2: 分层流架构配置和实现"""
        logger.info("🔍 检查关键问题2: 分层流架构")
        
        issues = []
        
        # 1. 检查infrastructure.yaml配置
        infra_config_file = "config/core/infrastructure.yaml"
        if os.path.exists(infra_config_file):
            try:
                with open(infra_config_file, 'r', encoding='utf-8') as f:
                    infra_config = yaml.safe_load(f)
                
                # 检查JetStream流配置
                jetstream = infra_config.get('nats', {}).get('jetstream', {})
                streams = jetstream.get('streams', {})
                
                expected_streams = ['local', 'signals', 'rpc', 'control']
                configured_streams = list(streams.keys())
                
                if set(expected_streams) <= set(configured_streams):
                    self.passed_checks.append("✅ infrastructure.yaml包含四层流配置")
                else:
                    missing = set(expected_streams) - set(configured_streams)
                    issues.append(f"❌ infrastructure.yaml缺少流配置: {missing}")
                
            except Exception as e:
                issues.append(f"❌ infrastructure.yaml解析失败: {e}")
        else:
            issues.append("❌ infrastructure.yaml文件不存在")
        
        # 2. 检查JetStreamClient实现
        jetstream_client_file = "src/messaging/jetstream_client.py"
        if os.path.exists(jetstream_client_file):
            with open(jetstream_client_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 检查关键方法是否存在
            required_methods = [
                'async def initialize(',
                'async def create_layered_streams(',
                'async def _create_local_stream(',
                'async def _create_signals_stream(',
                'async def _create_rpc_stream(',
                'async def _create_control_stream('
            ]
            
            for method in required_methods:
                if method in content:
                    self.passed_checks.append(f"✅ JetStreamClient包含方法: {method.split('(')[0]}")
                else:
                    issues.append(f"❌ JetStreamClient缺少方法: {method.split('(')[0]}")
        else:
            issues.append("❌ JetStreamClient文件不存在")
        
        # 3. 检查Coordinator是否使用initialize方法
        coordinator_file = "src/core/mt5_coordinator.py"
        if os.path.exists(coordinator_file):
            with open(coordinator_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            if "jetstream.initialize(" in content:
                self.passed_checks.append("✅ Coordinator使用JetStream.initialize()方法")
            else:
                issues.append("❌ Coordinator未使用JetStream.initialize()方法")
        
        if issues:
            self.issues.extend(issues)
            return False
        
        return True
    
    def check_critical_issue_3_priority_system(self) -> bool:
        """检查关键问题3: 优先级系统实现"""
        logger.info("🔍 检查关键问题3: 优先级系统")
        
        issues = []
        
        # 1. 检查PriorityMessageQueue实现
        priority_queue_file = "src/messaging/priority_queue.py"
        if os.path.exists(priority_queue_file):
            with open(priority_queue_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            required_components = [
                'class MessagePriority',
                'class PriorityMessageQueue',
                'async def enqueue(',
                'async def dequeue('
            ]
            
            for component in required_components:
                if component in content:
                    self.passed_checks.append(f"✅ PriorityQueue包含: {component}")
                else:
                    issues.append(f"❌ PriorityQueue缺少: {component}")
        else:
            issues.append("❌ priority_queue.py文件不存在")
        
        # 2. 检查Executor是否使用PriorityMessageQueue
        executor_file = "src/core/mt5_account_executor.py"
        if os.path.exists(executor_file):
            with open(executor_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            if "from ..messaging.priority_queue import PriorityMessageQueue" in content:
                self.passed_checks.append("✅ Executor导入PriorityMessageQueue")
                
                if "self.command_queue = PriorityMessageQueue(" in content:
                    self.passed_checks.append("✅ Executor使用PriorityMessageQueue")
                else:
                    issues.append("❌ Executor未实例化PriorityMessageQueue")
            else:
                issues.append("❌ Executor未导入PriorityMessageQueue")
        else:
            issues.append("❌ Executor文件不存在")
        
        # 3. 检查infrastructure.yaml中的优先级主题配置
        infra_config_file = "config/core/infrastructure.yaml"
        if os.path.exists(infra_config_file):
            try:
                with open(infra_config_file, 'r', encoding='utf-8') as f:
                    infra_config = yaml.safe_load(f)
                
                signals_stream = infra_config.get('nats', {}).get('jetstream', {}).get('streams', {}).get('signals', {})
                subjects = signals_stream.get('subjects', [])
                
                # 检查优先级主题
                priority_subjects = [s for s in subjects if 'CRITICAL' in s or 'HIGH' in s or 'NORMAL' in s or 'LOW' in s]
                
                if len(priority_subjects) >= 4:  # 至少四个优先级级别
                    self.passed_checks.append(f"✅ 配置包含优先级主题: {len(priority_subjects)}个")
                else:
                    issues.append(f"❌ 配置中优先级主题不足: 只有{len(priority_subjects)}个")
                    
            except Exception as e:
                issues.append(f"❌ 无法检查优先级主题配置: {e}")
        
        if issues:
            self.issues.extend(issues)
            return False
        
        return True
    
    def check_stream_config_manager_integration(self) -> bool:
        """检查StreamConfigManager集成"""
        logger.info("🔍 检查StreamConfigManager集成")
        
        issues = []
        
        # 1. 检查StreamConfigManager定义
        config_manager_file = "src/core/config_manager.py"
        if os.path.exists(config_manager_file):
            with open(config_manager_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            if "class StreamConfigManager:" in content:
                self.passed_checks.append("✅ StreamConfigManager类存在")
                
                required_methods = [
                    "def get_stream_config(",
                    "def get_consumer_config(",
                    "def get_subject_pattern("
                ]
                
                for method in required_methods:
                    if method in content:
                        self.passed_checks.append(f"✅ StreamConfigManager包含: {method.split('(')[0]}")
                    else:
                        issues.append(f"❌ StreamConfigManager缺少: {method.split('(')[0]}")
            else:
                issues.append("❌ StreamConfigManager类不存在")
        else:
            issues.append("❌ config_manager.py文件不存在")
        
        # 2. 检查JetStreamClient是否使用StreamConfigManager
        jetstream_client_file = "src/messaging/jetstream_client.py"
        if os.path.exists(jetstream_client_file):
            with open(jetstream_client_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            if "from ..core.config_manager import get_stream_config_manager" in content:
                self.passed_checks.append("✅ JetStreamClient导入StreamConfigManager")
                
                if "stream_config_manager.get_stream_config(" in content:
                    self.passed_checks.append("✅ JetStreamClient使用StreamConfigManager")
                else:
                    issues.append("❌ JetStreamClient未使用StreamConfigManager")
            else:
                issues.append("❌ JetStreamClient未导入StreamConfigManager")
        
        if issues:
            self.issues.extend(issues)
            return False
        
        return True
    
    def check_hardcoded_config_elimination(self) -> bool:
        """检查硬编码配置消除情况"""
        logger.info("🔍 检查硬编码配置消除")
        
        issues = []
        passed = 0
        
        # 检查关键文件中的硬编码模式
        files_to_check = [
            "src/messaging/jetstream_client.py",
            "src/core/mt5_coordinator.py",
            "src/core/mt5_account_monitor.py",
            "src/core/message_routing_coordinator.py"
        ]
        
        hardcoded_patterns = [
            (r'stream_name\s*=\s*"MT5_[A-Z_]+"', "硬编码流名称"),
            (r'subject\s*=\s*"MT5\.[A-Z.]+\.\w+"', "硬编码主题名称"),
            (r'servers\s*=\s*\["nats://[^"]+"\]', "硬编码NATS服务器"),
        ]
        
        for file_path in files_to_check:
            if os.path.exists(file_path):
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                file_has_hardcoded = False
                for pattern, description in hardcoded_patterns:
                    matches = re.findall(pattern, content)
                    if matches:
                        issues.append(f"❌ {file_path}中发现{description}: {len(matches)}处")
                        file_has_hardcoded = True
                
                if not file_has_hardcoded:
                    passed += 1
                    self.passed_checks.append(f"✅ {file_path}无明显硬编码")
        
        # 检查配置使用情况
        config_usage_patterns = [
            "get_stream_config_manager",
            "config_manager.get(",
            "stream_config_manager.get_stream_config"
        ]
        
        config_usage_files = 0
        for file_path in files_to_check:
            if os.path.exists(file_path):
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                for pattern in config_usage_patterns:
                    if pattern in content:
                        config_usage_files += 1
                        self.passed_checks.append(f"✅ {file_path}使用配置管理器")
                        break
        
        total_files = len(files_to_check)
        success_rate = (passed + config_usage_files) / (total_files * 2) * 100
        
        if success_rate >= 75:  # 75%以上消除硬编码
            return True
        else:
            issues.append(f"❌ 硬编码消除不足: 成功率{success_rate:.1f}%")
            
        if issues:
            self.issues.extend(issues)
            return False
        
        return True
    
    def _topics_match(self, publish_pattern: str, subscribe_pattern: str) -> bool:
        """检查发布模式是否匹配订阅模式"""
        # 简单的模式匹配检查
        # 将{host_id}和{account_id}视为通配符
        publish_normalized = re.sub(r'\{[^}]+\}', '*', publish_pattern)
        
        # 将NATS通配符转换为正则表达式
        # * 匹配单个token, > 匹配一个或多个token
        subscribe_regex = subscribe_pattern.replace('.', r'\.')
        subscribe_regex = subscribe_regex.replace('*', '[^.]*')  # 单个token
        subscribe_regex = subscribe_regex.replace('>', '.*')     # 多个token
        subscribe_regex = f"^{subscribe_regex}$"
        
        try:
            return bool(re.match(subscribe_regex, publish_normalized))
        except Exception:
            return False
    
    def generate_report(self) -> Tuple[bool, Dict[str, Any]]:
        """生成验证报告"""
        total_checks = len(self.passed_checks) + len(self.issues)
        passed_checks = len(self.passed_checks)
        failed_checks = len(self.issues)
        
        success_rate = (passed_checks / total_checks * 100) if total_checks > 0 else 0
        
        report = {
            "total_checks": total_checks,
            "passed_checks": passed_checks,
            "failed_checks": failed_checks,
            "success_rate": success_rate,
            "passed_details": self.passed_checks,
            "issues": self.issues,
            "overall_status": success_rate >= 80  # 80%成功率阈值
        }
        
        return success_rate >= 80, report


def main():
    """主函数"""
    print("="*80)
    print("🏗️ MT5分布式系统架构一致性验证")
    print("="*80)
    
    checker = ArchitectureConsistencyChecker()
    
    # 执行所有检查
    tests = [
        ("关键问题1-消息流", checker.check_critical_issue_1_message_flow),
        ("关键问题2-分层流", checker.check_critical_issue_2_layered_streams),
        ("关键问题3-优先级", checker.check_critical_issue_3_priority_system),
        ("StreamConfigManager集成", checker.check_stream_config_manager_integration),
        ("硬编码配置消除", checker.check_hardcoded_config_elimination),
    ]
    
    results = {}
    for test_name, test_func in tests:
        print(f"\n🔍 执行检查: {test_name}")
        results[test_name] = test_func()
    
    # 生成报告
    overall_success, report = checker.generate_report()
    
    print("\n" + "="*80)
    print("📊 架构一致性验证报告")
    print("="*80)
    
    print(f"总检查项: {report['total_checks']}")
    print(f"✅ 通过: {report['passed_checks']}")
    print(f"❌ 失败: {report['failed_checks']}")
    print(f"🎯 成功率: {report['success_rate']:.1f}%")
    
    print("\n📋 详细结果:")
    print("-"*40)
    
    # 通过的检查
    if report['passed_details']:
        print("✅ 通过的检查:")
        for detail in report['passed_details']:
            print(f"  {detail}")
    
    # 失败的检查
    if report['issues']:
        print("\n❌ 发现的问题:")
        for issue in report['issues']:
            print(f"  {issue}")
    
    print("\n" + "="*80)
    print("🚨 关键问题解决状态:")
    print("-"*40)
    
    critical_status = {
        "消息流修复": results.get("关键问题1-消息流", False),
        "分层流架构": results.get("关键问题2-分层流", False),
        "优先级系统": results.get("关键问题3-优先级", False)
    }
    
    for issue, resolved in critical_status.items():
        status = "✅ 已解决" if resolved else "❌ 未解决"
        print(f"{issue}: {status}")
    
    all_critical_resolved = all(critical_status.values())
    
    if all_critical_resolved and overall_success:
        print(f"\n🎉 总体状态: ✅ 系统架构修复成功!")
        print("\n🏁 系统现在具备:")
        print("  ✅ 统一的消息流 (Monitor → Router → Executor)")
        print("  ✅ 四层分层流架构 (Local/Signals/RPC/Control)")
        print("  ✅ 有效的优先级系统 (CRITICAL > HIGH > NORMAL > LOW)")
        print("  ✅ 配置驱动的架构 (消除硬编码)")
        print("  ✅ StreamConfigManager集成")
        
        print("\n🚀 可以进行下一步:")
        print("  1. 运行完整的端到端测试")
        print("  2. 实施剩余的增量跟踪和自适应轮询")
        print("  3. 进行性能基准测试")
        return 0
        
    elif all_critical_resolved:
        print(f"\n⚠️ 总体状态: 关键问题已解决，但仍有{report['failed_checks']}个次要问题")
        return 1
    else:
        unresolved = [k for k, v in critical_status.items() if not v]
        print(f"\n❌ 总体状态: 仍有关键问题未解决: {unresolved}")
        return 2


if __name__ == "__main__":
    exit_code = main()
    exit(exit_code)
