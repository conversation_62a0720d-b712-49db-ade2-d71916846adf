#!/usr/bin/env python3
"""
优先级消息队列
实现多优先级消息处理，确保关键信号优先处理
"""
import asyncio
import time
from typing import Dict, Any, Optional, Callable, List
from dataclasses import dataclass
from enum import Enum
import heapq

from ..utils.logger import get_logger
from ..utils.metrics import get_metrics_collector

logger = get_logger(__name__)
metrics = get_metrics_collector()


class MessagePriority(Enum):
    """统一消息优先级 - 四个等级 (0, 1, 2, 3)"""
    CRITICAL = 0    # 关键优先级 - 紧急平仓、止损触发、保证金不足
    HIGH = 1        # 高优先级 - 平仓信号、止损/止盈修改、订单取消
    NORMAL = 2      # 普通优先级 - 开仓信号、常规交易、账户查询
    LOW = 3         # 低优先级 - 行情查询、历史数据、状态更新


@dataclass
class PriorityMessage:
    """优先级消息"""
    priority: MessagePriority
    timestamp: float
    message_id: str
    data: Any
    callback: Optional[Callable] = None
    retry_count: int = 0
    max_retries: int = 3
    
    def __lt__(self, other):
        """比较函数，用于堆排序"""
        if self.priority.value != other.priority.value:
            return self.priority.value < other.priority.value
        return self.timestamp < other.timestamp


class PriorityMessageQueue:
    """优先级消息队列"""
    
    def __init__(self, max_sizes: Dict[MessagePriority, int] = None):
        """
        初始化优先级队列
        """
        self.max_sizes = max_sizes or {
            MessagePriority.CRITICAL: 100,    # 关键信号队列
            MessagePriority.HIGH: 500,        # 高优先级队列
            MessagePriority.NORMAL: 1000,     # 普通优先级队列
            MessagePriority.LOW: 2000         # 低优先级队列
        }
        
        # 使用堆来实现优先级队列
        self._heap: List[PriorityMessage] = []
        self._heap_lock = asyncio.Lock()
        
        # 分离的队列用于快速统计
        self._queues: Dict[MessagePriority, asyncio.Queue] = {}
        for priority in MessagePriority:
            self._queues[priority] = asyncio.Queue(maxsize=self.max_sizes[priority])
        
        # 统计信息
        self._stats = {
            'total_enqueued': 0,
            'total_dequeued': 0,
            'total_dropped': 0,
            'priority_stats': {p: {'enqueued': 0, 'dequeued': 0, 'dropped': 0} for p in MessagePriority}
        }
        
        # 处理器
        self._processors: Dict[MessagePriority, Callable] = {}
        self._running = False
        self._worker_tasks: List[asyncio.Task] = []
        
        logger.info("优先级消息队列初始化完成")
    
    async def enqueue(self, priority: MessagePriority, data: Any, 
                     message_id: str = None, callback: Callable = None) -> bool:
        """
        入队消息
        """
        try:
            if message_id is None:
                message_id = f"{priority.name}_{int(time.time() * 1000000)}"
            
            message = PriorityMessage(
                priority=priority,
                timestamp=time.time(),
                message_id=message_id,
                data=data,
                callback=callback
            )
            
            if self._queues[priority].full():
                self._stats['total_dropped'] += 1
                self._stats['priority_stats'][priority]['dropped'] += 1
                
                logger.warning(f"{priority.name}队列已满，丢弃消息: {message_id}")
                
                metrics.increment("priority_queue_dropped_total", 1,
                                 {'priority': priority.name})
                return False
            
            async with self._heap_lock:
                heapq.heappush(self._heap, message)
            
            await self._queues[priority].put(message)
            
            self._stats['total_enqueued'] += 1
            self._stats['priority_stats'][priority]['enqueued'] += 1
            
            metrics.increment("priority_queue_enqueued_total", 1,
                             {'priority': priority.name})
            metrics.record("priority_queue_size", len(self._heap))
            
            logger.debug(f"消息入队: {priority.name} - {message_id}")
            return True
            
        except Exception as e:
            logger.error(f"消息入队失败: {e}")
            return False
    
    async def dequeue(self, timeout: float = None) -> Optional[PriorityMessage]:
        """
        出队消息（按优先级）
        """
        try:
            async with self._heap_lock:
                if not self._heap:
                    return None
                
                message = heapq.heappop(self._heap)
            
            try:
                self._queues[message.priority].get_nowait()
            except asyncio.QueueEmpty:
                pass
            
            self._stats['total_dequeued'] += 1
            self._stats['priority_stats'][message.priority]['dequeued'] += 1
            
            metrics.increment("priority_queue_dequeued_total", 1,
                             {'priority': message.priority.name})
            metrics.record("priority_queue_size", len(self._heap))
            
            logger.debug(f"消息出队: {message.priority.name} - {message.message_id}")
            return message
            
        except Exception as e:
            logger.error(f"消息出队失败: {e}")
            return None
    
    async def dequeue_by_priority(self, priority: MessagePriority, 
                                 timeout: float = None) -> Optional[PriorityMessage]:
        """
        按指定优先级出队消息
        """
        try:
            if timeout is None:
                message = await self._queues[priority].get()
            else:
                message = await asyncio.wait_for(
                    self._queues[priority].get(), 
                    timeout=timeout
                )
            
            async with self._heap_lock:
                self._heap = [m for m in self._heap if m.message_id != message.message_id]
                heapq.heapify(self._heap)
            
            self._stats['total_dequeued'] += 1
            self._stats['priority_stats'][priority]['dequeued'] += 1
            
            metrics.increment("priority_queue_dequeued_total", 1,
                             {'priority': priority.name})
            metrics.record("priority_queue_size", len(self._heap))
            
            return message
            
        except asyncio.TimeoutError:
            return None
        except Exception as e:
            logger.error(f"按优先级出队失败: {e}")
            return None
    
    def register_processor(self, priority: MessagePriority, processor: Callable):
        """注册优先级处理器"""
        self._processors[priority] = processor
        logger.info(f"注册{priority.name}优先级处理器")
    
    async def start_workers(self, worker_count: int = 4):
        """启动工作线程"""
        if self._running:
            logger.warning("工作线程已在运行")
            return
        
        self._running = True
        
        for i in range(worker_count):
            task = asyncio.create_task(self._priority_worker(f"priority_worker_{i}"))
            self._worker_tasks.append(task)
        
        critical_task = asyncio.create_task(self._critical_worker())
        self._worker_tasks.append(critical_task)
        
        logger.info(f"启动了 {len(self._worker_tasks)} 个工作线程")
    
    async def stop_workers(self):
        """停止工作线程"""
        if not self._running:
            return
        
        self._running = False
        
        for task in self._worker_tasks:
            task.cancel()
        
        await asyncio.gather(*self._worker_tasks, return_exceptions=True)
        
        self._worker_tasks.clear()
        logger.info("所有工作线程已停止")
    
    async def _priority_worker(self, worker_name: str):
        """优先级工作线程"""
        logger.info(f"启动优先级工作线程: {worker_name}")
        
        while self._running:
            try:
                # 出队消息
                message = await self.dequeue(timeout=1.0)
                
                if message is None:
                    continue
                
                # 处理消息
                await self._process_message(message, worker_name)
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"优先级工作线程异常 {worker_name}: {e}")
                await asyncio.sleep(0.1)
        
        logger.info(f"优先级工作线程停止: {worker_name}")
    
    async def _critical_worker(self):
        """关键优先级专用工作线程"""
        logger.info("启动关键优先级工作线程")
        
        while self._running:
            try:
                message = await self.dequeue_by_priority(
                    MessagePriority.CRITICAL, timeout=0.5
                )
                
                if message is None:
                    continue
                
                await self._process_message(message, "critical_worker")
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"关键优先级工作线程异常: {e}")
                await asyncio.sleep(0.1)
        
        logger.info("关键优先级工作线程停止")
    
    async def _process_message(self, message: PriorityMessage, worker_name: str):
        """处理消息"""
        start_time = time.time()
        
        try:
            processor = self._processors.get(message.priority)
            
            if processor:
                await processor(message.data)
            elif message.callback:
                await message.callback(message.data)
            else:
                logger.warning(f"没有找到{message.priority.name}优先级的处理器")
            
            processing_time = (time.time() - start_time) * 1000
            metrics.record("priority_queue_processing_time_ms", processing_time,
                          {'priority': message.priority.name, 'worker': worker_name})
            
            logger.debug(f"消息处理完成: {message.message_id} ({processing_time:.2f}ms)")
            
        except Exception as e:
            logger.error(f"消息处理失败 {message.message_id}: {e}")
            
            if message.retry_count < message.max_retries:
                message.retry_count += 1
                await self.enqueue(message.priority, message.data, 
                                 f"{message.message_id}_retry_{message.retry_count}",
                                 message.callback)
                logger.info(f"消息重试 {message.message_id} (第{message.retry_count}次)")
            else:
                logger.error(f"消息重试次数超限，丢弃: {message.message_id}")
                metrics.increment("priority_queue_failed_total", 1,
                                 {'priority': message.priority.name})
    
    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        return {
            **self._stats,
            'queue_sizes': {p.name: self._queues[p].qsize() for p in MessagePriority},
            'heap_size': len(self._heap),
            'workers_running': len(self._worker_tasks),
            'is_running': self._running
        }
    
    def is_empty(self) -> bool:
        """检查队列是否为空"""
        return len(self._heap) == 0
    
    def size(self) -> int:
        """获取队列总大小"""
        return len(self._heap)
    
    def size_by_priority(self, priority: MessagePriority) -> int:
        """获取指定优先级队列大小"""
        return self._queues[priority].qsize()


# 全局优先级队列实例
_priority_queue = None

def get_priority_queue() -> PriorityMessageQueue:
    """获取全局优先级队列实例"""
    global _priority_queue
    if _priority_queue is None:
        _priority_queue = PriorityMessageQueue()
    return _priority_queue


# 便捷函数
async def enqueue_critical(data: Any, message_id: str = None, callback: Callable = None) -> bool:
    """入队关键优先级消息"""
    return await get_priority_queue().enqueue(MessagePriority.CRITICAL, data, message_id, callback)


async def enqueue_high(data: Any, message_id: str = None, callback: Callable = None) -> bool:
    """入队高优先级消息"""
    return await get_priority_queue().enqueue(MessagePriority.HIGH, data, message_id, callback)


async def enqueue_normal(data: Any, message_id: str = None, callback: Callable = None) -> bool:
    """入队普通优先级消息"""
    return await get_priority_queue().enqueue(MessagePriority.NORMAL, data, message_id, callback)


async def enqueue_low(data: Any, message_id: str = None, callback: Callable = None) -> bool:
    """入队低优先级消息"""
    return await get_priority_queue().enqueue(MessagePriority.LOW, data, message_id, callback)


# ==================== 统一优先级分析器 ====================

class PriorityAnalyzer:
    """统一优先级分析器 - 四个等级 (0, 1, 2, 3)"""

    @staticmethod
    def get_command_priority(method: str, params: Dict[str, Any]) -> MessagePriority:
        """
        确定命令优先级 - 统一四个等级
        """

        # === CRITICAL PRIORITY (0) - 紧急情况 ===

        # 1. 紧急平仓和风险控制
        if method == 'send_order':
            analyzed_priority = PriorityAnalyzer._analyze_trade_signal_priority(params)
            if analyzed_priority == MessagePriority.CRITICAL:
                return MessagePriority.CRITICAL
            elif analyzed_priority == MessagePriority.HIGH:
                return MessagePriority.HIGH

            # 检查紧急情况
            trade_action = params.get('action', '').upper()
            signal_type = params.get('signal_type', '').upper()

            # 紧急平仓信号
            if (trade_action in ['EMERGENCY_CLOSE', 'FORCE_CLOSE'] or
                signal_type in ['EMERGENCY_CLOSE', 'MARGIN_CALL', 'STOP_LOSS_HIT']):
                return MessagePriority.CRITICAL

        # 2. 系统紧急操作
        if method == 'emergency_stop' or params.get('emergency', False):
            return MessagePriority.CRITICAL

        # === HIGH PRIORITY (1) - 重要交易操作 ===

        # 1. 平仓和修改操作
        if method == 'send_order':
            trade_action = params.get('action', '').upper()
            signal_type = params.get('signal_type', '').upper()
            order_type = params.get('type', 0)

            # 平仓信号
            if trade_action in ['CLOSE'] or signal_type == 'POSITION_CLOSE':
                return MessagePriority.HIGH

            # 止损/止盈修改
            if (trade_action in ['MODIFY', 'MODIFY_SL', 'MODIFY_TP'] or
                signal_type == 'POSITION_MODIFY' or
                params.get('sl', 0) != 0 or params.get('tp', 0) != 0):
                return MessagePriority.HIGH

            # 止损单类型 (BUY_STOP=4, SELL_STOP=5)
            if order_type in [4, 5]:
                return MessagePriority.HIGH

            # 包含止损价格的订单
            if params.get('sl', 0) > 0:
                return MessagePriority.HIGH

        # 2. 订单取消操作
        if method == 'cancel_order' or params.get('action') == 'CANCEL':
            return MessagePriority.HIGH

        # 3. 关键系统操作
        if method == 'health_check' and params.get('critical', False):
            return MessagePriority.HIGH

        # === NORMAL PRIORITY (2) - 常规交易 ===

        # 1. 新开仓信号
        if method == 'send_order':
            signal_type = params.get('signal_type', '').upper()
            trade_action = params.get('action', '').upper()

            # 开仓信号
            if (signal_type == 'POSITION_OPEN' or
                trade_action in ['BUY', 'SELL', 'OPEN']):
                return MessagePriority.NORMAL

            # 限价单和止损限价单
            order_type = params.get('type', 0)
            if order_type in [2, 3, 6, 7]:  # LIMIT和STOP_LIMIT类型
                return MessagePriority.NORMAL

        # 2. 持仓和账户查询
        if method in ['get_positions', 'get_account_info', 'health_check']:
            return MessagePriority.NORMAL

        # === LOW PRIORITY (3) - 行情数据 ===

        # 1. 市场数据查询
        if method in ['get_symbol_info', 'get_market_data', 'get_tick_data']:
            return MessagePriority.LOW

        # 2. 历史数据查询
        if method in ['get_rates', 'get_history']:
            return MessagePriority.LOW

        # 3. 一般性查询
        if method.startswith('get_') and method not in ['get_positions', 'get_account_info']:
            return MessagePriority.LOW

        # Default to normal priority
        return MessagePriority.NORMAL

    @staticmethod
    def _analyze_trade_signal_priority(params: Dict[str, Any]) -> MessagePriority:
        """
        深度分析交易信号优先级 - 统一四个等级
        """

        signal_data = params.get('signal_data') or params.get('data') or params
        signal_type = signal_data.get('signal_type') or signal_data.get('type', '')
        if isinstance(signal_type, str):
            signal_type = signal_type.upper()

        # === CRITICAL PRIORITY (0) - 紧急情况 ===
        critical_signals = [
            'EMERGENCY_CLOSE',   # 紧急平仓
            'STOP_LOSS_HIT',     # 止损触发
            'MARGIN_CALL',       # 保证金不足
            'FORCE_CLOSE',       # 强制平仓
            'RISK_LIMIT_HIT'     # 风险限制触发
        ]

        if signal_type in critical_signals:
            return MessagePriority.CRITICAL

        # 检查紧急动作
        trade_action = signal_data.get('action', '').upper()
        if trade_action in ['EMERGENCY', 'FORCE_CLOSE', 'EMERGENCY_CLOSE']:
            return MessagePriority.CRITICAL

        # 检查紧急关键词
        comment = signal_data.get('comment', '').upper()
        critical_keywords = ['EMERGENCY', 'MARGIN', 'FORCE', 'RISK_LIMIT']
        if any(keyword in comment for keyword in critical_keywords):
            return MessagePriority.CRITICAL

        # === HIGH PRIORITY (1) - 重要交易操作 ===
        high_priority_signals = [
            'POSITION_CLOSE',    # 平仓信号
            'POSITION_MODIFY',   # 修改持仓（止损/止盈）
            'ORDER_CANCEL',      # 取消订单
        ]

        if signal_type in high_priority_signals:
            return MessagePriority.HIGH

        # 高优先级动作
        if trade_action in ['CLOSE', 'MODIFY', 'SLTP', 'REMOVE', 'CANCEL']:
            return MessagePriority.HIGH

        # 检查订单类型
        order_type = signal_data.get('order_type') or signal_data.get('type')
        if isinstance(order_type, int):
            # MT5 订单类型: 4=BUY_STOP, 5=SELL_STOP (止损单)
            if order_type in [4, 5]:
                return MessagePriority.HIGH

        # 检查止损/止盈
        sl = signal_data.get('sl', 0) or signal_data.get('stop_loss', 0)
        tp = signal_data.get('tp', 0) or signal_data.get('take_profit', 0)

        if sl > 0 or tp > 0:
            return MessagePriority.HIGH

        # 高优先级关键词
        high_keywords = ['STOP', 'LOSS', 'CLOSE']
        if any(keyword in comment for keyword in high_keywords):
            return MessagePriority.HIGH

        # === NORMAL PRIORITY (2) - 常规交易 ===
        if signal_type in ['POSITION_OPEN', 'ORDER_PLACE'] or trade_action in ['BUY', 'SELL', 'OPEN']:
            return MessagePriority.NORMAL

        # === LOW PRIORITY (3) - 查询和数据更新 ===
        if signal_type in ['HEARTBEAT', 'STATUS_UPDATE', 'MARKET_DATA']:
            return MessagePriority.LOW

        return MessagePriority.NORMAL
    
    @staticmethod
    def get_message_priority(method: str, signal_data: Dict[str, Any]) -> MessagePriority:
        """
        统一消息路由优先级分析 - 包含路由特定的逻辑
        """
        try:
            # 首先使用基础的命令优先级分析
            base_priority = PriorityAnalyzer.get_command_priority(method, signal_data)
            
            # 路由特定的优先级调整
            # 紧急标志 - 提升到最高优先级
            if signal_data.get('urgent', False):
                return MessagePriority.CRITICAL
                
            # 广播消息 - 降低优先级，避免阻塞点对点消息
            if signal_data.get('broadcast', False):
                return MessagePriority.LOW
                
            # 系统消息调整
            message_type = signal_data.get('message_type', '').upper()
            if message_type in ['SYSTEM_SHUTDOWN', 'EMERGENCY_STOP']:
                return MessagePriority.CRITICAL
            elif message_type in ['HEARTBEAT', 'STATUS_UPDATE']:
                return MessagePriority.LOW
                
            # 返回基础优先级
            return base_priority
            
        except Exception as e:
            # 失败时返回默认优先级
            return MessagePriority.NORMAL


# ==================== 优先级工具函数 ====================

def get_priority_from_string(priority_str: str) -> MessagePriority:
    """从字符串获取优先级"""
    priority_map = {
        'critical': MessagePriority.CRITICAL,
        'high': MessagePriority.HIGH,
        'normal': MessagePriority.NORMAL,
        'medium': MessagePriority.NORMAL,  # 兼容旧的medium
        'low': MessagePriority.LOW
    }
    return priority_map.get(priority_str.lower(), MessagePriority.NORMAL)

def get_priority_from_int(priority_int: int) -> MessagePriority:
    """从整数获取优先级"""
    priority_map = {
        0: MessagePriority.CRITICAL,
        1: MessagePriority.HIGH,
        2: MessagePriority.NORMAL,
        3: MessagePriority.LOW
    }
    return priority_map.get(priority_int, MessagePriority.NORMAL)


# ==================== 统一便捷函数 ====================

async def enqueue_command(method: str, params: Dict[str, Any],
                         message_id: str = None, callback: Callable = None) -> bool:
    """
    根据命令自动确定优先级并入队 - 统一接口
    """
    priority = PriorityAnalyzer.get_command_priority(method, params)

    command_data = {
        'method': method,
        'params': params,
        'priority': priority.value,  # 0, 1, 2, 3
        'priority_name': priority.name,  # CRITICAL, HIGH, NORMAL, LOW
        'timestamp': time.time()
    }

    return await get_priority_queue().enqueue(priority, command_data, message_id, callback)

# 向后兼容的别名
enqueue_rpc_command = enqueue_command
