#!/usr/bin/env python3
"""
优先级消息队列
实现多优先级消息处理，确保关键信号优先处理
"""
import asyncio
import time
from typing import Dict, Any, Optional, Callable, List
from dataclasses import dataclass
from enum import Enum
import heapq

from ..utils.logger import get_logger
from ..utils.metrics import get_metrics_collector

logger = get_logger(__name__)
metrics = get_metrics_collector()


class MessagePriority(Enum):
    """统一消息优先级 - 五个等级 - 使用HTML仪表盘标准名称"""
    SYSTEM_CRITICAL = 0    # 系统级风险 - 保证金不足、紧急平仓、风控强制全平
    RISK_COMMAND = 1       # 订单级风控 - 止损/止盈执行、订单修改、部分平仓
    SIGNAL_EXECUTION = 2   # 策略信号执行 - 开仓信号、策略主动平仓、反向跟单
    REALTIME_QUERY = 3     # 实时数据查询 - 最新报价、账户信息、持仓查询
    BACKGROUND_TASK = 4    # 后台任务 - 历史数据、日志记录、UI更新、统计分析


@dataclass
class PriorityMessage:
    """优先级消息"""
    priority: MessagePriority
    timestamp: float
    message_id: str
    data: Any
    callback: Optional[Callable] = None
    retry_count: int = 0
    max_retries: int = 3
    
    def __lt__(self, other):
        """比较函数，用于堆排序"""
        if self.priority.value != other.priority.value:
            return self.priority.value < other.priority.value
        return self.timestamp < other.timestamp


class PriorityMessageQueue:
    """优先级消息队列"""
    
    def __init__(self, max_sizes: Dict[MessagePriority, int] = None):
        """
        初始化优先级队列
        """
        # 从配置文件加载队列大小，消除硬编码
        if max_sizes is None:
            max_sizes = self._load_queue_sizes_from_config()
        
        self.max_sizes = max_sizes
        
        # 使用堆来实现优先级队列
        self._heap: List[PriorityMessage] = []
        self._heap_lock = asyncio.Lock()
        
        # 分离的队列用于快速统计
        self._queues: Dict[MessagePriority, asyncio.Queue] = {}
        for priority in MessagePriority:
            self._queues[priority] = asyncio.Queue(maxsize=self.max_sizes[priority])
        
        # 统计信息
        self._stats = {
            'total_enqueued': 0,
            'total_dequeued': 0,
            'total_dropped': 0,
            'priority_stats': {p: {'enqueued': 0, 'dequeued': 0, 'dropped': 0} for p in MessagePriority}
        }
        
        # 处理器
        self._processors: Dict[MessagePriority, Callable] = {}
        self._running = False
        self._worker_tasks: List[asyncio.Task] = []
        
        logger.info("优先级消息队列初始化完成")
    
    def _load_queue_sizes_from_config(self) -> Dict[MessagePriority, int]:
        """从配置文件加载队列大小，消除硬编码"""
        try:
            from ..core.config_manager import get_config_manager
            config_manager = get_config_manager()
            
            # 从配置中获取队列大小
            queue_config = config_manager.get('messaging.priority_queue.max_sizes', {})
            
            return {
                MessagePriority.SYSTEM_CRITICAL: queue_config.get('system_critical', 100),
                MessagePriority.RISK_COMMAND: queue_config.get('risk_command', 500),
                MessagePriority.SIGNAL_EXECUTION: queue_config.get('signal_execution', 1000),
                MessagePriority.REALTIME_QUERY: queue_config.get('realtime_query', 800),
                MessagePriority.BACKGROUND_TASK: queue_config.get('background_task', 2000)
            }
        except Exception as e:
            logger.warning(f"加载队列配置失败，使用紧急默认值: {e}")
            # 紧急默认值，只在配置加载失败时使用
            return {
                MessagePriority.SYSTEM_CRITICAL: 50,   # 小一点，确保安全
                MessagePriority.RISK_COMMAND: 200,
                MessagePriority.SIGNAL_EXECUTION: 300,
                MessagePriority.REALTIME_QUERY: 200,
                MessagePriority.BACKGROUND_TASK: 500
            }
    
    async def enqueue(self, priority: MessagePriority, data: Any, 
                     message_id: str = None, callback: Callable = None) -> bool:
        """
        入队消息
        """
        try:
            if message_id is None:
                message_id = f"{priority.name}_{int(time.time() * 1000000)}"
            
            message = PriorityMessage(
                priority=priority,
                timestamp=time.time(),
                message_id=message_id,
                data=data,
                callback=callback
            )
            
            if self._queues[priority].full():
                self._stats['total_dropped'] += 1
                self._stats['priority_stats'][priority]['dropped'] += 1
                
                logger.warning(f"{priority.name}队列已满，丢弃消息: {message_id}")
                
                metrics.increment("priority_queue_dropped_total", 1,
                                 {'priority': priority.name})
                return False
            
            async with self._heap_lock:
                heapq.heappush(self._heap, message)
            
            await self._queues[priority].put(message)
            
            self._stats['total_enqueued'] += 1
            self._stats['priority_stats'][priority]['enqueued'] += 1
            
            metrics.increment("priority_queue_enqueued_total", 1,
                             {'priority': priority.name})
            metrics.record("priority_queue_size", len(self._heap))
            
            logger.debug(f"消息入队: {priority.name} - {message_id}")
            return True
            
        except Exception as e:
            logger.error(f"消息入队失败: {e}")
            return False
    
    async def dequeue(self, timeout: float = None) -> Optional[PriorityMessage]:
        """
        出队消息（按优先级）
        """
        try:
            async with self._heap_lock:
                if not self._heap:
                    return None
                
                message = heapq.heappop(self._heap)
            
            try:
                self._queues[message.priority].get_nowait()
            except asyncio.QueueEmpty:
                pass
            
            self._stats['total_dequeued'] += 1
            self._stats['priority_stats'][message.priority]['dequeued'] += 1
            
            metrics.increment("priority_queue_dequeued_total", 1,
                             {'priority': message.priority.name})
            metrics.record("priority_queue_size", len(self._heap))
            
            logger.debug(f"消息出队: {message.priority.name} - {message.message_id}")
            return message
            
        except Exception as e:
            logger.error(f"消息出队失败: {e}")
            return None
    
    async def dequeue_by_priority(self, priority: MessagePriority, 
                                 timeout: float = None) -> Optional[PriorityMessage]:
        """
        按指定优先级出队消息
        """
        try:
            if timeout is None:
                message = await self._queues[priority].get()
            else:
                message = await asyncio.wait_for(
                    self._queues[priority].get(), 
                    timeout=timeout
                )
            
            async with self._heap_lock:
                self._heap = [m for m in self._heap if m.message_id != message.message_id]
                heapq.heapify(self._heap)
            
            self._stats['total_dequeued'] += 1
            self._stats['priority_stats'][priority]['dequeued'] += 1
            
            metrics.increment("priority_queue_dequeued_total", 1,
                             {'priority': priority.name})
            metrics.record("priority_queue_size", len(self._heap))
            
            return message
            
        except asyncio.TimeoutError:
            return None
        except Exception as e:
            logger.error(f"按优先级出队失败: {e}")
            return None
    
    def register_processor(self, priority: MessagePriority, processor: Callable):
        """注册优先级处理器"""
        self._processors[priority] = processor
        logger.info(f"注册{priority.name}优先级处理器")
    
    async def start_workers(self, worker_count: int = 4):
        """启动工作线程"""
        if self._running:
            logger.warning("工作线程已在运行")
            return
        
        self._running = True
        
        for i in range(worker_count):
            task = asyncio.create_task(self._priority_worker(f"priority_worker_{i}"))
            self._worker_tasks.append(task)
        
        critical_task = asyncio.create_task(self._critical_worker())
        self._worker_tasks.append(critical_task)
        
        logger.info(f"启动了 {len(self._worker_tasks)} 个工作线程")
    
    async def stop_workers(self):
        """停止工作线程"""
        if not self._running:
            return
        
        self._running = False
        
        for task in self._worker_tasks:
            task.cancel()
        
        await asyncio.gather(*self._worker_tasks, return_exceptions=True)
        
        self._worker_tasks.clear()
        logger.info("所有工作线程已停止")
    
    async def _priority_worker(self, worker_name: str):
        """优先级工作线程"""
        logger.info(f"启动优先级工作线程: {worker_name}")
        
        while self._running:
            try:
                # 出队消息
                message = await self.dequeue(timeout=1.0)
                
                if message is None:
                    continue
                
                # 处理消息
                await self._process_message(message, worker_name)
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"优先级工作线程异常 {worker_name}: {e}")
                await asyncio.sleep(0.1)
        
        logger.info(f"优先级工作线程停止: {worker_name}")
    
    async def _critical_worker(self):
        """系统关键优先级专用工作线程"""
        logger.info("启动系统关键优先级工作线程")
        
        while self._running:
            try:
                message = await self.dequeue_by_priority(
                    MessagePriority.SYSTEM_CRITICAL, timeout=0.5
                )
                
                if message is None:
                    continue
                
                await self._process_message(message, "critical_worker")
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"系统关键优先级工作线程异常: {e}")
                await asyncio.sleep(0.1)
        
        logger.info("系统关键优先级工作线程停止")
    
    async def _process_message(self, message: PriorityMessage, worker_name: str):
        """处理消息"""
        start_time = time.time()
        
        try:
            processor = self._processors.get(message.priority)
            
            if processor:
                await processor(message.data)
            elif message.callback:
                await message.callback(message.data)
            else:
                logger.warning(f"没有找到{message.priority.name}优先级的处理器")
            
            processing_time = (time.time() - start_time) * 1000
            metrics.record("priority_queue_processing_time_ms", processing_time,
                          {'priority': message.priority.name, 'worker': worker_name})
            
            logger.debug(f"消息处理完成: {message.message_id} ({processing_time:.2f}ms)")
            
        except Exception as e:
            logger.error(f"消息处理失败 {message.message_id}: {e}")
            
            if message.retry_count < message.max_retries:
                message.retry_count += 1
                await self.enqueue(message.priority, message.data, 
                                 f"{message.message_id}_retry_{message.retry_count}",
                                 message.callback)
                logger.info(f"消息重试 {message.message_id} (第{message.retry_count}次)")
            else:
                logger.error(f"消息重试次数超限，丢弃: {message.message_id}")
                metrics.increment("priority_queue_failed_total", 1,
                                 {'priority': message.priority.name})
    
    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        return {
            **self._stats,
            'queue_sizes': {p.name: self._queues[p].qsize() for p in MessagePriority},
            'heap_size': len(self._heap),
            'workers_running': len(self._worker_tasks),
            'is_running': self._running
        }
    
    def is_empty(self) -> bool:
        """检查队列是否为空"""
        return len(self._heap) == 0
    
    def size(self) -> int:
        """获取队列总大小"""
        return len(self._heap)
    
    def size_by_priority(self, priority: MessagePriority) -> int:
        """获取指定优先级队列大小"""
        return self._queues[priority].qsize()


# 全局优先级队列实例
_priority_queue = None

def get_priority_queue() -> PriorityMessageQueue:
    """获取全局优先级队列实例"""
    global _priority_queue
    if _priority_queue is None:
        _priority_queue = PriorityMessageQueue()
    return _priority_queue


# 便捷函数 - 使用HTML标准名称
async def enqueue_system_critical(data: Any, message_id: str = None, callback: Callable = None) -> bool:
    """入队系统级风险优先级消息"""
    return await get_priority_queue().enqueue(MessagePriority.SYSTEM_CRITICAL, data, message_id, callback)


async def enqueue_risk_command(data: Any, message_id: str = None, callback: Callable = None) -> bool:
    """入队订单级风控优先级消息"""
    return await get_priority_queue().enqueue(MessagePriority.RISK_COMMAND, data, message_id, callback)


async def enqueue_signal_execution(data: Any, message_id: str = None, callback: Callable = None) -> bool:
    """入队策略信号执行优先级消息"""
    return await get_priority_queue().enqueue(MessagePriority.SIGNAL_EXECUTION, data, message_id, callback)


async def enqueue_realtime_query(data: Any, message_id: str = None, callback: Callable = None) -> bool:
    """入队实时数据查询优先级消息"""
    return await get_priority_queue().enqueue(MessagePriority.REALTIME_QUERY, data, message_id, callback)


async def enqueue_background_task(data: Any, message_id: str = None, callback: Callable = None) -> bool:
    """入队后台任务优先级消息"""
    return await get_priority_queue().enqueue(MessagePriority.BACKGROUND_TASK, data, message_id, callback)


# ==================== 统一优先级分析器 ====================

class PriorityAnalyzer:
    """统一优先级分析器 - 五个等级 (0, 1, 2, 3, 4) - 基于'风险 > 机会 > 数据 > 后勤'原则"""

    @staticmethod
    def get_command_priority(method: str, params: Dict[str, Any]) -> MessagePriority:
        """
        确定命令优先级 - 统一五个等级
        
        优先级分层（基于"风险 > 机会 > 数据 > 后勤"原则）:
        0. SYSTEM_CRITICAL - 系统级风险：保证金不足、紧急平仓、风控强制全平
        1. RISK_COMMAND - 订单级风控：止损/止盈执行、订单修改、部分平仓
        2. SIGNAL_EXECUTION - 策略信号执行：开仓信号、策略主动平仓
        3. REALTIME_QUERY - 实时数据查询：最新报价、账户信息、持仓查询
        4. BACKGROUND_TASK - 后台任务：历史数据、日志记录、UI更新
        """

        # === SYSTEM_CRITICAL PRIORITY (0) - 系统级风险 ===

        # 1. 紧急平仓和风险控制
        if method == 'send_order':
            analyzed_priority = PriorityAnalyzer._analyze_trade_signal_priority(params)
            if analyzed_priority == MessagePriority.SYSTEM_CRITICAL:
                return MessagePriority.SYSTEM_CRITICAL
            elif analyzed_priority == MessagePriority.RISK_COMMAND:
                return MessagePriority.RISK_COMMAND

            # 检查系统级风险情况
            trade_action = params.get('action', '').upper()
            signal_type = params.get('signal_type', '').upper()

            # 系统级风险信号
            if (trade_action in ['EMERGENCY_CLOSE', 'FORCE_CLOSE'] or
                signal_type in ['EMERGENCY_CLOSE', 'MARGIN_CALL', 'STOP_LOSS_HIT']):
                return MessagePriority.SYSTEM_CRITICAL

        # 2. 系统紧急操作
        if method == 'emergency_stop' or params.get('emergency', False):
            return MessagePriority.SYSTEM_CRITICAL

        # === RISK_COMMAND PRIORITY (1) - 订单级风控 ===

        # 1. 平仓和修改操作
        if method == 'send_order':
            trade_action = params.get('action', '').upper()
            signal_type = params.get('signal_type', '').upper()
            order_type = params.get('type', 0)

            # 平仓信号
            if trade_action in ['CLOSE'] or signal_type == 'POSITION_CLOSE':
                return MessagePriority.RISK_COMMAND

            # 止损/止盈修改
            if (trade_action in ['MODIFY', 'MODIFY_SL', 'MODIFY_TP'] or
                signal_type == 'POSITION_MODIFY' or
                params.get('sl', 0) != 0 or params.get('tp', 0) != 0):
                return MessagePriority.RISK_COMMAND

            # 止损单类型 (BUY_STOP=4, SELL_STOP=5)
            if order_type in [4, 5]:
                return MessagePriority.RISK_COMMAND

            # 包含止损价格的订单
            if params.get('sl', 0) > 0:
                return MessagePriority.RISK_COMMAND

        # 2. 订单取消操作
        if method == 'cancel_order' or params.get('action') == 'CANCEL':
            return MessagePriority.RISK_COMMAND

        # 3. 关键系统操作
        if method == 'health_check' and params.get('critical', False):
            return MessagePriority.RISK_COMMAND

        # === SIGNAL_EXECUTION PRIORITY (2) - 策略信号执行 ===

        # 1. 新开仓信号
        if method == 'send_order':
            signal_type = params.get('signal_type', '').upper()
            trade_action = params.get('action', '').upper()

            # 开仓信号
            if (signal_type == 'POSITION_OPEN' or
                trade_action in ['BUY', 'SELL', 'OPEN']):
                return MessagePriority.SIGNAL_EXECUTION

            # 限价单和止损限价单
            order_type = params.get('type', 0)
            if order_type in [2, 3, 6, 7]:  # LIMIT和STOP_LIMIT类型
                return MessagePriority.SIGNAL_EXECUTION

        # 2. 策略主动平仓（非止损触发）
        if method == 'send_order' and params.get('reason') == 'strategy':
            return MessagePriority.SIGNAL_EXECUTION

        # 3. 反向跟单开仓
        if method == 'send_order' and params.get('copy_mode') == 'reverse':
            return MessagePriority.SIGNAL_EXECUTION

        # === REALTIME_QUERY PRIORITY (3) - 实时数据查询 ===

        # 1. 获取最新报价
        if method in ['get_tick_data', 'get_symbol_tick', 'get_last_tick']:
            return MessagePriority.REALTIME_QUERY

        # 2. 查询账户实时信息
        if method in ['get_account_info', 'get_margin_info', 'get_balance']:
            return MessagePriority.REALTIME_QUERY

        # 3. 查询当前持仓
        if method in ['get_positions', 'get_open_positions', 'get_position_info']:
            return MessagePriority.REALTIME_QUERY

        # 4. 查询当前挂单
        if method in ['get_orders', 'get_pending_orders']:
            return MessagePriority.REALTIME_QUERY

        # 5. 查询服务器连接状态
        if method in ['health_check', 'check_connection', 'get_terminal_info']:
            return MessagePriority.REALTIME_QUERY

        # === BACKGROUND_TASK PRIORITY (4) - 后台与非实时任务 ===

        # 1. 历史数据查询
        if method in ['get_rates', 'get_history', 'get_historical_data', 'get_bars']:
            return MessagePriority.BACKGROUND_TASK

        # 2. 日志和报告
        if method in ['write_log', 'save_trade', 'generate_report']:
            return MessagePriority.BACKGROUND_TASK

        # 3. UI更新
        if method.startswith('ui_') or method.endswith('_update'):
            return MessagePriority.BACKGROUND_TASK

        # 4. 系统心跳
        if method in ['heartbeat', 'send_heartbeat', 'ping']:
            return MessagePriority.BACKGROUND_TASK

        # 5. 市场信息查询（非实时）
        if method in ['get_symbol_info', 'get_market_info', 'get_trading_hours']:
            return MessagePriority.BACKGROUND_TASK

        # 6. 一般性查询（非关键）
        if method.startswith('get_') and not any(key in method for key in ['position', 'order', 'account', 'tick']):
            return MessagePriority.BACKGROUND_TASK

        # Default to realtime query for safety
        return MessagePriority.REALTIME_QUERY

    @staticmethod
    def _analyze_trade_signal_priority(params: Dict[str, Any]) -> MessagePriority:
        """
        深度分析交易信号优先级 - 统一五个等级
        """

        signal_data = params.get('signal_data') or params.get('data') or params
        signal_type = signal_data.get('signal_type') or signal_data.get('type', '')
        if isinstance(signal_type, str):
            signal_type = signal_type.upper()

        # === SYSTEM_CRITICAL PRIORITY (0) - 系统级风险 ===
        system_critical_signals = [
            'EMERGENCY_CLOSE',   # 紧急平仓
            'STOP_LOSS_HIT',     # 止损触发
            'MARGIN_CALL',       # 保证金不足
            'FORCE_CLOSE',       # 强制平仓
            'RISK_LIMIT_HIT'     # 风险限制触发
        ]

        if signal_type in system_critical_signals:
            return MessagePriority.SYSTEM_CRITICAL

        # 检查系统级风险动作
        trade_action = signal_data.get('action', '').upper()
        if trade_action in ['EMERGENCY', 'FORCE_CLOSE', 'EMERGENCY_CLOSE']:
            return MessagePriority.SYSTEM_CRITICAL

        # 检查系统级风险关键词
        comment = signal_data.get('comment', '').upper()
        critical_keywords = ['EMERGENCY', 'MARGIN', 'FORCE', 'RISK_LIMIT']
        if any(keyword in comment for keyword in critical_keywords):
            return MessagePriority.SYSTEM_CRITICAL

        # === RISK_COMMAND PRIORITY (1) - 订单级风控 ===
        risk_command_signals = [
            'POSITION_CLOSE',    # 平仓信号
            'POSITION_MODIFY',   # 修改持仓（止损/止盈）
            'ORDER_CANCEL',      # 取消订单
        ]

        if signal_type in risk_command_signals:
            return MessagePriority.RISK_COMMAND

        # 订单级风控动作
        if trade_action in ['CLOSE', 'MODIFY', 'SLTP', 'REMOVE', 'CANCEL']:
            return MessagePriority.RISK_COMMAND

        # 检查订单类型
        order_type = signal_data.get('order_type') or signal_data.get('type')
        if isinstance(order_type, int):
            # MT5 订单类型: 4=BUY_STOP, 5=SELL_STOP (止损单)
            if order_type in [4, 5]:
                return MessagePriority.RISK_COMMAND

        # 检查止损/止盈
        sl = signal_data.get('sl', 0) or signal_data.get('stop_loss', 0)
        tp = signal_data.get('tp', 0) or signal_data.get('take_profit', 0)

        if sl > 0 or tp > 0:
            return MessagePriority.RISK_COMMAND

        # 订单级风控关键词
        risk_keywords = ['STOP', 'LOSS', 'CLOSE']
        if any(keyword in comment for keyword in risk_keywords):
            return MessagePriority.RISK_COMMAND

        # === SIGNAL_EXECUTION PRIORITY (2) - 策略信号执行 ===
        if signal_type in ['POSITION_OPEN', 'ORDER_PLACE'] or trade_action in ['BUY', 'SELL', 'OPEN']:
            return MessagePriority.SIGNAL_EXECUTION

        # === REALTIME_QUERY PRIORITY (3) - 实时数据查询 ===
        realtime_query_signals = [
            'ACCOUNT_QUERY',      # 账户信息查询
            'POSITION_QUERY',     # 持仓查询
            'ORDER_QUERY',        # 订单查询
            'PRICE_QUERY',        # 价格查询
            'TICK_DATA',          # Tick数据
            'CONNECTION_CHECK'    # 连接检查
        ]
        
        if signal_type in realtime_query_signals:
            return MessagePriority.REALTIME_QUERY
            
        # 检查查询类型的动作
        if trade_action in ['QUERY', 'CHECK', 'GET_INFO']:
            return MessagePriority.REALTIME_QUERY

        # === BACKGROUND_TASK PRIORITY (4) - 后台任务 ===
        background_task_signals = [
            'HEARTBEAT',          # 心跳
            'STATUS_UPDATE',      # 状态更新
            'MARKET_DATA',        # 市场数据（非实时）
            'LOG_ENTRY',          # 日志记录
            'REPORT_GENERATION',  # 报告生成
            'UI_UPDATE',          # UI更新
            'HISTORICAL_DATA'     # 历史数据
        ]
        
        if signal_type in background_task_signals:
            return MessagePriority.BACKGROUND_TASK
            
        # 检查后台任务关键词
        background_keywords = ['LOG', 'REPORT', 'HISTORY', 'UI', 'BACKUP']
        if any(keyword in comment for keyword in background_keywords):
            return MessagePriority.BACKGROUND_TASK

        # 默认为实时查询优先级
        return MessagePriority.REALTIME_QUERY
    
    @staticmethod
    def get_message_priority(method: str, signal_data: Dict[str, Any]) -> MessagePriority:
        """
        统一消息路由优先级分析 - 包含路由特定的逻辑
        """
        try:
            # 首先使用基础的命令优先级分析
            base_priority = PriorityAnalyzer.get_command_priority(method, signal_data)
            
            # 路由特定的优先级调整
            # 紧急标志 - 提升到最高优先级
            if signal_data.get('urgent', False):
                return MessagePriority.SYSTEM_CRITICAL
                
            # 广播消息 - 降低优先级，避免阻塞点对点消息
            if signal_data.get('broadcast', False):
                return MessagePriority.BACKGROUND_TASK
                
            # 系统消息调整
            message_type = signal_data.get('message_type', '').upper()
            if message_type in ['SYSTEM_SHUTDOWN', 'EMERGENCY_STOP']:
                return MessagePriority.SYSTEM_CRITICAL
            elif message_type in ['HEARTBEAT', 'STATUS_UPDATE']:
                return MessagePriority.BACKGROUND_TASK
                
            # 返回基础优先级
            return base_priority
            
        except Exception as e:
            # 失败时返回默认优先级
            return MessagePriority.REALTIME_QUERY


# ==================== 优先级工具函数 ====================

def get_priority_from_string(priority_str: str) -> MessagePriority:
    """从字符串获取优先级 - 使用HTML标准名称"""
    priority_map = {
        'system_critical': MessagePriority.SYSTEM_CRITICAL,
        'risk_command': MessagePriority.RISK_COMMAND,
        'signal_execution': MessagePriority.SIGNAL_EXECUTION,
        'realtime_query': MessagePriority.REALTIME_QUERY,
        'background_task': MessagePriority.BACKGROUND_TASK
    }
    result = priority_map.get(priority_str.lower())
    if result is None:
        logger.warning(f"未知的优先级字符串: {priority_str}, 请使用HTML标准名称")
        return MessagePriority.REALTIME_QUERY
    return result

def get_priority_from_int(priority_int: int) -> MessagePriority:
    """从整数获取优先级"""
    priority_map = {
        0: MessagePriority.SYSTEM_CRITICAL,   # 系统级风险
        1: MessagePriority.RISK_COMMAND,      # 订单级风控
        2: MessagePriority.SIGNAL_EXECUTION,  # 策略信号执行
        3: MessagePriority.REALTIME_QUERY,    # 实时数据查询
        4: MessagePriority.BACKGROUND_TASK    # 后台任务
    }
    result = priority_map.get(priority_int)
    if result is None:
        logger.warning(f"未知的优先级整数: {priority_int}, 仅支持 0-4")
        return MessagePriority.REALTIME_QUERY
    return result


# ==================== 统一便捷函数 ====================

async def enqueue_command(method: str, params: Dict[str, Any],
                         message_id: str = None, callback: Callable = None) -> bool:
    """
    根据命令自动确定优先级并入队 - 统一接口
    """
    priority = PriorityAnalyzer.get_command_priority(method, params)

    command_data = {
        'method': method,
        'params': params,
        'priority': priority.value,  # 0, 1, 2, 3, 4
        'priority_name': priority.name,  # SYSTEM_CRITICAL, RISK_COMMAND, SIGNAL_EXECUTION, REALTIME_QUERY, BACKGROUND_TASK
        'timestamp': time.time()
    }

    return await get_priority_queue().enqueue(priority, command_data, message_id, callback)

# 向后兼容的别名
enqueue_rpc_command = enqueue_command
