"""
进程守护和自动恢复系统
监控MT5账户进程，提供自动重启和故障转移功能
"""
import asyncio
import time
import psutil
import signal
import os
import random
from typing import Dict, List, Optional, Any, Callable, Set
from dataclasses import dataclass, field
from enum import Enum
import json
from pathlib import Path

from ..utils.thread_safe_stats import get_stats, track_calls, track_timing
from ..utils.logger import get_logger
from ..core.config_manager import ConfigManager

logger = get_logger(__name__)


class ProcessStatus(Enum):
    """进程状态"""
    STARTING = "starting"
    RUNNING = "running"
    STOPPING = "stopping"
    STOPPED = "stopped"
    FAILED = "failed"
    CRASHED = "crashed"
    UNKNOWN = "unknown"


@dataclass
class ProcessInfo:
    """进程信息"""
    account_id: str
    process_id: Optional[int] = None
    status: ProcessStatus = ProcessStatus.STOPPED
    start_time: Optional[float] = None
    last_heartbeat: Optional[float] = None
    restart_count: int = 0
    crash_count: int = 0
    config: Dict[str, Any] = field(default_factory=dict)
    error_message: Optional[str] = None
    auto_restart: bool = True
    max_restarts: int = 5
    restart_delay: float = 10.0  # 重启延迟（秒）
    failure_pattern: List[str] = field(default_factory=list)  # 故障模式历史
    consecutive_failures: int = 0  # 连续失败次数
    last_failure_time: Optional[float] = None  # 上次失败时间
    backoff_multiplier: float = 1.0  # 退避倍数


class ProcessGuardian:
    """进程守护者"""
    
    def __init__(self, config_manager: ConfigManager):
        self.config_manager = config_manager
        self.processes: Dict[str, ProcessInfo] = {}
        
        # 监控配置
        self.monitor_interval = 5  # 缩短监控间隔至5秒
        self.heartbeat_timeout = 30  # 缩短心跳超时至30秒
        self.health_check_timeout = 15  # 缩短健康检查超时至15秒
        
        # 智能重启配置
        self.min_restart_delay = 5.0  # 最小重启延迟
        self.max_restart_delay = 300.0  # 最大重启延迟
        self.backoff_factor = 2.0  # 退避因子
        self.failure_window = 300.0  # 故障窗口（秒）
        self.max_consecutive_failures = 3  # 最大连续失败次数
        
        # 统计和监控
        self.stats = get_stats("process_guardian")
        self.running = False
        self._monitor_task: Optional[asyncio.Task] = None
        self._health_check_task: Optional[asyncio.Task] = None
        
        # 锁和队列
        self._process_lock = asyncio.Lock()
        self._restart_queue = asyncio.Queue()
        self._restart_task: Optional[asyncio.Task] = None
        
        # 回调函数
        self.on_process_started: Optional[Callable] = None
        self.on_process_stopped: Optional[Callable] = None
        self.on_process_crashed: Optional[Callable] = None
        self.on_process_restarted: Optional[Callable] = None
        
        logger.info("进程守护者初始化完成")
    
    async def start(self):
        """启动守护者"""
        if self.running:
            logger.warning("进程守护者已在运行")
            return
        
        try:
            self.running = True
            
            # 加载现有进程配置
            await self._load_process_configs()
            
            # 启动监控任务
            self._monitor_task = asyncio.create_task(self._monitor_loop())
            self._health_check_task = asyncio.create_task(self._health_check_loop())
            self._restart_task = asyncio.create_task(self._restart_processor())
            
            # 更新统计
            await self.stats.set_value("start_time", time.time())
            await self.stats.set_value("status", "running")
            
            logger.info("✅ 进程守护者启动成功")
            
        except Exception as e:
            logger.error(f"进程守护者启动失败: {e}")
            await self.stop()
            raise
    
    async def stop(self):
        """停止守护者"""
        if not self.running:
            return
        
        try:
            self.running = False
            
            # 取消任务
            if self._monitor_task and not self._monitor_task.done():
                self._monitor_task.cancel()
            if self._health_check_task and not self._health_check_task.done():
                self._health_check_task.cancel()
            if self._restart_task and not self._restart_task.done():
                self._restart_task.cancel()
            
            # 等待任务完成
            tasks = [self._monitor_task, self._health_check_task, self._restart_task]
            await asyncio.gather(*[t for t in tasks if t], return_exceptions=True)
            
            # 更新统计
            await self.stats.set_value("status", "stopped")
            await self.stats.set_value("stop_time", time.time())
            
            logger.info("进程守护者已停止")
            
        except Exception as e:
            logger.error(f"进程守护者停止异常: {e}")
    
    async def _load_process_configs(self):
        """加载进程配置"""
        try:
            accounts_config = self.config_manager.get_all_accounts()
            
            async with self._process_lock:
                for account_id, config in accounts_config.items():
                    if account_id not in self.processes:
                        process_info = ProcessInfo(
                            account_id=account_id,
                            config=config,
                            auto_restart=config.get('auto_restart', True),
                            max_restarts=config.get('max_restarts', 10),  # 增加最大重启次数
                            restart_delay=config.get('restart_delay', 10.0)
                        )
                        self.processes[account_id] = process_info
                        logger.info(f"加载进程配置: {account_id}")
            
            await self.stats.set_value("managed_processes", len(self.processes))
            
        except Exception as e:
            logger.error(f"加载进程配置失败: {e}")
    
    @track_calls("process_start", "process_guardian")
    async def start_process(self, account_id: str, force: bool = False) -> bool:
        """启动进程"""
        async with self._process_lock:
            if account_id not in self.processes:
                logger.error(f"未找到进程配置: {account_id}")
                return False
            
            process_info = self.processes[account_id]
            
            # 检查是否已在运行
            if not force and process_info.status == ProcessStatus.RUNNING:
                if await self._is_process_alive(process_info.process_id):
                    logger.info(f"进程已在运行: {account_id}")
                    return True
                else:
                    # 进程实际不存在，更新状态
                    process_info.status = ProcessStatus.CRASHED
            
            # 检查重启限制（使用智能策略）
            if not force and not self._should_restart_process(process_info):
                logger.error(f"进程不符合重启条件: {account_id}")
                process_info.status = ProcessStatus.FAILED
                await self.stats.increment("max_restarts_reached")
                return False
            
            try:
                # 标记为启动中
                process_info.status = ProcessStatus.STARTING
                process_info.error_message = None
                
                # 创建进程命令
                cmd = await self._build_process_command(account_id, process_info.config)
                
                # 启动进程
                process = await asyncio.create_subprocess_exec(
                    *cmd,
                    stdout=asyncio.subprocess.PIPE,
                    stderr=asyncio.subprocess.PIPE,
                    cwd=os.getcwd()
                )
                
                # 更新进程信息
                process_info.process_id = process.pid
                process_info.status = ProcessStatus.RUNNING
                process_info.start_time = time.time()
                process_info.last_heartbeat = time.time()
                process_info.restart_count += 1
                
                # 更新统计
                await self.stats.increment("processes_started")
                await self.stats.set_value(f"process_{account_id}_pid", process.pid)
                await self.stats.set_value(f"process_{account_id}_start_time", process_info.start_time)
                
                logger.info(f"✅ 进程启动成功: {account_id} (PID: {process.pid})")
                
                # 触发回调
                if self.on_process_started:
                    await self.on_process_started(account_id, process_info)
                
                return True
                
            except Exception as e:
                logger.error(f"启动进程失败 {account_id}: {e}")
                process_info.status = ProcessStatus.FAILED
                process_info.error_message = str(e)
                await self.stats.increment("process_start_failures")
                return False
    
    async def _build_process_command(self, account_id: str, config: Dict[str, Any]) -> List[str]:
        """构建进程启动命令"""
        try:
            python_executable = config.get('python_executable', 'python')
            script_path = config.get('script_path', 'scripts/run_account_process.py')
            
            cmd = [
                python_executable,
                script_path,
                '--account-id', account_id,
                '--config-file', self.config_manager.config_file
            ]
            
            # 添加额外参数
            if config.get('debug_mode'):
                cmd.extend(['--debug'])
            
            if config.get('log_level'):
                cmd.extend(['--log-level', config['log_level']])
            
            return cmd
            
        except Exception as e:
            logger.error(f"构建进程命令失败: {e}")
            raise
    
    @track_calls("process_stop", "process_guardian")
    async def stop_process(self, account_id: str, force: bool = False, timeout: float = 30.0) -> bool:
        """停止进程"""
        async with self._process_lock:
            if account_id not in self.processes:
                logger.error(f"未找到进程: {account_id}")
                return False
            
            process_info = self.processes[account_id]
            
            if process_info.status not in [ProcessStatus.RUNNING, ProcessStatus.STARTING]:
                logger.info(f"进程未在运行: {account_id}")
                return True
            
            try:
                # 标记为停止中
                process_info.status = ProcessStatus.STOPPING
                
                if process_info.process_id:
                    success = await self._terminate_process(process_info.process_id, force, timeout)
                    
                    if success:
                        process_info.status = ProcessStatus.STOPPED
                        process_info.process_id = None
                        
                        # 更新统计
                        await self.stats.increment("processes_stopped")
                        await self.stats.set_value(f"process_{account_id}_stop_time", time.time())
                        
                        logger.info(f"✅ 进程停止成功: {account_id}")
                        
                        # 触发回调
                        if self.on_process_stopped:
                            await self.on_process_stopped(account_id, process_info)
                        
                        return True
                    else:
                        logger.error(f"停止进程失败: {account_id}")
                        return False
                else:
                    # 没有PID，直接标记为停止
                    process_info.status = ProcessStatus.STOPPED
                    return True
                    
            except Exception as e:
                logger.error(f"停止进程异常 {account_id}: {e}")
                return False
    
    async def _terminate_process(self, pid: int, force: bool = False, timeout: float = 30.0) -> bool:
        """终止进程"""
        try:
            if not await self._is_process_alive(pid):
                return True
            
            process = psutil.Process(pid)
            
            if not force:
                # 优雅关闭
                process.terminate()
                
                # 等待进程退出
                for _ in range(int(timeout)):
                    if not process.is_running():
                        return True
                    await asyncio.sleep(1)
                
                logger.warning(f"进程未在超时时间内退出，强制杀死: {pid}")
            
            # 强制杀死
            if process.is_running():
                process.kill()
                await asyncio.sleep(1)
                return not process.is_running()
            
            return True
            
        except psutil.NoSuchProcess:
            return True
        except Exception as e:
            logger.error(f"终止进程失败 {pid}: {e}")
            return False
    
    async def restart_process(self, account_id: str, force: bool = False) -> bool:
        """重启进程"""
        logger.info(f"重启进程: {account_id}")
        
        # 停止进程
        await self.stop_process(account_id, force=force)
        
        # 等待一段时间
        if account_id in self.processes:
            delay = self.processes[account_id].restart_delay
            await asyncio.sleep(delay)
        
        # 启动进程
        return await self.start_process(account_id, force=True)
    
    async def _monitor_loop(self):
        """监控循环"""
        while self.running:
            try:
                await self._check_all_processes()
                await asyncio.sleep(self.monitor_interval)
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"监控循环异常: {e}")
                await asyncio.sleep(5)
    
    async def _check_all_processes(self):
        """检查所有进程状态"""
        current_time = time.time()
        crashed_processes = []
        
        async with self._process_lock:
            for account_id, process_info in self.processes.items():
                if process_info.status == ProcessStatus.RUNNING:
                    # 检查进程是否存活
                    if process_info.process_id:
                        if not await self._is_process_alive(process_info.process_id):
                            logger.warning(f"检测到进程崩溃: {account_id}")
                            process_info.status = ProcessStatus.CRASHED
                            process_info.crash_count += 1
                            crashed_processes.append(account_id)
                    
                    # 检查心跳超时
                    elif (process_info.last_heartbeat and 
                          current_time - process_info.last_heartbeat > self.heartbeat_timeout):
                        logger.warning(f"进程心跳超时: {account_id}")
                        process_info.status = ProcessStatus.CRASHED
                        crashed_processes.append(account_id)
        
        # 处理崩溃的进程
        for account_id in crashed_processes:
            await self._handle_crashed_process(account_id)
            
        # 检查进程健康状态
        await self._check_process_health()
        
        # 更新统计
        await self.stats.set_value("total_processes", len(self.processes))
        await self.stats.set_value("running_processes", 
                                 len([p for p in self.processes.values() 
                                     if p.status == ProcessStatus.RUNNING]))
        await self.stats.set_value("crashed_processes", len(crashed_processes))
    
    async def _handle_crashed_process(self, account_id: str):
        """处理崩溃的进程"""
        try:
            process_info = self.processes[account_id]
            
            # 更新统计
            await self.stats.increment("processes_crashed")
            
            # 触发回调
            if self.on_process_crashed:
                await self.on_process_crashed(account_id, process_info)
            
            # 记录故障模式
            await self._record_failure_pattern(account_id, "crash")
            
            # 检查是否需要自动重启（使用智能策略）
            if process_info.auto_restart and self._should_restart_process(process_info):
                # 计算智能重启延迟
                restart_delay = self._calculate_restart_delay(process_info)
                
                logger.info(f"将进程加入重启队列: {account_id} (延迟: {restart_delay:.1f}s)")
                await self._restart_queue.put((account_id, restart_delay))
                await self.stats.increment("processes_queued_for_restart")
            else:
                logger.warning(f"进程不会自动重启: {account_id} "
                             f"(auto_restart={process_info.auto_restart}, "
                             f"restart_count={process_info.restart_count})")
                
        except Exception as e:
            logger.error(f"处理崩溃进程异常: {e}")
    
    async def _restart_processor(self):
        """重启处理器"""
        while self.running:
            try:
                # 等待重启请求
                restart_item = await asyncio.wait_for(
                    self._restart_queue.get(), 
                    timeout=1.0
                )
                
                # 处理重启项目（可能是元组或字符串）
                if isinstance(restart_item, tuple):
                    account_id, delay = restart_item
                    await asyncio.sleep(delay)
                else:
                    account_id = restart_item
                
                # 执行重启
                if await self.restart_process(account_id):
                    await self.stats.increment("processes_restarted_successfully")
                    
                    # 重置故障计数
                    if account_id in self.processes:
                        self.processes[account_id].consecutive_failures = 0
                    
                    # 触发回调
                    if self.on_process_restarted:
                        process_info = self.processes.get(account_id)
                        if process_info:
                            await self.on_process_restarted(account_id, process_info)
                else:
                    await self.stats.increment("process_restart_failures")
                    # 记录重启失败
                    await self._record_failure_pattern(account_id, "restart_failed")
                
            except asyncio.TimeoutError:
                continue  # 队列为空，继续等待
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"重启处理器异常: {e}")
                await asyncio.sleep(1)
    
    async def _health_check_loop(self):
        """健康检查循环"""
        while self.running:
            try:
                await self._perform_health_checks()
                await asyncio.sleep(self.health_check_timeout)
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"健康检查循环异常: {e}")
                await asyncio.sleep(5)
    
    async def _perform_health_checks(self):
        """执行健康检查"""
        # 这里可以添加更详细的健康检查逻辑
        # 例如：检查进程的CPU/内存使用率、网络连接状态等
        
        async with self._process_lock:
            for account_id, process_info in self.processes.items():
                if process_info.status == ProcessStatus.RUNNING and process_info.process_id:
                    try:
                        process = psutil.Process(process_info.process_id)
                        
                        # 检查进程资源使用情况
                        cpu_percent = process.cpu_percent()
                        memory_info = process.memory_info()
                        
                        # 更新统计
                        await self.stats.set_value(f"process_{account_id}_cpu_percent", cpu_percent)
                        await self.stats.set_value(f"process_{account_id}_memory_mb", 
                                                 memory_info.rss / 1024 / 1024)
                        
                        # 检查异常情况（例如CPU使用率过高）
                        if cpu_percent > 80:  # 降低CPU阈值到80%
                            logger.warning(f"进程CPU使用率过高: {account_id} ({cpu_percent}%)")
                            await self.stats.increment("high_cpu_warnings")
                            await self._record_failure_pattern(account_id, "high_cpu")
                        
                        # 检查内存使用情况
                        memory_mb = memory_info.rss / 1024 / 1024
                        if memory_mb > 512:  # 降低内存阈值到512MB
                            logger.warning(f"进程内存使用过高: {account_id} ({memory_mb:.1f}MB)")
                            await self.stats.increment("high_memory_warnings")
                            await self._record_failure_pattern(account_id, "high_memory")
                        
                    except psutil.NoSuchProcess:
                        # 进程不存在
                        logger.warning(f"健康检查发现进程不存在: {account_id}")
                        process_info.status = ProcessStatus.CRASHED
    
    async def _is_process_alive(self, pid: Optional[int]) -> bool:
        """检查进程是否存活"""
        if not pid:
            return False
        
        try:
            process = psutil.Process(pid)
            return process.is_running()
        except (psutil.NoSuchProcess, psutil.AccessDenied):
            return False
    
    async def get_process_status(self, account_id: str) -> Optional[ProcessInfo]:
        """获取进程状态"""
        async with self._process_lock:
            return self.processes.get(account_id)
    
    async def get_all_process_status(self) -> Dict[str, Dict[str, Any]]:
        """获取所有进程状态"""
        async with self._process_lock:
            result = {}
            for account_id, process_info in self.processes.items():
                result[account_id] = {
                    "account_id": process_info.account_id,
                    "process_id": process_info.process_id,
                    "status": process_info.status.value,
                    "start_time": process_info.start_time,
                    "last_heartbeat": process_info.last_heartbeat,
                    "restart_count": process_info.restart_count,
                    "crash_count": process_info.crash_count,
                    "auto_restart": process_info.auto_restart,
                    "error_message": process_info.error_message,
                    "uptime": time.time() - process_info.start_time if process_info.start_time else 0
                }
            return result
    
    async def update_heartbeat(self, account_id: str):
        """更新进程心跳"""
        async with self._process_lock:
            if account_id in self.processes:
                self.processes[account_id].last_heartbeat = time.time()
                await self.stats.set_value(f"process_{account_id}_last_heartbeat", time.time())
    
    def _should_restart_process(self, process_info: ProcessInfo) -> bool:
        """判断是否应该重启进程"""
        current_time = time.time()
        
        # 检查最大重启次数
        if process_info.restart_count >= process_info.max_restarts:
            return False
        
        # 检查连续失败次数
        if process_info.consecutive_failures >= self.max_consecutive_failures:
            # 如果距离上次失败时间超过故障窗口，则允许重启
            if (process_info.last_failure_time and 
                current_time - process_info.last_failure_time > self.failure_window):
                process_info.consecutive_failures = 0
                return True
            return False
        
        return True
    
    def _calculate_restart_delay(self, process_info: ProcessInfo) -> float:
        """计算智能重启延迟"""
        base_delay = max(self.min_restart_delay, process_info.restart_delay)
        
        # 根据连续失败次数计算退避延迟
        if process_info.consecutive_failures > 0:
            backoff_delay = base_delay * (self.backoff_factor ** process_info.consecutive_failures)
            delay = min(backoff_delay, self.max_restart_delay)
        else:
            delay = base_delay
        
        # 添加随机抖动避免惊群效应
        jitter = random.uniform(0.8, 1.2)
        final_delay = delay * jitter
        
        logger.info(f"计算重启延迟: base={base_delay:.1f}s, failures={process_info.consecutive_failures}, "
                   f"final={final_delay:.1f}s")
        
        return final_delay
    
    async def _record_failure_pattern(self, account_id: str, failure_type: str):
        """记录故障模式"""
        if account_id not in self.processes:
            return
        
        process_info = self.processes[account_id]
        current_time = time.time()
        
        # 记录故障类型
        if len(process_info.failure_pattern) >= 10:  # 保留最近10个故障记录
            process_info.failure_pattern.pop(0)
        
        process_info.failure_pattern.append(f"{failure_type}@{current_time:.0f}")
        process_info.last_failure_time = current_time
        
        # 更新连续失败次数
        if failure_type in ["crash", "restart_failed", "timeout"]:
            process_info.consecutive_failures += 1
        
        # 更新统计
        await self.stats.increment(f"failure_pattern_{failure_type}")
        await self.stats.set_value(f"process_{account_id}_consecutive_failures", 
                                 process_info.consecutive_failures)
        
        logger.info(f"记录故障模式: {account_id} - {failure_type} "
                   f"(连续失败: {process_info.consecutive_failures})")
    
    async def _check_process_health(self):
        """检查进程健康状态"""
        current_time = time.time()
        
        async with self._process_lock:
            for account_id, process_info in self.processes.items():
                if process_info.status == ProcessStatus.RUNNING and process_info.process_id:
                    try:
                        process = psutil.Process(process_info.process_id)
                        
                        # 检查进程响应能力
                        if not process.is_running():
                            continue
                        
                        # 检查进程创建时间（检测进程是否被替换）
                        create_time = process.create_time()
                        if (process_info.start_time and 
                            abs(create_time - process_info.start_time) > 60):  # 1分钟误差
                            logger.warning(f"检测到进程被替换: {account_id}")
                            process_info.status = ProcessStatus.CRASHED
                            await self._record_failure_pattern(account_id, "process_replaced")
                        
                        # 检查进程线程数（异常增长可能表示问题）
                        num_threads = process.num_threads()
                        if num_threads > 50:  # 线程数过多
                            logger.warning(f"进程线程数过多: {account_id} ({num_threads} threads)")
                            await self._record_failure_pattern(account_id, "too_many_threads")
                        
                        # 检查进程打开的文件数
                        try:
                            num_fds = process.num_fds() if hasattr(process, 'num_fds') else 0
                            if num_fds > 500:  # 文件描述符过多
                                logger.warning(f"进程打开文件数过多: {account_id} ({num_fds} fds)")
                                await self._record_failure_pattern(account_id, "too_many_fds")
                        except (psutil.AccessDenied, AttributeError):
                            pass
                        
                    except (psutil.NoSuchProcess, psutil.AccessDenied):
                        pass
    
    async def get_failure_analysis(self, account_id: str) -> Dict[str, Any]:
        """获取故障分析"""
        if account_id not in self.processes:
            return {}
        
        process_info = self.processes[account_id]
        current_time = time.time()
        
        # 分析故障模式
        failure_types = {}
        recent_failures = []
        
        for pattern in process_info.failure_pattern:
            if '@' in pattern:
                failure_type, timestamp_str = pattern.split('@', 1)
                timestamp = float(timestamp_str)
                
                failure_types[failure_type] = failure_types.get(failure_type, 0) + 1
                
                # 最近1小时的故障
                if current_time - timestamp < 3600:
                    recent_failures.append({
                        'type': failure_type,
                        'timestamp': timestamp,
                        'time_ago': current_time - timestamp
                    })
        
        return {
            'account_id': account_id,
            'total_failures': len(process_info.failure_pattern),
            'consecutive_failures': process_info.consecutive_failures,
            'failure_types': failure_types,
            'recent_failures': recent_failures,
            'last_failure_time': process_info.last_failure_time,
            'restart_count': process_info.restart_count,
            'crash_count': process_info.crash_count,
            'backoff_multiplier': process_info.backoff_multiplier
        }
    
    async def get_guardian_stats(self) -> Dict[str, Any]:
        """获取守护者统计信息"""
        basic_stats = await self.stats.get_all_stats()
        
        # 添加故障分析统计
        failure_analysis = {}
        for account_id in self.processes:
            failure_analysis[account_id] = await self.get_failure_analysis(account_id)
        
        return {
            **basic_stats,
            'failure_analysis': failure_analysis,
            'guardian_config': {
                'monitor_interval': self.monitor_interval,
                'heartbeat_timeout': self.heartbeat_timeout,
                'health_check_timeout': self.health_check_timeout,
                'min_restart_delay': self.min_restart_delay,
                'max_restart_delay': self.max_restart_delay,
                'backoff_factor': self.backoff_factor,
                'max_consecutive_failures': self.max_consecutive_failures
            }
        }


# 全局守护者实例
_process_guardian: Optional[ProcessGuardian] = None
_guardian_lock = asyncio.Lock()


async def get_process_guardian(config_manager: ConfigManager) -> ProcessGuardian:
    """获取进程守护者实例"""
    global _process_guardian
    
    async with _guardian_lock:
        if _process_guardian is None:
            _process_guardian = ProcessGuardian(config_manager)
        
        return _process_guardian


async def create_process_guardian(config_manager: ConfigManager) -> ProcessGuardian:
    """创建新的进程守护者实例"""
    return ProcessGuardian(config_manager)