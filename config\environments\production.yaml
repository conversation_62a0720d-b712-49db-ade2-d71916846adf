# MT5分布式交易系统 - 生产环境配置
# 覆盖core/system.yaml中的设置，适用于生产部署

# ============================================================================
# 环境标识
# ============================================================================
environment:
  name: "production"
  description: "生产环境配置"
  debug_mode: false

# ============================================================================
# 应用程序配置覆盖
# ============================================================================
application:
  # 生产环境性能设置
  performance:
    max_workers: ${MT5_MAX_WORKERS:-20}    # 生产环境充分利用资源
    memory_limit_mb: ${MT5_MEMORY_LIMIT:-8192}  # 8GB内存限制
    
  # 生产环境安全设置
  security:
    authentication_enabled: true           # 生产环境启用认证
    api_rate_limiting: true                # 启用API限流
    data_encryption: true                  # 启用数据加密

# ============================================================================
# 基础设施配置覆盖
# ============================================================================
redis:
  connection:
    host: ${REDIS_HOST:-redis}
    port: ${REDIS_PORT:-6379}
    db: ${REDIS_DB:-0}
    password: ${REDIS_PASSWORD:-}
    
  pool:
    max_connections: 200                   # 生产环境增加连接池
    
  cache:
    default_ttl: 3600                      # 生产环境长缓存时间
    
nats:
  servers:
    # 生产环境多服务器配置
    - "nats://${NATS_HOST_1:-nats1}:4222"
    - "nats://${NATS_HOST_2:-nats2}:4222"
    - "nats://${NATS_HOST_3:-nats3}:4222"
    
  jetstream:
    max_msgs: 50000000                     # 生产环境大容量存储
    max_bytes: 53687091200                 # 50GB
    max_age: 604800                        # 7天
    replicas: 3                            # 生产环境3副本

# ============================================================================
# 日志配置覆盖
# ============================================================================
logging:
  global:
    level: ${MT5_LOG_LEVEL:-INFO}          # 生产环境适中日志级别
    
  handlers:
    console:
      enabled: false                       # 生产环境关闭控制台输出
      
    file:
      enabled: true
      level: "INFO"
      file: "logs/production.log"
      max_size_mb: 500                     # 生产环境大日志文件
      backup_count: 30                     # 保留30个备份
      
    json_file:
      enabled: true
      level: "INFO"
      file: "logs/production_structured.json"
      max_size_mb: 1000
      backup_count: 30
      
  filters:
    - name: "exclude_debug"
      enabled: true
    - name: "exclude_health_checks"
      enabled: true
      
  loggers:
    "mt5.coordinator": "INFO"
    "mt5.trading": "INFO"
    "mt5.messaging": "INFO"
    "mt5.monitoring": "WARNING"
    "redis": "WARNING"
    "nats": "WARNING"

# ============================================================================
# 监控配置覆盖
# ============================================================================
prometheus:
  enabled: true
  
  server:
    port: ${PROMETHEUS_PORT:-8000}
    
  pushgateway:
    enabled: true
    url: ${PROMETHEUS_PUSHGATEWAY_URL:-http://pushgateway:9091}
    push_interval: 15                      # 生产环境频繁推送指标
    
  metrics:
    # 生产环境启用所有指标
    enabled_metrics:
      - "trading_signals_total"
      - "trade_execution_duration_seconds"
      - "position_count"
      - "account_balance"
      - "process_memory_usage_bytes"
      - "message_queue_size"
      - "network_latency_seconds"
      - "connection_failures_total"
    
health_checks:
  enabled: true
  
  endpoint:
    timeout: 5                             # 生产环境严格超时
    
  checks:
    - name: "redis_connection"
      interval: 15                         # 生产环境频繁检查
      timeout: 3
      critical: true
      
    - name: "nats_connection"
      interval: 15
      timeout: 3
      critical: true
      
    - name: "disk_space"
      interval: 30
      threshold: "5GB"                     # 生产环境严格磁盘限制
      critical: true
      
    - name: "memory_usage"
      interval: 15
      threshold: "90%"
      critical: true

# ============================================================================
# 主机配置覆盖
# ============================================================================
deployment:
  default_strategy: "multi_host"
  load_balancing: true
  failover_enabled: true

# ============================================================================
# 账户配置覆盖
# ============================================================================
accounts:
  validation:
    enabled: true
    schema_validation: true
    cross_reference_check: true
    
  # 生产环境无全局覆盖，使用账户配置文件中的默认值

# ============================================================================
# 跟单关系配置覆盖
# ============================================================================
copy_trading:
  global_settings:
    # 生产环境跟单设置
    system_limits:
      max_concurrent_relationships: 10     # 生产环境支持更多关系
      emergency_stop_enabled: true
      
    performance:
      batch_processing: true               # 生产环境启用批处理优化
      batch_size: 10
      batch_timeout_ms: 500
      
    monitoring:
      collect_statistics: true
      statistics_interval: 300             # 5分钟统计
      performance_tracking: true
      
    fault_tolerance:
      continue_on_error: false             # 生产环境严格错误处理
      max_consecutive_failures: 3
      cooldown_period_ms: 60000            # 1分钟冷却期
      
    security:
      audit_trail: true                    # 生产环境启用审计
      encrypt_signals: true                # 加密交易信号

# ============================================================================
# 功能开关覆盖
# ============================================================================
features:
  copy_trading: true
  distributed_deployment: true            # 生产环境全分布式
  advanced_monitoring: true               # 启用高级监控
  api_gateway: true
  web_interface: false                    # 生产环境关闭web界面

# ============================================================================
# 网络配置覆盖
# ============================================================================
network:
  timeouts:
    connect: 15                            # 生产环境适中超时
    read: 30
    write: 15
    keepalive: 120
    
  retry:
    max_attempts: 5                        # 生产环境多次重试
    backoff_factor: 2
    jitter: true

# ============================================================================
# 数据配置覆盖
# ============================================================================
data:
  persistence:
    enabled: true
    backup_enabled: true
    backup_interval_hours: 6               # 生产环境频繁备份
    retention_days: 90                     # 3个月数据保留
    
  cache:
    enabled: true
    max_memory_mb: 2048                    # 生产环境大缓存

# ============================================================================
# 安全配置覆盖
# ============================================================================
security:
  tls:
    enabled: true                          # 生产环境启用TLS
    cert_file: "/certs/server.crt"
    key_file: "/certs/server.key"
    ca_file: "/certs/ca.crt"
    
  authentication:
    method: "token"                        # 生产环境令牌认证
    token_secret: ${AUTH_TOKEN_SECRET}
    
  access_control:
    whitelist_enabled: true                # 生产环境IP白名单
    allowed_ips: ${ALLOWED_IPS:-}
    rate_limiting: true

# ============================================================================
# 性能优化
# ============================================================================
performance:
  asyncio:
    event_loop_policy: "uvloop"           # 生产环境高性能事件循环
    max_workers: ${MT5_MAX_WORKERS:-20}
    
  memory:
    gc_threshold: [700, 10, 10]           # 优化垃圾回收
    max_memory_usage_mb: ${MT5_MEMORY_LIMIT:-8192}
    
  messaging:
    compression_level: 9                   # 最高压缩级别
    serialization_format: "msgpack"       # 高效序列化
    
  network:
    tcp_keepalive: true
    tcp_nodelay: true
    socket_timeout: 30

# ============================================================================
# 错误跟踪和异常监控
# ============================================================================
error_tracking:
  enabled: true
  
  aggregation:
    window_minutes: 1                      # 生产环境快速聚合
    threshold: 5
    
  exceptions:
    capture_locals: false                  # 生产环境隐私保护
    capture_args: false
    
  notifications:
    enabled: true                          # 生产环境启用错误通知

# ============================================================================
# 分布式追踪
# ============================================================================
tracing:
  enabled: true                            # 生产环境启用追踪
  
  jaeger:
    agent_host: ${JAEGER_AGENT_HOST:-jaeger}
    agent_port: ${JAEGER_AGENT_PORT:-6831}
    service_name: "mt5-trading-production"
    
  sampling:
    rate: 0.01                             # 生产环境低采样率

# ============================================================================
# 告警配置
# ============================================================================
alerting:
  enabled: true
  
  rules:
    - name: "critical_error_rate"
      condition: "error_rate > 0.01"       # 1%错误率告警
      duration: "1m"
      severity: "critical"
      
    - name: "high_latency"
      condition: "latency_p95 > 2.0"       # 2秒延迟告警
      duration: "5m"
      severity: "warning"
      
    - name: "memory_usage_high"
      condition: "memory_usage > 0.85"     # 85%内存使用告警
      duration: "2m"
      severity: "warning"
      
    - name: "disk_space_low"
      condition: "disk_free < 5000000000"  # 5GB剩余空间告警
      duration: "1m"
      severity: "critical"

# ============================================================================
# 环境变量默认值
# ============================================================================
environment_defaults:
  MT5_ENVIRONMENT: "production"
  MT5_DEBUG: "false"
  MT5_LOG_LEVEL: "INFO"
  MT5_MAX_WORKERS: "20"
  MT5_MEMORY_LIMIT: "8192"
  
  # 生产环境特定
  MT5_ENABLE_TLS: "true"
  MT5_ENABLE_AUTH: "true"
  MT5_ENABLE_MONITORING: "true"