# tests/test_distributed_process_isolation.py
"""
分布式进程隔离系统测试
验证MT5终端进程隔离、账户独立性、分布式通信
"""
import asyncio
import os
import sys
import time
import logging
from pathlib import Path

# 添加项目根目录到路径
sys.path.insert(0, str(Path(__file__).parent.parent))

from src.core.mt5_process_manager import MT5ProcessManager, MT5TerminalConfig
from src.core.mt5_accountprocess import AccountConfigManager
from src.core.mt5_coordinator import DistributedMT5Coordinator
from dotenv import load_dotenv
load_dotenv()

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class ProcessIsolationTester:
    """进程隔离测试器"""
    
    def __init__(self):
        self.config_path = "config/optimized_system.yaml"
        self.test_accounts = ["ACC001", "ACC002"]
        self.process_manager = None
        self.account_manager = None
        self.coordinator = None
        
    async def setup_test_environment(self):
        """设置测试环境"""
        logger.info("🔧 设置测试环境")
        
        # 检查环境变量
        missing_vars = []
        for account_id in self.test_accounts:
            env_var = f"MT5_{account_id}_PASSWORD"
            if not os.getenv(env_var):
                missing_vars.append(env_var)
        
        if missing_vars:
            logger.error(f"❌ 缺少环境变量: {missing_vars}")
            logger.info("请设置以下环境变量:")
            for var in missing_vars:
                logger.info(f"  set {var}=your_password")
            return False
        
        # 初始化组件
        self.process_manager = MT5ProcessManager(self.config_path)
        self.account_manager = AccountConfigManager(self.config_path)
        
        logger.info("✅ 测试环境设置完成")
        return True
    
    async def test_account_config_loading(self):
        """测试账户配置加载"""
        logger.info("📋 测试账户配置加载")
        
        try:
            # 加载所有账户配置
            configs = self.account_manager.load_all_accounts()
            
            logger.info(f"加载了 {len(configs)} 个账户配置:")
            for account_id, config in configs.items():
                logger.info(f"  {account_id}: {config.name} ({config.role})")
            
            # 获取配置摘要
            summary = self.account_manager.get_config_summary()
            logger.info(f"配置摘要: {summary}")
            
            # 验证环境变量
            env_validation = self.account_manager.validate_environment_variables()
            logger.info(f"环境变量验证: {env_validation}")
            
            return len(configs) > 0
            
        except Exception as e:
            logger.error(f"❌ 账户配置加载测试失败: {e}")
            return False
    
    async def test_terminal_process_isolation(self):
        """测试终端进程隔离"""
        logger.info("🔒 测试终端进程隔离")
        
        try:
            started_terminals = []
            
            # 启动多个终端进程
            for account_id in self.test_accounts[:2]:  # 只测试前两个账户
                logger.info(f"启动终端进程: {account_id}")
                
                success = await self.process_manager.start_terminal_process(account_id)
                if success:
                    started_terminals.append(account_id)
                    logger.info(f"✅ {account_id} 终端启动成功")
                else:
                    logger.error(f"❌ {account_id} 终端启动失败")
                
                # 等待一下，避免资源冲突
                await asyncio.sleep(3)
            
            # 检查进程状态
            logger.info("检查终端进程状态:")
            all_status = self.process_manager.get_all_terminal_status()
            
            for account_id, status_info in all_status.items():
                logger.info(f"  {account_id}: {status_info}")
            
            # 验证进程隔离
            isolation_verified = len(started_terminals) >= 2
            if isolation_verified:
                logger.info("✅ 终端进程隔离验证成功")
            else:
                logger.error("❌ 终端进程隔离验证失败")
            
            # 清理终端进程
            await self.cleanup_terminals()
            
            return isolation_verified
            
        except Exception as e:
            logger.error(f"❌ 终端进程隔离测试失败: {e}")
            await self.cleanup_terminals()
            return False
    
    async def test_distributed_coordinator(self):
        """测试分布式协调器"""
        logger.info("🌐 测试分布式协调器")
        
        try:
            # 创建协调器
            self.coordinator = DistributedMT5Coordinator(
                host_id="test_host_001",
                config_path=self.config_path
            )
            
            # 启动协调器（模拟启动，不连接真实的NATS）
            logger.info("启动分布式协调器...")
            
            # 检查系统状态
            status = await self.coordinator.get_system_status()
            logger.info(f"系统状态: {status}")
            
            logger.info("✅ 分布式协调器测试成功")
            return True
            
        except Exception as e:
            logger.error(f"❌ 分布式协调器测试失败: {e}")
            return False
    
    async def test_process_lifecycle(self):
        """测试进程生命周期管理"""
        logger.info("♻️ 测试进程生命周期管理")
        
        try:
            account_id = self.test_accounts[0]
            
            # 启动进程
            logger.info(f"启动进程: {account_id}")
            success = await self.process_manager.start_terminal_process(account_id)
            if not success:
                logger.error("进程启动失败")
                return False
            
            # 等待进程稳定
            await asyncio.sleep(5)
            
            # 检查状态
            status = self.process_manager.get_terminal_status(account_id)
            logger.info(f"进程状态: {status}")
            
            # 重启进程
            logger.info(f"重启进程: {account_id}")
            restart_success = await self.process_manager.restart_terminal_process(account_id)
            if not restart_success:
                logger.error("进程重启失败")
                return False
            
            # 等待重启完成
            await asyncio.sleep(5)
            
            # 检查重启后状态
            new_status = self.process_manager.get_terminal_status(account_id)
            logger.info(f"重启后状态: {new_status}")
            
            # 停止进程
            logger.info(f"停止进程: {account_id}")
            await self.process_manager.stop_terminal_process(account_id)
            
            # 验证停止
            final_status = self.process_manager.get_terminal_status(account_id)
            logger.info(f"最终状态: {final_status}")
            
            logger.info("✅ 进程生命周期管理测试成功")
            return True
            
        except Exception as e:
            logger.error(f"❌ 进程生命周期管理测试失败: {e}")
            return False
    
    async def cleanup_terminals(self):
        """清理所有终端进程"""
        if self.process_manager:
            logger.info("🧹 清理终端进程")
            await self.process_manager.stop_all_terminals()
    
    async def cleanup_coordinator(self):
        """清理协调器"""
        if self.coordinator:
            logger.info("🧹 清理协调器")
            await self.coordinator.stop()
    
    async def run_all_tests(self):
        """运行所有测试"""
        logger.info("🚀 开始分布式进程隔离系统测试")
        
        # 设置测试环境
        if not await self.setup_test_environment():
            logger.error("❌ 测试环境设置失败")
            return False
        
        test_results = {}
        
        # 运行测试
        tests = [
            ("账户配置加载", self.test_account_config_loading),
            ("终端进程隔离", self.test_terminal_process_isolation),
            ("分布式协调器", self.test_distributed_coordinator),
            ("进程生命周期", self.test_process_lifecycle),
        ]
        
        for test_name, test_func in tests:
            logger.info(f"\n{'='*50}")
            logger.info(f"运行测试: {test_name}")
            logger.info(f"{'='*50}")
            
            try:
                result = await test_func()
                test_results[test_name] = result
                
                if result:
                    logger.info(f"✅ {test_name} - 通过")
                else:
                    logger.error(f"❌ {test_name} - 失败")
                    
            except Exception as e:
                logger.error(f"❌ {test_name} - 异常: {e}")
                test_results[test_name] = False
            
            # 测试间隔
            await asyncio.sleep(2)
        
        # 清理资源
        await self.cleanup_terminals()
        await self.cleanup_coordinator()
        
        # 输出测试结果
        logger.info(f"\n{'='*50}")
        logger.info("测试结果汇总")
        logger.info(f"{'='*50}")
        
        passed = 0
        total = len(test_results)
        
        for test_name, result in test_results.items():
            status = "✅ 通过" if result else "❌ 失败"
            logger.info(f"{test_name}: {status}")
            if result:
                passed += 1
        
        logger.info(f"\n总计: {passed}/{total} 测试通过")
        
        if passed == total:
            logger.info("🎉 所有测试通过！分布式进程隔离系统工作正常")
            return True
        else:
            logger.error("⚠️ 部分测试失败，请检查系统配置")
            return False


async def main():
    """主函数"""
    tester = ProcessIsolationTester()
    success = await tester.run_all_tests()
    
    if success:
        logger.info("🎯 测试完成：系统正常")
        return 0
    else:
        logger.error("💥 测试完成：发现问题")
        return 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
