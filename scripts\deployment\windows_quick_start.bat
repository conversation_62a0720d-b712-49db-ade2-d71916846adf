@echo off
echo ========================================
echo MT5 远程终端快速启动脚本
echo ========================================
echo.

:: 设置项目路径
set PROJECT_PATH=%~dp0..
cd /d %PROJECT_PATH%

:: 检查Python虚拟环境
if not exist "venv" (
    echo 创建虚拟环境...
    python -m venv venv
    call venv\Scripts\activate
    echo 安装依赖...
    pip install -r requirements.txt
) else (
    call venv\Scripts\activate
)

echo.
echo 请选择要启动的终端类型:
echo 1. 主账户 (Master) - 发送交易信号
echo 2. 从账户 (Slave) - 接收信号跟单
echo 3. 配置向导
echo 4. 退出
echo.

set /p choice=请输入选项 (1-4): 

if "%choice%"=="1" goto start_master
if "%choice%"=="2" goto start_slave
if "%choice%"=="3" goto config_wizard
if "%choice%"=="4" goto end

:start_master
echo.
echo 可用的主账户配置文件:
dir /b config\*master*.yaml 2>nul
echo.
set /p config_file=请输入配置文件名 (例如: my_master.yaml): 
python scripts\remote_terminal.py --config config\%config_file% --role master
goto end

:start_slave
echo.
echo 可用的从账户配置文件:
dir /b config\*slave*.yaml 2>nul
echo.
set /p config_file=请输入配置文件名 (例如: my_slave.yaml): 
python scripts\remote_terminal.py --config config\%config_file% --role slave
goto end

:config_wizard
echo.
echo 配置向导
echo ========================================
set /p server_ip=请输入服务器IP地址: 
set /p account_type=账户类型 (master/slave): 
set /p mt5_login=MT5登录账号: 
set /p account_name=账户名称: 

if "%account_type%"=="master" (
    copy config\remote_master_example.yaml config\%account_name%_master.yaml
    echo 已创建配置文件: config\%account_name%_master.yaml
) else (
    copy config\remote_slave_example.yaml config\%account_name%_slave.yaml
    echo 已创建配置文件: config\%account_name%_slave.yaml
)

echo.
echo 请手动编辑配置文件，修改以下内容：
echo 1. 将 YOUR_SERVER_IP 替换为 %server_ip%
echo 2. 将 login 修改为 %mt5_login%
echo 3. 设置环境变量中的密码
echo.
pause
goto end

:end
echo.
echo 按任意键退出...
pause >nul