# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is an **MT5 High-Frequency Distributed Trading Copy System** - an enterprise-grade MetaTrader 5 trading signal replication solution designed for high-frequency trading scenarios. The system uses modern microservice architecture supporting microsecond-level signal transmission with distributed deployment and intelligent load balancing.

**Current branch**: `distributed-system-temp` (not main)
**Main branch for PRs**: `main`

## Key Commands

### Running the System

```bash
# Main entry point with configuration
python main.py --config config/core/system.yaml --host-id uk-lon-node-01

# Start distributed mode
python main.py --distributed --host-id uk-lon-node-02

# Quick start copy trading
python start_copy_trading_system.py

# Docker deployment (development)
docker-compose -f docker/environments/development/docker-compose.yml up -d

# Docker deployment (production)
docker-compose -f docker/environments/production/docker-compose.yml up -d
```

### Testing

```bash
# Run all tests
pytest

# Run specific test categories
pytest -m unit          # Unit tests only
pytest -m integration   # Integration tests only
pytest -m performance   # Performance tests only

# Run comprehensive test suite
python tests/run_all_tests.py

# Quick verification of fixes
python tests/verify_fixes.py

# Test specific components
python tests/test_mt5_integration_flow.py
```

### Code Quality

```bash
# Format code with black
black src/ tests/

# Lint code with flake8
flake8 src/ tests/

# Type checking with mypy
mypy src/
```

### Frontend Development (in web/frontend/)

```bash
# Install dependencies
npm install

# Development server
npm run dev

# Build for production
npm run build

# Run tests
npm run test

# Lint and format
npm run lint
npm run format
```

### Utility Commands

```bash
# Check system health
python scripts/utilities/health_check.py

# Monitor copy trading
python monitor_copy_trading.py

# Debug components
python debug_copy_trading.py
python debug_request_handler.py

# Validate configuration
python scripts/validation/validate_distributed_config.py

# Fix distributed system issues
python fix_distributed_system_issues.py
```

## High-Level Architecture

The system follows a **5-layer distributed architecture**:

1. **API/Presentation Layer** (`src/api/`): FastAPI endpoints, WebSocket connections, Grafana dashboards
2. **System Coordination Layer** (`src/distributed/coordinator.py`): DistributedMT5Coordinator managing the entire system
3. **Business Process Layer** (`src/core/mt5_account_process.py`): MT5AccountProcess instances for each trading account
4. **Process Management Layer** (`src/core/mt5_process_manager.py`): Handles Terminal64.exe processes
5. **Infrastructure Layer**: NATS JetStream (`src/messaging/`), Redis (`src/infrastructure/`), monitoring stack

### Key Components

- **MT5Coordinator** (`src/core/mt5_coordinator.py`): Central coordinator managing all account processes
- **MT5AccountProcess** (`src/core/base_executor.py`): Individual account process handling trading operations
- **MessageRouter** (`src/distributed/message_router.py`): Routes messages between accounts across hosts
- **SignalRouter** (`src/core/signal_router.py`): Manages signal distribution from masters to slaves
- **TradeProcessor** (`src/processors/trade_processor.py`): Processes and validates trading signals
- **NATS Client** (`src/messaging/nats_client.py`): High-performance messaging with JetStream
- **Redis Infrastructure** (`src/infrastructure/`): Connection pooling, hash management, caching

### Configuration Structure

All configurations are in YAML format under `config/`:
- `config/core/system.yaml` - Main system configuration
- `config/core/infrastructure.yaml` - NATS, Redis settings
- `config/accounts/*.yaml` - Individual account configurations
- `config/relationships/copy_trading.yaml` - Copy trading relationships
- `config/environments/` - Environment-specific settings

### Dynamic Master/Slave Architecture

The system uses **dynamic role assignment** - no hardcoded master/slave roles:
- Roles determined by account balance and trading status
- Automatic failover when master fails
- Load balancing across multiple slaves
- Configuration-driven relationships

## Development Notes

### Service Ports

**Production**:
- Redis: 6379
- NATS: 4222 (client), 8222 (monitoring)
- API: 8000
- Grafana: 3000

**Development**:
- Redis: 6380
- NATS: 4223 (client), 8223 (monitoring)
- API: 8001
- Grafana: 3001

### Performance Targets

- Signal latency: <1ms (P99) in LAN
- Throughput: 10,000+ signals/second
- Recovery time: <2 seconds after failure
- Startup time: <5 seconds

### Security Considerations

- TLS certificates in `certs/` directory
- Password management via `src/security/password_manager.py`
- API authentication required for all endpoints
- Role-based access control implemented

### Testing Strategy

- Unit tests for individual components
- Integration tests for complete flows
- Performance benchmarks for latency/throughput
- Mock MT5 for testing without real accounts
- Production readiness tests before deployment

### Common Issues and Solutions

1. **NATS Connection Issues**: Check NATS server is running and accessible
2. **Redis Connection**: Ensure Redis Sentinel is properly configured
3. **MT5 Process Failures**: Check Terminal64.exe paths and permissions
4. **Config Loading**: Validate YAML syntax and required fields
5. **Performance Degradation**: Monitor message queue depth and processing times

### Monitoring and Observability

- Prometheus metrics exposed at `/metrics`
- Grafana dashboards for real-time monitoring
- Custom alerts configured in `docker/monitoring/rules/`
- Distributed tracing for cross-host operations
- Comprehensive logging with correlation IDs