<!DOCTYPE html>
<html lang="zh-CN" class="scroll-smooth">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MT5 交易系统策略仪表盘</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <!-- Chosen Palette: Calm Harmony (Slate, Red, Orange, Blue, Cyan, Gray) -->
    <!-- Application Structure Plan: A multi-view SPA with a main Priority Dashboard, an Architecture Overview, a searchable Operation Finder, and a live System Simulation. This structure transforms the static report into an interactive tool for exploration and understanding, allowing users to view the data from different perspectives (hierarchical, searchable, and dynamic). -->
    <!-- Visualization & Content Choices: Priority levels are presented as interactive cards (HTML/CSS) for hierarchical exploration. The system architecture is visualized with a CSS-based diagram. A searchable HTML table enables quick lookups. A dynamic log feed (JS-generated divs) simulates the system in action. All choices avoid SVG/Mermaid, using standard web technologies for maximum performance and compatibility. -->
    <!-- CONFIRMATION: NO SVG graphics used. NO Mermaid JS used. -->
    <style>
        body {
            font-family: 'Inter', sans-serif;
            background-color: #f8fafc; /* slate-50 */
        }
        .priority-card-header {
            transition: background-color 0.3s ease;
        }
        .priority-card-header:hover {
            background-color: #f1f5f9; /* slate-100 */
        }
        .content-section {
            display: none;
        }
        .content-section.active {
            display: block;
        }
        .nav-link.active {
            background-color: #0284c7; /* sky-600 */
            color: white;
        }
        .log-entry {
            transition: all 0.5s ease-out;
            transform-origin: top;
        }
        .log-entry.new {
            transform: scaleY(0);
            opacity: 0;
        }
        .diagram-arrow {
            position: relative;
            padding-right: 1.25rem;
        }
        .diagram-arrow::after {
            content: '→';
            position: absolute;
            right: 0;
            top: 50%;
            transform: translateY(-50%);
            font-size: 1.5rem;
            color: #64748b; /* slate-500 */
        }
    </style>
</head>
<body class="text-slate-800">

    <div class="min-h-screen lg:flex">
        <!-- Sidebar Navigation -->
        <aside class="bg-slate-800 text-slate-100 w-full lg:w-64 lg:flex-shrink-0">
            <div class="flex items-center justify-between p-4 lg:p-6 border-b border-slate-700">
                <h1 class="text-xl font-bold">策略仪表盘</h1>
                <button id="mobile-menu-button" class="lg:hidden text-slate-300 hover:text-white">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16m-7 6h7"></path></svg>
                </button>
            </div>
            <nav id="main-nav" class="hidden lg:block p-4 space-y-2">
                <a href="#" class="nav-link flex items-center px-4 py-2 rounded-md font-medium transition-colors duration-200 hover:bg-slate-700" data-target="dashboard">
                    <span class="mr-3">🚦</span> 优先级仪表盘
                </a>
                <a href="#" class="nav-link flex items-center px-4 py-2 rounded-md font-medium transition-colors duration-200 hover:bg-slate-700" data-target="architecture">
                    <span class="mr-3">🏗️</span> 架构概览
                </a>
                <a href="#" class="nav-link flex items-center px-4 py-2 rounded-md font-medium transition-colors duration-200 hover:bg-slate-700" data-target="finder">
                    <span class="mr-3">🔍</span> 操作查找器
                </a>
                <a href="#" class="nav-link flex items-center px-4 py-2 rounded-md font-medium transition-colors duration-200 hover:bg-slate-700" data-target="simulation">
                    <span class="mr-3">⚡️</span> 系统模拟
                </a>
            </nav>
        </aside>

        <!-- Main Content -->
        <main class="flex-1 p-4 sm:p-6 lg:p-8">
            
            <!-- View: Priority Dashboard -->
            <section id="dashboard" class="content-section">
                <header class="mb-8">
                    <h2 class="text-3xl font-bold text-slate-900">消息优先级仪表盘</h2>
                    <p class="mt-2 text-lg text-slate-600">基于“风险 > 机会 > 数据 > 后勤”原则的五级优先级模型。点击下方卡片以展开查看详情。</p>
                </header>
                <div id="priority-cards-container" class="space-y-6">
                    <!-- Cards will be injected here by JavaScript -->
                </div>
            </section>

            <!-- View: Architecture Overview -->
            <section id="architecture" class="content-section">
                <header class="mb-8">
                    <h2 class="text-3xl font-bold text-slate-900">架构概览：混合通信模式</h2>
                    <p class="mt-2 text-lg text-slate-600">为了将性能压榨到极致，系统采用混合通信模式，智能选择最优通信路径。</p>
                </header>
                <div class="bg-white p-6 rounded-xl shadow-sm">
                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                        <article class="prose prose-slate max-w-none">
                            <h3>核心理念</h3>
                            <p>传统的分布式系统可能使用统一的消息总线（如NATS）处理所有通信，这保证了架构的简洁和位置透明性。但对于同主机内的组件间通信，网络协议会带来不必要的延迟。</p>
                            <p>我们的混合模式架构通过区分通信场景，实现了性能最大化：</p>
                            <ul>
                                <li><strong>同主机通信:</strong> 采用零开销的内存中优先级队列 (`In-Memory Priority Queue`)，实现微秒级延迟，确保本地信号和指令的极速响应。</li>
                                <li><strong>跨主机通信:</strong> 采用高可靠的NATS JetStream，保障分布式节点间的信号广播、状态同步和指令下发。</li>
                            </ul>
                            <p class="font-semibold text-sky-700">这种设计用适度的复杂度增加，换取了本地通信的极致性能，这对于高频交易系统是至关重要的权衡。</p>
                        </article>
                        <div>
                            <h3 class="text-lg font-semibold mb-4 text-center">通信流程图</h3>
                            <div class="bg-slate-50 p-4 rounded-lg border border-slate-200 space-y-4">
                                <div class="text-center font-bold">主机 A (e.g., uk-lon-node-01)</div>
                                <div class="flex items-center justify-center space-x-4">
                                    <div class="bg-green-100 border border-green-300 text-green-800 rounded-lg p-3 text-center">
                                        <p class="font-semibold">策略模块</p>
                                        <p class="text-sm">(Signal Gen)</p>
                                    </div>
                                    <div class="text-green-600 font-mono text-sm text-center">
                                        <div class="diagram-arrow">内存队列</div>
                                        <div class="text-xs">(微秒级)</div>
                                    </div>
                                    <div class="bg-green-100 border border-green-300 text-green-800 rounded-lg p-3 text-center">
                                        <p class="font-semibold">MT5执行器</p>
                                        <p class="text-sm">(Local Exec)</p>
                                    </div>
                                </div>
                                <div class="flex justify-center">
                                    <div class="text-blue-600 font-mono text-sm text-center transform -rotate-90 translate-y-8">
                                        <div class="diagram-arrow transform rotate-90">NATS JetStream</div>
                                        <div class="text-xs">(毫秒级)</div>
                                    </div>
                                </div>
                                <div class="text-center font-bold">主机 B (e.g., uk-lon-node-02)</div>
                                <div class="flex items-center justify-center">
                                     <div class="bg-sky-100 border border-sky-300 text-sky-800 rounded-lg p-3 text-center">
                                        <p class="font-semibold">跟单账户</p>
                                        <p class="text-sm">(Remote Slave)</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- View: Operation Finder -->
            <section id="finder" class="content-section">
                <header class="mb-8">
                    <h2 class="text-3xl font-bold text-slate-900">操作查找器</h2>
                    <p class="mt-2 text-lg text-slate-600">快速搜索系统中任何操作的优先级定义。</p>
                </header>
                <div class="bg-white p-6 rounded-xl shadow-sm">
                    <input type="text" id="operation-search-input" placeholder="输入操作名称进行搜索，例如“开仓”..." class="w-full px-4 py-2 border border-slate-300 rounded-md focus:ring-2 focus:ring-sky-500 focus:border-sky-500 transition">
                    <div class="mt-4 overflow-x-auto">
                        <table class="w-full text-left border-collapse">
                            <thead class="bg-slate-50">
                                <tr>
                                    <th class="p-4 text-sm font-semibold text-slate-600">操作 (Operation)</th>
                                    <th class="p-4 text-sm font-semibold text-slate-600">优先级</th>
                                    <th class="p-4 text-sm font-semibold text-slate-600">描述 (Description)</th>
                                </tr>
                            </thead>
                            <tbody id="operations-table-body">
                                <!-- Table rows will be injected here by JavaScript -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </section>

            <!-- View: System Simulation -->
            <section id="simulation" class="content-section">
                <header class="mb-8">
                    <h2 class="text-3xl font-bold text-slate-900">系统消息模拟</h2>
                    <p class="mt-2 text-lg text-slate-600">模拟实时消息流，直观感受不同优先级消息的处理顺序。</p>
                </header>
                <div class="bg-white p-6 rounded-xl shadow-sm">
                    <div class="flex items-center space-x-4 mb-4">
                        <button id="start-simulation-btn" class="px-4 py-2 bg-green-600 text-white font-semibold rounded-md hover:bg-green-700 transition">开始模拟</button>
                        <button id="stop-simulation-btn" class="px-4 py-2 bg-red-600 text-white font-semibold rounded-md hover:bg-red-700 transition" disabled>停止模拟</button>
                        <div class="text-sm text-slate-500">模拟速率: <span id="simulation-speed-label">中等</span></div>
                    </div>
                    <div id="log-feed-container" class="h-96 bg-slate-900 text-white font-mono text-sm p-4 rounded-md overflow-y-auto flex flex-col-reverse">
                        <!-- Log entries will be prepended here -->
                        <div class="text-slate-400">点击“开始模拟”以查看实时消息流...</div>
                    </div>
                </div>
            </section>

        </main>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            
            const priorityData = [
                {
                    level: 0,
                    name: "SYSTEM_CRITICAL",
                    title: "系统级风险",
                    description: "应对可能导致账户爆仓或重大亏损的系统性、灾难性事件。拥有绝对优先权，应能中断几乎所有其他操作。",
                    color: "red",
                    icon: "🚨",
                    operations: [
                        { name: "保证金不足强制平仓", desc: "执行由服务器（Broker）或本地监控触发的强制平仓指令。", trigger: "当 `account_info().margin_level` 低于预设的危险阈值时触发。" },
                        { name: "紧急一键清仓 (Kill Switch)", desc: "执行由用户手动触发的，清空该账户所有持仓和挂单的指令。", trigger: "用户通过UI或命令行工具发出的“恐慌”按钮。" },
                        { name: "系统失联恢复", desc: "在系统与MT5终端或Broker长时间失联后，重新连接成功时，立即执行的风险同步与核对操作。", trigger: "例如，网络中断恢复后，第一件事就是核对本地状态与服务器状态。" },
                        { name: "风控策略强制全平", desc: "由高级风控模块（如账户回撤达到日/周上限）触发的清仓指令。", trigger: "账户整体风控指标突破了最严重的阈值。" }
                    ]
                },
                {
                    level: 1,
                    name: "RISK_COMMAND",
                    title: "订单级风控",
                    description: "管理和控制已存在的订单和持仓所带来的风险，确保风险敞口可控。",
                    color: "orange",
                    icon: "⚠️",
                    operations: [
                        { name: "执行止损/止盈", desc: "策略逻辑或价格触及SL/TP点后，生成并执行市价平仓单。", trigger: "这是最常见的风险控制操作，时效性要求极高。" },
                        { name: "修改订单止损/止盈", desc: "实时修改一个已存在订单或持仓的SL/TP价格。", trigger: "例如，移动止损（Trailing Stop）或策略更新风险参数。" },
                        { name: "取消待成交挂单", desc: "取消一个尚未被市场触发的`BUY_LIMIT`, `SELL_STOP`等挂单。", trigger: "市场条件变化，原有的挂单逻辑不再成立。" },
                        { name: "部分平仓", desc: "根据风控规则（如利润回撤）或策略指令，平掉部分仓位。", trigger: "用于锁定利润或降低风险。" },
                        { name: "对锁单/解锁单 (Hedge)", desc: "为现有仓位开一个等量反向单进行锁定，或平掉对锁单中的一个。", trigger: "属于风险管理操作，而非新的交易机会。" }
                    ]
                },
                {
                    level: 2,
                    name: "SIGNAL_EXECUTION",
                    title: "策略信号执行",
                    description: "捕捉交易机会，执行策略的核心逻辑。这是系统利润的直接来源。",
                    color: "blue",
                    icon: "🎯",
                    operations: [
                        { name: "开仓信号", desc: "根据策略判断，生成并执行开仓指令（市价单或挂单）。", trigger: "这是最重要的进攻指令，其优先级必须高于任何非风险类操作。" },
                        { name: "策略主动平仓信号", desc: "并非由SL/TP触发，而是策略根据市场分析主动决定平仓。", trigger: "例如，趋势反转信号出现，策略决定提前离场。" },
                        { name: "反向跟单开仓", desc: "从账户接收到主账户信号后，执行反向开仓操作。", trigger: "属于策略执行的一部分。" }
                    ]
                },
                {
                    level: 3,
                    name: "REALTIME_QUERY",
                    title: "实时数据查询",
                    description: "获取用于即时决策的关键数据。这些查询的结果会直接影响到等级0, 1, 2的操作。",
                    color: "cyan",
                    icon: "📊",
                    operations: [
                        { name: "获取最新报价 (Tick)", desc: "查询一个或多个交易品种的最新买卖价。", trigger: "所有价格驱动型策略的基础。" },
                        { name: "查询账户实时信息", desc: "获取账户的余额、净值、已用保证金、可用保证金、保证金水平。", trigger: "开仓前计算手数、风控模块监控风险时必须调用。" },
                        { name: "查询当前持仓", desc: "获取所有当前持有的仓位列表及其详细信息（盈亏、手数等）。", trigger: "是计算总体风险敞口和执行平仓逻辑的基础。" },
                        { name: "查询当前挂单", desc: "获取所有未成交的挂单列表。", trigger: "用于管理和取消挂单。" },
                        { name: "查询服务器连接状态", desc: "检查与交易服务器的连接是否正常。", trigger: "系统心跳和健康检查的一部分。" }
                    ]
                },
                {
                    level: 4,
                    name: "BACKGROUND_TASK",
                    title: "后台与非实时任务",
                    description: "执行不影响即时交易决策的、可以容忍一定延迟的辅助性任务。",
                    color: "gray",
                    icon: "⚙️",
                    operations: [
                        { name: "写入交易日志", desc: "将已完成的交易操作（开仓、平仓、修改等）记录到文件或数据库。", trigger: "可以在操作完成后异步执行。" },
                        { name: "查询历史K线数据", desc: "获取用于策略分析或初始化的历史价格数据。", trigger: "通常在策略启动时或非交易时段执行。" },
                        { name: "查询历史订单/交易", desc: "从账户历史中获取已关闭的订单和交易记录。", trigger: "用于生成报告、统计分析。" },
                        { name: "UI界面状态更新", desc: "推送数据用于更新图形用户界面的显示。", trigger: "例如，仪表盘上的账户列表、日志滚动显示。" },
                        { name: "生成每日报告/分析", desc: "在收盘后执行的统计和分析任务，如生成对冲建议。", trigger: "属于非交易时段的批处理任务。" },
                        { name: "系统心跳信息", desc: "定期发送系统自身运行状态的指标（CPU、内存占用等）。", trigger: "用于系统监控，而非交易决策。" }
                    ]
                }
            ];

            const colorMap = {
                red: { border: 'border-red-500', text: 'text-red-500', bg: 'bg-red-50' },
                orange: { border: 'border-orange-500', text: 'text-orange-500', bg: 'bg-orange-50' },
                blue: { border: 'border-sky-500', text: 'text-sky-500', bg: 'bg-sky-50' },
                cyan: { border: 'border-cyan-500', text: 'text-cyan-500', bg: 'bg-cyan-50' },
                gray: { border: 'border-slate-400', text: 'text-slate-500', bg: 'bg-slate-50' }
            };

            // --- App State ---
            let state = {
                activeView: 'dashboard',
                simulationInterval: null,
            };

            // --- DOM Elements ---
            const navLinks = document.querySelectorAll('.nav-link');
            const contentSections = document.querySelectorAll('.content-section');
            const mobileMenuButton = document.getElementById('mobile-menu-button');
            const mainNav = document.getElementById('main-nav');
            const priorityCardsContainer = document.getElementById('priority-cards-container');
            const operationsTableBody = document.getElementById('operations-table-body');
            const searchInput = document.getElementById('operation-search-input');
            const startSimBtn = document.getElementById('start-simulation-btn');
            const stopSimBtn = document.getElementById('stop-simulation-btn');
            const logFeedContainer = document.getElementById('log-feed-container');

            // --- Initialization ---
            function init() {
                renderDashboard();
                renderOperationsTable();
                setupEventListeners();
                updateView();
            }

            // --- Rendering Functions ---
            function renderDashboard() {
                priorityCardsContainer.innerHTML = priorityData.map(p => {
                    const colors = colorMap[p.color] || colorMap.gray;
                    return `
                        <div class="bg-white rounded-xl shadow-sm border-l-4 ${colors.border}">
                            <div class="priority-card-header cursor-pointer p-4 flex justify-between items-center">
                                <div class="flex items-center">
                                    <span class="text-2xl mr-4">${p.icon}</span>
                                    <div>
                                        <h3 class="text-xl font-bold ${colors.text}">${p.title} (等级 ${p.level})</h3>
                                        <p class="text-slate-600">${p.description}</p>
                                    </div>
                                </div>
                                <span class="text-slate-400 transform transition-transform duration-300">▼</span>
                            </div>
                            <div class="priority-card-content hidden border-t border-slate-200 p-4">
                                <div class="overflow-x-auto">
                                    <table class="w-full text-left">
                                        <thead class="${colors.bg}">
                                            <tr>
                                                <th class="p-3 text-sm font-semibold text-slate-700">操作</th>
                                                <th class="p-3 text-sm font-semibold text-slate-700">描述</th>
                                                <th class="p-3 text-sm font-semibold text-slate-700">触发场景</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            ${p.operations.map(op => `
                                                <tr class="border-b border-slate-100 last:border-b-0">
                                                    <td class="p-3 font-medium">${op.name}</td>
                                                    <td class="p-3 text-slate-600">${op.desc}</td>
                                                    <td class="p-3 text-slate-500 text-sm">${op.trigger}</td>
                                                </tr>
                                            `).join('')}
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    `;
                }).join('');

                // Add event listeners to new cards
                document.querySelectorAll('.priority-card-header').forEach(header => {
                    header.addEventListener('click', () => {
                        const content = header.nextElementSibling;
                        const arrow = header.querySelector('span.transform');
                        content.classList.toggle('hidden');
                        arrow.classList.toggle('rotate-180');
                    });
                });
            }
            
            function renderOperationsTable() {
                const allOperations = priorityData.flatMap(p => 
                    p.operations.map(op => ({ ...op, priority: p }))
                );
                operationsTableBody.innerHTML = allOperations.map(op => {
                    const colors = colorMap[op.priority.color] || colorMap.gray;
                    return `
                        <tr class="border-t border-slate-200">
                            <td class="p-4 font-medium">${op.name}</td>
                            <td class="p-4">
                                <span class="px-3 py-1 text-sm font-semibold rounded-full ${colors.bg} ${colors.text}">
                                    ${op.priority.level}: ${op.priority.title}
                                </span>
                            </td>
                            <td class="p-4 text-slate-600">${op.desc}</td>
                        </tr>
                    `;
                }).join('');
            }

            function updateView() {
                contentSections.forEach(section => {
                    section.classList.toggle('active', section.id === state.activeView);
                });
                navLinks.forEach(link => {
                    link.classList.toggle('active', link.dataset.target === state.activeView);
                });
            }

            // --- Event Handling ---
            function setupEventListeners() {
                mobileMenuButton.addEventListener('click', () => {
                    mainNav.classList.toggle('hidden');
                });

                navLinks.forEach(link => {
                    link.addEventListener('click', (e) => {
                        e.preventDefault();
                        state.activeView = link.dataset.target;
                        updateView();
                        if (window.innerWidth < 1024) {
                           mainNav.classList.add('hidden');
                        }
                    });
                });
                
                searchInput.addEventListener('keyup', handleSearch);

                startSimBtn.addEventListener('click', startSimulation);
                stopSimBtn.addEventListener('click', stopSimulation);
            }

            function handleSearch(e) {
                const searchTerm = e.target.value.toLowerCase();
                const rows = operationsTableBody.querySelectorAll('tr');
                rows.forEach(row => {
                    const text = row.textContent.toLowerCase();
                    row.style.display = text.includes(searchTerm) ? '' : 'none';
                });
            }

            // --- Simulation Logic ---
            function startSimulation() {
                if (state.simulationInterval) return;
                startSimBtn.disabled = true;
                stopSimBtn.disabled = false;
                logFeedContainer.innerHTML = ''; // Clear previous logs

                const allOperationsFlat = priorityData.flatMap(p => p.operations.map(op => ({ ...op, priority: p })));

                state.simulationInterval = setInterval(() => {
                    const randomOp = allOperationsFlat[Math.floor(Math.random() * allOperationsFlat.length)];
                    const priority = randomOp.priority;
                    
                    const timestamp = new Date().toISOString();
                    const colors = colorMap[priority.color] || colorMap.gray;

                    const logEntry = document.createElement('div');
                    logEntry.className = `log-entry new p-2 border-l-4 ${colors.border} mb-1`;
                    logEntry.innerHTML = `
                        <span class="text-slate-400">${timestamp.split('T')[1].replace('Z','')}</span>
                        <span class="${colors.text} font-bold mx-2">[${priority.name}]</span>
                        <span>${randomOp.name}</span>
                    `;
                    
                    logFeedContainer.prepend(logEntry);
                    
                    // Trigger animation
                    requestAnimationFrame(() => {
                        logEntry.style.transform = 'scaleY(1)';
                        logEntry.style.opacity = '1';
                    });

                    // Keep log feed clean
                    if (logFeedContainer.children.length > 100) {
                        logFeedContainer.lastElementChild.remove();
                    }

                }, 800); // Medium speed
            }

            function stopSimulation() {
                clearInterval(state.simulationInterval);
                state.simulationInterval = null;
                startSimBtn.disabled = false;
                stopSimBtn.disabled = true;
            }

            // --- Run App ---
            init();
        });
    </script>
</body>
</html>
