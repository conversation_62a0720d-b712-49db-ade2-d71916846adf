# MT5跟单系统监控部署指南

## 快速启动

### 1. 启动监控栈
```bash
cd monitoring
docker-compose up -d
```

### 2. 访问监控界面
- **Prometheus**: http://localhost:9090
- **Grafana**: http://localhost:3000 (admin/admin123)
- **AlertManager**: http://localhost:9093

### 3. 导入Grafana仪表板
1. 登录Grafana
2. 导入 `grafana_dashboard.json`
3. 配置Prometheus数据源: http://prometheus:9090

## 监控指标说明

### 系统指标
- `mt5_signal_processing_duration`: 信号处理延迟
- `mt5_trade_matching_accuracy_ratio`: 交易匹配准确率
- `mt5_copy_execution_success_rate`: 跟单执行成功率
- `mt5_signal_queue_size`: 信号队列大小
- `mt5_nats_connection_status`: NATS连接状态

### 业务指标
- `mt5_signals_published_total`: 发布信号总数
- `mt5_signals_processed_total`: 处理信号总数
- `mt5_trades_executed_total`: 执行交易总数
- `mt5_errors_total`: 错误总数

## 告警配置

### 关键告警
1. **高延迟告警**: 信号处理延迟 > 100ms
2. **低成功率告警**: 跟单成功率 < 95%
3. **连接断开告警**: NATS连接断开
4. **高错误率告警**: 错误率 > 1/sec
5. **队列积压告警**: 信号队列 > 1000

### 告警通知
配置 `alertmanager.yml` 设置邮件、Slack等通知方式。

## 生产环境配置

### 1. 资源配置
- Prometheus: 2GB RAM, 50GB存储
- Grafana: 1GB RAM
- AlertManager: 512MB RAM

### 2. 数据保留
- 指标数据保留: 200小时
- 告警历史: 30天

### 3. 高可用配置
- Prometheus集群
- Grafana负载均衡
- AlertManager集群

## 故障排除

### 常见问题
1. **指标未显示**: 检查MT5系统是否启动指标服务器
2. **告警不触发**: 检查告警规则语法
3. **Grafana无数据**: 检查Prometheus数据源配置

### 日志查看
```bash
docker logs mt5-prometheus
docker logs mt5-grafana
docker logs mt5-alertmanager
```

## 性能调优

### 1. 采集频率优化
- 核心指标: 5秒
- 系统指标: 15秒
- 业务指标: 30秒

### 2. 存储优化
- 启用压缩
- 配置合适的保留策略
- 使用SSD存储

### 3. 查询优化
- 使用记录规则预计算
- 优化PromQL查询
- 配置查询超时
