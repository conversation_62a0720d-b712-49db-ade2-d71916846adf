# MT5分布式交易系统 - 开发环境配置
# 覆盖core/system.yaml中的设置，适用于本地开发

# ============================================================================
# 环境标识
# ============================================================================
environment:
  name: "development"
  description: "本地开发环境配置"
  debug_mode: true

# ============================================================================
# 应用程序配置覆盖
# ============================================================================
application:
  # 开发环境性能设置
  performance:
    max_workers: 4                  # 开发环境减少worker数量
    memory_limit_mb: 1024           # 1GB内存限制
    
  # 开发环境安全设置
  security:
    authentication_enabled: false  # 开发环境关闭认证
    api_rate_limiting: false        # 关闭API限流
    data_encryption: false          # 关闭数据加密

# ============================================================================
# 基础设施配置覆盖
# ============================================================================
redis:
  connection:
    host: "localhost"               # 使用本地Redis
    port: 6379
    db: 1                          # 使用非默认数据库避免冲突
    
  cache:
    default_ttl: 300               # 开发环境短缓存时间
    
nats:
  servers:
    - "nats://localhost:4222"      # 只使用本地NATS
    
  jetstream:
    max_msgs: 1000000              # 开发环境减少消息存储
    max_bytes: 1073741824          # 1GB
    max_age: 3600                  # 1小时

# ============================================================================
# 日志配置覆盖
# ============================================================================
logging:
  global:
    level: "DEBUG"                 # 开发环境详细日志
    
  handlers:
    console:
      enabled: true
      level: "DEBUG"
      
    file:
      enabled: true
      level: "DEBUG"
      file: "logs/development.log"
      max_size_mb: 50
      backup_count: 3
      
  loggers:
    "mt5.coordinator": "DEBUG"
    "mt5.trading": "DEBUG"
    "mt5.messaging": "DEBUG"

# ============================================================================
# 监控配置覆盖
# ============================================================================
prometheus:
  enabled: true
  server:
    port: 8001                     # 避免端口冲突
    
  pushgateway:
    enabled: false                 # 开发环境关闭pushgateway
    
health_checks:
  checks:
    - name: "redis_connection"
      interval: 60                 # 开发环境降低检查频率
      
    - name: "disk_space"
      enabled: false               # 开发环境关闭磁盘检查

# ============================================================================
# 主机配置覆盖
# ============================================================================
deployment:
  default_strategy: "single_host"
  default_host: "docker-dev"

# ============================================================================
# 账户配置覆盖
# ============================================================================
accounts:
  # 开发环境账户限制
  global_overrides:
    trading:
      volume_limits:
        max_volume: 0.1            # 开发环境极小手数
        default_volume: 0.01
        
    risk_management:
      limits:
        max_daily_loss: 10.0       # 开发环境极小亏损限制
        max_positions: 2           # 最多2个持仓
        max_daily_trades: 10       # 最多10笔交易

# ============================================================================
# 跟单关系配置覆盖
# ============================================================================
copy_trading:
  global_settings:
    # 开发环境跟单限制
    system_limits:
      max_concurrent_relationships: 1  # 只允许一个跟单关系
      emergency_stop_enabled: true
      
    performance:
      batch_processing: false      # 开发环境关闭批处理
      
  # 关系特定覆盖
  relationship_overrides:
    "ACC001_to_ACC002":
      copy_ratio: 0.5              # 开发环境减半跟单
      
      limits:
        max_volume_per_trade: 0.1
        max_positions: 2
        max_daily_volume: 1.0
        
      execution:
        delay_ms: 200              # 增加延迟便于观察

# ============================================================================
# 功能开关覆盖
# ============================================================================
features:
  copy_trading: true
  distributed_deployment: false   # 开发环境关闭分布式
  advanced_monitoring: false      # 关闭高级监控
  api_gateway: true
  web_interface: true              # 开发环境启用web界面

# ============================================================================
# 网络配置覆盖
# ============================================================================
network:
  timeouts:
    connect: 5                     # 开发环境短超时
    read: 10
    write: 5
    
# ============================================================================
# 数据配置覆盖
# ============================================================================
data:
  persistence:
    backup_enabled: false          # 开发环境关闭备份
    
  cache:
    max_memory_mb: 128             # 开发环境小缓存

# ============================================================================
# 开发工具配置
# ============================================================================
development:
  # 热重载
  hot_reload:
    enabled: true
    watch_paths:
      - "config/"
      - "src/"
      
  # 模拟配置
  simulation:
    enabled: true
    mock_mt5_connections: false    # 设为true可模拟MT5连接
    mock_trading: false            # 设为true可模拟交易
    
  # 测试数据
  test_data:
    enabled: true
    sample_accounts: ["ACC001", "ACC002"]
    sample_trades: 10
    
# ============================================================================
# 环境变量默认值
# ============================================================================
environment_defaults:
  MT5_ENVIRONMENT: "development"
  MT5_DEBUG: "true"
  MT5_LOG_LEVEL: "DEBUG"
  MT5_MAX_WORKERS: "4"
  MT5_MEMORY_LIMIT: "1024"
  REDIS_HOST: "localhost"
  REDIS_DB: "1"
  NATS_HOST: "localhost"