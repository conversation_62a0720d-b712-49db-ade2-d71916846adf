groups:
- name: mt5_copy_trading_alerts
  rules:
  - alert: HighSignalProcessingLatency
    annotations:
      description: 95th percentile latency is {{ $value }}ms, which is above the 100ms
        threshold
      summary: High signal processing latency detected
    expr: histogram_quantile(0.95, mt5_signal_processing_duration_bucket) > 100
    for: 2m
    labels:
      severity: warning
  - alert: LowCopyExecutionSuccessRate
    annotations:
      description: Copy execution success rate is {{ $value | humanizePercentage }},
        which is below 95%
      summary: Low copy execution success rate
    expr: avg(mt5_copy_execution_success_rate) < 0.95
    for: 5m
    labels:
      severity: critical
  - alert: NATSConnectionDown
    annotations:
      description: NATS connection status is {{ $value }}, system may not be processing
        signals
      summary: NATS connection is down
    expr: mt5_nats_connection_status == 0
    for: 1m
    labels:
      severity: critical
  - alert: HighErrorRate
    annotations:
      description: Error rate is {{ $value }} errors/sec for component {{ $labels.component
        }}
      summary: High error rate detected
    expr: rate(mt5_errors_total[5m]) > 1
    for: 3m
    labels:
      severity: warning
  - alert: LargeSignalQueue
    annotations:
      description: Signal queue size is {{ $value }} for instance {{ $labels.instance_id
        }}
      summary: Large signal queue detected
    expr: mt5_signal_queue_size > 1000
    for: 2m
    labels:
      severity: warning
  - alert: LowTradeMatchingAccuracy
    annotations:
      description: Trade matching accuracy is {{ $value | humanizePercentage }} for
        algorithm {{ $labels.algorithm }}
      summary: Low trade matching accuracy
    expr: mt5_trade_matching_accuracy_ratio < 0.90
    for: 10m
    labels:
      severity: warning
