#!/usr/bin/env python3
"""
MT5交易系统工具类
为mt5_coordinator.py提供辅助功能和系统组件管理
注：不再作为系统主入口，仅为工具类
"""

import asyncio
import os
import time
from pathlib import Path
from typing import Dict, List, Optional, Set
from dataclasses import dataclass

from ..utils.logger import get_logger
from .config_manager import ConfigManager
from ..messaging.jetstream_client import JetStreamClient, JetStreamConfig
from .mt5_client import MT5Client
from ..monitors.master_monitor import OptimizedMasterMonitor
from ..executors.slave_executor import OptimizedSlaveExecutor

logger = get_logger(__name__)


@dataclass
class SystemStatus:
    """系统状态"""
    running: bool = False
    healthy: bool = False
    components_status: Dict[str, bool] = None
    last_health_check: float = 0
    error_count: int = 0
    
    def __post_init__(self):
        if self.components_status is None:
            self.components_status = {}


class TradingSystemHelper:
    """交易系统辅助工具类 """
    
    def __init__(self, config_path: str, host_id: str, dry_run: bool = False):
        # 首先加载环境变量
        from dotenv import load_dotenv
        load_dotenv()

        self.config_path = config_path
        self.host_id = host_id
        self.dry_run = dry_run
        
        # 系统状态
        self.status = SystemStatus()
        
        # 核心组件
        self.config_manager = None
        self.jetstream_client = None
        
        # 账户组件
        self.master_monitors: Dict[str, OptimizedMasterMonitor] = {}
        self.slave_executors: Dict[str, OptimizedSlaveExecutor] = {}
        self.mt5_clients: Dict[str, MT5Client] = {}
        
        # 账户信息
        self.detected_accounts: Dict[str, Dict] = {}
        
        logger.info(f"初始化交易系统辅助工具 - 主机: {host_id}, 干运行: {dry_run}")
    
    async def start(self, enable_coordinator: bool = True, 
                   master_accounts: List[str] = None,
                   slave_accounts: List[str] = None) -> bool:
        """启动系统"""
        logger.info("🚀 启动MT5交易系统辅助组件...")
        
        try:
            # 1. 初始化配置管理器
            if not await self._init_config_manager():
                return False
            
            # 2. 初始化基础设施
            if not await self._init_infrastructure():
                return False
            
            # 3. 检测和验证账户配置
            if not await self._detect_and_validate_accounts():
                return False
            
            # 4. 创建和连接MT5客户端
            if not await self._init_mt5_clients():
                return False

            # 5. 确定主从账户关系
            master_accounts, slave_accounts = self._determine_account_roles()

            # 6. 启动账户服务
            if not await self._start_account_services(master_accounts, slave_accounts):
                return False
            
            # 6. 启动健康监控
            asyncio.create_task(self._health_monitor_loop())
            
            self.status.running = True
            self.status.healthy = True
            
            logger.info("✅ 交易系统启动成功")
            return True
            
        except Exception as e:
            logger.error(f"❌ 系统启动失败: {e}")
            await self.stop()
            return False
    
    async def _init_config_manager(self) -> bool:
        """初始化配置管理器"""
        try:
            logger.info("📋 初始化配置管理器...")
            
            self.config_manager = ConfigManager(self.config_path)
            
            # 验证配置文件
            if not self.config_manager.validate_config():
                logger.error("❌ 配置文件验证失败")
                return False
            
            logger.info("✅ 配置管理器初始化成功")
            return True
            
        except Exception as e:
            logger.error(f"配置管理器初始化失败: {e}")
            return False
    
    async def _init_infrastructure(self) -> bool:
        """初始化基础设施"""
        try:
            logger.info("🔧 初始化基础设施...")
            
            # 初始化JetStream
            nats_config = self.config_manager.get('nats', {})
            jetstream_config = JetStreamConfig(
                servers=nats_config.get('servers', ['nats://localhost:4222']),
                stream_name="MT5_SIGNALS",
                subjects=[
                    "MT5.SIGNALS.*",
                    "MT5.RPC.*", 
                    "MT5.CONTROL.*",
                    "MT5.MONITOR.*"
                ],
                max_age=nats_config.get('jetstream', {}).get('max_age', 3600),
                max_msgs=nats_config.get('jetstream', {}).get('max_msgs', 1000000)
            )
            
            self.jetstream_client = JetStreamClient(jetstream_config)
            
            if not await self.jetstream_client.connect():
                logger.error("❌ JetStream连接失败")
                return False
            
            self.status.components_status['jetstream'] = True
            logger.info("✅ 基础设施初始化成功")
            return True
            
        except Exception as e:
            logger.error(f"基础设施初始化失败: {e}")
            return False
    
    async def _detect_and_validate_accounts(self) -> bool:
        """检测和验证账户配置"""
        try:
            logger.info("👥 检测和验证账户配置...")
            
            # 从配置文件检测账户
            accounts_dir = Path("config/accounts")
            if not accounts_dir.exists():
                logger.warning("账户配置目录不存在")
                return True  # 允许无账户配置启动
            
            for account_file in accounts_dir.glob("*.yaml"):
                try:
                    account_config = self.config_manager.load_account_config(account_file)
                    account_id = account_config.get('account', {}).get('id')
                    
                    if not account_id:
                        logger.warning(f"账户配置缺少ID: {account_file}")
                        continue
                    
                    # 验证账户配置
                    if not self._validate_account_config(account_config):
                        logger.warning(f"账户配置验证失败: {account_id}")
                        continue
                    
                    # 检查主机匹配
                    account_host = account_config.get('deployment', {}).get('host_id')
                    if account_host and account_host != self.host_id:
                        logger.debug(f"跳过其他主机账户: {account_id} (主机: {account_host})")
                        continue
                    
                    self.detected_accounts[account_id] = {
                        'config': account_config,
                        'file_path': str(account_file),
                        'enabled': account_config.get('account', {}).get('enabled', True),
                        'type': account_config.get('account', {}).get('type', 'auto'),
                        'host_id': account_host or self.host_id
                    }
                    
                    logger.info(f"检测到账户: {account_id} (类型: {self.detected_accounts[account_id]['type']})")
                    
                except Exception as e:
                    logger.error(f"加载账户配置失败 {account_file}: {e}")
            
            logger.info(f"✅ 检测到 {len(self.detected_accounts)} 个有效账户配置")
            return True
            
        except Exception as e:
            logger.error(f"账户配置检测失败: {e}")
            return False
    
    def _validate_account_config(self, config: Dict) -> bool:
        """验证账户配置"""
        try:
            account = config.get('account', {})
            mt5_config = config.get('mt5', {}).get('connection', {})
            
            # 检查必需字段
            required_fields = ['id', 'enabled']
            for field in required_fields:
                if field not in account:
                    logger.error(f"账户配置缺少必需字段: {field}")
                    return False
            
            # 检查MT5连接配置
            if account.get('enabled', False):
                mt5_required = ['login', 'server']
                for field in mt5_required:
                    if field not in mt5_config:
                        logger.error(f"MT5配置缺少必需字段: {field}")
                        return False
                
                # 检查密码环境变量
                account_id = account['id']
                password_env = f"MT5_{account_id}_PASSWORD"
                if not os.environ.get(password_env):
                    logger.error(f"缺少密码环境变量: {password_env}")
                    return False
            
            return True
            
        except Exception as e:
            logger.error(f"账户配置验证异常: {e}")
            return False

    def _determine_account_roles(self) -> tuple:
        """确定主从账户关系"""
        try:
            # 获取跟单关系配置
            copy_relationships = self.config_manager.get_copy_relationships()

            master_accounts = set()
            slave_accounts = set()

            # 从跟单关系中确定角色
            for rel in copy_relationships:
                if rel.get('enabled', True):
                    master_account = rel.get('master_account')
                    slave_account = rel.get('slave_account')

                    if master_account and slave_account:
                        # 检查账户是否在检测到的账户中
                        if master_account in self.detected_accounts:
                            master_accounts.add(master_account)
                        if slave_account in self.detected_accounts:
                            slave_accounts.add(slave_account)

            # 从账户配置中确定角色（作为备选）
            for account_id, account_info in self.detected_accounts.items():
                account_type = account_info.get('type', 'auto')
                if account_type == 'master':
                    master_accounts.add(account_id)
                elif account_type == 'slave':
                    slave_accounts.add(account_id)

            master_list = list(master_accounts)
            slave_list = list(slave_accounts)

            logger.info(f"确定账户角色: 主账户={master_list}, 从账户={slave_list}")

            return master_list, slave_list

        except Exception as e:
            logger.error(f"确定账户角色失败: {e}")
            return [], []

    async def _init_mt5_clients(self) -> bool:
        """初始化MT5客户端"""
        try:
            logger.info("🔌 初始化MT5客户端...")
            
            for account_id, account_info in self.detected_accounts.items():
                if not account_info['enabled']:
                    continue
                
                # 创建MT5客户端
                mt5_client = await self._create_mt5_client(account_id, account_info)
                if mt5_client:
                    self.mt5_clients[account_id] = mt5_client
                    logger.info(f"✅ MT5客户端创建成功: {account_id}")
                else:
                    logger.error(f"❌ MT5客户端创建失败: {account_id}")
                    if not self.dry_run:  # 干运行模式下允许MT5连接失败
                        return False
            
            logger.info(f"✅ 初始化了 {len(self.mt5_clients)} 个MT5客户端")
            return True
            
        except Exception as e:
            logger.error(f"MT5客户端初始化失败: {e}")
            return False
    
    async def _create_mt5_client(self, account_id: str, account_info: Dict) -> Optional[MT5Client]:
        """创建MT5客户端"""
        try:
            if self.dry_run:
                logger.info(f"干运行模式 - 跳过MT5客户端创建: {account_id}")
                return None
            
            account_config = account_info['config']
            mt5_config = account_config.get('mt5', {}).get('connection', {})
            
            # 获取密码
            password = os.environ.get(f"MT5_{account_id}_PASSWORD")
            if not password:
                logger.error(f"未找到账户密码环境变量: MT5_{account_id}_PASSWORD")
                return None
            
            # 创建MT5客户端
            mt5_client = MT5Client(
                account_id=account_id,
                login=mt5_config.get('login'),
                password=password,
                server=mt5_config.get('server'),
                terminal_path=mt5_config.get('terminal_path')
            )
            
            # 连接MT5
            if await mt5_client.connect():
                # 验证连接状态
                if await mt5_client.verify_connection():
                    return mt5_client
                else:
                    logger.error(f"MT5连接验证失败: {account_id}")
                    await mt5_client.disconnect()
                    return None
            else:
                logger.error(f"MT5连接失败: {account_id}")
                return None
                
        except Exception as e:
            logger.error(f"创建MT5客户端异常 {account_id}: {e}")
            return None
    
    def is_running(self) -> bool:
        """检查系统是否运行中"""
        return self.status.running

    @property
    def running(self) -> bool:
        """运行状态属性"""
        return self.status.running
    
    def is_healthy(self) -> bool:
        """检查系统是否健康"""
        return self.status.healthy
    
    async def recover(self) -> bool:
        """系统恢复"""
        logger.info("🔄 尝试系统恢复...")
        
        try:
            # 检查并恢复JetStream连接
            if not self.jetstream_client or not self.jetstream_client.is_connected():
                logger.info("重新连接JetStream...")
                if self.jetstream_client:
                    await self.jetstream_client.disconnect()
                
                if not await self._init_infrastructure():
                    return False
            
            # 检查并恢复MT5连接
            for account_id, mt5_client in self.mt5_clients.items():
                if not mt5_client.is_connected():
                    logger.info(f"重新连接MT5客户端: {account_id}")
                    if not await mt5_client.reconnect():
                        logger.error(f"MT5客户端恢复失败: {account_id}")
                        return False
            
            self.status.healthy = True
            self.status.error_count = 0
            logger.info("✅ 系统恢复成功")
            return True
            
        except Exception as e:
            logger.error(f"系统恢复失败: {e}")
            return False

    async def _start_account_services(self, master_accounts: List[str] = None,
                                    slave_accounts: List[str] = None) -> bool:
        """启动账户服务"""
        try:
            logger.info("⚡ 启动账户服务...")

            for account_id, account_info in self.detected_accounts.items():
                if not account_info['enabled']:
                    logger.info(f"跳过禁用账户: {account_id}")
                    continue

                account_type = account_info['type']
                mt5_client = self.mt5_clients.get(account_id)

                # 启动主账户监控器
                if (account_type in ['master', 'auto'] and
                    (master_accounts is None or account_id in master_accounts)):

                    if await self._start_master_monitor(account_id, account_info, mt5_client):
                        logger.info(f"📊 主监控器启动成功: {account_id}")
                    else:
                        logger.error(f"❌ 主监控器启动失败: {account_id}")
                        return False

                # 启动从账户执行器
                if (account_type in ['slave', 'auto'] and
                    (slave_accounts is None or account_id in slave_accounts)):

                    if await self._start_slave_executor(account_id, account_info, mt5_client):
                        logger.info(f"⚡ 从执行器启动成功: {account_id}")
                    else:
                        logger.error(f"❌ 从执行器启动失败: {account_id}")
                        return False

            logger.info(f"✅ 启动了 {len(self.master_monitors)} 个主监控器，{len(self.slave_executors)} 个从执行器")
            return True

        except Exception as e:
            logger.error(f"账户服务启动失败: {e}")
            return False

    async def _start_master_monitor(self, account_id: str, account_info: Dict,
                                  mt5_client: Optional[MT5Client]) -> bool:
        """启动主账户监控器"""
        try:
            monitor = OptimizedMasterMonitor(
                account_id=account_id,
                jetstream_client=self.jetstream_client,
                config_manager=self.config_manager,
                mt5_client=mt5_client,
                host_id=self.host_id
            )

            if await monitor.start():
                self.master_monitors[account_id] = monitor
                self.status.components_status[f'master_{account_id}'] = True
                return True
            else:
                return False

        except Exception as e:
            logger.error(f"启动主监控器失败 {account_id}: {e}")
            return False

    async def _start_slave_executor(self, account_id: str, account_info: Dict,
                                  mt5_client: Optional[MT5Client]) -> bool:
        """启动从账户执行器"""
        try:
            executor = OptimizedSlaveExecutor(
                account_id=account_id,
                jetstream_client=self.jetstream_client,
                config_manager=self.config_manager,
                mt5_client=mt5_client,
                host_id=self.host_id
            )

            if await executor.start():
                self.slave_executors[account_id] = executor
                self.status.components_status[f'slave_{account_id}'] = True
                return True
            else:
                return False

        except Exception as e:
            logger.error(f"启动从执行器失败 {account_id}: {e}")
            return False

    async def _health_monitor_loop(self):
        """健康监控循环"""
        logger.info("🏥 启动健康监控")

        while self.status.running:
            try:
                await asyncio.sleep(30)  # 每30秒检查一次

                if not await self._perform_health_check():
                    self.status.error_count += 1
                    logger.warning(f"健康检查失败 (错误计数: {self.status.error_count})")

                    if self.status.error_count >= 3:
                        self.status.healthy = False
                        logger.error("系统健康状态异常")
                else:
                    self.status.error_count = 0
                    self.status.healthy = True

                self.status.last_health_check = time.time()

            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"健康监控异常: {e}")
                self.status.error_count += 1

    async def _perform_health_check(self) -> bool:
        """执行健康检查"""
        try:
            # 检查JetStream连接
            if not self.jetstream_client or not self.jetstream_client.is_connected():
                logger.warning("JetStream连接异常")
                return False

            # 检查MT5客户端连接
            for account_id, mt5_client in self.mt5_clients.items():
                if not mt5_client.is_connected():
                    logger.warning(f"MT5客户端连接异常: {account_id}")
                    return False

            # 检查监控器状态
            for account_id, monitor in self.master_monitors.items():
                if not monitor.is_healthy():
                    logger.warning(f"主监控器状态异常: {account_id}")
                    return False

            # 检查执行器状态
            for account_id, executor in self.slave_executors.items():
                if not executor.is_healthy():
                    logger.warning(f"从执行器状态异常: {account_id}")
                    return False

            return True

        except Exception as e:
            logger.error(f"健康检查异常: {e}")
            return False

    async def stop(self):
        """停止系统"""
        logger.info("🛑 停止优化交易系统...")
        self.status.running = False

        try:
            # 停止账户服务
            for monitor in self.master_monitors.values():
                try:
                    await monitor.stop()
                except Exception as e:
                    logger.error(f"停止主监控器失败: {e}")

            for executor in self.slave_executors.values():
                try:
                    await executor.stop()
                except Exception as e:
                    logger.error(f"停止从执行器失败: {e}")

            # 断开MT5连接
            for account_id, mt5_client in self.mt5_clients.items():
                try:
                    await mt5_client.disconnect()
                    logger.info(f"MT5客户端已断开: {account_id}")
                except Exception as e:
                    logger.error(f"断开MT5客户端失败 {account_id}: {e}")

            # 断开JetStream连接
            if self.jetstream_client:
                try:
                    await self.jetstream_client.disconnect()
                    logger.info("JetStream连接已断开")
                except Exception as e:
                    logger.error(f"断开JetStream连接失败: {e}")

            logger.info("✅ 优化交易系统已停止")

        except Exception as e:
            logger.error(f"系统停止异常: {e}")

    def get_system_status(self) -> Dict:
        """获取系统状态"""
        return {
            'running': self.status.running,
            'healthy': self.status.healthy,
            'host_id': self.host_id,
            'dry_run': self.dry_run,
            'detected_accounts': len(self.detected_accounts),
            'master_monitors': len(self.master_monitors),
            'slave_executors': len(self.slave_executors),
            'mt5_clients': len(self.mt5_clients),
            'components_status': self.status.components_status,
            'last_health_check': self.status.last_health_check,
            'error_count': self.status.error_count
        }
