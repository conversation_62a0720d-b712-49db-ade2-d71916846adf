#!/usr/bin/env python3
# 🚨 AUTO-MIGRATED: Old messaging components replaced with HybridQueueManager
# See MIGRATION_GUIDE.md for details

"""
性能优化组件集成
整合Protocol Buffers、优先级队列、连接池等性能优化
"""
import asyncio
import time
from typing import Dict, Any, List, Optional, Union
from dataclasses import dataclass

from ..messaging.message_envelope import get_protobuf_codec, ProtobufConfig
from ..messaging.priority_queue import get_priority_queue, MessagePriority
from ..infrastructure.connection_pool import get_connection_pool
from ..utils.logger import get_logger
from ..utils.metrics import get_metrics_collector
from ..messaging.message_types import TradeSignal

logger = get_logger(__name__)
metrics = get_metrics_collector()


@dataclass
class PerformanceConfig:
    """性能配置"""
    # Protocol Buffers配置
    use_protobuf: bool = True
    compression_enabled: bool = True
    compression_threshold: int = 1024
    
    # 优先级队列配置
    use_priority_queue: bool = True
    queue_worker_count: int = 4
    
    # 连接池配置
    use_connection_pool: bool = True
    max_connections: int = 10
    max_idle_time: int = 300
    
    # 批处理配置
    batch_enabled: bool = True
    batch_size: int = 50
    batch_timeout_ms: int = 100
    
    # 缓存配置
    cache_enabled: bool = True
    cache_ttl: int = 300


class OptimizedMessageProcessor:
    """优化的消息处理器"""
    
    def __init__(self, config: PerformanceConfig = None):
        self.config = config or PerformanceConfig()
        
        # 初始化组件
        self.message_envelope = None
        self.priority_queue = None
        self.connection_pool = None
        
        # 流式批量处理器
        from src.utils.batch_processor import StreamBatchProcessor
        self._stream_processor = StreamBatchProcessor(
            batch_size=self.config.batch_size,
            flush_interval=0.004  # 4ms刷新间隔
        )
        self._stream_processor.set_processor(self._process_signal_batch)
        
        # 缓存
        self._cache: Dict[str, Any] = {}
        self._cache_timestamps: Dict[str, float] = {}
        
        # 统计信息
        self._stats = {
            'messages_processed': 0,
            'messages_batched': 0,
            'cache_hits': 0,
            'cache_misses': 0,
            'compression_ratio': 0.0,
            'avg_processing_time_ms': 0.0
        }
        
        self._running = False
        
    async def initialize(self):
        """初始化优化组件"""
        logger.info("初始化性能优化组件...")
        
        try:
            # 初始化Protocol Buffers编解码器
            if self.config.use_protobuf:
                protobuf_config = ProtobufConfig(
                    compression_enabled=self.config.compression_enabled,
                    compression_threshold=self.config.compression_threshold
                )
                self.message_envelope = get_protobuf_codec()
                logger.info("Protocol Buffers编解码器已初始化")
            
            if self.config.use_priority_queue:
                self.priority_queue = get_priority_queue()
                await self.priority_queue.start_workers(self.config.queue_worker_count)
                
                self.priority_queue.register_processor(
                    MessagePriority.CRITICAL, self._process_critical_message
                )
                self.priority_queue.register_processor(
                    MessagePriority.HIGH, self._process_high_message
                )
                self.priority_queue.register_processor(
                    MessagePriority.NORMAL, self._process_normal_message
                )
                self.priority_queue.register_processor(
                    MessagePriority.REALTIME_QUERY, self._process_realtime_query_message
                )
                self.priority_queue.register_processor(
                    MessagePriority.LOW, self._process_low_message
                )
                
                logger.info("优先级队列已初始化")
            
            if self.config.use_connection_pool:
                self.connection_pool = get_connection_pool()
                
                self.connection_pool.set_connection_factory(self._create_mt5_connection)
                self.connection_pool.set_connection_validator(self._validate_mt5_connection)
                
                await self.connection_pool.start()
                logger.info("连接池已初始化")
            
            if self.config.batch_enabled:
                self._batch_task = asyncio.create_task(self._batch_processor())
                logger.info("批处理器已启动")
            
            self._running = True
            logger.info("🎉 性能优化组件初始化完成")
            
        except Exception as e:
            logger.error(f"性能优化组件初始化失败: {e}")
            raise
    
    async def shutdown(self):
        """关闭优化组件"""
        logger.info("关闭性能优化组件...")
        
        self._running = False
        
        try:
            if self._batch_task:
                self._batch_task.cancel()
                try:
                    await self._batch_task
                except asyncio.CancelledError:
                    pass
            
            if self.priority_queue:
                await self.priority_queue.stop_workers()
            
            if self.connection_pool:
                await self.connection_pool.stop()
            
            logger.info("性能优化组件已关闭")
            
        except Exception as e:
            logger.error(f"关闭性能优化组件失败: {e}")
    
    async def process_trade_signal(self, signal: Union[TradeSignal, Dict[str, Any]], 
                                  priority: MessagePriority = MessagePriority.NORMAL) -> bool:
        """
        处理交易信号
        """
        start_time = time.time()
        
        try:
            if isinstance(signal, TradeSignal):
                signal_dict = signal.__dict__
            else:
                signal_dict = signal
            
            signal_priority = self._determine_priority(signal_dict)
            if priority != MessagePriority.NORMAL:
                signal_priority = priority
            
            if self.priority_queue:
                success = await self.priority_queue.enqueue(
                    signal_priority, signal_dict, 
                    signal_dict.get('signal_id')
                )
                
                if success:
                    self._stats['messages_processed'] += 1
                    
                    processing_time = (time.time() - start_time) * 1000
                    self._update_avg_processing_time(processing_time)
                    
                    metrics.increment_counter("optimized_processor_processed_total")
                    metrics.record_histogram("optimized_processor_processing_time_ms", processing_time)
                    
                    return True
                else:
                    logger.warning(f"信号入队失败: {signal_dict.get('signal_id')}")
                    return False
            else:
                # 直接处理
                await self._process_signal_direct(signal_dict)
                return True
                
        except Exception as e:
            logger.error(f"处理交易信号失败: {e}")
            metrics.increment_counter("optimized_processor_error_total")
            return False
    
    async def process_batch_signals(self, signals: List[Union[TradeSignal, Dict[str, Any]]]) -> bool:
        """
        批量处理交易信号（使用流式批量处理器）
        """
        try:
            if not self.config.batch_enabled:
                results = []
                for signal in signals:
                    result = await self.process_trade_signal(signal)
                    results.append(result)
                return all(results)

            for signal in signals:
                if isinstance(signal, TradeSignal):
                    signal_dict = signal.__dict__
                else:
                    signal_dict = signal

                await self._stream_processor.process_item(signal_dict)

            self._stats['messages_batched'] += len(signals)
            return True

        except Exception as e:
            logger.error(f"批量处理信号失败: {e}")
            return False

    async def _process_signal_batch(self, signals: List[Dict[str, Any]]):
        """处理信号批次"""
        try:
            for signal_dict in signals:
                await self.process_trade_signal(signal_dict)
        except Exception as e:
            logger.error(f"流式批量处理信号失败: {e}")
    
    def _determine_priority(self, signal_dict: Dict[str, Any]) -> MessagePriority:
        """确定信号优先级 - 基于5级优先级系统"""
        action = signal_dict.get('action', '').upper()
        signal_type = signal_dict.get('signal_type', '').upper()
        
        # CRITICAL (0) - 系统级风险
        if action in ['EMERGENCY_STOP', 'FORCE_CLOSE', 'MARGIN_CALL'] or \
           signal_type in ['EMERGENCY_CLOSE', 'STOP_LOSS_HIT', 'RISK_LIMIT_HIT']:
            return MessagePriority.CRITICAL
        
        # HIGH (1) - 订单级风控
        elif action in ['CLOSE', 'CLOSE_ALL', 'CANCEL', 'MODIFY_SL', 'MODIFY_TP'] or \
             signal_type in ['POSITION_CLOSE', 'POSITION_MODIFY', 'ORDER_CANCEL']:
            return MessagePriority.HIGH
        
        # NORMAL (2) - 策略信号执行
        elif action in ['BUY', 'SELL', 'BUY_LIMIT', 'SELL_LIMIT', 'OPEN'] or \
             signal_type == 'POSITION_OPEN':
            return MessagePriority.NORMAL
        
        # REALTIME_QUERY (3) - 实时数据查询
        elif action in ['QUERY', 'GET_TICK', 'GET_ACCOUNT', 'GET_POSITIONS'] or \
             signal_dict.get('query_type') in ['tick_data', 'account_info', 'positions', 'orders', 'connection_status']:
            return MessagePriority.REALTIME_QUERY
        
        # LOW (4) - 后台任务
        else:
            return MessagePriority.LOW
    
    async def _process_critical_message(self, data: Dict[str, Any]):
        """处理关键优先级消息"""
        logger.info(f"处理关键信号: {data.get('signal_id')}")
        await self._process_signal_with_connection(data)
    
    async def _process_high_message(self, data: Dict[str, Any]):
        """处理高优先级消息"""
        logger.debug(f"处理高优先级信号: {data.get('signal_id')}")
        await self._process_signal_with_connection(data)
    
    async def _process_normal_message(self, data: Dict[str, Any]):
        """处理普通优先级消息"""
        logger.debug(f"处理普通信号: {data.get('signal_id')}")
        await self._process_signal_with_connection(data)
    
    async def _process_realtime_query_message(self, data: Dict[str, Any]):
        """处理实时查询优先级消息"""
        logger.debug(f"处理实时查询: {data.get('signal_id', data.get('query_type'))}")
        # 实时查询通常不需要完整的信号处理流程
        if data.get('query_type') in ['tick_data', 'account_info', 'positions']:
            # 直接路由到相应的查询处理器
            await self._handle_realtime_query(data)
        else:
            # 其他类型使用标准信号处理
            await self._process_signal_with_connection(data)
    
    async def _process_low_message(self, data: Dict[str, Any]):
        """处理低优先级消息"""
        logger.debug(f"处理低优先级信号: {data.get('signal_id')}")
        await self._process_signal_with_connection(data)
    
    async def _handle_realtime_query(self, data: Dict[str, Any]):
        """处理实时查询请求"""
        query_type = data.get('query_type')
        account_id = data.get('account_id')
        
        try:
            # 使用连接池获取连接
            if self.config.use_connection_pool and self.connection_pool:
                async with self.connection_pool.get_connection(account_id) as conn:
                    result = await self._execute_query(conn, query_type, data)
            else:
                # 直接执行查询
                result = await self._execute_query(None, query_type, data)
            
            # 发送查询结果
            if data.get('callback'):
                await data['callback'](result)
                
        except Exception as e:
            logger.error(f"实时查询失败 {query_type}: {e}")
            if data.get('callback'):
                await data['callback']({'error': str(e)})
    
    async def _execute_query(self, conn, query_type: str, data: Dict[str, Any]) -> Dict[str, Any]:
        """执行具体的查询操作"""
        # 这里应该根据查询类型调用相应的MT5 API
        # 例如: tick_data -> MT5.symbol_info_tick()
        #      account_info -> MT5.account_info()
        #      positions -> MT5.positions_get()
        return {'status': 'success', 'type': query_type, 'data': {}}
    
    async def _process_signal_with_connection(self, signal_dict: Dict[str, Any]):
        """使用连接池处理信号"""
        account_id = signal_dict.get('account_id')
        
        if not account_id:
            logger.error("信号缺少账户ID")
            return
        
        if self.connection_pool:
            # 使用连接池
            async with self.connection_pool.get_connection(account_id) as connection:
                if connection:
                    await self._execute_signal(connection.connection_object, signal_dict)
                else:
                    logger.error(f"无法获取连接 (账户: {account_id})")
        else:
            # 直接处理
            await self._process_signal_direct(signal_dict)
    
    async def _process_signal_direct(self, signal_dict: Dict[str, Any]):
        """直接处理信号"""
        # 这里实现实际的信号处理逻辑
        logger.debug(f"直接处理信号: {signal_dict.get('signal_id')}")
        
        # 模拟处理时间
        await asyncio.sleep(0.001)
    
    async def _execute_signal(self, connection, signal_dict: Dict[str, Any]):
        """执行信号"""
        # 这里实现实际的MT5信号执行逻辑
        logger.debug(f"执行信号: {signal_dict.get('signal_id')}")
        
        # 模拟执行时间
        await asyncio.sleep(0.005)
    
    async def _create_mt5_connection(self, account_id: str, server: str = None, login: int = None):
        """创建MT5连接"""
        # 这里实现实际的MT5连接创建逻辑
        logger.debug(f"创建MT5连接 (账户: {account_id})")
        
        # 模拟连接对象
        class MockMT5Connection:
            def __init__(self, account_id):
                self.account_id = account_id
                self.connected = True
            
            async def close(self):
                self.connected = False
        
        return MockMT5Connection(account_id)
    
    async def _validate_mt5_connection(self, connection) -> bool:
        """验证MT5连接"""
        # 这里实现实际的连接验证逻辑
        return hasattr(connection, 'connected') and connection.connected
    
    async def _batch_processor(self):
        """批处理器"""
        logger.info("🔄 启动批处理器")
        
        while self._running:
            try:
                await asyncio.sleep(self.config.batch_timeout_ms / 1000.0)
                
                async with self._batch_lock:
                    if self._batch_buffer:
                        await self._flush_batch()
                        
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"批处理器异常: {e}")
        
        logger.info("批处理器停止")
    
    async def _flush_batch(self):
        """刷新批处理缓冲区"""
        if not self._batch_buffer:
            return
        
        batch_size = len(self._batch_buffer)
        logger.debug(f"处理批次: {batch_size} 个信号")
        
        # 编码批次
        if self.message_envelope:
            try:
                encoded_data = self.message_envelope.encode_batch_signals(self._batch_buffer)
                
                # 计算压缩比
                original_size = len(str(self._batch_buffer).encode('utf-8'))
                compressed_size = len(encoded_data)
                compression_ratio = compressed_size / original_size if original_size > 0 else 1.0
                
                self._stats['compression_ratio'] = compression_ratio
                
                logger.debug(f"批次编码完成: {original_size} -> {compressed_size} bytes "
                           f"(压缩比: {compression_ratio:.2f})")
                
            except Exception as e:
                logger.error(f"批次编码失败: {e}")
        
        # 处理批次中的每个信号
        for signal_dict in self._batch_buffer:
            await self._process_signal_direct(signal_dict)
        
        # 清空缓冲区
        self._batch_buffer.clear()
        
        metrics.increment_counter("optimized_processor_batch_processed_total")
        metrics.record_histogram("optimized_processor_batch_size", batch_size)
    
    def _update_avg_processing_time(self, processing_time_ms: float):
        """更新平均处理时间"""
        current_avg = self._stats['avg_processing_time_ms']
        total_processed = self._stats['messages_processed']
        
        if total_processed == 1:
            self._stats['avg_processing_time_ms'] = processing_time_ms
        else:
            # 计算移动平均
            self._stats['avg_processing_time_ms'] = (
                (current_avg * (total_processed - 1) + processing_time_ms) / total_processed
            )
    
    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        stats = dict(self._stats)
        
        # 添加组件统计
        if self.priority_queue:
            stats['priority_queue'] = self.priority_queue.get_stats()
        
        if self.connection_pool:
            stats['connection_pool'] = self.connection_pool.get_stats()
        
        stats['batch_buffer_size'] = len(self._batch_buffer)
        stats['cache_size'] = len(self._cache)
        
        return stats


# 全局优化处理器实例
_optimized_processor = None

def get_optimized_processor() -> OptimizedMessageProcessor:
    """获取全局优化处理器实例"""
    global _optimized_processor
    if _optimized_processor is None:
        _optimized_processor = OptimizedMessageProcessor()
    return _optimized_processor
