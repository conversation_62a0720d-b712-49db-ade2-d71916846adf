#!/usr/bin/env python3
"""
异步IO批量命令执行功能单元测试
"""
import pytest
import asyncio
import time
from unittest.mock import Mock, AsyncMock, patch

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.infrastructure.connection_pool import (
    MT5ConnectionPool, BatchCommand, BatchResult, 
    ConnectionInfo, ConnectionStatus
)


class TestAsyncBatchCommands:
    """测试异步批量命令执行功能"""

    @pytest.fixture
    def connection_pool(self):
        """创建连接池实例"""
        pool = MT5ConnectionPool(max_connections=5, max_idle_time=300)
        pool.batch_size = 5
        pool.batch_timeout = 1.0
        return pool

    @pytest.fixture
    def mock_connection_factory(self):
        """模拟连接工厂"""
        async def factory(account_id, server=None, login=None):
            mock_connection = Mock()
            mock_connection.account_id = account_id
            mock_connection.server = server
            mock_connection.login = login
            
            # 模拟MT5方法
            mock_connection.order_send = AsyncMock(return_value={'retcode': 10009})
            mock_connection.positions_get = AsyncMock(return_value=[])
            mock_connection.history_orders_get = AsyncMock(return_value=[])
            mock_connection.account_info = AsyncMock(return_value={'balance': 10000})
            
            return mock_connection
        
        return factory

    @pytest.fixture
    def mock_connection_validator(self):
        """模拟连接验证器"""
        async def validator(connection):
            return True
        return validator

    @pytest.mark.asyncio
    async def test_batch_processor_initialization(self, connection_pool):
        """测试批量处理器初始化"""
        # 设置工厂和验证器
        connection_pool.set_connection_factory(AsyncMock())
        connection_pool.set_connection_validator(AsyncMock(return_value=True))
        
        await connection_pool.start()
        
        # 检查批量处理器状态
        assert connection_pool.batch_processor_running is True
        assert hasattr(connection_pool, '_batch_processor_task')
        assert connection_pool.command_queue is not None
        
        await connection_pool.stop()

    @pytest.mark.asyncio
    async def test_batch_command_creation(self):
        """测试批量命令创建"""
        command = BatchCommand(
            command_id="test_cmd_001",
            command_type="order_send",
            parameters={'symbol': 'EURUSD', 'volume': 0.1},
            priority=1,
            timeout=30.0
        )
        
        assert command.command_id == "test_cmd_001"
        assert command.command_type == "order_send"
        assert command.parameters['symbol'] == 'EURUSD'
        assert command.priority == 1
        assert command.retry_count == 0
        assert command.max_retries == 3

    @pytest.mark.asyncio
    async def test_single_command_execution(self, connection_pool, mock_connection_factory, mock_connection_validator):
        """测试单个命令执行"""
        # 设置连接池
        connection_pool.set_connection_factory(mock_connection_factory)
        connection_pool.set_connection_validator(mock_connection_validator)
        
        await connection_pool.start()
        
        # 创建测试命令
        command = BatchCommand(
            command_id="test_001",
            command_type="order_send",
            parameters={
                'account_id': 'test_account',
                'symbol': 'EURUSD',
                'volume': 0.1,
                'price': 1.1000
            }
        )
        
        # 执行批量命令
        results = await connection_pool.batch_execute_commands([command])
        
        # 验证结果
        assert len(results) == 1
        result = results[0]
        assert result.command_id == "test_001"
        assert result.success is True
        assert result.error is None
        
        await connection_pool.stop()

    @pytest.mark.asyncio
    async def test_multiple_command_execution(self, connection_pool, mock_connection_factory, mock_connection_validator):
        """测试多个命令执行"""
        # 设置连接池
        connection_pool.set_connection_factory(mock_connection_factory)
        connection_pool.set_connection_validator(mock_connection_validator)
        
        await connection_pool.start()
        
        # 创建多个测试命令
        commands = []
        for i in range(5):
            command = BatchCommand(
                command_id=f"test_{i:03d}",
                command_type="positions_get",
                parameters={'account_id': 'test_account'}
            )
            commands.append(command)
        
        # 执行批量命令
        results = await connection_pool.batch_execute_commands(commands)
        
        # 验证结果
        assert len(results) == 5
        for i, result in enumerate(results):
            assert result.command_id == f"test_{i:03d}"
            assert result.success is True
        
        await connection_pool.stop()

    @pytest.mark.asyncio
    async def test_command_priority_ordering(self, connection_pool, mock_connection_factory, mock_connection_validator):
        """测试命令优先级排序"""
        # 设置连接池
        connection_pool.set_connection_factory(mock_connection_factory)
        connection_pool.set_connection_validator(mock_connection_validator)
        
        await connection_pool.start()
        
        # 创建不同优先级的命令
        commands = [
            BatchCommand(command_id="low", command_type="account_info", 
                        parameters={'account_id': 'test'}, priority=3),
            BatchCommand(command_id="high", command_type="account_info", 
                        parameters={'account_id': 'test'}, priority=1),
            BatchCommand(command_id="medium", command_type="account_info", 
                        parameters={'account_id': 'test'}, priority=2)
        ]
        
        # 执行批量命令
        results = await connection_pool.batch_execute_commands(commands)
        
        # 验证所有命令都执行成功
        assert len(results) == 3
        for result in results:
            assert result.success is True
        
        await connection_pool.stop()

    @pytest.mark.asyncio
    async def test_command_retry_mechanism(self, connection_pool):
        """测试命令重试机制"""
        # 创建会失败的连接工厂
        call_count = 0
        
        async def failing_factory(account_id, server=None, login=None):
            nonlocal call_count
            call_count += 1
            if call_count <= 2:  # 前两次失败
                raise RuntimeError("Connection failed")
            
            # 第三次成功
            mock_connection = Mock()
            mock_connection.account_id = account_id
            mock_connection.account_info = AsyncMock(return_value={'balance': 10000})
            return mock_connection
        
        connection_pool.set_connection_factory(failing_factory)
        connection_pool.set_connection_validator(AsyncMock(return_value=True))
        
        await connection_pool.start()
        
        # 创建命令
        command = BatchCommand(
            command_id="retry_test",
            command_type="account_info",
            parameters={'account_id': 'test'},
            max_retries=3
        )
        
        # 执行命令
        results = await connection_pool.batch_execute_commands([command])
        
        # 验证结果
        assert len(results) == 1
        result = results[0]
        # 由于连接获取失败，命令应该失败
        assert result.success is False
        assert "无法获取连接" in result.error
        
        await connection_pool.stop()

    @pytest.mark.asyncio 
    async def test_command_timeout_handling(self, connection_pool, mock_connection_factory):
        """测试命令超时处理"""
        # 创建超时验证器
        async def slow_validator(connection):
            await asyncio.sleep(2.0)  # 模拟慢验证
            return True
        
        connection_pool.set_connection_factory(mock_connection_factory)
        connection_pool.set_connection_validator(slow_validator)
        
        await connection_pool.start()
        
        # 创建短超时命令
        command = BatchCommand(
            command_id="timeout_test",
            command_type="account_info",
            parameters={'account_id': 'test'},
            timeout=1.0  # 1秒超时
        )
        
        # 执行命令（应该超时）
        start_time = time.time()
        results = await connection_pool.batch_execute_commands([command])
        execution_time = time.time() - start_time
        
        # 验证超时处理
        assert len(results) == 1
        assert execution_time < 35  # 应该在整体超时时间内完成
        
        await connection_pool.stop()

    @pytest.mark.asyncio
    async def test_different_command_types(self, connection_pool, mock_connection_factory, mock_connection_validator):
        """测试不同类型的命令"""
        connection_pool.set_connection_factory(mock_connection_factory)
        connection_pool.set_connection_validator(mock_connection_validator)
        
        await connection_pool.start()
        
        # 创建不同类型的命令
        commands = [
            BatchCommand(command_id="order", command_type="order_send", 
                        parameters={'account_id': 'test', 'symbol': 'EURUSD'}),
            BatchCommand(command_id="positions", command_type="positions_get", 
                        parameters={'account_id': 'test'}),
            BatchCommand(command_id="history", command_type="history_orders_get", 
                        parameters={'account_id': 'test'}),
            BatchCommand(command_id="account", command_type="account_info", 
                        parameters={'account_id': 'test'})
        ]
        
        # 执行批量命令
        results = await connection_pool.batch_execute_commands(commands)
        
        # 验证结果
        assert len(results) == 4
        for result in results:
            assert result.success is True
        
        await connection_pool.stop()

    @pytest.mark.asyncio
    async def test_batch_size_optimization(self, connection_pool, mock_connection_factory, mock_connection_validator):
        """测试批次大小优化"""
        connection_pool.batch_size = 3  # 设置小的批次大小
        connection_pool.set_connection_factory(mock_connection_factory)
        connection_pool.set_connection_validator(mock_connection_validator)
        
        await connection_pool.start()
        
        # 创建大量命令
        commands = []
        for i in range(10):
            command = BatchCommand(
                command_id=f"batch_test_{i:03d}",
                command_type="account_info",
                parameters={'account_id': 'test'}
            )
            commands.append(command)
        
        # 执行批量命令
        start_time = time.time()
        results = await connection_pool.batch_execute_commands(commands)
        execution_time = time.time() - start_time
        
        # 验证结果
        assert len(results) == 10
        for result in results:
            assert result.success is True
        
        # 检查执行时间（批量处理应该比单独处理更快）
        assert execution_time < 2.0  # 合理的批量处理时间
        
        await connection_pool.stop()

    @pytest.mark.asyncio
    async def test_concurrent_batch_execution(self, connection_pool, mock_connection_factory, mock_connection_validator):
        """测试并发批量执行"""
        connection_pool.set_connection_factory(mock_connection_factory)
        connection_pool.set_connection_validator(mock_connection_validator)
        
        await connection_pool.start()
        
        async def execute_batch(batch_id, num_commands):
            commands = []
            for i in range(num_commands):
                command = BatchCommand(
                    command_id=f"batch_{batch_id}_cmd_{i}",
                    command_type="account_info",
                    parameters={'account_id': f'test_account_{batch_id}'}
                )
                commands.append(command)
            
            return await connection_pool.batch_execute_commands(commands)
        
        # 并发执行多个批次
        tasks = []
        for batch_id in range(3):
            task = asyncio.create_task(execute_batch(batch_id, 5))
            tasks.append(task)
        
        # 等待所有批次完成
        all_results = await asyncio.gather(*tasks)
        
        # 验证结果
        assert len(all_results) == 3
        for batch_results in all_results:
            assert len(batch_results) == 5
            for result in batch_results:
                assert result.success is True
        
        await connection_pool.stop()

    @pytest.mark.asyncio
    async def test_command_grouping_by_type(self, connection_pool):
        """测试按类型分组命令"""
        # 设置mock连接工厂，记录执行顺序
        execution_order = []
        
        async def tracking_factory(account_id, server=None, login=None):
            mock_connection = Mock()
            
            async def track_order_send(**kwargs):
                execution_order.append('order_send')
                return {'retcode': 10009}
            
            async def track_positions_get(**kwargs):
                execution_order.append('positions_get')
                return []
            
            mock_connection.order_send = track_order_send
            mock_connection.positions_get = track_positions_get
            mock_connection.account_id = account_id
            
            return mock_connection
        
        connection_pool.set_connection_factory(tracking_factory)
        connection_pool.set_connection_validator(AsyncMock(return_value=True))
        
        await connection_pool.start()
        
        # 创建混合类型的命令
        commands = [
            BatchCommand(command_id="order1", command_type="order_send", 
                        parameters={'account_id': 'test'}),
            BatchCommand(command_id="pos1", command_type="positions_get", 
                        parameters={'account_id': 'test'}),
            BatchCommand(command_id="order2", command_type="order_send", 
                        parameters={'account_id': 'test'}),
            BatchCommand(command_id="pos2", command_type="positions_get", 
                        parameters={'account_id': 'test'})
        ]
        
        # 执行批量命令
        results = await connection_pool.batch_execute_commands(commands)
        
        # 验证结果
        assert len(results) == 4
        for result in results:
            assert result.success is True
        
        # 验证相同类型的命令被分组执行
        assert len(execution_order) == 4
        
        await connection_pool.stop()

    @pytest.mark.asyncio
    async def test_batch_processor_queue_management(self, connection_pool, mock_connection_factory, mock_connection_validator):
        """测试批量处理器队列管理"""
        connection_pool.batch_size = 2  # 小批次大小
        connection_pool.batch_timeout = 0.5  # 短超时
        connection_pool.set_connection_factory(mock_connection_factory)
        connection_pool.set_connection_validator(mock_connection_validator)
        
        await connection_pool.start()
        
        # 验证队列初始状态
        assert connection_pool.command_queue.qsize() == 0
        
        # 创建命令
        commands = [
            BatchCommand(command_id="queue_test_1", command_type="account_info", 
                        parameters={'account_id': 'test'}),
            BatchCommand(command_id="queue_test_2", command_type="account_info", 
                        parameters={'account_id': 'test'})
        ]
        
        # 执行命令
        results = await connection_pool.batch_execute_commands(commands)
        
        # 验证结果
        assert len(results) == 2
        for result in results:
            assert result.success is True
        
        # 验证队列被正确处理
        assert connection_pool.command_queue.qsize() == 0
        
        await connection_pool.stop()


if __name__ == '__main__':
    pytest.main([__file__])