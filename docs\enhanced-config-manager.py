"""
增强的配置管理器 - 支持分布式自动配置
"""

import yaml
import os
import asyncio
from typing import Dict, List, Optional, Set
from dataclasses import dataclass
from watchdog.observers import Observer
from watchdog.events import FileSystemEventHandler
import hashlib

from src.utils.logger import get_logger
from src.core.state_manager import StateManager, StateScope

logger = get_logger(__name__)


@dataclass
class AccountConfig:
    """账户配置"""
    account_id: str
    account_type: str  # master or slave
    name: str
    host: str
    broker: str
    login: int
    server: str
    settings: Dict = None
    monitoring: Dict = None
    execution: Dict = None


@dataclass
class HostConfig:
    """主机配置"""
    host_id: str
    name: str
    datacenter: str
    nats_servers: List[str]
    resources: Dict


@dataclass
class PairingConfig:
    """配对配置"""
    master_id: str
    slave_id: str
    copy_ratio: float
    enabled: bool
    filters: Dict = None


class DistributedConfigManager:
    """分布式配置管理器"""
    
    def __init__(self, config_dir: str, state_manager: StateManager, current_host_id: str):
        self.config_dir = config_dir
        self.state_manager = state_manager
        self.current_host_id = current_host_id
        
        # 配置缓存
        self.hosts: Dict[str, HostConfig] = {}
        self.accounts: Dict[str, AccountConfig] = {}
        self.pairings: List[PairingConfig] = []
        
        # 本主机的账户
        self.local_accounts: Set[str] = set()
        
        # 配置文件监控
        self.observer = None
        self.config_version = 0
        
        logger.info(f"配置管理器初始化 - 主机: {current_host_id}")
    
    async def load_all_configs(self):
        """加载所有配置文件"""
        try:
            # 加载主机配置
            await self._load_hosts_config()
            
            # 加载账户配置
            await self._load_accounts_config()
            
            # 加载配对配置
            await self._load_pairings_config()
            
            # 发现本主机的账户
            await self._discover_local_accounts()
            
            # 发布配置到分布式状态
            await self._publish_config_to_state()
            
            logger.info(f"配置加载完成 - 版本: {self.config_version}")
            logger.info(f"本主机账户: {self.local_accounts}")
            
        except Exception as e:
            logger.error(f"配置加载失败: {e}")
            raise
    
    async def _load_hosts_config(self):
        """加载主机配置"""
        hosts_file = os.path.join(self.config_dir, "hosts.yaml")
        
        with open(hosts_file, 'r') as f:
            data = yaml.safe_load(f)
        
        self.hosts.clear()
        for host_id, host_data in data.get('hosts', {}).items():
            self.hosts[host_id] = HostConfig(
                host_id=host_id,
                name=host_data['name'],
                datacenter=host_data['datacenter'],
                nats_servers=host_data['nats_servers'],
                resources=host_data.get('resources', {})
            )
        
        logger.info(f"加载了 {len(self.hosts)} 个主机配置")
    
    async def _load_accounts_config(self):
        """加载账户配置"""
        accounts_file = os.path.join(self.config_dir, "accounts.yaml")
        
        with open(accounts_file, 'r') as f:
            data = yaml.safe_load(f)
        
        self.accounts.clear()
        for account_id, account_data in data.get('accounts', {}).items():
            self.accounts[account_id] = AccountConfig(
                account_id=account_id,
                account_type=account_data['type'],
                name=account_data['name'],
                host=account_data['host'],
                broker=account_data['broker'],
                login=account_data['login'],
                server=account_data['server'],
                settings=account_data.get('settings', {}),
                monitoring=account_data.get('monitoring', {}),
                execution=account_data.get('execution', {})
            )
        
        logger.info(f"加载了 {len(self.accounts)} 个账户配置")
    
    async def _load_pairings_config(self):
        """加载配对配置"""
        pairings_file = os.path.join(self.config_dir, "active_pairings.yaml")
        
        with open(pairings_file, 'r') as f:
            data = yaml.safe_load(f)
        
        self.pairings.clear()
        for pairing_group in data.get('active_pairings', []):
            master_id = pairing_group['master_account_id']
            
            for slave_config in pairing_group.get('slaves', []):
                self.pairings.append(PairingConfig(
                    master_id=master_id,
                    slave_id=slave_config['slave_account_id'],
                    copy_ratio=slave_config['copy_ratio'],
                    enabled=slave_config['enabled'],
                    filters=slave_config.get('filters')
                ))
        
        logger.info(f"加载了 {len(self.pairings)} 个配对关系")
    
    async def _discover_local_accounts(self):
        """发现本主机的账户"""
        self.local_accounts.clear()
        
        for account_id, account in self.accounts.items():
            if account.host == self.current_host_id:
                self.local_accounts.add(account_id)
        
        # 按类型分组
        local_masters = [aid for aid in self.local_accounts 
                        if self.accounts[aid].account_type == 'master']
        local_slaves = [aid for aid in self.local_accounts 
                       if self.accounts[aid].account_type == 'slave']
        
        logger.info(f"本主机发现 {len(local_masters)} 个主账户: {local_masters}")
        logger.info(f"本主机发现 {len(local_slaves)} 个从账户: {local_slaves}")
    
    async def _publish_config_to_state(self):
        """发布配置到分布式状态"""
        # 计算配置版本（基于内容哈希）
        config_data = {
            'hosts': {k: v.__dict__ for k, v in self.hosts.items()},
            'accounts': {k: v.__dict__ for k, v in self.accounts.items()},
            'pairings': [p.__dict__ for p in self.pairings]
        }
        
        config_hash = hashlib.md5(
            json.dumps(config_data, sort_keys=True).encode()
        ).hexdigest()[:8]
        
        self.config_version = int(config_hash, 16)
        
        # 发布到分布式状态
        await self.state_manager.set(
            "config:version",
            self.config_version,
            scope=StateScope.GLOBAL
        )
        
        await self.state_manager.set(
            "config:data",
            config_data,
            scope=StateScope.GLOBAL
        )
        
        # 发布本主机的账户列表
        await self.state_manager.set(
            f"host:{self.current_host_id}:accounts",
            list(self.local_accounts),
            scope=StateScope.GLOBAL
        )
    
    def get_account_config(self, account_id: str) -> Optional[AccountConfig]:
        """获取账户配置"""
        return self.accounts.get(account_id)
    
    def get_host_config(self, host_id: str) -> Optional[HostConfig]:
        """获取主机配置"""
        return self.hosts.get(host_id)
    
    def get_pairings_for_slave(self, slave_id: str) -> List[PairingConfig]:
        """获取从账户的所有配对"""
        return [p for p in self.pairings 
                if p.slave_id == slave_id and p.enabled]
    
    def get_pairings_for_master(self, master_id: str) -> List[PairingConfig]:
        """获取主账户的所有配对"""
        return [p for p in self.pairings 
                if p.master_id == master_id and p.enabled]
    
    def get_account_location(self, account_id: str) -> Optional[str]:
        """获取账户所在主机"""
        account = self.accounts.get(account_id)
        return account.host if account else None
    
    def is_local_account(self, account_id: str) -> bool:
        """检查是否为本地账户"""
        return account_id in self.local_accounts
    
    async def start_config_watch(self):
        """启动配置文件监控"""
        event_handler = ConfigFileHandler(self)
        self.observer = Observer()
        self.observer.schedule(event_handler, self.config_dir, recursive=False)
        self.observer.start()
        
        logger.info("配置文件监控已启动")
    
    async def stop_config_watch(self):
        """停止配置文件监控"""
        if self.observer:
            self.observer.stop()
            self.observer.join()
            logger.info("配置文件监控已停止")
    
    async def reload_configs(self):
        """重新加载配置"""
        logger.info("重新加载配置...")
        await self.load_all_configs()
        
        # 通知其他组件配置已更新
        await self.state_manager.publish(
            "config:reload",
            {
                'host_id': self.current_host_id,
                'version': self.config_version,
                'timestamp': time.time()
            }
        )


class ConfigFileHandler(FileSystemEventHandler):
    """配置文件变更处理器"""
    
    def __init__(self, config_manager: DistributedConfigManager):
        self.config_manager = config_manager
        self.reload_task = None
        
    def on_modified(self, event):
        if event.is_directory:
            return
            
        if event.src_path.endswith('.yaml'):
            logger.info(f"配置文件变更: {event.src_path}")
            
            # 防抖动，延迟重载
            if self.reload_task:
                self.reload_task.cancel()
            
            self.reload_task = asyncio.create_task(
                self._delayed_reload()
            )
    
    async def _delayed_reload(self):
        """延迟重载（防抖动）"""
        await asyncio.sleep(1.0)
        await self.config_manager.reload_configs()


# ========== 使用示例 ==========

async def example_usage():
    """使用示例"""
    
    # 创建配置管理器
    config_manager = DistributedConfigManager(
        config_dir="/app/config",
        state_manager=state_manager,
        current_host_id="host001"
    )
    
    # 加载配置
    await config_manager.load_all_configs()
    
    # 启动配置监控
    await config_manager.start_config_watch()
    
    # 查询示例
    
    # 1. 获取本主机的所有账户
    local_accounts = config_manager.local_accounts
    print(f"本主机账户: {local_accounts}")
    
    # 2. 获取账户配置
    account = config_manager.get_account_config("ACC001")
    if account:
        print(f"账户 {account.account_id} 在主机 {account.host}")
    
    # 3. 获取从账户的配对
    pairings = config_manager.get_pairings_for_slave("ACC102")
    for pairing in pairings:
        print(f"ACC102 跟随 {pairing.master_id}, 比例: {pairing.copy_ratio}")
    
    # 4. 检查账户位置
    location = config_manager.get_account_location("ACC103")
    print(f"ACC103 位于: {location}")
    
    # 5. 判断是否本地通信
    is_local = config_manager.is_local_account("ACC001")
    print(f"ACC001 是本地账户: {is_local}")
