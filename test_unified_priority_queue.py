#!/usr/bin/env python3
"""
统一优先级队列测试
验证RPC优先级分析器和统一优先级队列的集成
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import asyncio


def test_rpc_priority_analyzer():
    """测试RPC优先级分析器"""
    print("=" * 60)
    print("RPC优先级分析器测试")
    print("=" * 60)
    
    try:
        from src.messaging.priority_queue import RPCPriorityAnalyzer, RPCPriority
        
        analyzer = RPCPriorityAnalyzer()
        
        # 测试高优先级命令
        high_priority_tests = [
            # 平仓信号
            ("send_order", {"action": "CLOSE", "signal_type": "POSITION_CLOSE"}),
            # 止损修改
            ("send_order", {"action": "MODIFY", "sl": 1.1000}),
            # 紧急操作
            ("send_order", {"signal_type": "EMERGENCY_CLOSE"}),
            # 订单取消
            ("cancel_order", {"order_id": 12345}),
            # 关键健康检查
            ("health_check", {"critical": True}),
        ]
        
        print("🔴 高优先级命令测试:")
        for method, params in high_priority_tests:
            priority = analyzer.get_command_priority(method, params)
            if priority == RPCPriority.HIGH:
                print(f"  ✅ {method} - {params} -> {priority.value}")
            else:
                print(f"  ❌ {method} - {params} -> {priority.value} (期望: high)")
                return False
        
        # 测试中优先级命令
        medium_priority_tests = [
            # 开仓信号
            ("send_order", {"action": "BUY", "signal_type": "POSITION_OPEN"}),
            # 限价单
            ("send_order", {"type": 2, "action": "BUY_LIMIT"}),
            # 账户查询
            ("get_account_info", {}),
            # 持仓查询
            ("get_positions", {}),
        ]
        
        print("\n🟡 中优先级命令测试:")
        for method, params in medium_priority_tests:
            priority = analyzer.get_command_priority(method, params)
            if priority == RPCPriority.MEDIUM:
                print(f"  ✅ {method} - {params} -> {priority.value}")
            else:
                print(f"  ❌ {method} - {params} -> {priority.value} (期望: medium)")
                return False
        
        # 测试低优先级命令
        low_priority_tests = [
            # 行情查询
            ("get_symbol_info", {"symbol": "EURUSD"}),
            # 历史数据
            ("get_rates", {"symbol": "EURUSD", "timeframe": "H1"}),
            # 市场数据
            ("get_market_data", {"symbol": "EURUSD"}),
        ]
        
        print("\n🟢 低优先级命令测试:")
        for method, params in low_priority_tests:
            priority = analyzer.get_command_priority(method, params)
            if priority == RPCPriority.LOW:
                print(f"  ✅ {method} - {params} -> {priority.value}")
            else:
                print(f"  ❌ {method} - {params} -> {priority.value} (期望: low)")
                return False
        
        print("\n✅ RPC优先级分析器测试通过")
        return True
        
    except Exception as e:
        print(f"❌ RPC优先级分析器测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_priority_converter():
    """测试优先级转换器"""
    print("\n" + "=" * 60)
    print("优先级转换器测试")
    print("=" * 60)
    
    try:
        from src.messaging.priority_queue import PriorityConverter, RPCPriority, MessagePriority
        
        converter = PriorityConverter()
        
        # 测试RPC到Message的转换
        rpc_to_message_tests = [
            (RPCPriority.HIGH, MessagePriority.HIGH),
            (RPCPriority.MEDIUM, MessagePriority.NORMAL),
            (RPCPriority.LOW, MessagePriority.LOW),
        ]
        
        print("🔄 RPC -> Message 转换测试:")
        for rpc_priority, expected_message in rpc_to_message_tests:
            result = converter.rpc_to_message(rpc_priority)
            if result == expected_message:
                print(f"  ✅ {rpc_priority.value} -> {result.name}")
            else:
                print(f"  ❌ {rpc_priority.value} -> {result.name} (期望: {expected_message.name})")
                return False
        
        # 测试Message到RPC的转换
        message_to_rpc_tests = [
            (MessagePriority.CRITICAL, RPCPriority.HIGH),
            (MessagePriority.HIGH, RPCPriority.HIGH),
            (MessagePriority.NORMAL, RPCPriority.MEDIUM),
            (MessagePriority.LOW, RPCPriority.LOW),
        ]
        
        print("\n🔄 Message -> RPC 转换测试:")
        for message_priority, expected_rpc in message_to_rpc_tests:
            result = converter.message_to_rpc(message_priority)
            if result == expected_rpc:
                print(f"  ✅ {message_priority.name} -> {result.value}")
            else:
                print(f"  ❌ {message_priority.name} -> {result.value} (期望: {expected_rpc.value})")
                return False
        
        # 测试字符串转换
        string_tests = [
            ("high", MessagePriority.HIGH),
            ("medium", MessagePriority.NORMAL),
            ("low", MessagePriority.LOW),
        ]
        
        print("\n🔄 字符串 -> Message 转换测试:")
        for rpc_string, expected_message in string_tests:
            result = converter.rpc_string_to_message(rpc_string)
            if result == expected_message:
                print(f"  ✅ '{rpc_string}' -> {result.name}")
            else:
                print(f"  ❌ '{rpc_string}' -> {result.name} (期望: {expected_message.name})")
                return False
        
        print("\n✅ 优先级转换器测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 优先级转换器测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


async def test_rpc_command_enqueue():
    """测试RPC命令入队"""
    print("\n" + "=" * 60)
    print("RPC命令入队测试")
    print("=" * 60)
    
    try:
        from src.messaging.priority_queue import enqueue_rpc_command, get_priority_queue
        
        # 测试不同优先级的命令入队
        test_commands = [
            # 高优先级
            ("send_order", {"action": "CLOSE", "symbol": "EURUSD"}),
            # 中优先级
            ("send_order", {"action": "BUY", "symbol": "EURUSD", "volume": 0.1}),
            # 低优先级
            ("get_symbol_info", {"symbol": "EURUSD"}),
        ]
        
        queue = get_priority_queue()
        initial_size = queue.size()
        
        print("📥 命令入队测试:")
        for method, params in test_commands:
            success = await enqueue_rpc_command(method, params, f"test_{method}")
            if success:
                print(f"  ✅ {method} 入队成功")
            else:
                print(f"  ❌ {method} 入队失败")
                return False
        
        final_size = queue.size()
        enqueued_count = final_size - initial_size
        
        if enqueued_count == len(test_commands):
            print(f"\n✅ 成功入队 {enqueued_count} 个命令")
        else:
            print(f"\n❌ 期望入队 {len(test_commands)} 个命令，实际入队 {enqueued_count} 个")
            return False
        
        # 测试队列统计
        stats = queue.get_stats()
        print(f"\n📊 队列统计:")
        print(f"  总大小: {stats['heap_size']}")
        print(f"  各优先级队列大小: {stats['queue_sizes']}")
        print(f"  总入队: {stats['total_enqueued']}")
        
        return True
        
    except Exception as e:
        print(f"❌ RPC命令入队测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_mt5_request_handler_integration():
    """测试MT5请求处理器集成"""
    print("\n" + "=" * 60)
    print("MT5请求处理器集成测试")
    print("=" * 60)
    
    try:
        from src.core.mt5_request_handler import MT5RPCService
        
        # Mock JetStream客户端
        class MockJetStreamClient:
            def __init__(self):
                self.nc = MockNATSClient()
        
        class MockNATSClient:
            async def subscribe(self, subject, cb):
                return True
        
        jetstream = MockJetStreamClient()
        
        # 创建RPC服务
        rpc_service = MT5RPCService(
            account_id="TEST001",
            login=12345,
            password="test",
            server="test",
            terminal_path="test",
            jetstream_client=jetstream
        )
        
        # 验证统一优先级队列组件
        required_components = [
            'priority_queue',
            'priority_analyzer', 
            'priority_converter'
        ]
        
        print("🔧 组件集成验证:")
        for component in required_components:
            if hasattr(rpc_service, component):
                print(f"  ✅ {component} 已集成")
            else:
                print(f"  ❌ {component} 未集成")
                return False
        
        # 验证仍保持向后兼容
        if hasattr(rpc_service, 'priority_queues'):
            print("  ✅ 保持向后兼容的多进程队列")
        else:
            print("  ❌ 缺少向后兼容的多进程队列")
            return False
        
        print("\n✅ MT5请求处理器集成测试通过")
        return True
        
    except Exception as e:
        print(f"❌ MT5请求处理器集成测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_performance_component_compatibility():
    """测试性能组件兼容性"""
    print("\n" + "=" * 60)
    print("性能组件兼容性测试")
    print("=" * 60)
    
    try:
        from src.performance.optimized_components import OptimizedMessageProcessor
        from src.messaging.priority_queue import MessagePriority

        # 创建性能组件
        perf_components = OptimizedMessageProcessor()
        
        # 验证优先级队列使用
        if hasattr(perf_components, 'priority_queue'):
            print("✅ 性能组件包含优先级队列")
        else:
            print("❌ 性能组件缺少优先级队列")
            return False
        
        # 验证优先级确定逻辑
        test_signal = {
            'action': 'CLOSE',
            'symbol': 'EURUSD',
            'signal_id': 'test_001'
        }
        
        priority = perf_components._determine_priority(test_signal)
        if priority == MessagePriority.CRITICAL:
            print(f"✅ 性能组件优先级判断正确: {test_signal['action']} -> {priority.name}")
        else:
            print(f"❌ 性能组件优先级判断错误: {test_signal['action']} -> {priority.name}")
            return False
        
        print("\n✅ 性能组件兼容性测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 性能组件兼容性测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


async def main():
    """主测试函数"""
    print("统一优先级队列测试")
    print("验证RPC优先级分析器和统一优先级队列的集成")
    print("=" * 60)
    
    tests = [
        ("RPC优先级分析器", test_rpc_priority_analyzer),
        ("优先级转换器", test_priority_converter),
        ("RPC命令入队", test_rpc_command_enqueue),
        ("MT5请求处理器集成", test_mt5_request_handler_integration),
        ("性能组件兼容性", test_performance_component_compatibility),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if asyncio.iscoroutinefunction(test_func):
                result = await test_func()
            else:
                result = test_func()
            if result:
                passed += 1
                print(f"✅ {test_name} - 测试通过")
            else:
                print(f"❌ {test_name} - 测试失败")
        except Exception as e:
            print(f"❌ {test_name} - 异常: {e}")
    
    print("\n" + "=" * 60)
    print("统一优先级队列测试结果")
    print("=" * 60)
    print(f"通过测试: {passed}/{total}")
    print(f"成功率: {passed/total*100:.1f}%")
    
    if passed == total:
        print("\n🎉 统一优先级队列完美集成！")
        print("\n✅ 实现的功能:")
        print("1. RPC优先级分析器 - 智能判断命令优先级")
        print("2. 优先级转换器 - 在不同优先级系统间转换")
        print("3. 统一优先级队列 - 集中管理所有优先级消息")
        print("4. 向后兼容 - 保持原有多进程队列作为备用")
        print("5. 性能组件集成 - 无缝使用统一优先级系统")
        
        print("\n🚀 优先级分类标准:")
        print("- 🔴 高优先级: 止损、平仓、紧急操作")
        print("- 🟡 中优先级: 开仓、常规交易、账户查询")
        print("- 🟢 低优先级: 行情查询、历史数据、状态更新")
        
        print("\n📋 系统现在具备:")
        print("- 统一的优先级标准和分析逻辑")
        print("- 智能的命令分类和路由")
        print("- 高效的优先级队列处理")
        print("- 完整的向后兼容性")
    else:
        print(f"\n⚠️ 仍有 {total-passed} 个问题需要解决")


if __name__ == "__main__":
    asyncio.run(main())
