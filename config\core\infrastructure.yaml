# MT5分布式交易系统 - 基础设施配置
# 定义Redis、NATS、数据库等基础组件的配置

# ============================================================================
# Redis配置
# ============================================================================
redis:
  # 连接配置
  connection:
    host: ${REDIS_HOST:-localhost}
    port: ${REDIS_PORT:-6379}
    db: ${REDIS_DB:-0}
    password: ${REDIS_PASSWORD:-}
    
  # 连接池配置
  pool:
    max_connections: 100
    retry_on_timeout: true
    socket_timeout: 5
    socket_connect_timeout: 5
    socket_keepalive: true
    
  # 缓存配置
  cache:
    default_ttl: 3600
    account_info_ttl: 1800
    signal_cache_ttl: 300
    position_cache_ttl: 60
    
  # 性能优化
  optimization:
    encoding: "utf-8"
    decode_responses: true
    health_check_interval: 30

# ============================================================================
# NATS消息队列配置
# ============================================================================
nats:
  # 服务器配置
  servers:
    # 本地开发
    - "nats://localhost:4222"
    - "nats://127.0.0.1:4222"
    
  # 连接设置
  connection:
    timeout: 10
    ping_interval: 30
    max_pings_out: 3
    reconnect_wait: 2
    max_reconnect: 60
    
  # JetStream配置
  jetstream:
    enabled: true
    stream_name: "MT5_TRADING_STREAM"
    
    # 存储配置
    storage: "file"
    retention: "limits"
    max_msgs: ********
    max_bytes: ***********  # 10GB
    max_age: 86400          # 24小时
    max_msg_size: 1048576   # 1MB
    duplicate_window: 120   # 2分钟
    replicas: 1
    
    # 核心分离进程架构主题配置
    subjects:
      trading_signals: "MT5.SIGNALS.*"         # 基础交易信号
      trade_execution: "MT5.EXECUTION.*"       # 交易执行（保留兼容性）
      account_monitoring: "MT5.MONITOR.*"      # 账户监控事件
      system_control: "MT5.CONTROL.*"          # 系统控制
      metrics: "MT5.METRICS.*"                 # 指标数据
      execution_results: "MT5.RESULT.*"        # 执行结果
      execute_commands: "MT5.EXECUTE.*"        # 执行命令
      # request_response: "MT5.REQUEST.*"      # 移除：Request/Response不应该被JetStream持久化

# ============================================================================
# 数据库配置（可选）
# ============================================================================
database:
  enabled: false  # 当前系统主要使用Redis
  
  # SQLite配置（开发环境）
  sqlite:
    path: "data/mt5_trading.db"
    timeout: 30
    
  # PostgreSQL配置（生产环境）
  postgresql:
    host: ${POSTGRES_HOST:-localhost}
    port: ${POSTGRES_PORT:-5432}
    database: ${POSTGRES_DB:-mt5_trading}
    username: ${POSTGRES_USER:-mt5_user}
    password: ${POSTGRES_PASSWORD:-}
    pool_size: 20
    max_overflow: 30

# ============================================================================
# 对象存储配置（可选）
# ============================================================================
storage:
  enabled: false
  
  # 本地文件存储
  local:
    base_path: "data/storage"
    backup_path: "data/backups"
    
  # S3兼容存储
  s3:
    endpoint: ${S3_ENDPOINT:-}
    access_key: ${S3_ACCESS_KEY:-}
    secret_key: ${S3_SECRET_KEY:-}
    bucket: ${S3_BUCKET:-mt5-trading}
    region: ${S3_REGION:-us-east-1}

# ============================================================================
# 消息队列备选方案
# ============================================================================
message_queue:
  # 当NATS不可用时的备选方案
  fallback:
    enabled: true
    type: "redis"  # redis/memory
    
  # 本地队列配置
  local:
    max_size: 10000
    batch_size: 100
    flush_interval: 1000  # ms

# ============================================================================
# 服务发现配置
# ============================================================================
service_discovery:
  enabled: false  # 简化配置，暂时关闭
  
  # Consul配置
  consul:
    host: ${CONSUL_HOST:-localhost}
    port: ${CONSUL_PORT:-8500}
    
  # etcd配置  
  etcd:
    endpoints:
      - ${ETCD_ENDPOINT:-localhost:2379}

# ============================================================================
# 网络配置
# ============================================================================
network:
  # HTTP客户端配置
  http_client:
    timeout: 30
    max_connections: 100
    keepalive: true
    
  # TCP配置
  tcp:
    nodelay: true
    keepalive: true
    buffer_size: 65536

# ============================================================================
# 安全配置
# ============================================================================
security:
  # TLS配置
  tls:
    enabled: false  # 开发环境默认关闭
    cert_file: "certs/server.crt"
    key_file: "certs/server.key"
    ca_file: "certs/ca.crt"
    
  # 认证配置
  authentication:
    method: "none"  # none/token/certificate
    token_secret: ${AUTH_TOKEN_SECRET:-}
    
  # 访问控制
  access_control:
    whitelist_enabled: false
    allowed_ips: []
    rate_limiting: false