# MT5分布式交易系统 - 基础设施配置
# 定义Redis、NATS、数据库等基础组件的配置

# ============================================================================
# Redis配置
# ============================================================================
redis:
  # 连接配置
  connection:
    host: ${REDIS_HOST:-localhost}
    port: ${REDIS_PORT:-6379}
    db: ${REDIS_DB:-0}
    password: ${REDIS_PASSWORD:-}
    
  # 连接池配置
  pool:
    max_connections: 100
    retry_on_timeout: true
    socket_timeout: 5
    socket_connect_timeout: 5
    socket_keepalive: true
    
  # 缓存配置
  cache:
    default_ttl: 3600
    account_info_ttl: 1800
    signal_cache_ttl: 300
    position_cache_ttl: 60
    
  # 性能优化
  optimization:
    encoding: "utf-8"
    decode_responses: true
    health_check_interval: 30

# ============================================================================
# NATS消息队列配置 - 分层流架构
# ============================================================================
nats:
  # 服务器配置
  servers:
    # 本地开发
    - "nats://localhost:4222"
    - "nats://127.0.0.1:4222"

  # 连接设置
  connection:
    timeout: 10
    ping_interval: 30
    max_pings_out: 3
    reconnect_wait: 2
    max_reconnect: 60

  # JetStream分层流配置
  jetstream:
    enabled: true

    # 分层流定义 - 替代单一流架构
    streams:
      # 1. 本地高速通道流 - 同主机内通信
      local:
        name_template: "MT5_LOCAL_{host_id}"
        description: "本地高速通道，同主机内进程间通信"
        subjects:
          - "MT5.LOCAL.{host_id}.PRIORITY.*"    # 优先级消息
          - "MT5.LOCAL.{host_id}.INTERNAL.*"    # 内部通信
          - "MT5.LOCAL.{host_id}.EXECUTION.*"   # 本地执行
        storage: "memory"                       # 内存存储，极致性能
        retention: "workqueue"                  # 工作队列模式，消费即删除
        max_age: 300                           # 5分钟TTL
        max_msgs: 100000                       # 本地消息量限制
        max_bytes: 104857600                   # 100MB
        replicas: 1                            # 本地单副本
        duplicate_window: 60                   # 1分钟去重

      # 2. 跨主机交易信号流 - 分布式信号传递
      signals:
        name: "MT5_SIGNALS"
        description: "跨主机交易信号流，支持优先级路由"
        subjects:
          - "MT5.SIGNALS.SYSTEM_CRITICAL.*"    # 系统级风险信号
          - "MT5.SIGNALS.RISK_COMMAND.*"       # 订单级风控信号
          - "MT5.SIGNALS.SIGNAL_EXECUTION.*"   # 策略信号执行
          - "MT5.SIGNALS.REALTIME_QUERY.*"     # 实时数据查询
          - "MT5.SIGNALS.BACKGROUND_TASK.*"    # 后台任务
          - "MT5.COPY.*"                       # 跟单信号
        storage: "file"                        # 文件存储，可靠性
        retention: "limits"                    # 持久化保留
        max_age: 3600                         # 1小时保留
        max_msgs: 1000000                     # 100万条消息
        max_bytes: 1073741824                 # 1GB
        replicas: 3                           # 高可用三副本
        duplicate_window: 120                 # 2分钟去重

      # 3. RPC通信流 - 请求响应模式
      rpc:
        name: "MT5_RPC"
        description: "跨主机RPC通信流，支持负载均衡"
        subjects:
          - "MT5.RPC.EXECUTE.*"                # RPC执行请求
          - "MT5.RPC.QUERY.*"                  # RPC查询请求
          - "MT5.REQUEST.*"                    # 通用请求
        storage: "file"                        # 持久化存储
        retention: "workqueue"                 # 工作队列，消费即删除
        max_age: 600                          # 10分钟TTL
        max_msgs: 50000                       # RPC消息限制
        max_bytes: 52428800                   # 50MB
        replicas: 2                           # 双副本保证可靠性
        duplicate_window: 30                  # 30秒去重

      # 4. 系统控制和监控流 - 管理和监控
      control:
        name: "MT5_CONTROL"
        description: "系统控制和监控流"
        subjects:
          - "MT5.CONTROL.*"                    # 系统控制
          - "MT5.MONITOR.*"                    # 监控数据
          - "MT5.HEARTBEAT.*"                  # 心跳信息
          - "MT5.METRICS.*"                    # 指标数据
        storage: "file"                        # 文件存储
        retention: "limits"                    # 保留限制
        max_age: 86400                        # 24小时保留
        max_msgs: 500000                      # 监控数据限制
        max_bytes: 536870912                  # 512MB
        replicas: 1                           # 单副本即可
        duplicate_window: 300                 # 5分钟去重

    # 消费者策略配置 - 五级优先级系统
    consumers:
      # 信号分发消费者 - 广播模式
      signal_broadcast:
        system_critical:
          name_template: "system_critical_consumer_{host_id}"
          description: "系统级风险信号消费者"
          filter_subject: "MT5.SIGNALS.SYSTEM_CRITICAL.*"
          mode: "broadcast"                    # 每个账户都消费
          priority_level: 0                    # 最高优先级
          ack_policy: "explicit"
          max_deliver: 1                       # 系统级信号不重试
          ack_wait: 5                         # 5秒确认超时
          max_ack_pending: 10

        risk_command:
          name_template: "risk_command_consumer_{host_id}"
          description: "订单级风控信号消费者"
          filter_subject: "MT5.SIGNALS.RISK_COMMAND.*"
          mode: "broadcast"
          priority_level: 1
          ack_policy: "explicit"
          max_deliver: 2
          ack_wait: 10
          max_ack_pending: 20

        signal_execution:
          name_template: "signal_execution_consumer_{host_id}"
          description: "策略信号执行消费者"
          filter_subject: "MT5.SIGNALS.SIGNAL_EXECUTION.*"
          mode: "queue_group"
          queue_group: "signal_execution_processors"
          priority_level: 2
          ack_policy: "explicit"
          max_deliver: 3
          ack_wait: 30
          max_ack_pending: 50

        realtime_query:
          name_template: "realtime_query_consumer_{host_id}"
          description: "实时数据查询消费者"
          filter_subject: "MT5.SIGNALS.REALTIME_QUERY.*"
          mode: "queue_group"
          queue_group: "realtime_query_processors"
          priority_level: 3
          ack_policy: "explicit"
          max_deliver: 2                       # 查询可重试
          ack_wait: 15
          max_ack_pending: 100

        background_task:
          name_template: "background_task_consumer_{host_id}"
          description: "后台任务消费者"
          filter_subject: "MT5.SIGNALS.BACKGROUND_TASK.*"
          mode: "queue_group"
          queue_group: "background_task_processors"
          priority_level: 4                    # 最低优先级
          ack_policy: "explicit"
          max_deliver: 5                       # 后台任务允许多次重试
          ack_wait: 60
          max_ack_pending: 200

      # RPC消费者 - 队列组负载均衡
      rpc_workers:
        execution:
          name_template: "rpc_execution_consumer_{host_id}"
          description: "RPC执行消费者"
          filter_subject: "MT5.RPC.EXECUTE.*"
          queue_group: "rpc_execution_workers"  # 负载均衡
          ack_policy: "explicit"
          max_deliver: 3
          ack_wait: 30
          max_ack_pending: 100

        query:
          name_template: "rpc_query_consumer_{host_id}"
          description: "RPC查询消费者"
          filter_subject: "MT5.RPC.QUERY.*"
          queue_group: "rpc_query_workers"
          ack_policy: "explicit"
          max_deliver: 2
          ack_wait: 15
          max_ack_pending: 50

      # 监控消费者 - 拉取模式
      monitoring:
        pull:
          name_template: "monitoring_pull_{host_id}"
          description: "监控数据拉取消费者"
          filter_subject: "MT5.CONTROL.MONITOR.*"
          mode: "pull"                         # 拉取模式
          batch_size: 50
          max_bytes: 1048576                   # 1MB
          expires: 300                         # 5分钟过期

    # 主题命名规范
    subject_patterns:
      # 本地信号 (同主机内)
      local_signals: "MT5.LOCAL.{host_id}.SIGNALS.{account_id}"
      local_execution: "MT5.LOCAL.{host_id}.EXECUTION.{account_id}"
      local_internal: "MT5.LOCAL.{host_id}.INTERNAL.{component}"

      # 跨主机信号
      global_signals: "MT5.SIGNALS.{priority}.{master_account}"
      copy_signals: "MT5.COPY.{priority}.{slave_account}"

      # 跨主机直达
      cross_host: "MT5.CROSS.{source_host}.{target_host}.{account_id}"

      # 系统控制
      control: "MT5.CONTROL.{host_id}.{command_type}"
      monitor: "MT5.MONITOR.{host_id}.{metric_type}"
      heartbeat: "MT5.HEARTBEAT.{host_id}.{component}"

      # RPC请求
      rpc_method: "MT5.RPC.{method}.{account_id}"
      rpc_request: "MT5.REQUEST.{service}.{host_id}"

# ============================================================================
# 数据库配置（可选）
# ============================================================================
database:
  enabled: false  # 当前系统主要使用Redis
  
  # SQLite配置（开发环境）
  sqlite:
    path: "data/mt5_trading.db"
    timeout: 30
    
  # PostgreSQL配置（生产环境）
  postgresql:
    host: ${POSTGRES_HOST:-localhost}
    port: ${POSTGRES_PORT:-5432}
    database: ${POSTGRES_DB:-mt5_trading}
    username: ${POSTGRES_USER:-mt5_user}
    password: ${POSTGRES_PASSWORD:-}
    pool_size: 20
    max_overflow: 30

# ============================================================================
# 对象存储配置（可选）
# ============================================================================
storage:
  enabled: false
  
  # 本地文件存储
  local:
    base_path: "data/storage"
    backup_path: "data/backups"
    
  # S3兼容存储
  s3:
    endpoint: ${S3_ENDPOINT:-}
    access_key: ${S3_ACCESS_KEY:-}
    secret_key: ${S3_SECRET_KEY:-}
    bucket: ${S3_BUCKET:-mt5-trading}
    region: ${S3_REGION:-us-east-1}

# ============================================================================
# 消息队列备选方案
# ============================================================================
message_queue:
  # 当NATS不可用时的备选方案
  fallback:
    enabled: true
    type: "redis"  # redis/memory
    
  # 本地队列配置
  local:
    max_size: 10000
    batch_size: 100
    flush_interval: 1000  # ms

# ============================================================================
# 服务发现配置
# ============================================================================
service_discovery:
  enabled: false  # 简化配置，暂时关闭
  
  # Consul配置
  consul:
    host: ${CONSUL_HOST:-localhost}
    port: ${CONSUL_PORT:-8500}
    
  # etcd配置  
  etcd:
    endpoints:
      - ${ETCD_ENDPOINT:-localhost:2379}

# ============================================================================
# 网络配置
# ============================================================================
network:
  # HTTP客户端配置
  http_client:
    timeout: 30
    max_connections: 100
    keepalive: true
    
  # TCP配置
  tcp:
    nodelay: true
    keepalive: true
    buffer_size: 65536

# ============================================================================
# 安全配置
# ============================================================================
security:
  # TLS配置
  tls:
    enabled: false  # 开发环境默认关闭
    cert_file: "certs/server.crt"
    key_file: "certs/server.key"
    ca_file: "certs/ca.crt"
    
  # 认证配置
  authentication:
    method: "none"  # none/token/certificate
    token_secret: ${AUTH_TOKEN_SECRET:-}
    
  # 访问控制
  access_control:
    whitelist_enabled: false
    allowed_ips: []
    rate_limiting: false