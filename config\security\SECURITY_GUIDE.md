# MT5 Trading System Security Guide

## 🔐 Security Overview

This guide provides comprehensive security implementation strategies for the MT5 High-Frequency Trading System, covering encryption, authentication, authorization, and security best practices.

## 🚀 Quick Security Setup

### 快速安全配置步骤

1. **生成安全密钥** (必须首先执行)
   ```bash
   python3 scripts/generate_security_keys.py
   ```

2. **修复文件权限**
   ```bash
   chmod 600 .env
   chmod 600 config/security/*.yaml
   ```

3. **验证安全配置**
   ```bash
   python3 scripts/validate_security.py
   ```

4. **运行完整安全检查**
   ```bash
   bash scripts/security_check.sh
   ```

### 🔧 常见安全问题修复

**问题1: 缺少环境变量**
- 运行密钥生成脚本：`python3 scripts/generate_security_keys.py`
- 手动编辑 `.env` 文件设置必要变量

**问题2: 文件权限过高**
```bash
# 修复配置文件权限
chmod 600 config/security/encryption_keys.yaml
chmod 600 config/security/api_permissions.yaml
chmod 600 .env

# 修复脚本执行权限
chmod +x run.sh
chmod +x scripts/*.sh
chmod +x scripts/*.py
```

**问题3: TLS证书配置**
```bash
# 生成自签名证书（仅用于测试）
openssl req -x509 -newkey rsa:4096 -keyout config/security/tls_certificates/server.key -out config/security/tls_certificates/server.crt -days 365 -nodes
```

## 📋 Table of Contents

- [Quick Security Setup](#quick-security-setup)
- [Security Architecture](#security-architecture)
- [Encryption Implementation](#encryption-implementation)
- [Authentication & Authorization](#authentication--authorization)
- [Key Management](#key-management)
- [Security Configuration](#security-configuration)
- [Best Practices](#best-practices)
- [Compliance](#compliance)

## 🏗️ Security Architecture

### Multi-Layer Security Model

```mermaid
graph TB
    A[Transport Layer Security] --> B[Application Security]
    B --> C[API Authentication]
    C --> D[Authorization & RBAC]
    D --> E[Data Encryption]
    E --> F[Key Management]
    F --> G[Audit & Monitoring]
```

### Security Zones

| Zone | Components | Security Level |
|------|------------|----------------|
| **DMZ** | Load Balancer, Reverse Proxy | High |
| **Application** | API Servers, Web Interface | Very High |
| **Data** | Database, Cache, Message Queue | Critical |
| **Management** | Monitoring, Logging | High |

## 🔒 Encryption Implementation

### 1. Data at Rest Encryption

#### Database Encryption

```yaml
# config/security/encryption_keys.yaml
data_encryption:
  database:
    at_rest:
      enabled: true
      algorithm: "AES-256-CBC"
      key_derivation: "PBKDF2"
      iterations: 100000
    
    field_level:
      enabled: true
      sensitive_fields:
        - "password"
        - "api_key"
        - "mt5_password"
        - "telegram_token"
```

#### Implementation Example

```python
# src/security/encryption.py
from cryptography.fernet import Fernet
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
import os

class DataEncryption:
    def __init__(self, master_key: str):
        self.master_key = master_key.encode()
        
    def derive_key(self, salt: bytes) -> bytes:
        kdf = PBKDF2HMAC(
            algorithm=hashes.SHA256(),
            length=32,
            salt=salt,
            iterations=100000,
        )
        return kdf.derive(self.master_key)
    
    def encrypt_field(self, data: str, salt: bytes = None) -> tuple:
        if salt is None:
            salt = os.urandom(32)
        
        key = self.derive_key(salt)
        f = Fernet(key)
        encrypted_data = f.encrypt(data.encode())
        
        return encrypted_data, salt
    
    def decrypt_field(self, encrypted_data: bytes, salt: bytes) -> str:
        key = self.derive_key(salt)
        f = Fernet(key)
        decrypted_data = f.decrypt(encrypted_data)
        
        return decrypted_data.decode()
```

### 2. Data in Transit Encryption

#### TLS Configuration

```yaml
# config/security/encryption_keys.yaml
transport_encryption:
  tls:
    min_version: "TLS1.2"
    max_version: "TLS1.3"
    cipher_suites:
      - "ECDHE-ECDSA-AES256-GCM-SHA384"
      - "ECDHE-RSA-AES256-GCM-SHA384"
      - "ECDHE-ECDSA-AES128-GCM-SHA256"
```

#### Certificate Management

```bash
# Generate TLS certificates
openssl req -x509 -newkey rsa:4096 -keyout server.key -out server.crt -days 365 -nodes

# Set environment variables
export TLS_CERT_FILE="/path/to/server.crt"
export TLS_KEY_FILE="/path/to/server.key"
```

### 3. API Message Encryption

```python
# src/security/api_encryption.py
import jwt
from cryptography.hazmat.primitives import serialization, hashes
from cryptography.hazmat.primitives.asymmetric import rsa, padding

class APIEncryption:
    def __init__(self):
        self.private_key = self._load_private_key()
        self.public_key = self.private_key.public_key()
    
    def sign_message(self, message: str) -> str:
        """Sign API message for integrity verification"""
        signature = self.private_key.sign(
            message.encode(),
            padding.PSS(
                mgf=padding.MGF1(hashes.SHA256()),
                salt_length=padding.PSS.MAX_LENGTH
            ),
            hashes.SHA256()
        )
        return signature.hex()
    
    def verify_signature(self, message: str, signature: str) -> bool:
        """Verify message signature"""
        try:
            self.public_key.verify(
                bytes.fromhex(signature),
                message.encode(),
                padding.PSS(
                    mgf=padding.MGF1(hashes.SHA256()),
                    salt_length=padding.PSS.MAX_LENGTH
                ),
                hashes.SHA256()
            )
            return True
        except Exception:
            return False
```

## 🔑 Authentication & Authorization

### 1. JWT Authentication

#### Configuration

```yaml
# config/security/api_permissions.yaml
jwt:
  algorithm: "HS256"
  secret: "${JWT_SECRET}"
  access_token_expire_minutes: 30
  refresh_token_expire_days: 7
  
  claims:
    issuer: "mt5-trading-system"
    audience: "mt5-api"
```

#### Implementation

```python
# src/security/auth.py
import jwt
from datetime import datetime, timedelta
from typing import Optional

class JWTAuth:
    def __init__(self, secret_key: str, algorithm: str = "HS256"):
        self.secret_key = secret_key
        self.algorithm = algorithm
    
    def create_access_token(self, user_id: str, roles: list, expires_delta: Optional[timedelta] = None):
        if expires_delta:
            expire = datetime.utcnow() + expires_delta
        else:
            expire = datetime.utcnow() + timedelta(minutes=30)
        
        payload = {
            "user_id": user_id,
            "roles": roles,
            "exp": expire,
            "iat": datetime.utcnow(),
            "iss": "mt5-trading-system",
            "aud": "mt5-api"
        }
        
        return jwt.encode(payload, self.secret_key, algorithm=self.algorithm)
    
    def verify_token(self, token: str) -> dict:
        try:
            payload = jwt.decode(
                token, 
                self.secret_key, 
                algorithms=[self.algorithm],
                audience="mt5-api",
                issuer="mt5-trading-system"
            )
            return payload
        except jwt.ExpiredSignatureError:
            raise Exception("Token has expired")
        except jwt.InvalidTokenError:
            raise Exception("Invalid token")
```

### 2. Role-Based Access Control (RBAC)

#### Role Definition

```python
# src/security/rbac.py
from enum import Enum
from typing import Set, Dict

class Permission(Enum):
    ACCOUNTS_READ = "accounts:read"
    ACCOUNTS_WRITE = "accounts:write"
    TRADING_READ = "trading:read"
    TRADING_WRITE = "trading:write"
    SYSTEM_READ = "system:read"
    SYSTEM_WRITE = "system:write"

class Role:
    def __init__(self, name: str, permissions: Set[Permission]):
        self.name = name
        self.permissions = permissions

# Predefined roles
ROLES = {
    "super_admin": Role("super_admin", set(Permission)),
    "admin": Role("admin", {
        Permission.SYSTEM_READ, Permission.SYSTEM_WRITE,
        Permission.ACCOUNTS_READ, Permission.ACCOUNTS_WRITE,
        Permission.TRADING_READ, Permission.TRADING_WRITE
    }),
    "trader": Role("trader", {
        Permission.ACCOUNTS_READ,
        Permission.TRADING_READ, Permission.TRADING_WRITE
    }),
    "analyst": Role("analyst", {
        Permission.ACCOUNTS_READ,
        Permission.TRADING_READ
    }),
    "viewer": Role("viewer", {
        Permission.ACCOUNTS_READ
    })
}

class RBACManager:
    def __init__(self):
        self.roles = ROLES
    
    def check_permission(self, user_roles: list, required_permission: Permission) -> bool:
        for role_name in user_roles:
            role = self.roles.get(role_name)
            if role and required_permission in role.permissions:
                return True
        return False
    
    def get_user_permissions(self, user_roles: list) -> Set[Permission]:
        permissions = set()
        for role_name in user_roles:
            role = self.roles.get(role_name)
            if role:
                permissions.update(role.permissions)
        return permissions
```

### 3. API Key Authentication

```python
# src/security/api_key_auth.py
import hashlib
import secrets
from datetime import datetime, timedelta

class APIKeyManager:
    def __init__(self, db_connection):
        self.db = db_connection
    
    def generate_api_key(self, user_id: str, roles: list, expires_days: int = 90) -> tuple:
        # Generate random API key
        api_key = secrets.token_urlsafe(32)
        
        # Hash the key for storage
        key_hash = hashlib.sha256(api_key.encode()).hexdigest()
        
        # Set expiration
        expires_at = datetime.utcnow() + timedelta(days=expires_days)
        
        # Store in database
        self._store_api_key(user_id, key_hash, roles, expires_at)
        
        return api_key, expires_at
    
    def validate_api_key(self, api_key: str) -> dict:
        key_hash = hashlib.sha256(api_key.encode()).hexdigest()
        
        # Verify key in database
        key_info = self._get_api_key_info(key_hash)
        
        if not key_info:
            raise Exception("Invalid API key")
        
        if key_info['expires_at'] < datetime.utcnow():
            raise Exception("API key has expired")
        
        if not key_info['enabled']:
            raise Exception("API key is disabled")
        
        return key_info
    
    def rotate_api_key(self, old_key: str) -> tuple:
        # Validate old key
        key_info = self.validate_api_key(old_key)
        
        # Generate new key
        new_key, expires_at = self.generate_api_key(
            key_info['user_id'], 
            key_info['roles']
        )
        
        # Disable old key
        self._disable_api_key(old_key)
        
        return new_key, expires_at
```

## 🔐 Key Management

### 1. Environment Variables Setup

```bash
# .env file
# Master encryption keys
MASTER_ENCRYPTION_KEY=your-256-bit-master-key-here
SECONDARY_ENCRYPTION_KEY=your-256-bit-secondary-key-here

# JWT secrets
JWT_SECRET=your-jwt-secret-key-here
API_ENCRYPTION_KEY=your-api-encryption-key-here

# Database encryption
DB_ENCRYPTION_KEY=your-database-encryption-key-here

# TLS certificates
TLS_CERT_FILE=/path/to/server.crt
TLS_KEY_FILE=/path/to/server.key
TLS_CA_FILE=/path/to/ca.crt

# API keys (hashed values)
API_KEY_001_HASH=hashed-api-key-001
API_KEY_002_HASH=hashed-api-key-002
```

### 2. Key Rotation Implementation

```python
# src/security/key_rotation.py
import schedule
import time
from datetime import datetime, timedelta

class KeyRotationManager:
    def __init__(self, key_manager, notification_service):
        self.key_manager = key_manager
        self.notification_service = notification_service
        
    def setup_rotation_schedule(self):
        # Master keys - quarterly rotation
        schedule.every(90).days.do(self._rotate_master_keys)
        
        # API keys - monthly rotation
        schedule.every(30).days.do(self._rotate_api_keys)
        
        # JWT keys - weekly rotation
        schedule.every(7).days.do(self._rotate_jwt_keys)
        
        # Check for rotation needs daily
        schedule.every().day.at("02:00").do(self._check_rotation_needs)
    
    def _rotate_master_keys(self):
        try:
            # Generate new master key
            new_key = self.key_manager.generate_master_key()
            
            # Update key in secure storage
            self.key_manager.update_master_key(new_key)
            
            # Notify administrators
            self.notification_service.send_notification(
                "Master key rotated successfully",
                level="INFO"
            )
            
        except Exception as e:
            self.notification_service.send_notification(
                f"Master key rotation failed: {str(e)}",
                level="CRITICAL"
            )
    
    def _check_rotation_needs(self):
        # Check keys nearing expiration
        expiring_keys = self.key_manager.get_expiring_keys(days=7)
        
        for key in expiring_keys:
            self.notification_service.send_notification(
                f"Key {key['id']} expires in {key['days_remaining']} days",
                level="WARNING"
            )
    
    def run_rotation_scheduler(self):
        while True:
            schedule.run_pending()
            time.sleep(3600)  # Check every hour
```

### 3. Secure Key Storage

```python
# src/security/key_storage.py
import os
import hvac  # HashiCorp Vault client
from cryptography.fernet import Fernet

class SecureKeyStorage:
    def __init__(self, storage_type: str = "environment"):
        self.storage_type = storage_type
        
        if storage_type == "vault":
            self.vault_client = hvac.Client(
                url=os.getenv("VAULT_URL"),
                token=os.getenv("VAULT_TOKEN")
            )
    
    def store_key(self, key_id: str, key_value: str, metadata: dict = None):
        if self.storage_type == "environment":
            # For development/testing - not recommended for production
            os.environ[f"MT5_KEY_{key_id}"] = key_value
            
        elif self.storage_type == "vault":
            # Production recommended - HashiCorp Vault
            self.vault_client.secrets.kv.v2.create_or_update_secret(
                path=f"mt5-keys/{key_id}",
                secret={
                    "key": key_value,
                    "metadata": metadata or {},
                    "created_at": datetime.utcnow().isoformat()
                }
            )
            
        elif self.storage_type == "file":
            # File-based storage with encryption
            self._store_encrypted_file(key_id, key_value, metadata)
    
    def retrieve_key(self, key_id: str) -> str:
        if self.storage_type == "environment":
            return os.getenv(f"MT5_KEY_{key_id}")
            
        elif self.storage_type == "vault":
            response = self.vault_client.secrets.kv.v2.read_secret_version(
                path=f"mt5-keys/{key_id}"
            )
            return response['data']['data']['key']
            
        elif self.storage_type == "file":
            return self._retrieve_encrypted_file(key_id)
    
    def delete_key(self, key_id: str):
        if self.storage_type == "vault":
            self.vault_client.secrets.kv.v2.delete_metadata_and_all_versions(
                path=f"mt5-keys/{key_id}"
            )
```

## ⚙️ Security Configuration

### 1. Deployment Security Checklist

```bash
#!/bin/bash
# scripts/security_check.sh

echo "🔐 MT5 Trading System Security Deployment Checklist"

# Check 1: Environment variables
echo "1. Checking critical environment variables..."
required_vars=("JWT_SECRET" "MASTER_ENCRYPTION_KEY" "DB_ENCRYPTION_KEY")
for var in "${required_vars[@]}"; do
    if [ -z "${!var}" ]; then
        echo "❌ Missing required environment variable: $var"
        exit 1
    else
        echo "✅ $var is set"
    fi
done

# Check 2: File permissions
echo "2. Checking file permissions..."
if [ -f "config/security/encryption_keys.yaml" ]; then
    perms=$(stat -c "%a" config/security/encryption_keys.yaml)
    if [ "$perms" != "600" ]; then
        echo "❌ Security config file has incorrect permissions: $perms"
        echo "   Run: chmod 600 config/security/encryption_keys.yaml"
        exit 1
    else
        echo "✅ Security config file permissions are correct"
    fi
fi

# Check 3: TLS certificates
echo "3. Checking TLS certificates..."
if [ -n "$TLS_CERT_FILE" ] && [ -f "$TLS_CERT_FILE" ]; then
    # Check certificate expiration
    expiry=$(openssl x509 -enddate -noout -in "$TLS_CERT_FILE" | cut -d= -f2)
    echo "✅ TLS certificate found, expires: $expiry"
else
    echo "⚠️  TLS certificate not found or not configured"
fi

# Check 4: Strong passwords
echo "4. Checking password strength..."
python3 -c "
import os
import re

def check_password_strength(password):
    if len(password) < 12:
        return False, 'Password too short (minimum 12 characters)'
    if not re.search(r'[A-Z]', password):
        return False, 'Password must contain uppercase letters'
    if not re.search(r'[a-z]', password):
        return False, 'Password must contain lowercase letters'
    if not re.search(r'[0-9]', password):
        return False, 'Password must contain numbers'
    if not re.search(r'[!@#$%^&*(),.?\":{}|<>]', password):
        return False, 'Password must contain special characters'
    return True, 'Password strength OK'

# Check JWT secret
jwt_secret = os.getenv('JWT_SECRET', '')
valid, msg = check_password_strength(jwt_secret)
if valid:
    print('✅ JWT_SECRET strength OK')
else:
    print(f'❌ JWT_SECRET: {msg}')
    exit(1)
"

echo "5. Security configuration complete! ✅"
```

### 2. Runtime Security Monitoring

```python
# src/security/monitoring.py
import logging
import time
from collections import defaultdict
from datetime import datetime, timedelta

class SecurityMonitor:
    def __init__(self):
        self.failed_attempts = defaultdict(list)
        self.suspicious_activity = []
        self.logger = logging.getLogger("security")
    
    def log_failed_authentication(self, ip_address: str, user_agent: str, endpoint: str):
        timestamp = datetime.utcnow()
        
        # Track failed attempts per IP
        self.failed_attempts[ip_address].append(timestamp)
        
        # Check for brute force attacks
        recent_attempts = [
            t for t in self.failed_attempts[ip_address] 
            if t > timestamp - timedelta(minutes=15)
        ]
        
        if len(recent_attempts) > 5:
            self._handle_brute_force_attack(ip_address, user_agent)
        
        # Log the event
        self.logger.warning(
            f"Failed authentication from {ip_address} "
            f"for endpoint {endpoint} "
            f"User-Agent: {user_agent}"
        )
    
    def log_suspicious_activity(self, activity_type: str, details: dict):
        timestamp = datetime.utcnow()
        
        event = {
            "timestamp": timestamp,
            "type": activity_type,
            "details": details
        }
        
        self.suspicious_activity.append(event)
        
        self.logger.warning(
            f"Suspicious activity detected: {activity_type} "
            f"Details: {details}"
        )
        
        # Check for patterns
        self._analyze_activity_patterns()
    
    def _handle_brute_force_attack(self, ip_address: str, user_agent: str):
        # Implement IP blocking or rate limiting
        self.logger.critical(
            f"Brute force attack detected from {ip_address} "
            f"User-Agent: {user_agent}"
        )
        
        # Add to blocked IPs (implement in your firewall/rate limiter)
        self._block_ip_address(ip_address)
    
    def _analyze_activity_patterns(self):
        # Analyze recent suspicious activities for patterns
        recent_activities = [
            event for event in self.suspicious_activity
            if event["timestamp"] > datetime.utcnow() - timedelta(hours=1)
        ]
        
        # Check for coordinated attacks
        if len(recent_activities) > 10:
            self.logger.critical("Potential coordinated attack detected")
```

## 📋 Best Practices

### 1. Security Development Lifecycle

```yaml
# Security development checklist
security_sdl:
  design_phase:
    - threat_modeling: "Complete threat model for all components"
    - security_requirements: "Define security requirements"
    - architecture_review: "Security architecture review"
    
  implementation_phase:
    - secure_coding: "Follow secure coding guidelines"
    - code_review: "Security-focused code reviews"
    - static_analysis: "Static security analysis tools"
    
  testing_phase:
    - penetration_testing: "Regular penetration testing"
    - vulnerability_scanning: "Automated vulnerability scans"
    - security_testing: "Security-specific test cases"
    
  deployment_phase:
    - configuration_review: "Security configuration review"
    - environment_hardening: "Production environment hardening"
    - monitoring_setup: "Security monitoring implementation"
```

### 2. Incident Response Plan

```yaml
# Incident response procedures
incident_response:
  detection:
    - monitoring_alerts: "24/7 security monitoring"
    - user_reports: "User-reported security issues"
    - automated_detection: "Automated threat detection"
    
  response_team:
    - security_lead: "Primary security contact"
    - system_admin: "System administrator"
    - development_lead: "Lead developer"
    - legal_counsel: "Legal representative"
    
  response_procedures:
    severity_1:  # Critical security breach
      - containment: "Immediate system isolation"
      - assessment: "Damage assessment"
      - communication: "Stakeholder notification"
      - recovery: "System restoration"
      
    severity_2:  # Significant security issue
      - investigation: "Detailed investigation"
      - mitigation: "Apply security patches"
      - monitoring: "Enhanced monitoring"
      
    severity_3:  # Minor security concern
      - evaluation: "Risk evaluation"
      - planning: "Remediation planning"
      - implementation: "Fix implementation"
```

### 3. Security Training

```markdown
## Security Training Requirements

### For Developers
- Secure coding practices
- OWASP Top 10 awareness
- Cryptography fundamentals
- Security testing methods

### For Operations
- Security monitoring
- Incident response
- Access management
- Configuration security

### For Users
- Password security
- Phishing awareness
- Access control
- Data handling
```

## 📊 Compliance

### 1. Regulatory Compliance

```yaml
# Compliance frameworks
compliance_frameworks:
  financial_regulations:
    - mifid_ii: "Markets in Financial Instruments Directive"
    - sox: "Sarbanes-Oxley Act"
    - basel_iii: "Basel III banking regulations"
    
  data_protection:
    - gdpr: "General Data Protection Regulation"
    - ccpa: "California Consumer Privacy Act"
    - pipeda: "Personal Information Protection Act"
    
  security_standards:
    - iso_27001: "Information Security Management"
    - nist_csf: "NIST Cybersecurity Framework"
    - pci_dss: "Payment Card Industry Data Security"
```

### 2. Audit Trail

```python
# src/security/audit.py
import json
from datetime import datetime
from typing import Any, Dict

class SecurityAuditLogger:
    def __init__(self, storage_backend):
        self.storage = storage_backend
    
    def log_security_event(self, 
                          event_type: str, 
                          user_id: str, 
                          resource: str, 
                          action: str, 
                          result: str, 
                          ip_address: str = None,
                          user_agent: str = None,
                          additional_data: Dict[str, Any] = None):
        
        audit_entry = {
            "timestamp": datetime.utcnow().isoformat(),
            "event_id": self._generate_event_id(),
            "event_type": event_type,
            "user_id": user_id,
            "resource": resource,
            "action": action,
            "result": result,
            "ip_address": ip_address,
            "user_agent": user_agent,
            "additional_data": additional_data or {}
        }
        
        # Store audit entry
        self.storage.store_audit_entry(audit_entry)
        
        # Log to security log
        self._log_to_security_file(audit_entry)
    
    def _generate_event_id(self) -> str:
        import uuid
        return str(uuid.uuid4())
    
    def _log_to_security_file(self, audit_entry: dict):
        log_line = json.dumps(audit_entry, default=str)
        
        with open("logs/security_audit.log", "a") as f:
            f.write(log_line + "\n")
```

---

## 🚀 Implementation Steps

### Phase 1: Basic Security (Week 1-2)
1. Implement JWT authentication
2. Set up basic RBAC
3. Configure TLS encryption
4. Basic input validation

### Phase 2: Advanced Security (Week 3-4)
1. Implement data encryption
2. Set up key management
3. Advanced audit logging
4. Security monitoring

### Phase 3: Enterprise Security (Week 5-6)
1. Integrate with enterprise identity providers
2. Advanced threat detection
3. Compliance reporting
4. Security automation

**Remember**: Security is not a one-time implementation but an ongoing process that requires regular updates, monitoring, and improvement.

---

*Last Updated: 2025*  
*Security Guide Version: 1.0.0*