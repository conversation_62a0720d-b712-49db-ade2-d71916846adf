## 🏗️ 分布式进程隔离架构下的跟单系统设计

graph TB
    subgraph "主协调器进程"
        Coordinator[MT5Coordinator]
        RequestHandler[MT5RequestHandler]
        ProcessManager[MT5ProcessManager]
        JetStream[JetStream Client]
    end
    
    subgraph "MT5终端进程 (PID: 34780/24092)"
        MT5_ACC001[Terminal64.exe ACC001]
        MT5_ACC002[Terminal64.exe ACC002]
        MT5Process1[MT5Process ACC001]
        MT5Process2[MT5Process ACC002]
    end
    
    subgraph "监控器进程 (PID: 10848/16780)"
        Monitor1[Monitor ACC001]
        Monitor2[Monitor ACC002]
        ProxyClient1[JetStream Proxy Client]
        ProxyClient2[JetStream Proxy Client]
    end
    
    subgraph "执行器进程 (PID: 2488/33380)"
        Executor1[Executor ACC001]
        Executor2[Executor ACC002]
    end
    
    subgraph "NATS JetStream"
        NATS[(NATS Server)]
        Stream[MT5_TRADING_STREAM]
    end
    
    %% 正常连接
    Coordinator --> ProcessManager
    ProcessManager --> MT5Process1
    ProcessManager --> MT5Process2
    RequestHandler --> JetStream
    JetStream --> NATS
    
    %% 问题连接 - 红色表示失败
    Monitor1 -.->|❌ 请求失败| ProxyClient1
    Monitor2 -.->|❌ 请求失败| ProxyClient2
    ProxyClient1 -.->|📤 MT5.REQUEST.ACC001| NATS
    ProxyClient2 -.->|📤 MT5.REQUEST.ACC002| NATS
    
    %% 缺失的连接 - 虚线表示应该存在但不工作
    NATS -.->|❌ 无响应| RequestHandler
    RequestHandler -.->|❌ 无法路由| ProcessManager
    
    %% 状态标注
    MT5Process1 -.->|connected: False| ProcessManager
    MT5Process2 -.->|connected: False| ProcessManager
    
    style ProxyClient1 fill:#ffcccc
    style ProxyClient2 fill:#ffcccc
    style RequestHandler fill:#ffcccc
    style NATS fill:#ffffcc








### 📊 完整信号流程图

```mermaid
graph TB
    subgraph "账户1进程 (主账户)"
        A1_MT5[MT5客户端进程1]
        A1_Monitor[监控器进程1]
        A1_Bridge[信号桥接器1]
    end
    
    subgraph "账户2进程 (从账户)"
        A2_Listener[监听器进程2]
        A2_Executor[执行器进程2]
        A2_MT5[MT5客户端进程2]
    end
    
    subgraph "账户3进程 (主账户)"
        A3_MT5[MT5客户端进程3]
        A3_Monitor[监控器进程3]
        A3_Bridge[信号桥接器3]
    end
    
    subgraph "账户4进程 (从账户)"
        A4_Listener[监听器进程4]
        A4_Executor[执行器进程4]
        A4_MT5[MT5客户端进程4]
    end
    
    subgraph "消息基础设施"
        NATS[(NATS JetStream)]
        Redis[(Redis)]
    end
    
    %% 信号流
    A1_MT5 -->|持仓变化| A1_Monitor
    A1_Monitor -->|生成信号| A1_Bridge
    A1_Bridge -->|发布| NATS
    
    NATS -->|订阅| A2_Listener
    A2_Listener -->|路由| A2_Executor
    A2_Executor -->|执行| A2_MT5
    
    %% 状态同步
    A1_Monitor -.->|状态| Redis
    A2_Executor -.->|结果| Redis
    
    %% 第二对
    A3_MT5 -->|持仓变化| A3_Monitor
    A3_Monitor -->|生成信号| A3_Bridge
    A3_Bridge -->|发布| NATS
    
    NATS -->|订阅| A4_Listener
    A4_Listener -->|路由| A4_Executor
    A4_Executor -->|执行| A4_MT5
```

## 🔑 关键组件设计

### 1. 进程管理器（Process Manager）

```yaml
关键职责:
  - 启动和管理每个账户的独立进程组
  - 进程健康监控
  - 崩溃自动重启
  - 资源隔离（CPU/内存限制）

实现要点:
  - 使用 multiprocessing 创建进程
  - 每个账户3个核心进程：MT5客户端、监控器、执行器
  - 进程间通过 NATS 通信，避免共享内存
  - 使用进程监督器模式
```

### 2. MT5客户端进程（MT5 Client Process）

```yaml
关键职责:
  - 维护单一MT5连接
  - 提供RPC接口供其他进程调用
  - 处理MT5 API的所有交互

实现要点:
  - 每个账户独立的 terminal64.exe 实例
  - 通过 NATS Request-Reply 模式提供服务
  - 实现连接池和重连机制
  - 本地缓存减少API调用
```

### 3. 监控器进程（Monitor Process）

```yaml
关键职责:
  - 定期轮询MT5客户端获取持仓
  - 检测持仓变化
  - 生成标准化交易信号
  - 发布信号到消息总线

实现要点:
  - 异步轮询，可配置检查间隔
  - 持仓快照对比算法
  - 信号去重和验证
  - 批量发送优化
```

### 4. 信号路由系统（Signal Router）

```yaml
关键职责:
  - 接收主账户交易信号
  - 查询跟单关系配置
  - 构建目标账户的跟单信号
  - 分发到指定从账户

实现要点:
  - 基于 NATS 主题的发布订阅
  - 支持一对多跟单
  - 信号转换（正向/反向）
  - 负载均衡和故障转移
```

## 🔄 完整信号流程

```mermaid
sequenceDiagram
    participant MT5_1 as MT5进程1(主)
    participant Mon1 as 监控器1
    participant NATS as NATS JetStream
    participant Redis as Redis
    participant Router as 信号路由器
    participant Exec2 as 执行器2
    participant MT5_2 as MT5进程2(从)
    
    Note over MT5_1,Mon1: 主账户检测交易
    loop 每秒轮询
        Mon1->>MT5_1: RPC: get_positions()
        MT5_1-->>Mon1: 当前持仓列表
        Mon1->>Mon1: 检测变化
    end
    
    Note over Mon1,NATS: 发布原始信号
    Mon1->>NATS: publish("trades.acc1.raw", signal)
    Mon1->>Redis: set("position:acc1:xxx", data)
    
    Note over NATS,Router: 路由处理
    NATS->>Router: 信号订阅触发
    Router->>Redis: get("relations:acc1")
    Redis-->>Router: [acc2:正向1.0]
    
    Note over Router,Exec2: 分发跟单信号
    Router->>NATS: publish("copy.acc2", copySignal)
    NATS->>Exec2: 跟单信号
    
    Note over Exec2,MT5_2: 执行交易
    Exec2->>MT5_2: RPC: send_order(request)
    MT5_2-->>Exec2: 交易结果
    Exec2->>Redis: set("trade:acc2:xxx", result)
    Exec2->>NATS: publish("results.acc2", result)
```

## 🛡️ 确保可靠性的关键设计

### 1. 进程隔离保证

```yaml
独立进程空间:
  - 每个账户完全独立的进程组
  - 独立的MT5终端实例
  - 独立的数据目录
  - 崩溃隔离

通信隔离:
  - 不使用共享内存
  - 所有通信通过NATS
  - 使用主题命名空间隔离
```

### 2. 消息可靠性

```yaml
NATS JetStream配置:
  持久化: 
    - 所有交易信号持久化
    - 消息保留策略
    - 至少一次投递
    
  消费者组:
    - 每个从账户独立消费者
    - 消息确认机制
    - 重试策略
    
  主题设计:
    - trades.{account}.raw     # 原始交易信号
    - copy.{account}          # 跟单指令
    - monitor.{account}.rpc   # RPC请求
    - results.{account}       # 执行结果
```

### 3. 状态管理

```yaml
Redis数据结构:
  账户状态:
    - account:{id}:status     # 在线状态
    - account:{id}:positions  # 当前持仓
    - account:{id}:health     # 健康检查
    
  跟单关系:
    - relations:master:{id}   # 主账户的从账户列表
    - relations:slave:{id}    # 从账户的主账户列表
    
  信号追踪:
    - signal:{id}:status      # 信号执行状态
    - signal:{id}:results     # 执行结果
```

### 4. RPC通信模式

```yaml
MT5客户端RPC服务:
  请求主题: mt5.{account}.rpc.{method}
  响应主题: _INBOX.{unique_id}
  
  支持的方法:
    - get_positions
    - send_order
    - close_position
    - modify_position
    - get_account_info
    
  错误处理:
    - 超时重试
    - 熔断机制
    - 降级策略
```

## 📋 配置示例

```yaml
# 账户配置
accounts:
  ACC001:
    type: master
    process_group:
      mt5_client:
        terminal_path: "C:/MT5_1/terminal64.exe"
        data_path: "C:/MT5_Data/ACC001"
      monitor:
        check_interval: 1000  # ms
        batch_size: 10
      
  ACC002:
    type: slave
    follows: ACC001
    process_group:
      mt5_client:
        terminal_path: "C:/MT5_2/terminal64.exe"
        data_path: "C:/MT5_Data/ACC002"
      executor:
        max_concurrent: 5
        retry_attempts: 3

# 跟单关系
copy_relations:
  - master: ACC001
    slaves:
      - account: ACC002
        mode: forward
        ratio: 1.0
      - account: ACC004
        mode: reverse
        ratio: 0.5
```

## 🚦 进程启动顺序

```mermaid
graph TD
    A[系统启动] --> B[启动基础设施]
    B --> B1[NATS JetStream]
    B --> B2[Redis]
    
    B --> C[启动进程管理器]
    C --> D[为每个账户创建进程组]
    
    D --> E1[账户1进程组]
    E1 --> E11[MT5客户端1]
    E1 --> E12[监控器1]
    E1 --> E13[信号桥接1]
    
    D --> E2[账户2进程组]
    E2 --> E21[MT5客户端2]
    E2 --> E22[监听器2]
    E2 --> E23[执行器2]
    
    E11 & E12 & E13 --> F1[账户1就绪]
    E21 & E22 & E23 --> F2[账户2就绪]
    
    F1 & F2 --> G[系统就绪]
```

## 🔧 实施要点总结

1. **进程架构**
   - 每个账户3个核心进程
   - 进程间零共享，纯消息通信
   - 支持横向扩展

2. **通信设计**
   - NATS作为进程间通信总线
   - Request-Reply模式的RPC
   - Publish-Subscribe的信号分发

3. **可靠性保证**
   - JetStream消息持久化
   - Redis状态快照
   - 进程监督和自动重启

4. **性能优化**
   - 批量信号处理
   - 本地缓存减少RPC
   - 异步非阻塞设计

5. **监控告警**
   - 进程健康检查
   - 消息延迟监控
   - 交易成功率统计

这样的设计可以确保在进程完全隔离的情况下，实现可靠的跨主机分布式跟单交易！