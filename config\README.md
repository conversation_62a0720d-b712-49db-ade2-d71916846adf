# MT5交易系统配置文件说明

## 当前配置文件

### optimized_system.yaml (主配置)
- **用途**: 生产环境主配置文件
- **版本**: 4.0.0
- **特性**: 融合所有优化的最终配置
- **包含**: 分布式路由、账户管理、故障转移、动态角色切换

### system.yaml (参考配置)
- **用途**: 原始分布式配置参考
- **版本**: 3.0.0
- **状态**: 保留作为参考，不推荐使用

## 备份文件 (backup目录)
- enhanced_system.yaml: 增强架构配置备份
- system_distributed.yaml: 分布式配置副本备份

## 配置优势

### 最优化配置特性:
1. **统一架构**: 融合混合架构和分布式特性
2. **环境变量**: 支持环境变量配置，便于部署
3. **性能优化**: 完整的性能调优参数
4. **故障转移**: 完善的故障转移机制
5. **监控告警**: 集成监控和告警配置
6. **安全配置**: 完整的安全和认证配置

### 使用建议:
- 生产环境: 使用 optimized_system.yaml
- 开发环境: 可以基于 optimized_system.yaml 创建开发配置
- 测试环境: 使用 optimized_system.yaml 的简化版本

## 迁移完成
系统已成功迁移到最优化配置架构。
