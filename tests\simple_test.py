#!/usr/bin/env python3
"""
简化的Docker Compose部署测试脚本
测试所有6个工业级服务的部署状态
"""
import sys
import time
import requests
import redis


def test_redis_connection():
    """测试Redis连接"""
    try:
        client = redis.Redis(host='localhost', port=6379, decode_responses=True)
        client.ping()
        # 测试Hash操作
        client.hset('test:deployment', 'status', 'success')
        result = client.hget('test:deployment', 'status')
        client.delete('test:deployment')
        return result == 'success'
    except Exception as e:
        print(f"Redis测试失败: {e}")
        return False


def test_prometheus_connection():
    """测试Prometheus连接"""
    try:
        response = requests.get('http://localhost:9090/api/v1/query?query=up', timeout=5)
        return response.status_code == 200 and 'data' in response.json()
    except Exception as e:
        print(f"Prometheus测试失败: {e}")
        return False


def test_pushgateway_connection():
    """测试Pushgateway连接"""
    try:
        # 推送测试指标
        data = 'test_metric{job="deployment_test"} 1\n'
        response = requests.post(
            'http://localhost:9091/metrics/job/deployment_test',
            data=data,
            headers={'Content-Type': 'text/plain'},
            timeout=5
        )
        return response.status_code == 200
    except Exception as e:
        print(f"Pushgateway测试失败: {e}")
        return False


def test_grafana_connection():
    """测试Grafana连接"""
    try:
        response = requests.get('http://localhost:3000/api/health', timeout=15)
        if response.status_code == 200:
            data = response.json()
            return 'database' in data and data['database'] == 'ok'
        return False
    except Exception as e:
        print(f"Grafana测试失败: {e}")
        return False


def test_redis_commander_connection():
    """测试Redis Commander连接"""
    try:
        response = requests.get('http://localhost:8081/', timeout=5)
        return response.status_code == 200
    except Exception as e:
        print(f"Redis Commander测试失败: {e}")
        return False


def test_nats_connection():
    """测试NATS连接"""
    try:
        # 简单的HTTP监控端点测试
        response = requests.get('http://localhost:8222/varz', timeout=5)
        return response.status_code == 200
    except Exception as e:
        print(f"NATS测试失败: {e}")
        return False


def main():
    """主测试函数"""
    print("🚀 开始Docker Compose部署测试...")
    print("=" * 60)
    
    # 等待服务启动
    print("⏳ 等待服务完全启动...")
    time.sleep(3)
    
    tests = [
        ("Redis Hash优化", test_redis_connection),
        ("NATS JetStream", test_nats_connection),
        ("Prometheus监控", test_prometheus_connection),
        ("Pushgateway指标", test_pushgateway_connection),
        ("Grafana仪表板", test_grafana_connection),
        ("Redis Commander", test_redis_commander_connection),
    ]
    
    results = {}
    
    for service_name, test_func in tests:
        print(f"🔍 测试 {service_name}...", end=" ")
        try:
            result = test_func()
            results[service_name] = result
            print(f"{'✅ 通过' if result else '❌ 失败'}")
        except Exception as e:
            results[service_name] = False
            print(f"❌ 异常: {e}")
    
    print("\n" + "=" * 60)
    print("📊 测试结果汇总:")
    print("=" * 60)
    
    passed = sum(1 for result in results.values() if result)
    total = len(results)
    
    for service, result in results.items():
        status = "✅ 正常" if result else "❌ 异常"
        print(f"  {service:<20} {status}")
    
    print(f"\n🎯 总体结果: {passed}/{total} 服务正常运行")
    
    if passed == total:
        print("🎉 所有工业级服务部署成功！")
        print("\n🌐 服务访问地址:")
        print("  • Grafana仪表板:     http://localhost:3000")
        print("  • Prometheus监控:    http://localhost:9090")
        print("  • Redis Commander:   http://localhost:8081")
        print("  • Pushgateway:       http://localhost:9091")
        print("  • NATS监控:          http://localhost:8222")
        print("  • Redis:             localhost:6379")
        print("  • NATS:              localhost:4222")
        return 0
    else:
        print("⚠️  部分服务存在问题，请检查Docker日志")
        return 1


if __name__ == "__main__":
    sys.exit(main())
