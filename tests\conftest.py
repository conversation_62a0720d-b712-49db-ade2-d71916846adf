"""
测试配置和fixtures
"""
import pytest
import sys
from unittest.mock import Mock, MagicMock
import asyncio


# 创建模拟的MetaTrader5模块
class MockMT5:
    """模拟MetaTrader5模块"""
    
    TRADE_ACTION_DEAL = 1
    TRADE_ACTION_PENDING = 5
    ORDER_TYPE_BUY = 0
    ORDER_TYPE_SELL = 1
    
    @staticmethod
    def initialize():
        return True
    
    @staticmethod
    def shutdown():
        pass
    
    @staticmethod
    def login(login, password, server):
        return True
    
    @staticmethod
    def account_info():
        return MagicMock(
            login=5000001,
            balance=10000.0,
            equity=10000.0,
            margin=0.0,
            free_margin=10000.0,
            leverage=100,
            currency="USD"
        )
    
    @staticmethod
    def positions_get():
        return []
    
    @staticmethod
    def orders_get():
        return []
    
    @staticmethod
    def order_send(request):
        return MagicMock(
            retcode=10009,  # TRADE_RETCODE_DONE
            deal=123456,
            order=123456,
            volume=request.get('volume', 0.1),
            price=request.get('price', 1.0),
            comment="Mock trade"
        )
    
    @staticmethod
    def symbol_info(symbol):
        return MagicMock(
            name=symbol,
            bid=1.1234,
            ask=1.1237,
            spread=3,
            digits=5,
            trade_mode=1
        )


# 在导入之前设置模拟模块
sys.modules['MetaTrader5'] = MockMT5()


# 创建模拟的sklearn模块
class MockSklearn:
    """模拟sklearn模块"""
    
    class LinearRegression:
        def __init__(self):
            self.coef_ = [1.0]
            self.intercept_ = 0.0
            
        def fit(self, X, y):
            return self
            
        def predict(self, X):
            return [1.0] * len(X)
    
    class StandardScaler:
        def __init__(self):
            pass
            
        def fit(self, X):
            return self
            
        def transform(self, X):
            return X
            
        def fit_transform(self, X):
            return X


# 设置sklearn模拟
sys.modules['sklearn'] = MockSklearn()
sys.modules['sklearn.linear_model'] = MagicMock()
sys.modules['sklearn.linear_model'].LinearRegression = MockSklearn.LinearRegression
sys.modules['sklearn.preprocessing'] = MagicMock()
sys.modules['sklearn.preprocessing'].StandardScaler = MockSklearn.StandardScaler


@pytest.fixture
def event_loop():
    """创建事件循环"""
    loop = asyncio.new_event_loop()
    yield loop
    loop.close()


@pytest.fixture
def mock_mt5():
    """提供MT5模拟对象"""
    return MockMT5()


@pytest.fixture
def sample_config():
    """提供示例配置"""
    return {
        'account': {
            'id': 'TEST_ACC_001',
            'name': '测试账户',
            'enabled': True,
            'connection': {
                'login': 5000001,
                'password': 'test_password',
                'server': 'Demo-Server',
                'terminal_path': 'C:/MT5/terminal.exe'
            },
            'settings': {
                'initial_grade': 0,
                'timezone': 0,
                'currency': 'USD'
            },
            'trading': {
                'allowed_symbols': ['EURUSD', 'GBPUSD'],
                'max_spread': 3.0,
                'slippage': 10
            },
            'risk_management': {
                'max_positions': 5,
                'max_lot_size': 1.0,
                'max_daily_loss': 1000,
                'max_daily_trades': 20,
                'scale_factor': 1.0
            },
            'monitoring': {
                'polling_interval': 0.001,
                'adaptive_polling': True,
                'alert_on_error': True
            },
            'role_config': {
                'default_role': 'standalone',
                'master_settings': {
                    'enabled': True,
                    'broadcast_channel': 'trade_signals'
                },
                'slave_settings': {
                    'enabled': True,
                    'subscribed_masters': []
                }
            },
            'remote_connection': {
                'nats_host': 'localhost',
                'nats_port': 4222,
                'redis_host': 'localhost',
                'redis_port': 6379,
                'api_host': 'localhost',
                'api_port': 8000
            },
            'notifications': {
                'telegram': {
                    'enabled': False,
                    'bot_token': '',
                    'chat_id': ''
                },
                'email': {
                    'enabled': False,
                    'address': ''
                }
            }
        }
    }


@pytest.fixture
def mock_redis():
    """模拟Redis客户端"""
    redis_mock = Mock()
    redis_mock.get = Mock(return_value=None)
    redis_mock.set = Mock(return_value=True)
    redis_mock.scan_keys = Mock(return_value=[])
    return redis_mock


@pytest.fixture
def mock_nats():
    """模拟NATS客户端"""
    nats_mock = Mock()
    nats_mock.publish = Mock()
    nats_mock.subscribe = Mock()
    return nats_mock