#!/usr/bin/env python3
"""
MT5账户监控器 - 严格职责分离架构
仅负责监控MT5账户状态和持仓变化
通过消息系统发布监控事件，不执行任何交易操作
"""

import asyncio
import time
from typing import Dict, Any, Optional, Set, List
from dataclasses import dataclass
from datetime import datetime

# 移除基类依赖，直接实现监控功能
from ..messaging.message_types import TradeSignal, OrderType, SignalType
from ..utils.logger import get_logger
from ..messaging.jetstream_client import JetStreamClient
from ..utils.batch_processor import get_signal_batch_processor

# 明确导入RPC客户端（用于架构验证）
try:
    from .mt5_rpc_client import MT5RPCClient
except ImportError:
    pass  # 运行时导入即可

logger = get_logger(__name__)


@dataclass
class MonitoringEvent:
    """监控事件"""
    event_type: str
    account_id: str
    timestamp: float
    data: Dict[str, Any]
    event_id: str


class MT5AccountMonitor:
    """
    MT5账户监控器 - 严格职责分离实现
    
    职责：
    监控持仓变化
    监控账户状态
    发布监控事件
    
    职责边界：
    不执行任何交易
    不修改MT5状态  
    不处理跟单逻辑
    """
    
    def __init__(self, account_id: str, account_config: Dict[str, Any],
                 event_publisher: JetStreamClient, rpc_client, host_id: str = None):
        # 基础属性
        self.account_config = account_config
        self.running = False
        self.start_time = None
        self.account_id = account_id
        self.host_id = host_id or 'unknown'
        self.event_publisher = event_publisher
        
        # 只使用RPC架构 - 移除双重模式
        self.rpc_client = rpc_client
        if not self.rpc_client:
            raise RuntimeError(f"监控器必须使用RPC架构: {self.account_id}")
        
        logger.info(f"监控器使用统一RPC架构: {self.account_id}")
        
        self.current_positions: Dict[int, Dict] = {}
        self.last_position_check = 0
        
        try:
            from .config_manager import get_config_manager
            config_mgr = get_config_manager()

            self.position_check_interval = config_mgr.get('monitoring.position_check_interval', 0.5)

            self.sleep_interval = config_mgr.get('monitoring.sleep_interval', 0.2)
            self.polling_interval = config_mgr.get('monitoring.position_monitoring.polling_interval', 0.5)

            logger.info(f"监控配置加载: {self.account_id} - 持仓检查间隔:{self.position_check_interval}s, 睡眠间隔:{self.sleep_interval}s")

        except Exception as e:
            logger.warning(f"配置加载失败，使用默认值: {e}")
            self.position_check_interval = 0.5  # 默认0.5秒间隔
            self.sleep_interval = 0.2
            self.polling_interval = 0.5
        
        self.monitoring_stats = {
            'events_published': 0,
            'position_changes_detected': 0,
            'monitoring_errors': 0,
            'last_check_time': 0
        }
        
        self.event_topic = f"MT5.MONITOR.{self.host_id}.{self.account_id}"
        
        # 初始化批处理器
        self.signal_batch_processor = get_signal_batch_processor()
        self.signal_batch_processor.set_signal_router(self)
        
        logger.info(f"MT5账户监控器初始化: {account_id} - 纯监控模式 (启用批处理)")
    
    async def start(self):
        """启动监控器 - 实现启动接口"""  
        return await self.start_monitoring()
    
    async def start_monitoring(self) -> bool:
        """启动监控 - 仅监控，不执行"""
        if self.running:
            logger.warning(f"监控器已在运行: {self.account_id}")
            return False
        
        try:
            # 使用RPC检查连接状态
            health_result = await self.health_check_rpc()
            if health_result.get('status') != 'success':
                logger.warning(f"RPC健康检查失败，但监控器将尝试启动: {self.account_id}")
            
            self.running = True
            self.start_time = time.time()
            
            asyncio.create_task(self._monitoring_loop())
            
            await self._publish_monitoring_event("monitor_started", {
                "account_id": self.account_id,
                "start_time": self.start_time
            })
            
            logger.info(f"MT5账户监控器已启动: {self.account_id}")
            return True
            
        except Exception as e:
            logger.error(f"启动监控器失败: {e}")
            self.running = False
            return False
    
    async def stop_monitoring(self):
        """停止监控"""
        if not self.running:
            return
        
        self.running = False
        
        await self._publish_monitoring_event("monitor_stopped", {
            "account_id": self.account_id,
            "stop_time": time.time(),
            "stats": self.monitoring_stats
        })
        
        logger.info(f"MT5账户监控器已停止: {self.account_id}")

    async def _ensure_connection(self) -> bool:
        """确保 RPC 连接状态 - 增强版本，支持重试和状态跟踪"""
        max_retries = 3
        retry_delay = 1.0
        
        for attempt in range(max_retries):
            try:
                # 使用RPC进行健康检查
                health_result = await asyncio.wait_for(
                    self.health_check_rpc(), 
                    timeout=10.0  # 10秒超时
                )
                
                if health_result.get('status') == 'success':
                    if attempt > 0:
                        logger.info(f"RPC连接恢复正常: {self.account_id}, 尝试次数: {attempt + 1}")
                    else:
                        logger.debug(f"RPC连接状态正常: {self.account_id}")
                    
                    # 重置错误计数
                    if not hasattr(self, 'connection_errors'):
                        self.connection_errors = 0
                    self.connection_errors = 0
                    
                    return True
                else:
                    error_msg = health_result.get('error', '未知错误')
                    logger.warning(f"RPC连接状态异常: {self.account_id}, 尝试: {attempt + 1}, 错误: {error_msg}")
                    
                    # 记录错误
                    if not hasattr(self, 'connection_errors'):
                        self.connection_errors = 0
                    self.connection_errors += 1

            except asyncio.TimeoutError:
                logger.warning(f"RPC健康检查超时: {self.account_id}, 尝试: {attempt + 1}")
            except Exception as e:
                logger.error(f"RPC连接检查异常: {self.account_id}, 尝试: {attempt + 1}, 错误: {e}")
            
            # 如果不是最后一次尝试，等待后重试
            if attempt < max_retries - 1:
                await asyncio.sleep(retry_delay * (2 ** attempt))  # 指数退避
        
        # 所有重试都失败
        logger.error(f"RPC连接检查最终失败: {self.account_id}, 总尝试次数: {max_retries}")
        
        # 记录连续错误
        if not hasattr(self, 'connection_errors'):
            self.connection_errors = 0
        self.connection_errors += 1
        
        # 如果连续错误过多，建议重启
        if self.connection_errors > 10:
            logger.critical(f"RPC连接持续失败: {self.account_id}, 连续错误: {self.connection_errors}次，建议重启监控器")
        
        return False

    async def _monitoring_loop(self):
        """监控主循环"""
        logger.info(f"监控循环启动: {self.account_id}")

        loop_count = 0
        while self.running:
            try:
                current_time = time.time()
                loop_count += 1
                
                if loop_count % 50 == 0:   
                    logger.info(f"监控循环运行中: {self.account_id}, 循环次数: {loop_count}, 间隔: {self.position_check_interval}s")

                if current_time - self.last_position_check >= self.position_check_interval:
                    logger.info(f"触发持仓检查: {self.account_id}, 距离上次检查: {current_time - self.last_position_check:.2f}s")
                    await self._check_position_changes()
                    self.last_position_check = current_time
                    self.monitoring_stats['last_check_time'] = current_time
                else:
                    wait_time = self.position_check_interval - (current_time - self.last_position_check)
                    if loop_count % 100 == 0:  
                        logger.info(f"等待下次检查: {self.account_id}, 还需等待: {wait_time:.2f}s")
                
                await asyncio.sleep(self.sleep_interval)
                
            except Exception as e:
                logger.error(f"监控循环异常: {e}")
                self.monitoring_stats['monitoring_errors'] += 1
                await asyncio.sleep(1)
    
    async def _check_position_changes(self):
        """检查持仓变化 """
        try:
            logger.info(f"触发持仓检查: {self.account_id}, 距离上次检查: {time.time() - getattr(self, 'last_check_time', 0):.2f}s")
            
            connection_ready = await self._ensure_connection()
            if not connection_ready:
                logger.debug(f"连接未完全就绪，但继续尝试获取数据: {self.account_id}")
            
            logger.info(f"正在检查持仓变化: {self.account_id}")
            
            try:
                # 增强的RPC调用，支持重试和超时
                positions_result = await self._execute_rpc_with_retry(
                    'get_positions', 
                    self.account_id,
                    max_retries=2,
                    timeout=15.0
                )

                if positions_result and positions_result.get("status") == "success":
                    positions_list = positions_result.get("positions", [])
                    logger.debug(f"RPC获取持仓成功: {self.account_id}, 持仓数量: {len(positions_list)}")
                else:
                    error_msg = positions_result.get('error', '未知错误') if positions_result else 'RPC调用返回空结果'
                    logger.warning(f"RPC获取持仓失败: {self.account_id}, 错误: {error_msg}")
                    positions_list = []
                    
            except Exception as pos_error:
                logger.error(f"获取持仓数据失败: {self.account_id}, 错误: {pos_error}")
                positions_list = []
            
            if not positions_list:
                current_positions_data = {}
                logger.info(f"持仓列表为空: {self.account_id}")
            else:
                logger.info(f"发现持仓: {self.account_id}, 数量: {len(positions_list)}")
                
                current_positions_data = {}
                for i, pos in enumerate(positions_list):
                    if isinstance(pos, dict):
                        ticket = pos.get('ticket') or pos.get('position_id') or f"pos_{i}"
                        current_positions_data[ticket] = pos
                        logger.info(f"  持仓{i+1}: ticket={ticket}, symbol={pos.get('symbol', 'N/A')}, volume={pos.get('volume', 'N/A')}")
                    else:
                        logger.warning(f"无效的持仓数据格式: {self.account_id}, 索引: {i}, 类型: {type(pos)}")
                
                logger.info(f"转换后的持仓数据: {self.account_id}, 持仓数: {len(current_positions_data)}")
            
            changes = self._detect_position_changes(current_positions_data)
            logger.info(f"检测到的变化数量: {self.account_id}, 变化数: {len(changes)}")
            
            if changes:
                logger.info(f"发现持仓变化: {self.account_id}")
                for change in changes:
                    logger.info(f"  变化类型: {change.get('type', 'unknown')}, ticket: {change.get('ticket', 'N/A')}")
                
                await self._publish_position_events(changes)
                
                self.monitoring_stats['position_changes_detected'] += len(changes)
                logger.info(f"已发布 {len(changes)} 个持仓变化事件: {self.account_id}")
            else:
                if not hasattr(self, '_no_change_count'):
                    self._no_change_count = 0
                self._no_change_count += 1
                
                if self._no_change_count % 10 == 0:
                    logger.debug(f"持续无持仓变化: {self.account_id}, 检查次数: {self._no_change_count}")
            
            self.last_check_time = time.time()
            self.current_positions = current_positions_data

        except Exception as e:
            logger.error(f"检查持仓变化异常: {self.account_id}, 错误: {e}")
            self.monitoring_stats['monitoring_errors'] += 1
    
    async def _publish_position_events(self, changes):
        """发布持仓变化事件"""
        try:
            for change in changes:
                await self._publish_position_change_event(change)
        except Exception as e:
            logger.error(f"发布持仓事件失败: {self.account_id}, 错误: {e}")
    
    
    def _detect_position_changes(self, new_positions: Dict[int, Dict]) -> List[Dict]:
        """检测持仓变化"""
        changes = []
        
        for ticket, position in new_positions.items():
            if ticket not in self.current_positions:
                changes.append({
                    'type': 'position_opened',
                    'ticket': ticket,
                    'position': position,
                    'timestamp': time.time()
                })
        
        for ticket, position in self.current_positions.items():
            if ticket not in new_positions:
                changes.append({
                    'type': 'position_closed',
                    'ticket': ticket,
                    'position': position,
                    'timestamp': time.time()
                })
        
        for ticket, new_pos in new_positions.items():
            if ticket in self.current_positions:
                old_pos = self.current_positions[ticket]
                if (new_pos.get('sl') != old_pos.get('sl') or 
                    new_pos.get('tp') != old_pos.get('tp')):
                    changes.append({
                        'type': 'position_modified',
                        'ticket': ticket,
                        'old_position': old_pos,
                        'new_position': new_pos,
                        'timestamp': time.time()
                    })
        
        return changes
    
    async def _publish_position_change_event(self, change: Dict):
        """发布持仓变化事件"""
        try:
            event = MonitoringEvent(
                event_type=change['type'],
                account_id=self.account_id,
                timestamp=change['timestamp'],
                data=change,
                event_id=f"{self.account_id}_{change['ticket']}_{int(change['timestamp'] * 1000)}"
            )
            
            await self.event_publisher.publish(
                subject=self.event_topic,
                data={
                    'event_type': event.event_type,
                    'account_id': event.account_id,
                    'timestamp': event.timestamp,
                    'data': event.data,
                    'event_id': event.event_id
                }
            )
            
            self.monitoring_stats['events_published'] += 1
            
            logger.debug(f"发布持仓变化事件: {change['type']} - {change['ticket']}")
            
        except Exception as e:
            logger.error(f"发布持仓变化事件失败: {e}")
    
    async def publish_trade_signal(self, signal_data: Dict) -> bool:
        """发布交易信号 - 批处理接口实现"""
        try:
            if self.event_publisher:
                return await self.event_publisher.publish(
                    subject=self.event_topic, 
                    data=signal_data
                )
            return False
        except Exception as e:
            logger.error(f"发布交易信号失败: {e}")
            return False

    async def _publish_monitoring_event(self, event_type: str, data: Dict):
        """发布监控事件 - 使用批处理优化"""
        try:
            event_data = {
                'event_type': event_type,
                'account_id': self.account_id,
                'timestamp': time.time(),
                'data': data,
                'event_id': f"{self.account_id}_{event_type}_{int(time.time() * 1000)}",
                'topic': self.event_topic
            }
            
            # 使用批处理器发布事件
            await self.signal_batch_processor.process_item(event_data)
            
            self.monitoring_stats['events_published'] += 1
            
        except Exception as e:
            logger.error(f"发布监控事件失败: {e}")
    
    def get_monitoring_stats(self) -> Dict[str, Any]:
        """获取监控统计"""
        uptime = time.time() - (self.start_time or time.time())

        return {
            **self.monitoring_stats,
            'account_id': self.account_id,
            'running': self.running,
            'uptime_seconds': uptime,
            'positions_count': len(self.current_positions),
            'event_topic': self.event_topic,
            'rpc_enabled': self.rpc_client is not None
        }

    async def health_check_rpc(self) -> Dict[str, Any]:
        """使用RPC进行健康检查"""
        if not self.rpc_client:
            return {"status": "failed", "error": "RPC client not available"}

        try:
            health_result = await self.rpc_client.health_check(self.account_id)
            return health_result
        except Exception as e:
            logger.error(f"RPC健康检查失败: {self.account_id}, 错误: {e}")
            return {"status": "failed", "error": str(e)}
    
    async def monitor_positions(self):
        """监控持仓 - 父类接口实现"""
        return await self._check_position_changes()
    
    async def monitor_account_status(self):
        """监控账户状态 - 只使用RPC架构"""
        try:
            # 使用RPC获取账户信息
            account_result = await self.rpc_client.get_account_info(self.account_id)
            
            if account_result.get("status") == "success":
                logger.debug(f"RPC账户状态正常: {self.account_id}")
                return True
            else:
                await self._publish_monitoring_event("account_unhealthy", {
                    "account_id": self.account_id,
                    "timestamp": time.time(),
                    "reason": f"RPC账户信息获取失败: {account_result.get('error')}"
                })
                return False
                
        except Exception as e:
            await self._publish_monitoring_event("account_error", {
                "account_id": self.account_id,
                "timestamp": time.time(),
                "error": f"RPC账户状态检查异常: {str(e)}"
            })
            return False
    
    async def _execute_rpc_with_retry(self, method_name: str, *args, 
                                    max_retries: int = 3, timeout: float = 30.0,
                                    **kwargs) -> Optional[Dict[str, Any]]:
        """执行RPC调用，支持重试和超时控制"""
        last_error = None
        
        for attempt in range(max_retries):
            try:
                if attempt > 0:
                    # 指数退避重试
                    delay = 1.0 * (2 ** (attempt - 1))
                    logger.info(f"RPC重试: {method_name}, 账户: {self.account_id}, "
                               f"尝试: {attempt + 1}, 延迟: {delay:.1f}s")
                    await asyncio.sleep(delay)
                
                # 执行RPC调用，带超时控制
                if hasattr(self.rpc_client, method_name):
                    rpc_method = getattr(self.rpc_client, method_name)
                    result = await asyncio.wait_for(
                        rpc_method(*args, **kwargs), 
                        timeout=timeout
                    )
                else:
                    # 通用RPC调用
                    result = await asyncio.wait_for(
                        self.rpc_client.call(method_name, *args, **kwargs),
                        timeout=timeout
                    )
                
                # 检查结果
                if result and isinstance(result, dict):
                    status = result.get('status')
                    if status == 'success':
                        if attempt > 0:
                            logger.info(f"RPC重试成功: {method_name}, 账户: {self.account_id}, "
                                       f"总尝试: {attempt + 1}")
                        return result
                    elif self._is_retryable_error(result):
                        last_error = result.get('error', '未知错误')
                        logger.warning(f"RPC可重试错误: {method_name}, 账户: {self.account_id}, "
                                      f"尝试: {attempt + 1}, 错误: {last_error}")
                        continue
                    else:
                        # 不可重试错误，直接返回
                        logger.error(f"RPC不可重试错误: {method_name}, 账户: {self.account_id}, "
                                    f"错误: {result.get('error')}")
                        return result
                else:
                    last_error = "RPC返回无效结果"
                    logger.warning(f"RPC返回无效结果: {method_name}, 账户: {self.account_id}")
                
            except asyncio.TimeoutError:
                last_error = f"RPC调用超时 ({timeout}s)"
                logger.warning(f"RPC超时: {method_name}, 账户: {self.account_id}, "
                              f"尝试: {attempt + 1}, 超时: {timeout}s")
            except Exception as e:
                last_error = str(e)
                logger.error(f"RPC调用异常: {method_name}, 账户: {self.account_id}, "
                            f"尝试: {attempt + 1}, 错误: {e}")
        
        # 所有重试都失败
        logger.error(f"RPC调用最终失败: {method_name}, 账户: {self.account_id}, "
                    f"总尝试: {max_retries}, 最后错误: {last_error}")
        
        return {
            "status": "error",
            "error": f"RPC调用失败: {last_error}",
            "method": method_name,
            "account_id": self.account_id
        }
    
    def _is_retryable_error(self, result: Dict[str, Any]) -> bool:
        """判断是否为可重试的错误"""
        error_msg = str(result.get('error', '')).lower()
        
        # 可重试的错误模式
        retryable_patterns = [
            'timeout',
            'connection',
            'network',
            'temporary',
            'busy',
            'unavailable',
            'rate limit'
        ]
        
        return any(pattern in error_msg for pattern in retryable_patterns)