#!/usr/bin/env python3
"""
RPC通信测试
测试跨进程的RPC通信功能
"""

import asyncio
import json
import os
import sys
import tempfile
import time
import multiprocessing as mp
from pathlib import Path
from typing import Dict, Any

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# 导入真实组件
try:
    from src.core.mt5_rpc_client import MT5RPCClient
    from src.core.mt5_request_handler import MT5Re<PERSON>Handler
    from src.messaging.jetstream_client import JetStreamClient
    from src.core.config_manager import get_stream_config_manager
    
    REAL_COMPONENTS_AVAILABLE = True
    print("✅ RPC组件导入成功")
    
except ImportError as e:
    print(f"❌ 导入RPC组件失败: {e}")
    sys.exit(1)


class RPCCommunicationTest:
    """RPC通信测试类"""
    
    def __init__(self):
        self.test_data_dir = Path(tempfile.mkdtemp(prefix="rpc_test_"))
        self.jetstream_clients = []
        self.processes = []
        print(f"测试数据目录: {self.test_data_dir}")
    
    def cleanup(self):
        """清理测试资源"""
        import shutil
        try:
            # 终止所有进程
            for process in self.processes:
                if process.is_alive():
                    process.terminate()
                    process.join(timeout=5)
            
            # 断开所有JetStream连接
            for client in self.jetstream_clients:
                if hasattr(client, 'disconnect'):
                    asyncio.create_task(client.disconnect())
            
            shutil.rmtree(self.test_data_dir)
            print("🧹 RPC测试环境清理完成")
        except Exception as e:
            print(f"⚠️ 清理警告: {e}")
    
    async def setup_rpc_infrastructure(self):
        """设置RPC基础设施"""
        print("\n🔧 设置RPC基础设施...")
        
        try:
            # 创建RPC客户端JetStream连接
            client_config = {
                'servers': ['nats://localhost:4222'],
                'stream_name': 'RPC_TEST_CLIENT',
                'subjects': ['RPC.TEST.CLIENT.>']
            }
            
            client_jetstream = JetStreamClient(client_config)
            connected = await client_jetstream.connect()
            
            if not connected:
                print("  ❌ 无法连接到NATS服务器")
                return None, None
            
            print("  ✅ RPC客户端JetStream连接成功")
            self.jetstream_clients.append(client_jetstream)
            
            # 创建RPC服务端JetStream连接
            server_config = {
                'servers': ['nats://localhost:4222'],
                'stream_name': 'RPC_TEST_SERVER',
                'subjects': ['RPC.TEST.SERVER.>']
            }
            
            server_jetstream = JetStreamClient(server_config)
            await server_jetstream.connect()
            print("  ✅ RPC服务端JetStream连接成功")
            self.jetstream_clients.append(server_jetstream)
            
            return client_jetstream, server_jetstream
            
        except Exception as e:
            print(f"  ❌ RPC基础设施设置失败: {e}")
            return None, None
    
    async def test_basic_rpc_call(self):
        """测试基础RPC调用"""
        print("\n📞 测试基础RPC调用...")
        
        # 设置基础设施
        client_jetstream, server_jetstream = await self.setup_rpc_infrastructure()
        if not client_jetstream:
            return False
        
        try:
            # 创建RPC客户端
            rpc_client = MT5RPCClient(client_jetstream)
            
            # 创建模拟的MT5ProcessManager
            class MockMT5ProcessManager:
                def __init__(self):
                    self.requests_handled = 0
                
                async def handle_request(self, request_data):
                    self.requests_handled += 1
                    method = request_data.get('method', 'unknown')
                    params = request_data.get('params', {})
                    
                    # 模拟不同方法的响应
                    if method == 'get_account_info':
                        return {
                            "status": "success",
                            "data": {
                                "account_id": params.get('account_id', 'TEST_001'),
                                "balance": 10000.0,
                                "equity": 10000.0,
                                "margin": 0.0,
                                "free_margin": 10000.0
                            }
                        }
                    elif method == 'get_positions':
                        return {
                            "status": "success",
                            "data": {
                                "positions": [],
                                "count": 0
                            }
                        }
                    elif method == 'place_order':
                        return {
                            "status": "success",
                            "data": {
                                "order_id": f"ORDER_{self.requests_handled}",
                                "symbol": params.get('symbol', 'EURUSD'),
                                "volume": params.get('volume', 0.1)
                            }
                        }
                    else:
                        return {
                            "status": "error",
                            "message": f"Unknown method: {method}"
                        }
            
            mock_manager = MockMT5ProcessManager()
            
            # 创建RPC请求处理器
            rpc_handler = MT5RequestHandler(server_jetstream, mock_manager)
            
            print("  ✅ RPC组件创建成功")
            
            # 启动RPC处理器
            await rpc_handler.start()
            print("  ✅ RPC处理器启动成功")
            
            # 等待处理器完全启动
            await asyncio.sleep(1)
            
            # 测试多种RPC调用
            test_calls = [
                {
                    'method': 'get_account_info',
                    'params': {'account_id': 'RPC_TEST_001'},
                    'expected_status': 'success'
                },
                {
                    'method': 'get_positions',
                    'params': {'account_id': 'RPC_TEST_001'},
                    'expected_status': 'success'
                },
                {
                    'method': 'place_order',
                    'params': {
                        'symbol': 'EURUSD',
                        'action': 'buy',
                        'volume': 0.1
                    },
                    'expected_status': 'success'
                },
                {
                    'method': 'invalid_method',
                    'params': {},
                    'expected_status': 'error'
                }
            ]
            
            successful_calls = 0
            
            for i, test_call in enumerate(test_calls):
                try:
                    print(f"  📞 RPC调用 {i+1}: {test_call['method']}")
                    
                    response = await rpc_client.call_rpc(
                        method=test_call['method'],
                        params=test_call['params'],
                        timeout=5.0
                    )
                    
                    if response and response.get('status') == test_call['expected_status']:
                        print(f"    ✅ 调用成功: {response.get('status')}")
                        successful_calls += 1
                    else:
                        print(f"    ⚠️ 调用结果异常: {response}")
                        
                except asyncio.TimeoutError:
                    print(f"    ⚠️ 调用超时: {test_call['method']}")
                except Exception as e:
                    print(f"    ❌ 调用失败: {e}")
            
            # 停止RPC处理器
            await rpc_handler.stop()
            
            success_rate = successful_calls / len(test_calls)
            print(f"  📊 RPC调用成功率: {successful_calls}/{len(test_calls)} ({success_rate*100:.1f}%)")
            
            return success_rate >= 0.75  # 75%以上算成功
            
        except Exception as e:
            print(f"  ❌ 基础RPC调用测试失败: {e}")
            return False
    
    async def test_concurrent_rpc_calls(self):
        """测试并发RPC调用"""
        print("\n🔄 测试并发RPC调用...")
        
        # 设置基础设施
        client_jetstream, server_jetstream = await self.setup_rpc_infrastructure()
        if not client_jetstream:
            return False
        
        try:
            # 创建RPC客户端
            rpc_client = MT5RPCClient(client_jetstream)
            
            # 创建高性能模拟管理器
            class HighPerformanceMockManager:
                def __init__(self):
                    self.request_count = 0
                    self.start_time = time.time()
                
                async def handle_request(self, request_data):
                    self.request_count += 1
                    # 模拟快速响应
                    return {
                        "status": "success",
                        "data": {
                            "request_id": self.request_count,
                            "timestamp": time.time(),
                            "processing_time": 0.001
                        }
                    }
            
            mock_manager = HighPerformanceMockManager()
            rpc_handler = MT5RequestHandler(server_jetstream, mock_manager)
            
            # 启动RPC处理器
            await rpc_handler.start()
            await asyncio.sleep(1)
            
            # 并发RPC调用任务
            async def concurrent_rpc_task(task_id, call_count=10):
                successful = 0
                for i in range(call_count):
                    try:
                        response = await rpc_client.call_rpc(
                            method='concurrent_test',
                            params={'task_id': task_id, 'call_index': i},
                            timeout=3.0
                        )
                        if response and response.get('status') == 'success':
                            successful += 1
                    except Exception:
                        pass
                return successful
            
            # 启动多个并发任务
            concurrent_tasks = [
                concurrent_rpc_task(i, call_count=5)
                for i in range(10)  # 10个并发任务，每个5次调用
            ]
            
            print(f"  🚀 启动 {len(concurrent_tasks)} 个并发RPC任务...")
            
            start_time = time.time()
            results = await asyncio.gather(*concurrent_tasks)
            end_time = time.time()
            
            # 统计结果
            total_successful = sum(results)
            total_calls = len(concurrent_tasks) * 5
            success_rate = total_successful / total_calls
            duration = end_time - start_time
            throughput = total_calls / duration
            
            print(f"  📊 并发RPC测试结果:")
            print(f"    总调用数: {total_calls}")
            print(f"    成功调用: {total_successful}")
            print(f"    成功率: {success_rate*100:.1f}%")
            print(f"    耗时: {duration:.2f}秒")
            print(f"    吞吐量: {throughput:.0f} 调用/秒")
            
            # 停止RPC处理器
            await rpc_handler.stop()
            
            return success_rate >= 0.8 and throughput >= 10  # 80%成功率且10调用/秒
            
        except Exception as e:
            print(f"  ❌ 并发RPC调用测试失败: {e}")
            return False
    
    async def test_rpc_error_handling(self):
        """测试RPC错误处理"""
        print("\n🚨 测试RPC错误处理...")
        
        # 设置基础设施
        client_jetstream, server_jetstream = await self.setup_rpc_infrastructure()
        if not client_jetstream:
            return False
        
        try:
            rpc_client = MT5RPCClient(client_jetstream)
            
            # 创建会产生各种错误的模拟管理器
            class ErrorProneManager:
                def __init__(self):
                    self.call_count = 0
                
                async def handle_request(self, request_data):
                    self.call_count += 1
                    method = request_data.get('method', '')
                    
                    if method == 'timeout_test':
                        # 模拟超时
                        await asyncio.sleep(10)
                        return {"status": "success"}
                    elif method == 'exception_test':
                        # 模拟异常
                        raise Exception("Simulated processing error")
                    elif method == 'invalid_response':
                        # 返回无效响应
                        return "invalid_json_response"
                    else:
                        return {"status": "success", "data": "normal_response"}
            
            error_manager = ErrorProneManager()
            rpc_handler = MT5RequestHandler(server_jetstream, error_manager)
            
            await rpc_handler.start()
            await asyncio.sleep(1)
            
            # 测试各种错误情况
            error_tests = [
                {
                    'name': '超时测试',
                    'method': 'timeout_test',
                    'timeout': 2.0,
                    'expected_error': 'timeout'
                },
                {
                    'name': '异常测试',
                    'method': 'exception_test',
                    'timeout': 5.0,
                    'expected_error': 'exception'
                },
                {
                    'name': '无效响应测试',
                    'method': 'invalid_response',
                    'timeout': 5.0,
                    'expected_error': 'invalid_response'
                },
                {
                    'name': '正常调用测试',
                    'method': 'normal_call',
                    'timeout': 5.0,
                    'expected_error': None
                }
            ]
            
            handled_errors = 0
            
            for test in error_tests:
                try:
                    print(f"  🧪 {test['name']}...")
                    
                    response = await rpc_client.call_rpc(
                        method=test['method'],
                        params={},
                        timeout=test['timeout']
                    )
                    
                    if test['expected_error'] is None:
                        # 期望成功
                        if response and response.get('status') == 'success':
                            print(f"    ✅ 正常调用成功")
                            handled_errors += 1
                        else:
                            print(f"    ⚠️ 正常调用异常: {response}")
                    else:
                        print(f"    ⚠️ 期望错误但收到响应: {response}")
                        
                except asyncio.TimeoutError:
                    if test['expected_error'] == 'timeout':
                        print(f"    ✅ 超时错误正确处理")
                        handled_errors += 1
                    else:
                        print(f"    ❌ 意外超时")
                        
                except Exception as e:
                    if test['expected_error'] in ['exception', 'invalid_response']:
                        print(f"    ✅ 异常错误正确处理: {type(e).__name__}")
                        handled_errors += 1
                    else:
                        print(f"    ❌ 意外异常: {e}")
            
            await rpc_handler.stop()
            
            error_handling_rate = handled_errors / len(error_tests)
            print(f"  📊 错误处理成功率: {handled_errors}/{len(error_tests)} ({error_handling_rate*100:.1f}%)")
            
            return error_handling_rate >= 0.75
            
        except Exception as e:
            print(f"  ❌ RPC错误处理测试失败: {e}")
            return False
    
    async def run_all_tests(self):
        """运行所有RPC测试"""
        print("🚀 开始RPC通信测试...")
        
        test_results = {}
        
        # 运行各项测试
        test_results['basic_rpc'] = await self.test_basic_rpc_call()
        test_results['concurrent_rpc'] = await self.test_concurrent_rpc_calls()
        test_results['error_handling'] = await self.test_rpc_error_handling()
        
        # 统计结果
        passed = sum(1 for result in test_results.values() if result)
        total = len(test_results)
        success_rate = (passed / total) * 100
        
        print(f"\n📊 RPC通信测试结果:")
        print(f"总测试数: {total}")
        print(f"通过: {passed}")
        print(f"失败: {total - passed}")
        print(f"成功率: {success_rate:.1f}%")
        
        print(f"\n📋 详细结果:")
        for test_name, result in test_results.items():
            status = "✅ 通过" if result else "❌ 失败"
            print(f"  {test_name}: {status}")
        
        return success_rate >= 66  # 66%以上算成功


async def main():
    """主函数"""
    if not REAL_COMPONENTS_AVAILABLE:
        print("❌ RPC组件不可用，无法运行测试")
        return False
    
    test_suite = RPCCommunicationTest()
    
    try:
        success = await test_suite.run_all_tests()
        
        if success:
            print("\n🎉 RPC通信测试成功!")
        else:
            print("\n⚠️ RPC通信测试部分失败")
        
        return success
        
    finally:
        test_suite.cleanup()


if __name__ == '__main__':
    result = asyncio.run(main())
    sys.exit(0 if result else 1)
