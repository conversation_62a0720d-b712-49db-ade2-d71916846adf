#!/usr/bin/env python3
"""
统一协调器 - 架构唯一权威实现
集成依赖注入容器和服务发现，消除复杂初始化依赖链
硬迁移：零向后兼容，强制统一，SSOT
"""
import asyncio
import logging
import time
from typing import Dict, Any, Optional, List, Type, Set
from pathlib import Path

from .dependency_injection import UnifiedDependencyContainer, get_container
from .service_discovery import UnifiedServiceDiscovery, get_service_discovery, ServiceEndpoint, ServiceType
from .mt5_configuration import AccountConfigManager
from ..messaging.hybrid_queue_manager import HybridQueueManager
from ..messaging.hybrid_message_router import HybridMessageRouter
from ..infrastructure.connection_pool import MT5ConnectionPool

logger = logging.getLogger(__name__)

class UnifiedMT5Coordinator:
    """
    统一MT5协调器 - 架构唯一权威实现
    消除复杂初始化依赖链，实现优雅降级和动态重启
    """
    
    def __init__(self, host_id: str, config_path: str):
        self.host_id = host_id
        self.config_path = config_path
        
        # 核心组件
        self.container = get_container()
        self.service_discovery = get_service_discovery()
        
        # 状态管理
        self.running = False
        self.initialization_successful = False
        self.degraded_mode = False
        
        # 服务实例引用
        self.account_manager: Optional[AccountConfigManager] = None
        self.queue_manager: Optional[HybridQueueManager] = None
        self.message_router: Optional[HybridMessageRouter] = None
        self.connection_pool: Optional[MT5ConnectionPool] = None
        
        # 本地账户信息
        self.local_accounts: Dict[str, Dict] = {}
        
        logger.info(f"🎯 统一MT5协调器已创建 - 主机: {host_id}")
    
    async def initialize(self, allow_partial_failure: bool = True) -> bool:
        """
        统一初始化流程 - 消除复杂依赖链
        """
        logger.info("🚀 开始统一初始化流程")
        start_time = time.time()
        
        try:
            # 1. 注册所有服务到容器
            await self._register_services()
            
            # 2. 启动服务发现
            await self.service_discovery.start()
            
            # 3. 异步初始化所有服务
            initialization_results = await self.container.initialize_all(allow_partial_failure)
            
            # 4. 分析初始化结果
            success_count = sum(1 for r in initialization_results.values() if r.success)
            total_count = len(initialization_results)
            
            if success_count == 0:
                logger.error("❌ 所有服务初始化失败，无法启动系统")
                return False
            
            if success_count < total_count:
                logger.warning(f"⚠️ 部分服务初始化失败 ({success_count}/{total_count})，进入降级模式")
                self.degraded_mode = True
            else:
                logger.info(f"✅ 所有服务初始化成功 ({success_count}/{total_count})")
            
            # 5. 获取服务实例
            await self._get_service_instances()
            
            # 6. 发现和启动本地账户
            await self._discover_and_start_accounts()
            
            # 7. 注册服务到服务发现
            await self._register_to_service_discovery()
            
            # 8. 启动健康监控
            await self._start_health_monitoring()
            
            self.initialization_successful = True
            total_time = time.time() - start_time
            
            logger.info(f"✅ 统一初始化完成 - 用时 {total_time:.2f}s, 降级模式: {self.degraded_mode}")
            return True
            
        except Exception as e:
            logger.error(f"❌ 统一初始化失败: {e}")
            import traceback
            logger.debug(traceback.format_exc())
            return False
    
    async def _register_services(self):
        """注册所有服务到依赖注入容器"""
        logger.info("📦 注册服务到依赖注入容器")
        
        # 注册账户配置管理器
        self.container.register_singleton(
            AccountConfigManager,
            factory=lambda: AccountConfigManager(
                config_dir=str(Path(self.config_path).parent.parent)
            ),
            health_check=self._health_check_account_manager,
            required=True,
            initialization_order=1
        )
        
        # 注册消息队列管理器
        self.container.register_singleton(
            HybridQueueManager,
            factory=self._create_queue_manager,
            health_check=self._health_check_queue_manager,
            fallback_factory=self._create_fallback_queue_manager,
            required=False,  # 可以降级到本地模式
            initialization_order=2
        )
        
        # 注册消息路由器
        self.container.register_singleton(
            HybridMessageRouter,
            factory=self._create_message_router,
            dependencies={HybridQueueManager},
            health_check=self._health_check_message_router,
            required=False,
            initialization_order=3
        )
        
        # 注册连接池
        self.container.register_singleton(
            MT5ConnectionPool,
            factory=self._create_connection_pool,
            health_check=self._health_check_connection_pool,
            required=True,
            initialization_order=4
        )
        
        logger.info("✅ 服务注册完成")
    
    async def _create_queue_manager(self) -> HybridQueueManager:
        """创建消息队列管理器"""
        try:
            # 从配置加载NATS配置
            config = self._load_messaging_config()
            
            queue_manager = HybridQueueManager(config)
            success = await queue_manager.initialize()
            
            if not success:
                raise RuntimeError("消息队列管理器初始化失败")
            
            return queue_manager
            
        except Exception as e:
            logger.error(f"创建消息队列管理器失败: {e}")
            raise
    
    async def _create_fallback_queue_manager(self) -> HybridQueueManager:
        """创建回退消息队列管理器（本地模式）"""
        logger.info("🔄 创建回退消息队列管理器（本地模式）")
        
        # 本地模式配置
        local_config = {
            'primary_backend': 'local',
            'enable_dual_write': False,
            'local_fallback': True
        }
        
        queue_manager = HybridQueueManager(local_config)
        await queue_manager.initialize()
        
        return queue_manager
    
    async def _create_message_router(self) -> HybridMessageRouter:
        """创建消息路由器"""
        config = self._load_messaging_config()
        
        router = HybridMessageRouter(
            config=config,
            host_id=self.host_id
        )
        
        # 注入队列管理器
        queue_manager = await self.container.get(HybridQueueManager)
        if queue_manager:
            router.queue_manager = queue_manager
        
        await router.start()
        return router
    
    async def _create_connection_pool(self) -> MT5ConnectionPool:
        """创建连接池"""
        pool = MT5ConnectionPool(max_connections=20)
        
        # 设置连接工厂
        pool.set_connection_factory(self._create_mt5_connection)
        pool.set_connection_validator(self._validate_mt5_connection)
        
        await pool.start()
        return pool
    
    async def _create_mt5_connection(self, account_id: str, server: str, login: int):
        """创建MT5连接"""
        # 这里实现实际的MT5连接创建逻辑
        # 返回MT5连接对象
        logger.debug(f"创建MT5连接: {account_id} - {login}@{server}")
        
        # 模拟连接创建
        class MockMT5Connection:
            def __init__(self, account_id, server, login):
                self.account_id = account_id
                self.server = server
                self.login = login
                self.connected = True
            
            async def close(self):
                self.connected = False
        
        return MockMT5Connection(account_id, server, login)
    
    async def _validate_mt5_connection(self, connection) -> bool:
        """验证MT5连接"""
        return hasattr(connection, 'connected') and connection.connected
    
    def _load_messaging_config(self) -> Dict[str, Any]:
        """加载消息配置"""
        # 这里应该从配置文件加载实际配置
        return {
            'host_id': self.host_id,
            'nats_servers': ['nats://localhost:4222'],
            'redis_url': 'redis://localhost:6379'
        }
    
    async def _get_service_instances(self):
        """获取服务实例"""
        self.account_manager = await self.container.get(AccountConfigManager)
        self.queue_manager = await self.container.get(HybridQueueManager)
        self.message_router = await self.container.get(HybridMessageRouter)
        self.connection_pool = await self.container.get(MT5ConnectionPool)
        
        logger.info("✅ 服务实例获取完成")
    
    async def _discover_and_start_accounts(self):
        """发现和启动本地账户"""
        if not self.account_manager:
            logger.warning("⚠️ 账户管理器不可用，跳过账户启动")
            return
        
        try:
            # 发现本地账户
            all_accounts = self.account_manager.load_all_accounts()
            
            for account_id, account_config in all_accounts.items():
                if account_config.host_id == self.host_id or not account_config.host_id:
                    self.local_accounts[account_id] = {
                        'login': account_config.login,
                        'server': account_config.server,
                        'terminal_path': account_config.terminal_path,
                        'config': account_config
                    }
            
            logger.info(f"✅ 发现本地账户: {len(self.local_accounts)} 个")
            
            # TODO: 启动账户进程（如果需要）
            
        except Exception as e:
            logger.error(f"❌ 账户发现失败: {e}")
    
    async def _register_to_service_discovery(self):
        """注册到服务发现"""
        try:
            # 注册协调器服务
            coordinator_endpoint = ServiceEndpoint(
                host=self.host_id,
                port=8000,  # 从配置获取
                protocol="http"
            )
            
            await self.service_discovery.register_service(
                service_name="mt5-coordinator",
                endpoint=coordinator_endpoint,
                service_type=ServiceType.CORE,
                metadata={
                    'host_id': self.host_id,
                    'account_count': len(self.local_accounts),
                    'degraded_mode': self.degraded_mode
                },
                tags={'coordinator', 'mt5'},
                health_checker=self._health_check_coordinator
            )
            
            logger.info("✅ 已注册到服务发现")
            
        except Exception as e:
            logger.error(f"❌ 服务发现注册失败: {e}")
    
    async def _start_health_monitoring(self):
        """启动健康监控"""
        # 添加服务变更监听器
        self.service_discovery.add_service_change_listener(self._on_service_change)
        
        # 定期执行容器健康检查
        asyncio.create_task(self._container_health_monitor())
        
        logger.info("✅ 健康监控已启动")
    
    async def _container_health_monitor(self):
        """容器健康监控循环"""
        while self.running:
            try:
                # 执行所有服务的健康检查
                health_results = await self.container.health_check_all()
                
                unhealthy_services = [
                    service_type.__name__ for service_type, is_healthy in health_results.items()
                    if not is_healthy
                ]
                
                if unhealthy_services:
                    logger.warning(f"⚠️ 检测到不健康服务: {unhealthy_services}")
                    
                    # TODO: 实现自动重启逻辑
                
                await asyncio.sleep(60)  # 每分钟检查一次
                
            except Exception as e:
                logger.error(f"❌ 容器健康监控异常: {e}")
                await asyncio.sleep(10)
    
    async def _on_service_change(self, event_type: str, service):
        """服务变更处理"""
        logger.info(f"🔄 服务变更: {event_type} - {service.service_name}")
        
        if event_type == 'restart_required':
            # TODO: 实现服务重启逻辑
            pass
    
    # 健康检查方法
    async def _health_check_account_manager(self, instance) -> bool:
        """账户管理器健康检查"""
        try:
            accounts = instance.load_all_accounts()
            return len(accounts) > 0
        except:
            return False
    
    async def _health_check_queue_manager(self, instance) -> bool:
        """队列管理器健康检查"""
        try:
            return hasattr(instance, 'is_healthy') and await instance.is_healthy()
        except:
            return False
    
    async def _health_check_message_router(self, instance) -> bool:
        """消息路由器健康检查"""
        try:
            return hasattr(instance, 'is_running') and instance.is_running
        except:
            return False
    
    async def _health_check_connection_pool(self, instance) -> bool:
        """连接池健康检查"""
        try:
            stats = instance.get_stats()
            return stats['total_connections'] >= 0
        except:
            return False
    
    async def _health_check_coordinator(self, service) -> bool:
        """协调器健康检查"""
        return self.running and self.initialization_successful
    
    async def start(self):
        """启动协调器"""
        if self.running:
            logger.warning("协调器已在运行")
            return
        
        logger.info("🚀 启动统一MT5协调器")
        
        # 初始化所有组件
        success = await self.initialize(allow_partial_failure=True)
        
        if not success:
            logger.error("❌ 协调器初始化失败")
            return False
        
        self.running = True
        
        logger.info(f"✅ 统一MT5协调器启动成功 - 降级模式: {self.degraded_mode}")
        return True
    
    async def stop(self):
        """停止协调器"""
        if not self.running:
            return
        
        logger.info("🛑 停止统一MT5协调器")
        self.running = False
        
        try:
            # 停止服务发现
            await self.service_discovery.stop()
            
            # 关闭所有服务
            await self.container.shutdown_all()
            
            logger.info("✅ 统一MT5协调器已停止")
            
        except Exception as e:
            logger.error(f"❌ 协调器停止异常: {e}")
    
    async def get_system_status(self) -> Dict[str, Any]:
        """获取系统状态"""
        # 获取容器状态
        container_report = self.container.get_initialization_report()
        
        # 获取服务发现状态
        service_report = await self.service_discovery.get_service_status_report()
        
        return {
            'host_id': self.host_id,
            'running': self.running,
            'initialization_successful': self.initialization_successful,
            'degraded_mode': self.degraded_mode,
            'local_accounts_count': len(self.local_accounts),
            'container_status': container_report,
            'service_discovery_status': service_report,
            'components': {
                'account_manager': self.account_manager is not None,
                'queue_manager': self.queue_manager is not None,
                'message_router': self.message_router is not None,
                'connection_pool': self.connection_pool is not None
            }
        }
    
    async def restart_service(self, service_type: Type) -> bool:
        """动态重启服务"""
        logger.info(f"🔄 重启服务: {service_type.__name__}")
        
        try:
            # 停止服务监控
            service_id = f"{service_type.__name__}"
            
            # 重新初始化服务
            result = await self.container._initialize_service(service_type)
            
            if result.success:
                logger.info(f"✅ 服务重启成功: {service_type.__name__}")
                return True
            else:
                logger.error(f"❌ 服务重启失败: {service_type.__name__} - {result.error}")
                return False
                
        except Exception as e:
            logger.error(f"❌ 服务重启异常: {service_type.__name__} - {e}")
            return False