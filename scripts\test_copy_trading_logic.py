#!/usr/bin/env python3
"""
跟单逻辑测试 - 专注于测试跟单功能，跳过MT5连接
"""

import asyncio
import sys
import time
from pathlib import Path

# 添加项目路径
sys.path.insert(0, str(Path(__file__).parent.parent))

from src.utils.logger import get_logger, setup_logging
from src.core.signal_types import UnifiedTradeSignal, OrderType, SignalAction, CopyMode
from src.messaging.jetstream_client import JetStreamClient
from src.core.copy_utils import UnifiedCopyProcessor
from src.utils.config_manager import ConfigManager

logger = get_logger(__name__)


class CopyTradingLogicTester:
    """跟单逻辑测试器"""
    
    def __init__(self):
        self.config_manager = None
        self.jetstream_client = None
        self.strategy_processor = None
        self.test_results = {}
        
    async def setup(self):
        """初始化测试环境"""
        logger.info("🔧 初始化测试环境...")
        
        # 初始化配置管理器
        self.config_manager = ConfigManager('config/optimized_system.yaml')
        
        # 初始化 JetStream 客户端
        self.jetstream_client = JetStreamClient(['nats://localhost:4222'])
        await self.jetstream_client.connect()
        
        # 初始化统一处理器
        self.copy_processor = UnifiedCopyProcessor()
        
        logger.info("✅ 测试环境初始化完成")
    
    async def test_config_loading(self) -> bool:
        """测试配置加载"""
        logger.info("🧪 测试配置加载...")
        
        try:
            # 测试跟单关系配置
            relationships = self.config_manager.get_copy_relationships()
            logger.info(f"加载的跟单关系: {relationships}")
            
            if relationships:
                for rel in relationships:
                    logger.info(f"关系: {rel['master_account']} -> {rel['slave_account']}, "
                              f"模式: {rel['copy_mode']}, 比例: {rel['copy_ratio']}")
                
                self.test_results['config_loading'] = True
                return True
            else:
                logger.error("❌ 没有找到跟单关系配置")
                self.test_results['config_loading'] = False
                return False
                
        except Exception as e:
            logger.error(f"❌ 配置加载测试异常: {e}")
            self.test_results['config_loading'] = False
            return False
    
    async def test_forward_copy_strategy(self) -> bool:
        """测试正向跟单策略"""
        logger.info("🧪 测试正向跟单策略...")
        
        try:
            # 创建原始信号
            original_signal = UnifiedTradeSignal.create_open_signal(
                master_account="ACC001",
                symbol="EURUSD",
                order_type=OrderType.BUY,
                volume=1.0,
                price=1.1000,
                position_id=12345,
                sl=1.0950,
                tp=1.1050
            )
            
            logger.info(f"原始信号: {original_signal.symbol} {original_signal.order_type.value} {original_signal.volume}")
            
            # 应用正向跟单策略
            copy_signal = self.strategy_processor.apply_copy_strategy(
                signal=original_signal,
                master_account="ACC001",
                slave_account="ACC002"
            )
            
            if copy_signal:
                logger.info(f"✅ 跟单信号: {copy_signal.symbol} {copy_signal.order_type.value} {copy_signal.volume}")
                
                # 验证策略应用
                if (copy_signal.order_type == original_signal.order_type and  # 方向相同
                    copy_signal.symbol == original_signal.symbol and         # 品种相同
                    copy_signal.volume == 0.5):                             # 手数减半
                    logger.info("✅ 正向跟单策略验证通过")
                    self.test_results['forward_copy'] = True
                    return True
                else:
                    logger.error(f"❌ 策略验证失败: 期望手数=0.5, 实际={copy_signal.volume}")
                    self.test_results['forward_copy'] = False
                    return False
            else:
                logger.error("❌ 跟单信号生成失败")
                self.test_results['forward_copy'] = False
                return False
                
        except Exception as e:
            logger.error(f"❌ 正向跟单策略测试异常: {e}")
            self.test_results['forward_copy'] = False
            return False
    
    async def test_reverse_copy_strategy(self) -> bool:
        """测试反向跟单策略"""
        logger.info("🧪 测试反向跟单策略...")
        
        try:
            # 临时修改配置为反向跟单
            original_config = self.strategy_processor.get_strategy_config("ACC001", "ACC002")
            
            # 创建反向配置
            from src.core.copy_strategy_processor import CopyStrategyConfig
            reverse_config = CopyStrategyConfig(
                copy_mode=CopyMode.REVERSE,
                copy_ratio=0.5,
                enabled=True
            )
            
            # 临时缓存反向配置
            cache_key = "ACC001->ACC002"
            self.strategy_processor.strategy_cache[cache_key] = reverse_config
            
            # 创建原始信号
            original_signal = UnifiedTradeSignal.create_open_signal(
                master_account="ACC001",
                symbol="EURUSD",
                order_type=OrderType.BUY,
                volume=1.0,
                price=1.1000,
                position_id=12345,
                sl=1.0950,
                tp=1.1050
            )
            
            logger.info(f"原始信号: {original_signal.symbol} {original_signal.order_type.value} {original_signal.volume}")
            
            # 应用反向跟单策略
            copy_signal = self.strategy_processor.apply_copy_strategy(
                signal=original_signal,
                master_account="ACC001",
                slave_account="ACC002"
            )
            
            if copy_signal:
                logger.info(f"✅ 反向跟单信号: {copy_signal.symbol} {copy_signal.order_type.value} {copy_signal.volume}")
                
                # 验证反向策略应用
                if (copy_signal.order_type == OrderType.SELL and      # 方向反转
                    copy_signal.symbol == original_signal.symbol and  # 品种相同
                    copy_signal.volume == 0.5):                      # 手数减半
                    logger.info("✅ 反向跟单策略验证通过")
                    self.test_results['reverse_copy'] = True
                    return True
                else:
                    logger.error(f"❌ 反向策略验证失败: 期望方向=SELL, 实际={copy_signal.order_type.value}")
                    self.test_results['reverse_copy'] = False
                    return False
            else:
                logger.error("❌ 反向跟单信号生成失败")
                self.test_results['reverse_copy'] = False
                return False
                
        except Exception as e:
            logger.error(f"❌ 反向跟单策略测试异常: {e}")
            self.test_results['reverse_copy'] = False
            return False
        finally:
            # 恢复原始配置
            if 'original_config' in locals():
                self.strategy_processor.strategy_cache[cache_key] = original_config
    
    async def test_signal_routing(self) -> bool:
        """测试信号路由"""
        logger.info("🧪 测试信号路由...")
        
        try:
            # 创建测试信号
            test_signal = UnifiedTradeSignal.create_open_signal(
                master_account="ACC001",
                symbol="EURUSD",
                order_type=OrderType.BUY,
                volume=0.1,
                price=1.1000,
                position_id=99999,
                comment="Signal routing test"
            )
            
            # 发布主账户信号
            success1 = await self.jetstream_client.publish(
                subject="MT5.TRADES.ACC001",
                data=test_signal.to_dict()
            )
            
            # 应用跟单策略
            copy_signal = self.strategy_processor.apply_copy_strategy(
                signal=test_signal,
                master_account="ACC001",
                slave_account="ACC002"
            )
            
            if copy_signal:
                # 发布跟单信号 - 使用现有的主题格式
                success2 = await self.jetstream_client.publish(
                    subject="MT5.TRADES.ACC002",
                    data=copy_signal.to_dict()
                )
                
                if success1 and success2:
                    logger.info("✅ 信号路由测试通过")
                    
                    # 获取流信息
                    stream_info = await self.jetstream_client.get_stream_info()
                    if stream_info:
                        logger.info(f"流状态: 消息数={stream_info['messages']}")
                    
                    self.test_results['signal_routing'] = True
                    return True
                else:
                    logger.error("❌ 信号发布失败")
                    self.test_results['signal_routing'] = False
                    return False
            else:
                logger.error("❌ 跟单信号生成失败")
                self.test_results['signal_routing'] = False
                return False
                
        except Exception as e:
            logger.error(f"❌ 信号路由测试异常: {e}")
            self.test_results['signal_routing'] = False
            return False
    
    async def cleanup(self):
        """清理测试环境"""
        logger.info("🧹 清理测试环境...")
        
        if self.jetstream_client:
            await self.jetstream_client.disconnect()
            logger.info("✅ JetStream 连接已断开")
    
    def print_test_summary(self):
        """打印测试总结"""
        logger.info("=" * 60)
        logger.info("📊 跟单逻辑测试总结")
        logger.info("=" * 60)
        
        test_categories = {
            'config_loading': '配置加载',
            'forward_copy': '正向跟单策略',
            'reverse_copy': '反向跟单策略',
            'signal_routing': '信号路由'
        }
        
        passed_tests = 0
        total_tests = len(test_categories)
        
        for test_key, test_name in test_categories.items():
            if test_key in self.test_results:
                result = self.test_results[test_key]
                status = "✅ 通过" if result else "❌ 失败"
                logger.info(f"  {test_name}: {status}")
                if result:
                    passed_tests += 1
            else:
                logger.info(f"  {test_name}: ⏭️ 跳过")
        
        logger.info(f"\n总计: {passed_tests}/{total_tests} 个测试通过")
        
        if passed_tests == total_tests:
            logger.info("🎉 所有测试通过！跟单逻辑功能正常")
            return True
        else:
            logger.error("💥 部分测试失败，需要进一步调试")
            return False


async def main():
    """主函数"""
    # 设置日志
    setup_logging({
        'level': 'INFO',
        'format': 'standard',
        'file': 'logs/copy_trading_logic_test.log'
    })
    
    logger.info("=" * 60)
    logger.info("🚀 跟单逻辑功能测试")
    logger.info("=" * 60)
    
    tester = CopyTradingLogicTester()
    
    try:
        # 初始化测试环境
        await tester.setup()
        
        # 运行测试序列
        tests = [
            ("配置加载测试", tester.test_config_loading),
            ("正向跟单策略测试", tester.test_forward_copy_strategy),
            ("反向跟单策略测试", tester.test_reverse_copy_strategy),
            ("信号路由测试", tester.test_signal_routing)
        ]
        
        for test_name, test_func in tests:
            logger.info(f"运行测试: {test_name}")
            try:
                success = await test_func()
                if success:
                    logger.info(f"✅ {test_name}: 通过")
                else:
                    logger.error(f"❌ {test_name}: 失败")
            except Exception as e:
                logger.error(f"💥 {test_name}: 异常 - {e}")
            
            logger.info("-" * 40)
            await asyncio.sleep(1)  # 短暂暂停
        
        # 打印测试总结
        success = tester.print_test_summary()
        
        return 0 if success else 1
        
    except Exception as e:
        logger.error(f"测试运行异常: {e}")
        return 1
    finally:
        await tester.cleanup()


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
