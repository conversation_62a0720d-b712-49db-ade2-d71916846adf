# MT5分布式交易系统 - 监控和日志配置
# 定义系统监控、日志记录、指标收集等配置

# ============================================================================
# 日志配置
# ============================================================================
logging:
  # 全局日志设置
  global:
    level: ${MT5_LOG_LEVEL:-INFO}
    format: "json"
    timezone: "UTC"
    
  # 输出配置
  handlers:
    console:
      enabled: true
      level: "INFO"
      format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
      
    file:
      enabled: true
      level: "DEBUG"
      file: "logs/mt5_system.log"
      max_size_mb: 100
      backup_count: 10
      rotation: "daily"
      
    json_file:
      enabled: true
      level: "INFO"
      file: "logs/mt5_structured.json"
      max_size_mb: 200
      backup_count: 30
      
  # 过滤器
  filters:
    - name: "exclude_health_checks"
      pattern: "*/health"
      action: "exclude"
      
    - name: "exclude_metrics"
      pattern: "*/metrics"
      action: "exclude"
      
  # 特定组件日志级别
  loggers:
    "mt5.coordinator": "INFO"
    "mt5.trading": "DEBUG"
    "mt5.messaging": "INFO"
    "mt5.monitoring": "WARNING"
    "redis": "WARNING"
    "nats": "INFO"

# ============================================================================
# Prometheus指标配置
# ============================================================================
prometheus:
  enabled: true
  
  # 指标服务器
  server:
    host: "0.0.0.0"
    port: ${PROMETHEUS_PORT:-8000}
    path: "/metrics"
    
  # 推送网关配置
  pushgateway:
    enabled: true
    url: ${PROMETHEUS_PUSHGATEWAY_URL:-http://localhost:9091}
    job_name: "mt5-trading-system"
    push_interval: 30
    
  # 自定义指标
  metrics:
    # 交易相关指标
    trading:
      - name: "trading_signals_total"
        type: "counter"
        description: "Total number of trading signals processed"
        labels: ["account_id", "signal_type", "status"]
        
      - name: "trade_execution_duration_seconds"
        type: "histogram"
        description: "Time taken to execute trades"
        labels: ["account_id", "symbol", "action"]
        buckets: [0.1, 0.5, 1.0, 2.0, 5.0, 10.0]
        
      - name: "position_count"
        type: "gauge"
        description: "Current number of open positions"
        labels: ["account_id", "symbol"]
        
      - name: "account_balance"
        type: "gauge"
        description: "Current account balance"
        labels: ["account_id", "currency"]
        
    # 系统性能指标
    system:
      - name: "process_memory_usage_bytes"
        type: "gauge"
        description: "Process memory usage in bytes"
        labels: ["process_name", "host_id"]
        
      - name: "message_queue_size"
        type: "gauge"
        description: "Current message queue size"
        labels: ["queue_name", "host_id"]
        
      - name: "redis_operations_total"
        type: "counter"
        description: "Total Redis operations"
        labels: ["operation_type", "status"]
        
    # 网络和连接指标
    network:
      - name: "network_latency_seconds"
        type: "histogram"
        description: "Network latency between components"
        labels: ["source", "destination"]
        buckets: [0.001, 0.005, 0.01, 0.05, 0.1, 0.5, 1.0]
        
      - name: "connection_failures_total"
        type: "counter"
        description: "Total connection failures"
        labels: ["service", "error_type"]

# ============================================================================
# 健康检查配置
# ============================================================================
health_checks:
  enabled: true
  
  # 健康检查端点
  endpoint:
    path: "/health"
    port: ${HEALTH_CHECK_PORT:-8001}
    timeout: 10
    
  # 检查项目
  checks:
    - name: "redis_connection"
      type: "redis"
      interval: 30
      timeout: 5
      critical: true
      
    - name: "nats_connection"
      type: "nats"
      interval: 30
      timeout: 5
      critical: true
      
    - name: "disk_space"
      type: "disk"
      interval: 60
      timeout: 5
      critical: false
      threshold: "10GB"
      
    - name: "memory_usage"
      type: "memory"
      interval: 30
      timeout: 5
      critical: false
      threshold: "80%"
      
    - name: "mt5_terminal_status"
      type: "custom"
      interval: 60
      timeout: 10
      critical: true

# ============================================================================
# MT5监控配置
# ============================================================================
monitoring:
  # 持仓检查间隔 - 优化为高频监控
  position_check_interval: 0.5    # 0.5秒检查一次持仓变化，提高跟单响应速度
  sleep_interval: 0.2             # 监控循环睡眠间隔，提高监控频率

  # 账户监控
  account_monitoring:
    enabled: true
    polling_interval: 5.0         # 5秒检查一次账户状态

  # 持仓监控
  position_monitoring:
    enabled: true
    polling_interval: 0.5         # 0.5秒检查一次持仓，提高跟单响应速度

# ============================================================================
# 性能监控
# ============================================================================
performance:
  enabled: true
  
  # 性能数据收集
  collection:
    interval: 30
    retention_hours: 168  # 7天
    
  # 监控项目
  monitors:
    cpu:
      enabled: true
      alert_threshold: 80
      
    memory:
      enabled: true
      alert_threshold: 85
      
    disk_io:
      enabled: true
      alert_threshold: 1000  # IOPS
      
    network_io:
      enabled: true
      alert_threshold: 1000  # MB/s

# ============================================================================
# 错误跟踪和异常监控
# ============================================================================
error_tracking:
  enabled: true
  
  # 错误聚合
  aggregation:
    window_minutes: 5
    threshold: 10
    
  # 异常监控
  exceptions:
    capture_locals: false
    capture_args: true
    ignore_patterns:
      - "KeyboardInterrupt"
      - "SystemExit"
      
  # 错误通知
  notifications:
    enabled: false  # 在通知配置中单独配置

# ============================================================================
# 分布式追踪
# ============================================================================
tracing:
  enabled: false  # 开发环境默认关闭
  
  # Jaeger配置
  jaeger:
    agent_host: ${JAEGER_AGENT_HOST:-localhost}
    agent_port: ${JAEGER_AGENT_PORT:-6831}
    service_name: "mt5-trading-system"
    
  # 采样配置
  sampling:
    rate: 0.1  # 10%采样率

# ============================================================================
# 业务监控
# ============================================================================
business_metrics:
  enabled: true
  
  # 交易性能监控
  trading_performance:
    enabled: true
    calculation_interval: 300  # 5分钟
    
    metrics:
      - "win_rate"
      - "profit_factor"
      - "average_trade_duration"
      - "maximum_drawdown"
      - "daily_pnl"
      
  # 跟单效率监控
  copy_trading_efficiency:
    enabled: true
    latency_threshold_ms: 1000
    success_rate_threshold: 95

# ============================================================================
# 告警配置
# ============================================================================
alerting:
  enabled: true
  
  # 告警规则
  rules:
    - name: "high_error_rate"
      condition: "error_rate > 0.05"
      duration: "5m"
      severity: "warning"
      
    - name: "service_down"
      condition: "up == 0"
      duration: "1m"
      severity: "critical"
      
    - name: "high_latency"
      condition: "latency_p95 > 5.0"
      duration: "10m"
      severity: "warning"
      
  # 告警抑制
  inhibit_rules:
    - source_match:
        severity: "critical"
      target_match:
        severity: "warning"