"""
分布式状态管理器
支持三层缓存架构：L1(内存) -> L2(Redis) -> L3(PostgreSQL)
"""
import asyncio
import json
import time
from typing import Any, Dict, List, Optional, Union, TypeVar, Generic, Set
from dataclasses import dataclass, asdict
from enum import Enum
import redis.asyncio as redis
import asyncpg
from contextlib import asynccontextmanager

from ..utils.logger import get_logger
from ..utils.metrics import get_metrics_collector
from ..utils.recovery_strategies import RetryStrategy, ExponentialBackoff

logger = get_logger(__name__)
metrics = get_metrics_collector()

T = TypeVar('T')

class StateScope(Enum):
    """状态作用域"""
    LOCAL = "local"           # 本地状态
    HOST = "host"             # 主机级状态
    CLUSTER = "cluster"       # 集群级状态
    GLOBAL = "global"         # 全局状态

class CacheLevel(Enum):
    """缓存级别"""
    L1_MEMORY = 1      # 内存缓存
    L2_REDIS = 2       # Redis缓存
    L3_POSTGRES = 3    # PostgreSQL持久化

@dataclass
class StateMetadata:
    """状态元数据"""
    key: str
    scope: StateScope
    ttl: Optional[int] = None
    version: int = 1
    last_updated: float = 0
    source: str = "unknown"

class StateManager:
    """分布式状态管理器"""

    def __init__(self, config: Dict[str, Any]):
        self.config = config

        # L1缓存 - 内存
        self.l1_cache: Dict[str, Any] = {}
        self.l1_metadata: Dict[str, StateMetadata] = {}
        self.l1_ttl = config.get('l1_ttl', 100)  # 100ms

        # L2缓存 - Redis连接池
        self.redis_pool: Optional[redis.ConnectionPool] = None
        self.redis: Optional[redis.Redis] = None

        # L3持久化 - PostgreSQL连接池
        self.pg_pool: Optional[asyncpg.Pool] = None

        # 配置
        self.host_id = config.get('host_id', 'localhost')
        self.cluster_id = config.get('cluster_id', 'default')

        # 状态同步
        self.sync_enabled = config.get('sync_enabled', True)
        self.sync_interval = config.get('sync_interval', 1.0)

        # 运行状态
        self.running = False
        self._sync_task: Optional[asyncio.Task] = None
        
        # L3异步写入任务跟踪和重试
        self._l3_write_tasks: Set[asyncio.Task] = set()
        self._l3_retry_strategy = ExponentialBackoff(
            max_retries=config.get('l3_max_retries', 3),
            base_delay=config.get('l3_base_delay', 1.0),
            max_delay=config.get('l3_max_delay', 30.0)
        )
        
        # 连接状态监控
        self._redis_connected = False
        self._postgres_connected = False
        self._last_redis_check = 0
        self._last_postgres_check = 0
        self._connection_check_interval = config.get('connection_check_interval', 60.0)

    async def start(self):
        """启动状态管理器"""
        if self.running:
            return

        self.running = True

        # 检查是否为测试模式
        test_mode = self.config.get('test_mode', False)
        redis_enabled = self.config.get('redis', {}).get('enabled', True)
        postgres_enabled = self.config.get('database', {}).get('enabled', True)

        # 初始化连接
        if not test_mode and redis_enabled:
            await self._init_redis()
        if not test_mode and postgres_enabled:
            await self._init_postgres()

        # 启动同步任务
        if self.sync_enabled and not test_mode:
            self._sync_task = asyncio.create_task(self._sync_loop())

        logger.info("分布式状态管理器启动完成")

    async def stop(self):
        """停止状态管理器"""
        self.running = False

        if self._sync_task:
            self._sync_task.cancel()
            try:
                await self._sync_task
            except asyncio.CancelledError:
                pass

        # 等待所有L3写入完成
        await self.wait_for_l3_writes(timeout=30.0)
        
        # 刷新所有缓存到持久化层
        await self._flush_all_to_persistence()

        # 关闭连接
        if self.redis:
            await self.redis.close()
        if self.pg_pool:
            await self.pg_pool.close()

        logger.info("状态管理器已停止")

    async def _init_redis(self):
        """初始化Redis连接"""
        try:
            import os
            redis_config = self.config.get('redis', {})
            # 优先使用环境变量，然后是配置文件，最后是默认值
            host = os.getenv('REDIS_HOST', redis_config.get('host', 'localhost'))
            port = int(os.getenv('REDIS_PORT', redis_config.get('port', 6379)))

            logger.info(f"🔗 尝试连接Redis: {host}:{port}")

            # 创建连接池
            self.redis_pool = redis.ConnectionPool(
                host=host,
                port=port,
                db=redis_config.get('db', 0),
                password=redis_config.get('password'),
                max_connections=redis_config.get('max_connections', 20),
                retry_on_timeout=True,
                socket_connect_timeout=5,  # 5秒连接超时
                socket_timeout=5  # 5秒操作超时
            )

            self.redis = redis.Redis(connection_pool=self.redis_pool)

            # 测试连接（带超时）
            logger.debug("📡 测试Redis连接...")
            await asyncio.wait_for(self.redis.ping(), timeout=5.0)
            self._redis_connected = True
            logger.info("✅ Redis连接初始化成功")

        except asyncio.TimeoutError:
            logger.error(f"❌ Redis连接超时: {host}:{port}")
            logger.error("💡 请检查Redis服务是否正常运行")
            await self._handle_redis_failure("连接超时")
        except Exception as e:
            logger.error(f"❌ Redis连接初始化失败: {e}")
            logger.error("💡 请检查Redis服务配置和网络连接")
            await self._handle_redis_failure(f"连接失败: {e}")

    async def _init_postgres(self):
        """初始化PostgreSQL连接"""
        try:
            pg_config = self.config.get('postgres', {})
            host = pg_config.get('host', 'localhost')
            port = pg_config.get('port', 5432)
            database = pg_config.get('database', 'mt5_trading')

            logger.info(f"🔗 尝试连接PostgreSQL: {host}:{port}/{database}")

            # 创建连接池（带超时）
            self.pg_pool = await asyncio.wait_for(
                asyncpg.create_pool(
                    host=host,
                    port=port,
                    database=database,
                    user=pg_config.get('user', 'postgres'),
                    password=pg_config.get('password', ''),
                    min_size=pg_config.get('min_connections', 5),
                    max_size=pg_config.get('max_connections', 20),
                    command_timeout=pg_config.get('command_timeout', 30),
                    server_settings={
                        'application_name': f'mt5_state_manager_{self.host_id}',
                        'timezone': 'UTC'
                    }
                ),
                timeout=10.0
            )

            # 验证连接并创建必要的表（带超时）
            logger.debug("📡 测试PostgreSQL连接...")
            async with self.pg_pool.acquire() as conn:
                await asyncio.wait_for(conn.fetchval("SELECT 1"), timeout=5.0)

            await asyncio.wait_for(self._create_state_tables(), timeout=10.0)
            self._postgres_connected = True
            logger.info(f"✅ PostgreSQL连接池初始化成功 (min={pg_config.get('min_connections', 5)}, max={pg_config.get('max_connections', 20)})")

        except asyncio.TimeoutError:
            logger.error(f"❌ PostgreSQL连接超时: {host}:{port}")
            logger.error("💡 请检查PostgreSQL服务是否正常运行")
            await self._handle_postgres_failure("连接超时")
        except Exception as e:
            logger.error(f"❌ PostgreSQL连接初始化失败: {e}")
            logger.error("💡 请检查PostgreSQL服务配置和网络连接")
            await self._handle_postgres_failure(f"连接失败: {e}")

    async def _create_state_tables(self):
        """创建状态表"""
        if not self.pg_pool:
            return
            
        async with self.pg_pool.acquire() as conn:
            # 创建主状态表
            await conn.execute("""
                CREATE TABLE IF NOT EXISTS distributed_state (
                    id BIGSERIAL PRIMARY KEY,
                    key VARCHAR(255) NOT NULL UNIQUE,
                    scope VARCHAR(20) NOT NULL CHECK (scope IN ('local', 'host', 'cluster', 'global')),
                    value JSONB NOT NULL,
                    metadata JSONB NOT NULL DEFAULT '{}',
                    version BIGINT NOT NULL DEFAULT 1,
                    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
                    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
                    expires_at TIMESTAMP WITH TIME ZONE NULL,
                    source_host VARCHAR(100) NOT NULL DEFAULT 'unknown',
                    checksum VARCHAR(64) NULL
                )
            """)

            # 创建索引
            await conn.execute("""
                CREATE INDEX IF NOT EXISTS idx_distributed_state_scope 
                ON distributed_state(scope)
            """)
            
            await conn.execute("""
                CREATE INDEX IF NOT EXISTS idx_distributed_state_expires 
                ON distributed_state(expires_at) WHERE expires_at IS NOT NULL
            """)
            
            await conn.execute("""
                CREATE INDEX IF NOT EXISTS idx_distributed_state_updated 
                ON distributed_state(updated_at)
            """)
            
            await conn.execute("""
                CREATE INDEX IF NOT EXISTS idx_distributed_state_source 
                ON distributed_state(source_host)
            """)

            # 创建状态历史表
            await conn.execute("""
                CREATE TABLE IF NOT EXISTS state_history (
                    id BIGSERIAL PRIMARY KEY,
                    state_key VARCHAR(255) NOT NULL,
                    scope VARCHAR(20) NOT NULL,
                    change_type VARCHAR(20) NOT NULL CHECK (change_type IN ('INSERT', 'UPDATE', 'DELETE')),
                    old_value JSONB NULL,
                    new_value JSONB NULL,
                    version_before BIGINT NULL,
                    version_after BIGINT NOT NULL,
                    changed_by VARCHAR(100) NOT NULL,
                    change_reason VARCHAR(255) NULL,
                    changed_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
                )
            """)

            # 创建历史表索引
            await conn.execute("""
                CREATE INDEX IF NOT EXISTS idx_state_history_key 
                ON state_history(state_key)
            """)
            
            await conn.execute("""
                CREATE INDEX IF NOT EXISTS idx_state_history_changed_at 
                ON state_history(changed_at)
            """)

            # 创建系统配置表
            await conn.execute("""
                CREATE TABLE IF NOT EXISTS system_config (
                    config_key VARCHAR(255) PRIMARY KEY,
                    config_value JSONB NOT NULL,
                    config_type VARCHAR(50) NOT NULL DEFAULT 'general',
                    description TEXT NULL,
                    version INTEGER NOT NULL DEFAULT 1,
                    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
                    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
                )
            """)

            # 创建性能指标表  
            await conn.execute("""
                CREATE TABLE IF NOT EXISTS state_metrics (
                    id BIGSERIAL PRIMARY KEY,
                    metric_name VARCHAR(100) NOT NULL,
                    metric_value NUMERIC NOT NULL,
                    metric_type VARCHAR(20) NOT NULL CHECK (metric_type IN ('counter', 'gauge', 'histogram')),
                    tags JSONB NOT NULL DEFAULT '{}',
                    recorded_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
                    source_host VARCHAR(100) NOT NULL
                )
            """)
            
            await conn.execute("""
                CREATE INDEX IF NOT EXISTS idx_state_metrics_name_time 
                ON state_metrics(metric_name, recorded_at)
            """)

            logger.info("PostgreSQL表结构创建/更新完成")

    # ========== 状态读取接口 ==========

    async def get(self, key: str, default: Any = None,
                scope: StateScope = StateScope.LOCAL) -> Any:
        """获取状态值"""
        try:
            full_key = self._build_key(key, scope)

            # L1缓存查找（带即时过期检查）
            value = await self._get_from_l1(full_key, check_expiry=True)
            if value is not None:
                # metrics.increment('state_cache_hits', labels={'level': 'L1'})
                return value

            # L2缓存查找
            value = await self._get_from_l2(full_key)
            if value is not None:
                # 回写到L1
                await self._set_to_l1(full_key, value)
                # metrics.increment('state_cache_hits', labels={'level': 'L2'})
                return value

            # L3持久化查找
            value = await self._get_from_l3(full_key)
            if value is not None:
                # 回写到L2和L1
                await self._set_to_l2(full_key, value)
                await self._set_to_l1(full_key, value)
                # metrics.increment('state_cache_hits', labels={'level': 'L3'})
                return value

            # metrics.increment('state_cache_misses')
            return default

        except Exception as e:
            logger.error(f"获取状态失败 {key}: {e}")
            return default

    async def set(self, key: str, value: Any, ttl: Optional[int] = None,
                scope: StateScope = StateScope.LOCAL) -> bool:
        """设置状态值"""
        try:
            logger.debug(f"StateManager.set 开始: key={key}, scope={scope}")

            full_key = self._build_key(key, scope)
            logger.debug(f"StateManager.set 构建键: {full_key}")

            # 创建元数据
            metadata = StateMetadata(
                key=full_key,
                scope=scope,
                ttl=ttl,
                last_updated=time.time(),
                source=self.host_id
            )
            logger.debug(f"StateManager.set 创建元数据完成")

            # 同步写入所有层
            success = True

            # L1缓存
            logger.debug(f"StateManager.set 开始写入L1缓存")
            success &= await self._set_to_l1(full_key, value, metadata)
            logger.debug(f"StateManager.set L1缓存写入完成: {success}")

            # L2缓存
            logger.debug(f"StateManager.set 开始写入L2缓存")
            success &= await self._set_to_l2(full_key, value, ttl)
            logger.debug(f"StateManager.set L2缓存写入完成: {success}")

            # L3持久化（可靠的异步写入）
            if scope in [StateScope.CLUSTER, StateScope.GLOBAL]:
                logger.debug(f"StateManager.set 启动L3异步写入")
                l3_task = asyncio.create_task(
                    self._set_to_l3_with_retry(full_key, value, metadata)
                )
                self._l3_write_tasks.add(l3_task)
                l3_task.add_done_callback(self._l3_write_done)

            # 暂时禁用 metrics 以避免阻塞问题
            # if success:
            #     metrics.increment('state_sets_success')
            # else:
            #     metrics.increment('state_sets_failed')

            logger.debug(f"StateManager.set 完成: success={success}")
            return success

        except Exception as e:
            logger.error(f"设置状态失败 {key}: {e}")
            import traceback
            logger.error(f"详细错误: {traceback.format_exc()}")
            metrics.increment('state_sets_failed')
            return False

    async def delete(self, key: str, scope: StateScope = StateScope.LOCAL) -> bool:
        """删除状态"""
        try:
            full_key = self._build_key(key, scope)

            # 从所有层删除
            await self._delete_from_l1(full_key)
            await self._delete_from_l2(full_key)

            if scope in [StateScope.CLUSTER, StateScope.GLOBAL]:
                await self._delete_from_l3(full_key)

            return True

        except Exception as e:
            logger.error(f"删除状态失败 {key}: {e}")
            return False

    # ========== 高级状态操作 ==========

    async def increment(self, key: str, delta: Union[int, float] = 1,
                        scope: StateScope = StateScope.LOCAL) -> Union[int, float]:
        """原子递增"""
        full_key = self._build_key(key, scope)

        try:
            # 检查Redis连接
            if not self.redis or not self._redis_connected:
                logger.warning(f"Redis不可用，递增操作 {key} 降级为非原子操作")
                if not self._redis_connected:
                    await self._send_alert("原子操作降级", {
                        "operation": "increment",
                        "key": key,
                        "reason": "Redis连接不可用"
                    })
                # fallback到普通操作
                current = await self.get(key, 0, scope)
                new_value = current + delta
                await self.set(key, new_value, scope=scope)
                return new_value

            # 使用Redis的原子操作
            if isinstance(delta, int):
                new_value = await self.redis.incr(full_key, delta)
            else:
                new_value = await self.redis.incrbyfloat(full_key, delta)

            # 更新L1缓存
            await self._set_to_l1(full_key, new_value)

            return new_value

        except Exception as e:
            logger.error(f"递增操作失败 {key}: {e}")
            await self._send_alert("原子操作失败", {
                "operation": "increment",
                "key": key,
                "error": str(e)
            })
            # fallback到普通操作
            current = await self.get(key, 0, scope)
            new_value = current + delta
            await self.set(key, new_value, scope=scope)
            return new_value

    async def get_pattern(self, pattern: str,
                        scope: StateScope = StateScope.LOCAL) -> Dict[str, Any]:
        """按模式获取多个状态"""
        try:
            # 检查Redis连接
            if not self.redis or not self._redis_connected:
                logger.warning(f"Redis不可用，无法执行模式查询 {pattern}")
                if not self._redis_connected:
                    await self._send_alert("模式查询不可用", {
                        "pattern": pattern,
                        "reason": "Redis连接不可用"
                    })
                return {}

            full_pattern = self._build_key(pattern, scope)

            # 从Redis获取匹配的键
            keys = await self.redis.keys(full_pattern)

            if not keys:
                return {}

            # 批量获取值
            values = await self.redis.mget(keys)

            result = {}
            for key, value in zip(keys, values):
                if value:
                    # 确保键是字符串类型
                    if isinstance(key, bytes):
                        key = key.decode('utf-8')
                    # 确保值是字符串类型
                    if isinstance(value, bytes):
                        value = value.decode('utf-8')

                    # 解码键名
                    original_key = self._decode_key(key, scope)
                    result[original_key] = json.loads(value)

            return result

        except Exception as e:
            logger.error(f"模式获取失败 {pattern}: {e}")
            await self._send_alert("模式查询失败", {
                "pattern": pattern,
                "error": str(e)
            })
            return {}

    # ========== L1缓存操作（内存） ==========

    async def _get_from_l1(self, key: str, check_expiry: bool = False) -> Any:
        """从L1缓存获取"""
        if key not in self.l1_cache:
            return None

        metadata = self.l1_metadata.get(key)
        if metadata and metadata.ttl:
            # 检查过期
            if time.time() - metadata.last_updated > (metadata.ttl / 1000):
                await self._delete_from_l1(key)
                return None

        # 如果启用了即时过期检查，额外清理过期数据
        if check_expiry:
            await self._cleanup_expired_neighbors(key)

        return self.l1_cache.get(key)

    async def _set_to_l1(self, key: str, value: Any,
                        metadata: Optional[StateMetadata] = None) -> bool:
        """设置到L1缓存"""
        try:
            self.l1_cache[key] = value

            if metadata:
                self.l1_metadata[key] = metadata

            return True
        except Exception as e:
            logger.error(f"L1缓存设置失败 {key}: {e}")
            return False

    async def _delete_from_l1(self, key: str):
        """从L1缓存删除"""
        self.l1_cache.pop(key, None)
        self.l1_metadata.pop(key, None)

    # ========== L2缓存操作（Redis） ==========

    async def _get_from_l2(self, key: str) -> Any:
        """从L2缓存获取"""
        if not self.redis:
            logger.debug(f"Redis未连接，跳过L2缓存获取: {key}")
            return None

        try:
            value = await self.redis.get(key)
            if value:
                return json.loads(value)
            return None
        except Exception as e:
            logger.debug(f"L2缓存获取失败 {key}: {e}")
            return None

    async def _set_to_l2(self, key: str, value: Any, ttl: Optional[int] = None) -> bool:
        """设置到L2缓存"""
        if not self.redis:
            logger.debug(f"Redis未连接，跳过L2缓存设置: {key}")
            return True

        try:
            serialized = json.dumps(value, default=str)

            if ttl:
                await self.redis.setex(key, ttl, serialized)
            else:
                await self.redis.set(key, serialized)

            return True
        except Exception as e:
            logger.debug(f"L2缓存设置失败 {key}: {e}")
            return False

    async def _delete_from_l2(self, key: str):
        """从L2缓存删除"""
        try:
            await self.redis.delete(key)
        except Exception as e:
            logger.error(f"L2缓存删除失败 {key}: {e}")

    # ========== L3持久化操作（PostgreSQL） ==========

    async def _get_from_l3(self, key: str) -> Any:
        """从L3持久化获取（增强版）"""
        if not self.pg_pool:
            return None
            
        try:
            async with self.pg_pool.acquire() as conn:
                row = await conn.fetchrow("""
                    SELECT value, metadata, version, expires_at, checksum
                    FROM distributed_state 
                    WHERE key = $1 
                    AND (expires_at IS NULL OR expires_at > NOW())
                """, key)

                if row:
                    # 记录访问指标
                    await self._record_metric('l3_read_success', 1, 
                                            tags={'key_prefix': key.split(':')[0]})
                    
                    # 验证数据完整性（如果有校验和）
                    if row['checksum']:
                        calculated_checksum = await self._calculate_checksum(row['value'])
                        if calculated_checksum != row['checksum']:
                            logger.warning(f"L3数据校验失败: {key}")
                            await self._record_metric('l3_checksum_error', 1)
                            return None
                    
                    # 返回解析后的值
                    return json.loads(row['value']) if isinstance(row['value'], str) else row['value']
                    
                return None

        except Exception as e:
            logger.error(f"L3持久化获取失败 {key}: {e}")
            await self._record_metric('l3_read_error', 1)
            return None

    async def _set_to_l3(self, key: str, value: Any, metadata: StateMetadata):
        """设置到L3持久化（增强版）"""
        if not self.pg_pool:
            return
            
        try:
            async with self.pg_pool.acquire() as conn:
                # 序列化值
                json_value = json.dumps(value, ensure_ascii=False) if not isinstance(value, str) else value
                
                # 计算校验和
                checksum = await self._calculate_checksum(json_value)
                
                # 计算过期时间
                expires_sql = "NULL"
                expires_value = None
                if metadata.ttl:
                    expires_sql = "NOW() + INTERVAL '%s seconds'" % metadata.ttl
                
                # 获取旧版本用于历史记录
                old_row = await conn.fetchrow(
                    "SELECT value, version FROM distributed_state WHERE key = $1", key
                )
                old_value = old_row['value'] if old_row else None
                old_version = old_row['version'] if old_row else 0
                
                # 执行插入或更新
                result = await conn.fetchrow(f"""
                    INSERT INTO distributed_state 
                    (key, scope, value, metadata, expires_at, source_host, checksum)
                    VALUES ($1, $2, $3, $4, {expires_sql}, $5, $6)
                    ON CONFLICT (key) DO UPDATE SET
                        value = $3,
                        metadata = $4,
                        updated_at = NOW(),
                        version = distributed_state.version + 1,
                        expires_at = {expires_sql},
                        source_host = $5,
                        checksum = $6
                    RETURNING version
                """, key, metadata.scope.value, json_value, 
                     json.dumps(asdict(metadata)), self.host_id, checksum)
                
                new_version = result['version']
                
                # 记录状态变更历史
                change_type = 'UPDATE' if old_value is not None else 'INSERT'
                await self._record_state_history(
                    conn, key, change_type, old_value, value, 
                    old_version, new_version, f'state_manager_{self.host_id}'
                )
                
                # 记录指标
                await self._record_metric('l3_write_success', 1)
                
        except Exception as e:
            logger.error(f"L3持久化设置失败 {key}: {e}")
            await self._record_metric('l3_write_error', 1)

    async def _delete_from_l3(self, key: str):
        """从L3持久化删除（增强版）"""
        if not self.pg_pool:
            return
            
        try:
            async with self.pg_pool.acquire() as conn:
                # 获取要删除的数据用于历史记录
                old_row = await conn.fetchrow(
                    "SELECT value, version FROM distributed_state WHERE key = $1", key
                )
                
                if old_row:
                    # 执行删除
                    await conn.execute(
                        "DELETE FROM distributed_state WHERE key = $1", key
                    )
                    
                    # 记录删除历史
                    await self._record_state_history(
                        conn, key, 'DELETE', old_row['value'], None,
                        old_row['version'], old_row['version'], f'state_manager_{self.host_id}'
                    )
                    
                    # 记录指标
                    await self._record_metric('l3_delete_success', 1)
                    
        except Exception as e:
            logger.error(f"L3持久化删除失败 {key}: {e}")
            await self._record_metric('l3_delete_error', 1)

    # ========== 辅助方法 ==========

    def _build_key(self, key: str, scope: StateScope) -> str:
        """构建完整的键名"""
        if scope == StateScope.LOCAL:
            return f"{self.host_id}:local:{key}"
        elif scope == StateScope.HOST:
            return f"{self.host_id}:host:{key}"
        elif scope == StateScope.CLUSTER:
            return f"{self.cluster_id}:cluster:{key}"
        else:  # GLOBAL
            return f"global:{key}"

    def _decode_key(self, full_key: str, scope: StateScope) -> str:
        """解码键名"""
        parts = full_key.split(':')
        if len(parts) >= 3:
            return ':'.join(parts[2:])
        return full_key
    
    async def _calculate_checksum(self, data: str) -> str:
        """计算数据校验和"""
        import hashlib
        return hashlib.sha256(data.encode('utf-8')).hexdigest()
    
    async def _record_state_history(self, conn, key: str, change_type: str, 
                                   old_value: Any, new_value: Any,
                                   old_version: int, new_version: int, 
                                   changed_by: str, reason: str = None):
        """记录状态变更历史"""
        try:
            await conn.execute("""
                INSERT INTO state_history 
                (state_key, scope, change_type, old_value, new_value, 
                 version_before, version_after, changed_by, change_reason)
                VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
            """, key, 
                 key.split(':')[1] if ':' in key else 'unknown',  # scope
                 change_type,
                 json.dumps(old_value) if old_value is not None else None,
                 json.dumps(new_value) if new_value is not None else None,
                 old_version, new_version, changed_by, reason)
        except Exception as e:
            logger.error(f"记录状态历史失败: {e}")
    
    async def _record_metric(self, metric_name: str, value: float, 
                           metric_type: str = 'counter', tags: Dict = None):
        """记录性能指标"""
        if not self.pg_pool:
            return
            
        try:
            async with self.pg_pool.acquire() as conn:
                await conn.execute("""
                    INSERT INTO state_metrics 
                    (metric_name, metric_value, metric_type, tags, source_host)
                    VALUES ($1, $2, $3, $4, $5)
                """, metric_name, value, metric_type, 
                     json.dumps(tags or {}), self.host_id)
        except Exception as e:
            logger.debug(f"记录指标失败: {e}")  # 使用debug级别，避免干扰主要功能

    async def _sync_loop(self):
        """状态同步循环"""
        while self.running:
            try:
                # 清理过期的L1缓存
                await self._cleanup_l1_expired()

                # 定期刷新热数据到Redis
                await self._sync_hot_data_to_l2()
                
                # 定期检查连接状态
                await self._check_connection_health()

                await asyncio.sleep(self.sync_interval)

            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"状态同步异常: {e}")
                await asyncio.sleep(5)
    
    async def _check_connection_health(self):
        """检查连接健康状态"""
        current_time = time.time()
        
        # 检查Redis连接
        if (current_time - self._last_redis_check) > self._connection_check_interval:
            await self._health_check_redis()
            self._last_redis_check = current_time
        
        # 检查PostgreSQL连接
        if (current_time - self._last_postgres_check) > self._connection_check_interval:
            await self._health_check_postgres()
            self._last_postgres_check = current_time
    
    async def _health_check_redis(self):
        """Redis健康检查"""
        if self.redis:
            try:
                await asyncio.wait_for(self.redis.ping(), timeout=5.0)
                if not self._redis_connected:
                    self._redis_connected = True
                    logger.info("✅ Redis连接已恢复")
            except Exception as e:
                if self._redis_connected:
                    self._redis_connected = False
                    logger.warning(f"⚠️ Redis连接丢失: {e}")
                    await self._send_alert("Redis连接丢失", {"error": str(e)})
    
    async def _health_check_postgres(self):
        """PostgreSQL健康检查"""
        if self.pg_pool:
            try:
                async with self.pg_pool.acquire() as conn:
                    await asyncio.wait_for(conn.fetchval("SELECT 1"), timeout=5.0)
                if not self._postgres_connected:
                    self._postgres_connected = True
                    logger.info("✅ PostgreSQL连接已恢复")
            except Exception as e:
                if self._postgres_connected:
                    self._postgres_connected = False
                    logger.warning(f"⚠️ PostgreSQL连接丢失: {e}")
                    environment = self.config.get('environment', 'production')
                    if environment == 'production':
                        await self._send_critical_alert("PostgreSQL连接丢失", {"error": str(e)})
                    else:
                        await self._send_alert("PostgreSQL连接丢失", {"error": str(e)})

    async def _cleanup_l1_expired(self):
        """清理过期的L1缓存"""
        current_time = time.time()
        expired_keys = []

        for key, metadata in self.l1_metadata.items():
            if metadata.ttl and (current_time - metadata.last_updated) > (metadata.ttl / 1000):
                expired_keys.append(key)

        for key in expired_keys:
            await self._delete_from_l1(key)

    async def _sync_hot_data_to_l2(self):
        """同步热数据到L2"""
        # 识别热数据（访问频繁的数据）
        # 这里可以根据访问统计来决定
        pass

    async def _flush_all_to_persistence(self):
        """刷新所有缓存到持久化层"""
        try:
            for key, value in self.l1_cache.items():
                metadata = self.l1_metadata.get(key)
                if metadata and metadata.scope in [StateScope.CLUSTER, StateScope.GLOBAL]:
                    await self._set_to_l3(key, value, metadata)

            logger.info("缓存刷新到持久化层完成")

        except Exception as e:
            logger.error(f"刷新缓存失败: {e}")

    # ========== 连接状态管理 ==========

    async def _handle_redis_failure(self, reason: str):
        """处理Redis连接失败"""
        self._redis_connected = False
        self.redis = None
        self.redis_pool = None
        
        # 发送告警
        await self._send_alert("Redis连接失败", {
            "reason": reason,
            "host_id": self.host_id,
            "timestamp": time.time(),
            "environment": self.config.get('environment', 'unknown')
        })
        
        logger.warning("⚠️ 系统将在无Redis模式下运行")

    async def _handle_postgres_failure(self, reason: str):
        """处理PostgreSQL连接失败"""
        self._postgres_connected = False
        self.pg_pool = None
        
        # 在生产环境中，PostgreSQL失败是严重问题
        environment = self.config.get('environment', 'production')
        if environment == 'production':
            await self._send_critical_alert("PostgreSQL连接失败 - 数据持久化不可用", {
                "reason": reason,
                "host_id": self.host_id,
                "timestamp": time.time(),
                "environment": environment
            })
            logger.critical("🚨 生产环境PostgreSQL不可用，数据持久化受影响!")
        else:
            logger.warning("⚠️ 开发环境PostgreSQL不可用，禁用L3持久化层")

    async def _send_alert(self, message: str, details: Dict[str, Any]):
        """发送普通告警"""
        logger.warning(f"📢 告警: {message}")
        logger.warning(f"详情: {details}")
        # 这里可以集成实际的告警系统（如钉钉、邮件等）
        
    async def _send_critical_alert(self, message: str, details: Dict[str, Any]):
        """发送严重告警"""
        logger.critical(f"🚨 严重告警: {message}")
        logger.critical(f"详情: {details}")
        # 这里可以集成实际的告警系统

    # ========== L3异步写入可靠性 ==========

    async def _set_to_l3_with_retry(self, key: str, value: Any, metadata: StateMetadata):
        """带重试的L3写入"""
        async def _write_operation():
            return await self._set_to_l3(key, value, metadata)
        
        try:
            await self._l3_retry_strategy.execute(_write_operation)
        except Exception as e:
            logger.error(f"L3写入重试失败 {key}: {e}")
            await self._send_alert("L3写入持续失败", {
                "key": key,
                "error": str(e),
                "retries": self._l3_retry_strategy.max_retries
            })

    def _l3_write_done(self, task: asyncio.Task):
        """L3写入任务完成回调"""
        self._l3_write_tasks.discard(task)
        
        if task.exception():
            logger.error(f"L3异步写入任务异常: {task.exception()}")

    async def wait_for_l3_writes(self, timeout: float = 30.0) -> bool:
        """等待所有L3写入完成"""
        if not self._l3_write_tasks:
            return True
        
        try:
            await asyncio.wait_for(
                asyncio.gather(*self._l3_write_tasks, return_exceptions=True),
                timeout=timeout
            )
            return True
        except asyncio.TimeoutError:
            logger.warning(f"L3写入等待超时，剩余任务: {len(self._l3_write_tasks)}")
            return False

    # ========== L1缓存优化 ==========

    async def _cleanup_expired_neighbors(self, current_key: str):
        """清理当前键附近的过期数据（LRU式优化）"""
        current_time = time.time()
        cleanup_count = 0
        max_cleanup = 10  # 限制单次清理数量
        
        for key, metadata in list(self.l1_metadata.items()):
            if cleanup_count >= max_cleanup:
                break
                
            if (metadata.ttl and 
                (current_time - metadata.last_updated) > (metadata.ttl / 1000)):
                await self._delete_from_l1(key)
                cleanup_count += 1
        
        if cleanup_count > 0:
            logger.debug(f"即时清理过期L1缓存: {cleanup_count}项")

    async def force_l2_to_l3_sync(self) -> int:
        """强制将L2数据同步到L3（运维工具）"""
        if not self.redis or not self.pg_pool:
            logger.warning("无法执行L2到L3同步：连接不可用")
            return 0
        
        synced_count = 0
        try:
            # 获取所有CLUSTER和GLOBAL作用域的键
            cluster_keys = await self.redis.keys(f"{self.cluster_id}:cluster:*")
            global_keys = await self.redis.keys("global:*")
            
            all_keys = cluster_keys + global_keys
            
            for key in all_keys:
                if isinstance(key, bytes):
                    key = key.decode('utf-8')
                    
                try:
                    value = await self._get_from_l2(key)
                    if value is not None:
                        # 构造元数据
                        parts = key.split(':')
                        scope = StateScope.CLUSTER if 'cluster' in key else StateScope.GLOBAL
                        metadata = StateMetadata(
                            key=key,
                            scope=scope,
                            last_updated=time.time(),
                            source=f"sync_{self.host_id}"
                        )
                        
                        await self._set_to_l3(key, value, metadata)
                        synced_count += 1
                        
                except Exception as e:
                    logger.error(f"同步键 {key} 失败: {e}")
                    
            logger.info(f"L2到L3同步完成: {synced_count}项")
            return synced_count
            
        except Exception as e:
            logger.error(f"L2到L3同步异常: {e}")
            return 0


# ========== RelationshipManager集成 ==========

class StateManagerStorageBackend:
    """基于StateManager的存储后端 - 用于RelationshipManager集成
    
    实现RelationshipManager的StorageBackend接口，将关系数据存储到StateManager的三层缓存中
    """
    
    def __init__(self, state_manager: StateManager, key_prefix: str = "relationships"):
        self.state_manager = state_manager
        self.key_prefix = key_prefix
        
    async def save_relationships(self, relationships: Dict[str, Any]) -> bool:
        """保存关系到StateManager"""
        try:
            # 序列化关系数据
            serialized_data = {}
            for rel_id, relationship in relationships.items():
                # 转换为字典并序列化时间和枚举
                rel_data = self._serialize_relationship(relationship)
                serialized_data[rel_id] = rel_data
            
            # 使用CLUSTER作用域存储，便于分布式共享
            success = await self.state_manager.set(
                key=f"{self.key_prefix}:all",
                value=serialized_data,
                scope=StateScope.CLUSTER
            )
            
            # 同时为每个关系创建单独的键，便于查询
            for rel_id, rel_data in serialized_data.items():
                await self.state_manager.set(
                    key=f"{self.key_prefix}:item:{rel_id}",
                    value=rel_data,
                    scope=StateScope.CLUSTER
                )
            
            return success
            
        except Exception as e:
            logger.error(f"StateManager保存关系失败: {e}")
            return False
    
    async def load_relationships(self) -> Dict[str, Any]:
        """从StateManager加载关系"""
        try:
            # 先尝试从总键加载
            serialized_data = await self.state_manager.get(
                key=f"{self.key_prefix}:all",
                scope=StateScope.CLUSTER
            )
            
            if serialized_data:
                relationships = {}
                for rel_id, rel_data in serialized_data.items():
                    relationship = self._deserialize_relationship(rel_data)
                    relationships[rel_id] = relationship
                return relationships
            
            # 如果总键不存在，尝试从单个键加载
            pattern_key = f"{self.key_prefix}:item:*"
            items = await self.state_manager.get_pattern(
                pattern=pattern_key,
                scope=StateScope.CLUSTER
            )
            
            relationships = {}
            for key, rel_data in items.items():
                # 从键名提取关系ID
                rel_id = key.split(':')[-1]
                relationship = self._deserialize_relationship(rel_data)
                relationships[rel_id] = relationship
            
            return relationships
            
        except Exception as e:
            logger.error(f"StateManager加载关系失败: {e}")
            return {}
    
    async def delete_relationship(self, relationship_id: str) -> bool:
        """删除特定关系"""
        try:
            # 删除单个关系键
            await self.state_manager.delete(
                key=f"{self.key_prefix}:item:{relationship_id}",
                scope=StateScope.CLUSTER
            )
            
            # 从总键中移除（需要重新保存）
            all_data = await self.state_manager.get(
                key=f"{self.key_prefix}:all",
                scope=StateScope.CLUSTER
            )
            
            if all_data and relationship_id in all_data:
                del all_data[relationship_id]
                await self.state_manager.set(
                    key=f"{self.key_prefix}:all",
                    value=all_data,
                    scope=StateScope.CLUSTER
                )
            
            return True
            
        except Exception as e:
            logger.error(f"StateManager删除关系失败 {relationship_id}: {e}")
            return False
    
    def _serialize_relationship(self, relationship) -> Dict[str, Any]:
        """序列化TradingRelationship对象"""
        from dataclasses import asdict
        from datetime import datetime
        from enum import Enum
        
        # 转换为字典
        if hasattr(relationship, '__dict__'):
            data = relationship.__dict__.copy()
        else:
            data = asdict(relationship)
        
        # 序列化特殊类型
        for key, value in data.items():
            if isinstance(value, datetime):
                data[key] = value.isoformat()
            elif isinstance(value, Enum):
                data[key] = value.value
            elif hasattr(value, '__dict__'):  # 嵌套对象
                data[key] = self._serialize_nested_object(value)
        
        return data
    
    def _serialize_nested_object(self, obj) -> Dict[str, Any]:
        """序列化嵌套对象"""
        from dataclasses import asdict
        from datetime import datetime
        from enum import Enum
        
        if hasattr(obj, '__dict__'):
            data = obj.__dict__.copy()
        else:
            data = asdict(obj)
        
        for key, value in data.items():
            if isinstance(value, datetime):
                data[key] = value.isoformat()
            elif isinstance(value, Enum):
                data[key] = value.value
        
        return data
    
    def _deserialize_relationship(self, data: Dict[str, Any]):
        """反序列化为TradingRelationship对象"""
        from datetime import datetime
        
        # 动态导入以避免循环依赖
        from ..relationships.relationship_manager import (
            TradingRelationship, RelationshipType, RelationshipStatus,
            VolumeMapping, SymbolFilter, TimeWindow
        )
        
        # 处理时间字段
        datetime_fields = ['created_at', 'updated_at', 'starts_at', 'expires_at']
        for field in datetime_fields:
            if field in data and data[field]:
                try:
                    data[field] = datetime.fromisoformat(data[field])
                except:
                    data[field] = None
        
        # 处理枚举字段
        if 'relationship_type' in data:
            data['relationship_type'] = RelationshipType(data['relationship_type'])
        if 'status' in data:
            data['status'] = RelationshipStatus(data['status'])
        
        # 处理嵌套对象
        if 'volume_mapping' in data and isinstance(data['volume_mapping'], dict):
            data['volume_mapping'] = VolumeMapping(**data['volume_mapping'])
        if 'symbol_filter' in data and isinstance(data['symbol_filter'], dict):
            data['symbol_filter'] = SymbolFilter(**data['symbol_filter'])
        if 'time_window' in data and isinstance(data['time_window'], dict):
            data['time_window'] = TimeWindow(**data['time_window'])
        
        return TradingRelationship(**data)


# ========== 使用示例 ==========

async def create_integrated_relationship_manager(state_manager: StateManager):
    """创建集成StateManager的RelationshipManager示例"""
    from ..relationships.relationship_manager import RelationshipManager
    
    # 创建StateManager存储后端
    storage_backend = StateManagerStorageBackend(state_manager)
    
    # 创建RelationshipManager，使用StateManager作为存储后端
    relationship_manager = RelationshipManager(
        storage_backend=storage_backend,
        auto_save=True
    )
    
    # 异步初始化
    await relationship_manager.initialize()
    
    logger.info("✅ RelationshipManager与StateManager集成完成")
    logger.info("🚀 关系数据现在使用三层缓存架构：L1(内存) -> L2(Redis) -> L3(PostgreSQL)")
    logger.info("🌐 关系数据在集群范围内自动共享和同步")
    
    return relationship_manager


async def example_usage():
    """使用示例"""
    # 1. 创建StateManager配置
    config = {
        'host_id': 'trading-node-01',
        'cluster_id': 'prod-cluster',
        'environment': 'production',
        'redis': {
            'enabled': True,
            'host': 'localhost',
            'port': 6379
        },
        'database': {
            'enabled': True,
            'host': 'localhost',
            'port': 5432,
            'database': 'mt5_trading'
        },
        'l3_max_retries': 3,
        'l3_base_delay': 1.0,
        'connection_check_interval': 60.0
    }
    
    # 2. 创建并启动StateManager
    state_manager = StateManager(config)
    await state_manager.start()
    
    try:
        # 3. 创建集成的RelationshipManager
        relationship_manager = await create_integrated_relationship_manager(state_manager)
        
        # 4. 使用RelationshipManager（现在数据存储在StateManager中）
        async with relationship_manager:
            relationship = await relationship_manager.create_relationship(
                source_account="ACC001",
                target_account="ACC002",
                relationship_type="forward"
            )
            
            logger.info(f"创建关系: {relationship.relationship_id}")
            
            # 关系数据现在自动存储在：
            # - L1: 内存缓存（快速访问）
            # - L2: Redis（集群共享）
            # - L3: PostgreSQL（持久化存储）
            
    finally:
        # 5. 优雅关闭
        await state_manager.stop()


async def validate_improvements():
    """验证StateManager改进效果"""
    logger.info("🔧 验证StateManager改进效果...")
    
    config = {
        'host_id': 'test-node',
        'cluster_id': 'test-cluster',
        'test_mode': True,  # 测试模式，不连接真实的Redis/PostgreSQL
        'l1_ttl': 100,
        'sync_interval': 1.0,
        'l3_max_retries': 3,
        'connection_check_interval': 60.0
    }
    
    state_manager = StateManager(config)
    await state_manager.start()
    
    try:
        # 1. 测试L1缓存过期优化
        logger.info("✅ L1缓存即时过期检查已启用")
        
        # 2. 测试L3异步写入跟踪
        logger.info(f"✅ L3写入任务跟踪池: {len(state_manager._l3_write_tasks)}")
        logger.info(f"✅ L3重试策略: max_retries={state_manager._l3_retry_strategy.max_retries}")
        
        # 3. 测试连接状态监控
        logger.info(f"✅ Redis连接状态: {state_manager._redis_connected}")
        logger.info(f"✅ PostgreSQL连接状态: {state_manager._postgres_connected}")
        
        # 4. 测试告警系统
        await state_manager._send_alert("测试告警", {"type": "validation"})
        
        # 5. 测试RelationshipManager集成
        try:
            storage_backend = StateManagerStorageBackend(state_manager)
            logger.info("✅ StateManagerStorageBackend创建成功")
            logger.info("✅ RelationshipManager可以集成StateManager三层缓存")
        except Exception as e:
            logger.warning(f"⚠️ RelationshipManager集成测试失败: {e}")
        
        logger.info("🎉 所有StateManager改进验证完成！")
        
    finally:
        await state_manager.stop()


if __name__ == "__main__":
    import asyncio
    # asyncio.run(example_usage())
    asyncio.run(validate_improvements())