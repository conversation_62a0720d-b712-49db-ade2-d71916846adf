"""
MT5跟单系统SDK
"""
import asyncio
import aiohttp
import json
from typing import List, Dict, Optional, Any
from dataclasses import dataclass

from ..utils.logger import get_logger


logger = get_logger(__name__)


@dataclass
class CrossHostPairingConfig:
    """跨主机跟单配置"""
    pairing_id: str
    master_host: str
    master_account: str
    slave_host: str
    slave_account: str
    strategy: str = "proportional"
    lot_multiplier: float = 1.0
    max_lot: float = 10.0
    min_lot: float = 0.01
    enabled: bool = True


@dataclass
class SystemHealthInfo:
    """系统健康信息"""
    total_hosts: int
    online_hosts: int
    offline_hosts: int
    total_accounts: int
    active_accounts: int
    total_pairings: int
    active_pairings: int
    message_throughput: float
    average_latency: float
    error_rate: float


@dataclass
class HostInfo:
    """主机信息"""
    host_id: str
    hostname: str
    ip_address: str
    port: int = 8080
    capabilities: List[str] = None
    max_accounts: int = 10
    status: str = "active"


@dataclass
class AccountInfo:
    """账户信息"""
    account_id: str
    login: int
    password: str
    server: str
    host_id: str
    account_type: str
    terminal_path: Optional[str] = None
    max_lot_size: float = 10.0
    risk_level: str = "medium"
    allowed_symbols: List[str] = None


@dataclass
class PairingConfig:
    """配对配置"""
    master_account: str
    slave_accounts: List[str]
    strategy: str = "full_copy"
    lot_multiplier: float = 1.0
    max_lot_size: float = 10.0
    min_lot_size: float = 0.01
    symbols_filter: Optional[List[str]] = None
    copy_sl_tp: bool = True
    reverse_copy: bool = False
    max_drawdown: float = 0.2
    daily_loss_limit: float = 1000.0
    max_positions: int = 10
    start_time: Optional[str] = None
    end_time: Optional[str] = None


class MT5CopierSDK:
    """MT5跟单系统SDK"""
    
    def __init__(self, base_url: str, api_key: Optional[str] = None, timeout: int = 30):
        self.base_url = base_url.rstrip('/')
        self.api_key = api_key
        self.timeout = timeout
        self.session: Optional[aiohttp.ClientSession] = None
        
        # 请求头
        self.headers = {
            'Content-Type': 'application/json',
            'User-Agent': 'MT5CopierSDK/1.0'
        }
        
        if api_key:
            self.headers['Authorization'] = f'Bearer {api_key}'
    
    async def __aenter__(self):
        """异步上下文管理器入口"""
        connector = aiohttp.TCPConnector(
            limit=100,
            limit_per_host=10,
            ttl_dns_cache=300,
            use_dns_cache=True
        )
        
        timeout = aiohttp.ClientTimeout(total=self.timeout)
        
        self.session = aiohttp.ClientSession(
            connector=connector,
            timeout=timeout,
            headers=self.headers
        )
        
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        if self.session:
            await self.session.close()
    
    async def _request(self, method: str, endpoint: str, data: Optional[Dict] = None, 
                      params: Optional[Dict] = None) -> Dict[str, Any]:
        """发送HTTP请求"""
        if not self.session:
            raise RuntimeError("SDK未初始化，请使用async with语句")
        
        url = f"{self.base_url}{endpoint}"
        
        try:
            async with self.session.request(
                method=method,
                url=url,
                json=data,
                params=params
            ) as response:
                
                response_text = await response.text()
                
                if response.status >= 400:
                    try:
                        error_data = json.loads(response_text)
                        error_msg = error_data.get('detail', response_text)
                    except:
                        error_msg = response_text
                    
                    raise Exception(f"HTTP {response.status}: {error_msg}")
                
                if response_text:
                    return json.loads(response_text)
                else:
                    return {}
                
        except aiohttp.ClientError as e:
            raise Exception(f"网络请求失败: {e}")
        except json.JSONDecodeError as e:
            raise Exception(f"响应解析失败: {e}")
    
    async def register_host(self, host_info: HostInfo) -> bool:
        """注册主机"""
        data = {
            "host_id": host_info.host_id,
            "hostname": host_info.hostname,
            "ip_address": host_info.ip_address,
            "port": host_info.port,
            "capabilities": host_info.capabilities or [],
            "max_accounts": host_info.max_accounts,
            "status": host_info.status
        }
        
        try:
            await self._request('POST', '/api/hosts/register', data=data)
            logger.info(f"主机注册成功: {host_info.host_id}")
            return True
        except Exception as e:
            logger.error(f"主机注册失败: {host_info.host_id}, 错误: {e}")
            return False
    
    async def register_account(self, account_info: AccountInfo) -> bool:
        """注册账户"""
        data = {
            "account_id": account_info.account_id,
            "login": account_info.login,
            "password": account_info.password,
            "server": account_info.server,
            "host_id": account_info.host_id,
            "account_type": account_info.account_type,
            "terminal_path": account_info.terminal_path,
            "max_lot_size": account_info.max_lot_size,
            "risk_level": account_info.risk_level,
            "allowed_symbols": account_info.allowed_symbols or []
        }
        
        try:
            await self._request('POST', '/api/accounts/register', data=data)
            logger.info(f"账户注册成功: {account_info.account_id}")
            return True
        except Exception as e:
            logger.error(f"账户注册失败: {account_info.account_id}, 错误: {e}")
            return False
    
    async def create_pairing(self, config: PairingConfig) -> str:
        """创建账户配对"""
        data = {
            "master_account": config.master_account,
            "slave_accounts": config.slave_accounts,
            "strategy": config.strategy,
            "lot_multiplier": config.lot_multiplier,
            "max_lot_size": config.max_lot_size,
            "min_lot_size": config.min_lot_size,
            "symbols_filter": config.symbols_filter,
            "copy_sl_tp": config.copy_sl_tp,
            "reverse_copy": config.reverse_copy,
            "max_drawdown": config.max_drawdown,
            "daily_loss_limit": config.daily_loss_limit,
            "max_positions": config.max_positions,
            "start_time": config.start_time,
            "end_time": config.end_time
        }
        
        try:
            response = await self._request('POST', '/api/pairings', data=data)
            pairing_id = response.get('pairing_id')
            logger.info(f"配对创建成功: {pairing_id}")
            return pairing_id
        except Exception as e:
            logger.error(f"配对创建失败: {e}")
            raise
    
    async def update_pairing(self, pairing_id: str, updates: Dict[str, Any]) -> bool:
        """更新配对"""
        try:
            await self._request('PUT', f'/api/pairings/{pairing_id}', data=updates)
            logger.info(f"配对更新成功: {pairing_id}")
            return True
        except Exception as e:
            logger.error(f"配对更新失败: {pairing_id}, 错误: {e}")
            return False
    
    async def delete_pairing(self, pairing_id: str) -> bool:
        """删除配对"""
        try:
            await self._request('DELETE', f'/api/pairings/{pairing_id}')
            logger.info(f"配对删除成功: {pairing_id}")
            return True
        except Exception as e:
            logger.error(f"配对删除失败: {pairing_id}, 错误: {e}")
            return False
    
    async def get_pairing(self, pairing_id: str) -> Optional[Dict[str, Any]]:
        """获取配对详情"""
        try:
            response = await self._request('GET', f'/api/pairings/{pairing_id}')
            return response
        except Exception as e:
            logger.error(f"获取配对失败: {pairing_id}, 错误: {e}")
            return None
    
    async def list_pairings(self) -> List[Dict[str, Any]]:
        """列出所有配对"""
        try:
            response = await self._request('GET', '/api/pairings')
            return response.get('pairings', [])
        except Exception as e:
            logger.error(f"获取配对列表失败: {e}")
            return []
    
    async def get_pairing_status(self, pairing_id: str) -> Dict[str, Any]:
        """获取配对状态"""
        try:
            response = await self._request('GET', f'/api/pairings/{pairing_id}/status')
            return response
        except Exception as e:
            logger.error(f"获取配对状态失败: {pairing_id}, 错误: {e}")
            return {}
    
    async def enable_pairing(self, pairing_id: str) -> bool:
        """启用配对"""
        return await self.update_pairing(pairing_id, {'enabled': True})
    
    async def disable_pairing(self, pairing_id: str) -> bool:
        """禁用配对"""
        return await self.update_pairing(pairing_id, {'enabled': False})
    
    async def get_system_status(self) -> Dict[str, Any]:
        """获取系统状态"""
        try:
            response = await self._request('GET', '/api/system/status')
            return response
        except Exception as e:
            logger.error(f"获取系统状态失败: {e}")
            return {}
    
    async def get_system_health(self) -> Dict[str, Any]:
        """获取系统健康状态"""
        try:
            response = await self._request('GET', '/api/system/health')
            return response
        except Exception as e:
            logger.error(f"获取系统健康状态失败: {e}")
            return {}
    
    async def get_hosts(self) -> List[Dict[str, Any]]:
        """获取主机列表"""
        try:
            response = await self._request('GET', '/api/hosts')
            return response.get('hosts', [])
        except Exception as e:
            logger.error(f"获取主机列表失败: {e}")
            return []
    
    async def get_host_status(self, host_id: str) -> Dict[str, Any]:
        """获取主机状态"""
        try:
            response = await self._request('GET', f'/api/hosts/{host_id}/status')
            return response
        except Exception as e:
            logger.error(f"获取主机状态失败: {host_id}, 错误: {e}")
            return {}
    
    async def get_accounts(self) -> List[Dict[str, Any]]:
        """获取账户列表"""
        try:
            response = await self._request('GET', '/api/accounts')
            return response.get('accounts', [])
        except Exception as e:
            logger.error(f"获取账户列表失败: {e}")
            return []
    
    async def get_account_status(self, account_id: str) -> Dict[str, Any]:
        """获取账户状态"""
        try:
            response = await self._request('GET', f'/api/accounts/{account_id}/status')
            return response
        except Exception as e:
            logger.error(f"获取账户状态失败: {account_id}, 错误: {e}")
            return {}
    
    async def get_performance_metrics(self) -> Dict[str, Any]:
        """获取性能指标"""
        try:
            response = await self._request('GET', '/api/metrics/performance')
            return response
        except Exception as e:
            logger.error(f"获取性能指标失败: {e}")
            return {}
    
    async def get_trading_stats(self, pairing_id: Optional[str] = None) -> Dict[str, Any]:
        """获取交易统计"""
        try:
            endpoint = '/api/metrics/trading'
            params = {'pairing_id': pairing_id} if pairing_id else None
            response = await self._request('GET', endpoint, params=params)
            return response
        except Exception as e:
            logger.error(f"获取交易统计失败: {e}")
            return {}
    
    async def emergency_stop(self, pairing_id: str) -> bool:
        """紧急停止配对"""
        try:
            await self._request('POST', f'/api/pairings/{pairing_id}/emergency-stop')
            logger.warning(f"紧急停止配对: {pairing_id}")
            return True
        except Exception as e:
            logger.error(f"紧急停止失败: {pairing_id}, 错误: {e}")
            return False
    
    async def emergency_stop_all(self) -> bool:
        """紧急停止所有配对"""
        try:
            await self._request('POST', '/api/system/emergency-stop')
            logger.warning("紧急停止所有配对")
            return True
        except Exception as e:
            logger.error(f"紧急停止所有配对失败: {e}")
            return False
    
    async def restart_pairing(self, pairing_id: str) -> bool:
        """重启配对"""
        try:
            await self._request('POST', f'/api/pairings/{pairing_id}/restart')
            logger.info(f"重启配对: {pairing_id}")
            return True
        except Exception as e:
            logger.error(f"重启配对失败: {pairing_id}, 错误: {e}")
            return False
    
    async def export_config(self, pairing_id: str) -> Dict[str, Any]:
        """导出配对配置"""
        try:
            response = await self._request('GET', f'/api/pairings/{pairing_id}/export')
            return response
        except Exception as e:
            logger.error(f"导出配置失败: {pairing_id}, 错误: {e}")
            return {}
    
    async def import_config(self, config: Dict[str, Any]) -> str:
        """导入配对配置"""
        try:
            response = await self._request('POST', '/api/pairings/import', data=config)
            pairing_id = response.get('pairing_id')
            logger.info(f"导入配置成功: {pairing_id}")
            return pairing_id
        except Exception as e:
            logger.error(f"导入配置失败: {e}")
            raise
    
    # 便捷方法
    async def quick_setup(self, master_login: int, master_password: str, master_server: str,
                         slave_login: int, slave_password: str, slave_server: str,
                         host_id: str = "default_host", lot_multiplier: float = 1.0) -> str:
        """快速设置配对"""
        # 注册主机
        host_info = HostInfo(
            host_id=host_id,
            hostname=f"host_{host_id}",
            ip_address="127.0.0.1",
            capabilities=["master", "slave"]
        )
        await self.register_host(host_info)
        
        # 注册主账户
        master_account = AccountInfo(
            account_id=f"master_{master_login}",
            login=master_login,
            password=master_password,
            server=master_server,
            host_id=host_id,
            account_type="master"
        )
        await self.register_account(master_account)
        
        # 注册从账户
        slave_account = AccountInfo(
            account_id=f"slave_{slave_login}",
            login=slave_login,
            password=slave_password,
            server=slave_server,
            host_id=host_id,
            account_type="slave"
        )
        await self.register_account(slave_account)
        
        # 创建配对
        pairing_config = PairingConfig(
            master_account=master_account.account_id,
            slave_accounts=[slave_account.account_id],
            strategy="proportional",
            lot_multiplier=lot_multiplier
        )
        
        return await self.create_pairing(pairing_config)
    
    async def batch_create_pairings(self, configs: List[PairingConfig]) -> List[str]:
        """批量创建配对"""
        pairing_ids = []
        
        for config in configs:
            try:
                pairing_id = await self.create_pairing(config)
                pairing_ids.append(pairing_id)
            except Exception as e:
                logger.error(f"批量创建配对失败: {e}")
                # 继续处理其他配对
                continue
        
        return pairing_ids

    # 跨主机功能
    async def register_host(self, host_info: HostInfo) -> bool:
        """注册主机"""
        try:
            response = await self._request('POST', '/api/hosts/register', data={
                "host_id": host_info.host_id,
                "hostname": host_info.hostname,
                "ip_address": host_info.ip_address,
                "port": host_info.port,
                "capabilities": host_info.capabilities or [],
                "metadata": getattr(host_info, 'metadata', {})
            })
            return response.get('success', False)
        except Exception as e:
            logger.error(f"注册主机失败: {e}")
            return False

    async def unregister_host(self, host_id: str) -> bool:
        """注销主机"""
        try:
            response = await self._request('DELETE', f'/api/hosts/{host_id}')
            return response.get('success', False)
        except Exception as e:
            logger.error(f"注销主机失败: {e}")
            return False

    async def get_host_list(self) -> List[HostInfo]:
        """获取主机列表"""
        try:
            response = await self._request('GET', '/api/hosts')
            hosts = []
            for host_data in response.get('hosts', []):
                hosts.append(HostInfo(**host_data))
            return hosts
        except Exception as e:
            logger.error(f"获取主机列表失败: {e}")
            return []

    async def get_host_status(self, host_id: str) -> Dict[str, Any]:
        """获取主机状态"""
        try:
            response = await self._request('GET', f'/api/hosts/{host_id}/status')
            return response
        except Exception as e:
            logger.error(f"获取主机状态失败: {e}")
            return {}

    async def create_cross_host_pairing(self, config: CrossHostPairingConfig) -> str:
        """创建跨主机跟单配置"""
        try:
            response = await self._request('POST', '/api/pairings/cross-host', data={
                "master_host": config.master_host,
                "master_account": config.master_account,
                "slave_host": config.slave_host,
                "slave_account": config.slave_account,
                "strategy": config.strategy,
                "lot_multiplier": config.lot_multiplier,
                "max_lot": config.max_lot,
                "min_lot": config.min_lot,
                "enabled": config.enabled
            })
            return response.get('pairing_id', '')
        except Exception as e:
            logger.error(f"创建跨主机跟单配置失败: {e}")
            raise

    async def delete_cross_host_pairing(self, pairing_id: str) -> bool:
        """删除跨主机跟单配置"""
        try:
            response = await self._request('DELETE', f'/api/pairings/cross-host/{pairing_id}')
            return response.get('success', False)
        except Exception as e:
            logger.error(f"删除跨主机跟单配置失败: {e}")
            return False

    async def get_cross_host_pairings(self) -> List[Dict[str, Any]]:
        """获取跨主机跟单配置列表"""
        try:
            response = await self._request('GET', '/api/pairings/cross-host')
            return response.get('pairings', [])
        except Exception as e:
            logger.error(f"获取跨主机跟单配置失败: {e}")
            return []

    async def get_system_health(self) -> SystemHealthInfo:
        """获取系统健康状态"""
        try:
            response = await self._request('GET', '/api/system/health')
            return SystemHealthInfo(**response)
        except Exception as e:
            logger.error(f"获取系统健康状态失败: {e}")
            return SystemHealthInfo(0, 0, 0, 0, 0, 0, 0, 0.0, 0.0, 1.0)

    async def get_performance_metrics(self) -> Dict[str, Any]:
        """获取性能指标"""
        try:
            response = await self._request('GET', '/api/system/metrics')
            return response
        except Exception as e:
            logger.error(f"获取性能指标失败: {e}")
            return {}

    async def get_message_routing_stats(self) -> Dict[str, Any]:
        """获取消息路由统计"""
        try:
            response = await self._request('GET', '/api/system/routing-stats')
            return response
        except Exception as e:
            logger.error(f"获取消息路由统计失败: {e}")
            return {}

    async def test_cross_host_connectivity(self, target_host: str) -> Dict[str, Any]:
        """测试跨主机连通性"""
        try:
            response = await self._request('POST', '/api/system/test-connectivity', data={
                "target_host": target_host
            })
            return response
        except Exception as e:
            logger.error(f"测试跨主机连通性失败: {e}")
            return {"success": False, "error": str(e)}
    
    async def wait_for_healthy(self, timeout: int = 60) -> bool:
        """等待系统健康"""
        import time
        start_time = time.time()
        
        while time.time() - start_time < timeout:
            try:
                health = await self.get_system_health()
                if health.get('status') == 'healthy':
                    return True
                
                await asyncio.sleep(5)
                
            except Exception as e:
                logger.warning(f"健康检查失败: {e}")
                await asyncio.sleep(5)
        
        return False