#!/usr/bin/env python3
"""
端到端交易测试 - 实际MT5连接和跟单测试
"""

import asyncio
import sys
import os
import time
from pathlib import Path
from typing import Dict, Any, List
from dotenv import load_dotenv

# 添加项目路径
sys.path.insert(0, str(Path(__file__).parent.parent))

from src.utils.logger import get_logger, setup_logging
from src.core.mt5_client import MT5Client
from src.core.signal_types import UnifiedTradeSignal, OrderType, SignalAction
from src.messaging.jetstream_client import JetStreamClient
from src.core.copy_strategy_processor import CopyStrategyProcessor
from src.utils.config_manager import ConfigManager

logger = get_logger(__name__)


class EndToEndTradingTester:
    """端到端交易测试器"""
    
    def __init__(self):
        self.config_manager = None
        self.mt5_clients = {}
        self.jetstream_client = None
        self.strategy_processor = None
        self.test_results = {}
        
    async def setup(self):
        """初始化测试环境"""
        logger.info("🔧 初始化端到端测试环境...")
        
        # 加载环境变量
        load_dotenv()
        logger.info("✅ 环境变量加载完成")
        
        # 初始化配置管理器
        self.config_manager = ConfigManager('config/optimized_system.yaml')
        
        # 初始化 JetStream 客户端
        self.jetstream_client = JetStreamClient(['nats://localhost:4222'])
        await self.jetstream_client.connect()
        
        # 初始化策略处理器
        self.strategy_processor = CopyStrategyProcessor(self.config_manager)
        
        logger.info("✅ 测试环境初始化完成")
    
    async def test_mt5_connections_with_env(self) -> bool:
        """使用环境变量测试MT5连接"""
        logger.info("🧪 测试MT5终端连接（使用环境变量）...")
        
        accounts = ['ACC001', 'ACC002']
        connection_results = {}
        
        for account_id in accounts:
            try:
                logger.info(f"测试账户 {account_id} 连接...")
                
                # 加载账户配置
                account_config_path = f"config/accounts/{account_id}.yaml"
                if not Path(account_config_path).exists():
                    logger.error(f"账户配置文件不存在: {account_config_path}")
                    connection_results[account_id] = False
                    continue
                
                import yaml
                with open(account_config_path, 'r', encoding='utf-8') as f:
                    account_config = yaml.safe_load(f)
                
                # 获取账户信息
                mt5_config = account_config.get('mt5', {}).get('connection', {})
                login = mt5_config.get('login')
                server = mt5_config.get('server')
                password = os.getenv(f'MT5_{account_id}_PASSWORD')
                
                logger.info(f"账户信息: login={login}, server={server}, password={'***' if password else 'None'}")
                
                if not all([login, server, password]):
                    logger.error(f"账户 {account_id} 配置不完整")
                    connection_results[account_id] = False
                    continue
                
                # 创建 MT5 客户端
                mt5_client = MT5Client(
                    account_id=account_id,
                    login=int(login),
                    password=password,
                    server=server
                )
                
                # 尝试连接
                success = await mt5_client.connect()
                
                if success:
                    logger.info(f"✅ {account_id} 连接成功")
                    
                    # 获取账户信息
                    account_info = await mt5_client.get_account_info()
                    if account_info:
                        logger.info(f"账户信息: 余额={account_info.get('balance', 'N/A')}, "
                                  f"权益={account_info.get('equity', 'N/A')}, "
                                  f"保证金={account_info.get('margin', 'N/A')}")
                    
                    # 获取市场数据
                    symbol_info = await mt5_client.get_symbol_info('EURUSD')
                    if symbol_info:
                        logger.info(f"EURUSD 信息: 买价={symbol_info.get('bid', 'N/A')}, "
                                  f"卖价={symbol_info.get('ask', 'N/A')}")
                    
                    # 检查交易权限
                    trading_allowed = await mt5_client.is_trading_allowed()
                    logger.info(f"交易权限: {'✅ 允许' if trading_allowed else '❌ 禁止'}")
                    
                    # 获取当前持仓
                    positions = await mt5_client.get_positions()
                    logger.info(f"当前持仓数量: {len(positions) if positions else 0}")
                    
                    self.mt5_clients[account_id] = mt5_client
                    connection_results[account_id] = True
                    
                else:
                    logger.error(f"❌ {account_id} 连接失败")
                    connection_results[account_id] = False
                    
            except Exception as e:
                logger.error(f"❌ {account_id} 连接异常: {e}")
                connection_results[account_id] = False
        
        # 总结连接结果
        successful_connections = sum(1 for result in connection_results.values() if result)
        total_accounts = len(accounts)
        
        logger.info(f"连接测试完成: {successful_connections}/{total_accounts} 个账户连接成功")
        
        self.test_results['mt5_connections'] = connection_results
        return successful_connections > 0
    
    async def monitor_account_changes(self, duration_seconds: int = 30):
        """监控账户变化"""
        logger.info(f"🔍 开始监控账户变化 ({duration_seconds}秒)...")
        
        # 记录初始状态
        initial_states = {}
        for account_id, client in self.mt5_clients.items():
            try:
                account_info = await client.get_account_info()
                positions = await client.get_positions()
                initial_states[account_id] = {
                    'balance': account_info.get('balance', 0) if account_info else 0,
                    'equity': account_info.get('equity', 0) if account_info else 0,
                    'positions': len(positions) if positions else 0,
                    'position_details': positions if positions else []
                }
                logger.info(f"{account_id} 初始状态: 余额={initial_states[account_id]['balance']}, "
                          f"权益={initial_states[account_id]['equity']}, "
                          f"持仓={initial_states[account_id]['positions']}")
            except Exception as e:
                logger.error(f"获取 {account_id} 初始状态失败: {e}")
        
        # 监控变化
        start_time = time.time()
        last_check = {}
        
        while time.time() - start_time < duration_seconds:
            await asyncio.sleep(2)  # 每2秒检查一次
            
            for account_id, client in self.mt5_clients.items():
                try:
                    account_info = await client.get_account_info()
                    positions = await client.get_positions()
                    
                    current_state = {
                        'balance': account_info.get('balance', 0) if account_info else 0,
                        'equity': account_info.get('equity', 0) if account_info else 0,
                        'positions': len(positions) if positions else 0,
                        'position_details': positions if positions else []
                    }
                    
                    # 检查变化
                    if account_id in last_check:
                        prev_state = last_check[account_id]
                        
                        # 检查余额变化
                        if abs(current_state['balance'] - prev_state['balance']) > 0.01:
                            logger.info(f"🔄 {account_id} 余额变化: {prev_state['balance']} -> {current_state['balance']}")
                        
                        # 检查权益变化
                        if abs(current_state['equity'] - prev_state['equity']) > 0.01:
                            logger.info(f"🔄 {account_id} 权益变化: {prev_state['equity']} -> {current_state['equity']}")
                        
                        # 检查持仓变化
                        if current_state['positions'] != prev_state['positions']:
                            logger.info(f"🔄 {account_id} 持仓数量变化: {prev_state['positions']} -> {current_state['positions']}")
                            
                            # 显示新持仓详情
                            if current_state['position_details']:
                                for pos in current_state['position_details']:
                                    logger.info(f"  持仓: {pos.get('symbol', 'N/A')} "
                                              f"{pos.get('type', 'N/A')} "
                                              f"{pos.get('volume', 'N/A')} "
                                              f"@ {pos.get('price_open', 'N/A')}")
                    
                    last_check[account_id] = current_state
                    
                except Exception as e:
                    logger.error(f"监控 {account_id} 时出错: {e}")
        
        logger.info("✅ 账户监控完成")
        
        # 显示最终状态对比
        logger.info("📊 最终状态对比:")
        for account_id in initial_states:
            if account_id in last_check:
                initial = initial_states[account_id]
                final = last_check[account_id]
                
                balance_change = final['balance'] - initial['balance']
                equity_change = final['equity'] - initial['equity']
                position_change = final['positions'] - initial['positions']
                
                logger.info(f"{account_id}: 余额变化={balance_change:+.2f}, "
                          f"权益变化={equity_change:+.2f}, "
                          f"持仓变化={position_change:+d}")
    
    async def test_signal_publishing(self) -> bool:
        """测试信号发布"""
        logger.info("🧪 测试信号发布...")
        
        try:
            # 创建测试信号
            test_signal = UnifiedTradeSignal.create_open_signal(
                master_account="ACC001",
                symbol="EURUSD",
                order_type=OrderType.BUY,
                volume=0.01,  # 最小手数
                price=1.1000,
                position_id=99999,
                sl=1.0950,
                tp=1.1050,
                comment="End-to-end test signal"
            )
            
            logger.info(f"发布测试信号: {test_signal.symbol} {test_signal.order_type.value} {test_signal.volume}")
            
            # 发布信号
            success = await self.jetstream_client.publish(
                subject="MT5.TRADES.ACC001",
                data=test_signal.to_dict()
            )
            
            if success:
                logger.info("✅ 信号发布成功")
                
                # 应用跟单策略
                copy_signal = self.strategy_processor.apply_copy_strategy(
                    signal=test_signal,
                    master_account="ACC001",
                    slave_account="ACC002"
                )
                
                if copy_signal:
                    logger.info(f"✅ 跟单信号生成: {copy_signal.symbol} {copy_signal.order_type.value} {copy_signal.volume}")
                    
                    # 发布跟单信号
                    copy_success = await self.jetstream_client.publish(
                        subject="MT5.TRADES.ACC002",
                        data=copy_signal.to_dict()
                    )
                    
                    if copy_success:
                        logger.info("✅ 跟单信号发布成功")
                        self.test_results['signal_publishing'] = True
                        return True
                    else:
                        logger.error("❌ 跟单信号发布失败")
                else:
                    logger.error("❌ 跟单信号生成失败")
            else:
                logger.error("❌ 原始信号发布失败")
            
            self.test_results['signal_publishing'] = False
            return False
            
        except Exception as e:
            logger.error(f"❌ 信号发布测试异常: {e}")
            self.test_results['signal_publishing'] = False
            return False
    
    async def cleanup(self):
        """清理测试环境"""
        logger.info("🧹 清理测试环境...")
        
        # 断开 MT5 连接
        for account_id, client in self.mt5_clients.items():
            try:
                await client.disconnect()
                logger.info(f"✅ {account_id} 连接已断开")
            except Exception as e:
                logger.warning(f"⚠️ {account_id} 断开连接时出错: {e}")
        
        # 断开 JetStream 连接
        if self.jetstream_client:
            await self.jetstream_client.disconnect()
            logger.info("✅ JetStream 连接已断开")
    
    def print_test_summary(self):
        """打印测试总结"""
        logger.info("=" * 60)
        logger.info("📊 端到端交易测试总结")
        logger.info("=" * 60)
        
        test_categories = {
            'mt5_connections': 'MT5终端连接',
            'signal_publishing': '信号发布和跟单'
        }
        
        passed_tests = 0
        total_tests = len(test_categories)
        
        for test_key, test_name in test_categories.items():
            if test_key in self.test_results:
                result = self.test_results[test_key]
                if isinstance(result, dict):
                    # MT5连接测试结果是字典
                    success_count = sum(1 for v in result.values() if v)
                    total_count = len(result)
                    status = "✅ 通过" if success_count > 0 else "❌ 失败"
                    logger.info(f"  {test_name}: {status} ({success_count}/{total_count})")
                    if success_count > 0:
                        passed_tests += 1
                else:
                    # 其他测试结果是布尔值
                    status = "✅ 通过" if result else "❌ 失败"
                    logger.info(f"  {test_name}: {status}")
                    if result:
                        passed_tests += 1
            else:
                logger.info(f"  {test_name}: ⏭️ 跳过")
        
        logger.info(f"\n总计: {passed_tests}/{total_tests} 个测试通过")
        
        if passed_tests == total_tests:
            logger.info("🎉 所有测试通过！系统准备就绪")
            return True
        else:
            logger.error("💥 部分测试失败，需要进一步调试")
            return False


async def main():
    """主函数"""
    # 设置日志
    setup_logging({
        'level': 'INFO',
        'format': 'standard',
        'file': 'logs/end_to_end_test.log'
    })
    
    logger.info("=" * 60)
    logger.info("🚀 MT5端到端交易测试")
    logger.info("=" * 60)
    
    tester = EndToEndTradingTester()
    
    try:
        # 初始化测试环境
        await tester.setup()
        
        # 测试MT5连接
        logger.info("第一步: 测试MT5连接")
        mt5_success = await tester.test_mt5_connections_with_env()
        
        if not mt5_success:
            logger.error("❌ MT5连接失败，无法继续测试")
            return 1
        
        # 测试信号发布
        logger.info("第二步: 测试信号发布")
        signal_success = await tester.test_signal_publishing()
        
        # 开始监控
        logger.info("第三步: 开始账户监控")
        logger.info("🎯 现在请您在MT5终端进行交易操作，我将监控跟单行为")
        logger.info("建议操作:")
        logger.info("  1. 在ACC001账户开仓EURUSD买单（最小手数0.01）")
        logger.info("  2. 观察ACC002是否自动跟单（手数应为0.005）")
        logger.info("  3. 修改ACC001的止损止盈")
        logger.info("  4. 平仓ACC001的订单")
        
        # 监控30秒
        await tester.monitor_account_changes(30)
        
        # 打印测试总结
        tester.print_test_summary()
        
        return 0
        
    except Exception as e:
        logger.error(f"测试运行异常: {e}")
        return 1
    finally:
        await tester.cleanup()


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
