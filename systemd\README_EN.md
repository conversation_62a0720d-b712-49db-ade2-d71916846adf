# MT5 SystemD Service Configuration

<div align="center">

English | [简体中文](README.md)

</div>

This directory contains systemd service configuration files for the MT5 copy trading system.

## Service List

- `mt5-api.service` - Main API service
- `mt5-master.service` - Master account monitoring service
- `mt5-slave.service` - Slave account execution service

## Installation Steps

1. Create system user:
```bash
sudo useradd -r -s /bin/false mt5
sudo mkdir -p /opt/mt5-copier
sudo chown mt5:mt5 /opt/mt5-copier
```

2. Copy service files:
```bash
sudo cp systemd/*.service /etc/systemd/system/
sudo systemctl daemon-reload
```

3. Enable services:
```bash
sudo systemctl enable mt5-api.service
sudo systemctl enable mt5-master.service
sudo systemctl enable mt5-slave.service
```

4. Start services:
```bash
sudo systemctl start mt5-api.service
sudo systemctl start mt5-master.service
sudo systemctl start mt5-slave.service
```

## Management Commands

### Check Service Status
```bash
sudo systemctl status mt5-api.service
sudo systemctl status mt5-master.service
sudo systemctl status mt5-slave.service
```

### View Logs
```bash
sudo journalctl -u mt5-api.service -f
sudo journalctl -u mt5-master.service -f
sudo journalctl -u mt5-slave.service -f
```

### Restart Services
```bash
sudo systemctl restart mt5-api.service
sudo systemctl restart mt5-master.service
sudo systemctl restart mt5-slave.service
```

### Stop Services
```bash
sudo systemctl stop mt5-api.service
sudo systemctl stop mt5-master.service
sudo systemctl stop mt5-slave.service
```

## Configuration Description

### Environment Variables
- `MT5_CONFIG_PATH` - Configuration file path
- `MT5_LOG_LEVEL` - Log level
- `MT5_ACCOUNT_ID` - Account ID

### Resource Limits
- Memory limit: API service 2GB, other services 1GB
- CPU limit: API service 200%, other services 100%
- File descriptor limit: 65536
- Process limit: 4096

### Security Settings
- Run as non-privileged user
- Protect system files
- Limit file access permissions
- Disable new privilege acquisition

## Log Management

Service logs are managed through systemd journal:

```bash
# View latest 100 log lines
sudo journalctl -u mt5-api.service -n 100

# View logs for specific time period
sudo journalctl -u mt5-api.service --since "2024-01-01 00:00:00" --until "2024-01-01 23:59:59"

# Continuously monitor logs
sudo journalctl -u mt5-api.service -f
```

## Troubleshooting

### Common Issues

1. **Service startup failure**
   - Check configuration file path
   - Confirm Python virtual environment is correct
   - Check file permissions

2. **Redis/NATS connection failure**
   - Confirm Redis and NATS services are running
   - Check network connection
   - Verify connection information in configuration file

3. **Permission errors**
   - Confirm mt5 user has read/write permissions for relevant directories
   - Check SELinux settings (if enabled)

### Performance Tuning

1. **Memory Usage**
   - Adjust MemoryMax setting based on actual needs
   - Monitor memory usage: `sudo systemctl status mt5-api.service`

2. **CPU Usage**
   - Adjust CPUQuota setting
   - Monitor CPU usage: `top -p $(pgrep -f mt5-api)`

3. **File Descriptors**
   - Adjust LimitNOFILE based on connection count
   - Monitor file descriptor usage: `lsof -p $(pgrep -f mt5-api)`

## Backup and Recovery

### Backup Configuration
```bash
sudo cp /etc/systemd/system/mt5-*.service /backup/location/
```

### Restore Configuration
```bash
sudo cp /backup/location/mt5-*.service /etc/systemd/system/
sudo systemctl daemon-reload
sudo systemctl restart mt5-api.service mt5-master.service mt5-slave.service
```