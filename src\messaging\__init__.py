#!/usr/bin/env python3
"""
MT5 Messaging Module - 统一权威实现

🚨 HARD MIGRATION NOTICE:
旧的消息队列组件已被完全移除，不再提供向后兼容。

❌ DEPRECATED (已删除):
- NATSManager, NATSClient, JetStreamClient
- MessageCodec, ProtobufCodec  

✅ NEW UNIFIED IMPLEMENTATION (强制使用):
- HybridQueueManager (单一权威队列管理器)
- MessageQueueInterface (统一接口)
- MessageEnvelope (统一消息格式)

See MIGRATION_GUIDE.md for complete migration instructions.
"""

# 🚀 NEW UNIFIED MESSAGING COMPONENTS (MANDATORY)
from .hybrid_queue_manager import (
    HybridQueueManager,
    HybridQueueConfig, 
    QueueBackendType,
    FailoverStrategy,
    RoutingMode
)

from .message_queue_interface import (
    MessageQueueInterface,
    QueueConfig,
    QueueStatus,
    QueueMetrics
)

from .message_types import (
    MessageEnvelope,
    MessagePriority,
    TradeSignal,
    OrderTypeEnum,
    TradeAction
)

from .hybrid_message_router import (
    HybridMessageRouter,
    RouteRule,
    RateLimitConfig
)

# Keep priority queue as it's still used
from .priority_queue import (
    PriorityMessageQueue,
    PriorityAnalyzer,
    get_priority_queue
)

# 🚨 DEPRECATED COMPONENT WARNINGS
import warnings

def __getattr__(name):
    """Handle attempts to import deprecated components"""
    deprecated_components = {
        'NATSManager': 'HybridQueueManager',
        'NATSClient': 'HybridQueueManager.backends[QueueBackendType.NATS]',
        'JetStreamClient': 'HybridQueueManager.backends[QueueBackendType.NATS]', 
        'MessageCodec': 'MessageEnvelope',
        'ProtobufCodec': 'MessageEnvelope',
        'encode_message': 'MessageEnvelope(...)',
        'decode_message': 'message.payload',
        'get_protobuf_codec': 'MessageEnvelope(...)'
    }
    
    if name in deprecated_components:
        replacement = deprecated_components[name]
        error_msg = (
            f"\n🚨 DEPRECATED COMPONENT: {name}\n\n"
            f"This component has been PERMANENTLY REMOVED in the hard migration.\n"
            f"Use {replacement} instead.\n\n"
            f"Migration guide: MIGRATION_GUIDE.md\n"
        )
        raise ImportError(error_msg)
    
    raise AttributeError(f"module '{__name__}' has no attribute '{name}'")


# 🎯 EXPORT NEW UNIFIED COMPONENTS ONLY
__all__ = [
    # 🚀 Primary Queue Management (MANDATORY)
    'HybridQueueManager',
    'HybridQueueConfig',
    'QueueBackendType',
    'FailoverStrategy', 
    'RoutingMode',
    
    # 🔌 Queue Interface
    'MessageQueueInterface',
    'QueueConfig',
    'QueueStatus',
    'QueueMetrics',
    
    # 📨 Message Types
    'MessageEnvelope',
    'MessagePriority',
    'TradeSignal',
    'OrderTypeEnum',
    'TradeAction',
    
    # 🔀 Message Routing
    'HybridMessageRouter',
    'RouteRule',
    'RateLimitConfig',
    
    # ⚡ Priority Processing (retained)
    'PriorityMessageQueue',
    'PriorityAnalyzer',
    'get_priority_queue'
]

# 📊 VERSION INFO
__version__ = "2.0.0-UNIFIED"
__migration_status__ = "HARD_MIGRATION_COMPLETE"

# 🚨 STARTUP WARNING
warnings.warn(
    "\n🚀 MT5 Messaging System - HARD MIGRATION COMPLETE\n\n"
    "✅ Now using unified HybridQueueManager implementation\n"
    "❌ Old components (NATSManager, etc.) have been REMOVED\n\n"
    "See MIGRATION_GUIDE.md if you encounter import errors.\n",
    UserWarning,
    stacklevel=2
)