"""
Messaging module for MT5 trading system
"""


try:
    from .jetstream_client import JetStreamClient
    from .nats_client import NATSClient
    NATS_AVAILABLE = True
except ImportError:
    NATS_AVAILABLE = False

try:
    from .message_codec import MessageCodec, encode_message, decode_message
    from .priority_queue import PriorityMessageQueue as PriorityQueue, MessagePriority
    from .protobuf_codec import ProtobufCodec, get_protobuf_codec
    CODECS_AVAILABLE = True
except ImportError:
    CODECS_AVAILABLE = False

__all__ = [
    'TradeSignal',
    'SignalType', 
    'PositionSignalData',
    'OrderSignalData'
]

# Add optional components to __all__ if available
if NATS_AVAILABLE:
    __all__.extend(['JetStreamClient', 'NATSClient'])

if CODECS_AVAILABLE:
    __all__.extend([
        'MessageCodec',
        'encode_message',
        'decode_message',
        'PriorityQueue',
        'MessagePriority',
        'ProtobufCodec',
        'get_protobuf_codec'
    ])