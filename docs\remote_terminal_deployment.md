# 远程MT5终端部署指南

## 系统架构说明

主系统通过 Docker 部署在服务器上，远程MT5终端通过以下组件连接：

- **NATS** (端口 4222): 消息队列，负责信号传输
- **Redis** (端口 6379): 配置存储，同步账户配置
- **API** (端口 8000): REST API，管理接口
- **Web** (端口 3001): Web界面，监控和管理

## 部署步骤

### 1. 服务器端配置

#### 1.1 开放端口
确保服务器防火墙开放以下端口：
```bash
# 必需端口
4222  # NATS消息队列
6379  # Redis配置存储
8000  # API服务
3001  # Web界面

# 可选端口（监控）
3000  # Grafana
9091  # Prometheus
```

#### 1.2 获取服务器IP
```bash
# 获取公网IP
curl ifconfig.me
```

### 2. 远程终端配置

#### 2.1 准备环境

在运行MT5的Windows机器上：

```bash
# 1. 克隆项目
git clone https://github.com/your-repo/mt5-python.git
cd mt5-python

# 2. 创建虚拟环境
python -m venv venv
venv\Scripts\activate  # Windows

# 3. 安装依赖
pip install -r requirements.txt
```

#### 2.2 配置账户

1. **主账户配置** (发送信号):
```yaml
# 复制配置模板
cp config/remote_master_example.yaml config/my_master.yaml

# 编辑配置，修改以下内容：
# - YOUR_SERVER_IP 替换为实际服务器IP
# - MT5登录信息
# - 交易品种和风险参数
```

2. **从账户配置** (接收信号跟单):
```yaml
# 复制配置模板
cp config/remote_slave_example.yaml config/my_slave.yaml

# 编辑配置，修改以下内容：
# - YOUR_SERVER_IP 替换为实际服务器IP
# - MT5登录信息
# - 订阅的主账户ID
# - 跟单参数（手数、过滤条件等）
```

#### 2.3 设置环境变量

创建 `.env` 文件：
```bash
# MT5账户密码
MT5_MASTER_PASSWORD=your_master_password
MT5_SLAVE_PASSWORD=your_slave_password

# Telegram通知（可选）
TELEGRAM_BOT_TOKEN=your_bot_token
```

### 3. 启动远程终端

#### 3.1 启动主账户（信号源）
```bash
python scripts/remote_terminal.py --config config/my_master.yaml --role master
```

#### 3.2 启动从账户（跟单）
```bash
python scripts/remote_terminal.py --config config/my_slave.yaml --role slave
```

#### 3.3 使用批处理脚本（Windows）

创建 `start_master.bat`:
```batch
@echo off
cd /d C:\path\to\mt5-python
call venv\Scripts\activate
python scripts/remote_terminal.py --config config/my_master.yaml --role master
pause
```

创建 `start_slave.bat`:
```batch
@echo off
cd /d C:\path\to\mt5-python
call venv\Scripts\activate
python scripts/remote_terminal.py --config config/my_slave.yaml --role slave
pause
```

### 4. 验证连接

#### 4.1 检查服务状态
访问 Web 界面：`http://YOUR_SERVER_IP:3001`

#### 4.2 查看日志
```bash
# 本地日志
tail -f logs/remote_MASTER_REMOTE_001.log
tail -f logs/remote_SLAVE_REMOTE_001.log
```

#### 4.3 使用API检查
```bash
# 获取所有账户状态
curl http://YOUR_SERVER_IP:8000/api/accounts/status

# 获取特定账户信息
curl http://YOUR_SERVER_IP:8000/api/accounts/MASTER_REMOTE_001
```

### 5. 高级配置

#### 5.1 多账户管理

可以在同一台机器上运行多个账户：

```bash
# 启动多个主账户
python scripts/remote_terminal.py --config config/master1.yaml --role master &
python scripts/remote_terminal.py --config config/master2.yaml --role master &

# 启动多个从账户
python scripts/remote_terminal.py --config config/slave1.yaml --role slave &
python scripts/remote_terminal.py --config config/slave2.yaml --role slave &
```

#### 5.2 跟单策略配置

在从账户配置中调整跟单策略：

```yaml
copy_settings:
  # 手数计算模式
  lot_calculation_mode: "proportional"  # 可选: fixed/proportional/risk_based
  
  # 信号过滤器
  signal_filters:
    min_profit_pips: 10      # 只跟单盈利超过10点的信号
    max_drawdown_pips: 50    # 忽略回撤超过50点的信号
    allowed_symbols:         # 只跟单特定品种
      - "EURUSD"
      - "GBPUSD"
```

#### 5.3 风险管理

```yaml
risk_management:
  max_positions: 5          # 最大同时持仓数
  max_lot_size: 1.0        # 单笔最大手数
  max_daily_loss: 100      # 日最大亏损（美元）
  max_daily_trades: 20     # 日最大交易次数
```

### 6. 故障排除

#### 6.1 连接失败

1. 检查网络连接：
```bash
# 测试NATS连接
telnet YOUR_SERVER_IP 4222

# 测试Redis连接
redis-cli -h YOUR_SERVER_IP ping
```

2. 检查防火墙设置
3. 确认服务器端Docker容器正在运行

#### 6.2 MT5连接问题

1. 确保MT5终端已安装并可以正常登录
2. 检查terminal_path路径是否正确
3. 确认账户密码正确（使用环境变量）

#### 6.3 跟单不执行

1. 检查主账户是否正常发送信号
2. 确认从账户订阅设置正确
3. 查看信号过滤器是否过于严格
4. 检查账户余额是否充足

### 7. 监控和维护

#### 7.1 实时监控

- Web界面：`http://YOUR_SERVER_IP:3001`
- Grafana监控：`http://YOUR_SERVER_IP:3000` (admin/admin)

#### 7.2 日志分析

定期检查日志文件：
- 错误日志：`logs/error.log`
- 交易日志：`logs/trades/`
- 性能日志：`logs/performance/`

#### 7.3 定期维护

1. 每周检查系统性能
2. 每月清理旧日志文件
3. 定期更新配置参数
4. 监控账户盈亏情况

## 安全建议

1. **使用VPN或专线**连接，避免直接暴露端口
2. **启用API认证**，设置强密码
3. **定期更换密码**，包括MT5和系统密码
4. **限制IP访问**，只允许特定IP连接
5. **启用SSL/TLS**加密通信

## 联系支持

如遇到问题，请提供以下信息：
- 配置文件（隐藏敏感信息）
- 错误日志
- 系统环境信息